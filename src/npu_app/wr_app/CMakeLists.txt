cmake_minimum_required(VERSION 2.8.3)
project(wr_app)

## Find catkin macros and libraries
## if COMPONENTS list like find_package(catkin REQUIRED COMPONENTS xyz)
## is used, also find other catkin packages
find_package(catkin REQUIRED
    wr_npu_ice
)

## System dependencies are found with CMake's conventions
# find_package(Boost REQUIRED COMPONENTS system)


## Uncomment this if the package has a setup.py. This macro ensures
## modules and global scripts declared therein get installed
## See http://ros.org/doc/api/catkin/html/user_guide/setup_dot_py.html
# catkin_python_setup()

################################################
## Declare ROS messages, services and actions ##
################################################

## To declare and build messages, services or actions from within this
## package, follow these steps:
## * Let MSG_DEP_SET be the set of packages whose message types you use in
##   your messages/services/actions (e.g. std_msgs, actionlib_msgs, ...).
## * In the file package.xml:
##   * add a build_depend and a run_depend tag for each package in MSG_DEP_SET
##   * If MSG_DEP_SET isn't empty the following dependencies might have been
##     pulled in transitively but can be declared for certainty nonetheless:
##     * add a build_depend tag for "message_generation"
##     * add a run_depend tag for "message_runtime"
## * In this file (CMakeLists.txt):
##   * add "message_generation" and every package in MSG_DEP_SET to
##     find_package(catkin REQUIRED COMPONENTS ...)
##   * add "message_runtime" and every package in MSG_DEP_SET to
##     catkin_package(CATKIN_DEPENDS ...)
##   * uncomment the add_*_files sections below as needed
##     and list every .msg/.srv/.action file to be processed
##   * uncomment the generate_messages entry below
##   * add every package in MSG_DEP_SET to generate_messages(DEPENDENCIES ...)

## Generate messages in the 'msg' folder
# add_message_files(
#   FILES
#   Message1.msg
#   Message2.msg
# )

## Generate services in the 'srv' folder
# add_service_files(
#   FILES
#   Service1.srv
#   Service2.srv
# )

## Generate actions in the 'action' folder
# add_action_files(
#   FILES
#   Action1.action
#   Action2.action
# )

## Generate added messages and services with any dependencies listed here
# generate_messages(
#   DEPENDENCIES
#   std_msgs  # Or other packages containing msgs
# )

###################################
## catkin specific configuration ##
###################################
## The catkin_package macro generates cmake config files for your package
## Declare things to be passed to dependent projects
## INCLUDE_DIRS: uncomment this if you package contains header files
## LIBRARIES: libraries you create in this project that dependent projects also need
## CATKIN_DEPENDS: catkin_packages dependent projects also need
## DEPENDS: system dependencies of this project that dependent projects also need
catkin_package(
#  INCLUDE_DIRS include
#  LIBRARIES test
#  CATKIN_DEPENDS other_catkin_pkg
#  DEPENDS system_lib
)

###########
## Build ##
###########

## Specify additional locations of header files
## Your package locations should be listed before other locations
# include_directories(include)

## Declare a cpp library
# add_library(test
#   src/${PROJECT_NAME}/test.cpp
# )

## Declare a cpp executable
# add_executable(test_node src/test_node.cpp)

## Add cmake target dependencies of the executable/library
## as an example, message headers may need to be generated before nodes
# add_dependencies(test_node test_generate_messages_cpp)

## Specify libraries to link a library or executable target against
# target_link_libraries(test_node
#   ${catkin_LIBRARIES}
# )
if(${CMAKE_SYSTEM_PROCESSOR} STREQUAL "x86")
    set(_platform "x86")
elseif(${CMAKE_SYSTEM_PROCESSOR} STREQUAL "x86_64")
    set(_platform "x86_64")
elseif(${CMAKE_SYSTEM_PROCESSOR} MATCHES "arm")
    set(_platform "armhf")
endif()
set(WR_ICEAPI_FILE ${wr_npu_ice_SOURCE_DIR}/slice/npuice_api.ice)
add_custom_target(system_info_file ALL)
add_custom_command(TARGET system_info_file
  COMMAND echo "Generating system info file..."
  COMMAND
    ${PROJECT_SOURCE_DIR}/yaml-generator.py
  COMMAND
    cp ${PROJECT_SOURCE_DIR}/kobuki.yaml ${PROJECT_SOURCE_DIR}/config/kobuki.yaml
  COMMAND
    ${PROJECT_SOURCE_DIR}/make-system-info-file.sh
        ${_platform}
        ${WR_RELEASE_FLAG}
        ${WR_DEFAULT_CONFIG}
        ${WR_DEFAULT_MAP}
        ${WR_ICEAPI_FILE}
        ${WR_DEFAULT_BAG}
  COMMAND
    rm -rf ${CMAKE_INSTALL_PREFIX}/version
  COMMAND
    cp ${PROJECT_SOURCE_DIR}/version.${_platform} ${CMAKE_INSTALL_PREFIX}/version
  COMMAND
    rm -rf ${CMAKE_INSTALL_PREFIX}/config
  COMMAND
    cp -r ${PROJECT_SOURCE_DIR}/config ${CMAKE_INSTALL_PREFIX}/config
  COMMAND
    cp ${PROJECT_SOURCE_DIR}/update-npu-link ${CMAKE_INSTALL_PREFIX}/update-npu-link
  COMMENT ""
)
#############
## Install ##
#############

# all install targets should use catkin DESTINATION variables
# See http://ros.org/doc/api/catkin/html/adv_user_guide/variables.html

## Mark executable scripts (Python etc.) for installation
## in contrast to setup.py, you can choose the destination
# install(PROGRAMS
#   scripts/my_python_script
#   DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark executables and/or libraries for installation
# install(TARGETS test test_node
#   ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark cpp header files for installation
# install(DIRECTORY include/${PROJECT_NAME}/
#   DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
#   FILES_MATCHING PATTERN "*.h"
#   PATTERN ".svn" EXCLUDE
# )
install(DIRECTORY launch
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
  USE_SOURCE_PERMISSIONS
)

install(DIRECTORY bag map script
  DESTINATION ${CMAKE_INSTALL_PREFIX}
  USE_SOURCE_PERMISSIONS
)

install(FILES rules/57-ttyMapper.rules rules/57-ttyMapper.py
  DESTINATION ./rules
)

## Mark other files for installation (e.g. launch and bag files, etc.)
# install(FILES
#   script/npu_autostart.sh
#   script/npu_check.sh
#   script/npu_config.sh
#   script/npu_run.sh
#   script/npu_setup.sh
#   script/npu_stop.sh
#   DESTINATION ${CMAKE_INSTALL_PREFIX}
# )

#############
## Testing ##
#############

## Add gtest based cpp test target and link libraries
# catkin_add_gtest(${PROJECT_NAME}-test test/test_test.cpp)
# if(TARGET ${PROJECT_NAME}-test)
#   target_link_libraries(${PROJECT_NAME}-test ${PROJECT_NAME})
# endif()

## Add folders to be run by python nosetests
# catkin_add_nosetests(test)
