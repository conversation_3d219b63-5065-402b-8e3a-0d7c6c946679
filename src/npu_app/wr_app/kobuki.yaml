##############################################################################
# Firmware Source
##############################################################################

device_port: /dev/kobuki

# published joint states
wheel_left_joint_name: wheel_left_joint
wheel_right_joint_name: wheel_right_joint

# battery voltage at full charge (100%) (float, default: 16.5)
battery_capacity: 16.5

# battery voltage at first warning (15%) (float, default: 13.5)
battery_low: 14.0

# battery voltage at critical level (5%) (float, default: 13.2)
battery_dangerous: 13.2

# If a new command isn't received within this many seconds, the base is stopped (double, default: 0.6)
cmd_vel_timeout: 0.6

# Causes node to publish TF for odom_frame to base_frame. Disable only if you plan to use robot_pose_ekf
# (see use_imu_heading description) (bool, default: true)
publish_tf: true

# Use imu readings for heading instead of encoders. That's the normal operation mode for Kobuki, as its
# gyro is very reliable. Disable only if you want to fuse encoders and imu readings in a more sophisticated
# way, for example filtering and fussing with robot_pose_ekf (bool, default: true)
use_imu_heading: true

# Name of the odometry TF frame (string, default: odom)
odom_frame: odom_frame

# Name of the base TF frame  (string, default: base_footprint)
base_frame: base_ftp_frame
