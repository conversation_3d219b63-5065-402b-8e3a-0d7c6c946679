#! /bin/bash

if [ "$1" == "--ssh-client" ] ; then
    RED=""
    GREEN=""
    END=""
    SUDO=""
else
    RED="$(tput setaf 1)"
    GREEN="$(tput setaf 2)"
    END="$(tput sgr 0)"
    SUDO="sudo "
fi

echo "${RED}Need root permission...${END}"
if [ "${SUDO}" != "" ] ; then
    if [ "${LOC_PASSWORD}" != "" ] ; then
        echo ${LOC_PASSWORD} | ${SUDO} -S -p "" echo "" > /dev/null
    else
        ${SUDO} echo "" > /dev/null
    fi
fi
if [ "$?" != "0" ] ; then
    exit
fi

NPU_INSTALL_PATH="/npu"
echo "Set install_dir(${RED}\"${NPU_INSTALL_PATH}\"${END})"

if [ -e "../.npu_project" ]; then
    echo "Change current directory to config_dir(${RED}\"`dirname $0`\"${END})"
    cd `dirname $0`
    echo "Copy file to install_dir."
    file_list="npu_config.sh npu_run.sh npu_setup.sh npu_autostart.sh npu_stop.sh"
    for _file in ${file_list}; do
        echo "  ${RED}${_file}${END} -> ${RED}${NPU_INSTALL_PATH}/${_file}${END}"
        cp -rf ./${_file} ${NPU_INSTALL_PATH}/${_file}
        chmod +x ${NPU_INSTALL_PATH}/${_file}
    done
fi

echo "Change current directory to install_dir(${RED}\"${NPU_INSTALL_PATH}\"${END})"
cd ${NPU_INSTALL_PATH}
if [ -e "./.npu_install" ]; then
    echo "Link ${RED}./npu_run.sh${END} -> ${RED}/bin/npu_run${END}"
    _tmp=`ls -l /bin/ | grep "^l" | awk '{print $11}'`
    if [ "${_tmp}" != "${NPU_INSTALL_PATH}/npu_run" ]; then
        ${SUDO} rm -rf /bin/npu_run
    fi

    ${SUDO} ln -s -f ${NPU_INSTALL_PATH}/npu_run.sh /bin/npu_run

    echo "Copy ${RED}./rules/*${END} -> ${RED}/etc/udev/rules.d/*${END}"
    ${SUDO} cp -f ${NPU_INSTALL_PATH}/rules/* /etc/udev/rules.d/
    ${SUDO} service udev reload
    ${SUDO} service udev restart
fi

if [ "`uname -i`" == "armv7l" ] ; then
    echo "Link ${RED}/odroid_root${END} -> ${RED}/${END}"
    ${SUDO} rm -rf /odroid_root
    ${SUDO} ln -s -f / /odroid_root
fi

echo "${GREEN}All done${END}"

