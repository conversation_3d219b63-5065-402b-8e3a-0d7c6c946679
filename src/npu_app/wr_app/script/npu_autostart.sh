#! /bin/bash

ret=`ps -ef | grep -i -v grep | grep -i -v "$0" | grep -i -e /npu.x86_64 -e /etc/ros`
if [ x"${ret}" != x"" ]; then
    echo ""
    echo "$(tput -T xterm setaf 1)There is one(or more) another ROS process running, please terminate it first.$(tput -T xterm sgr 0)"
    exit 0
fi

first_char=${0:0:1}
if [ x"${first_char}" == x"/" ] ; then
  _dir=`dirname $0`
else
  _pwd=`pwd`
  _dir=`dirname ${_pwd}/$0`
fi
_basename=`basename $0`
cd ${_dir}

logger_level="info"
master_ip="127.0.0.1"
master_port="11311"
gateway_ip="***********"

ARGS=`getopt -o "" -l "log-level:,master-ip:,master-port:,gateway:" -n "$_basename" -- "$@"`
eval set -- "${ARGS}"
while true; do
 case "${1}" in
   --log-level)
     shift
     logger_level=${1}
     ;;
   --master-ip)
     shift
     master_ip=${1}
     ;;
   --master-port)
     shift
     master_port=${1}
     ;;
   --gateway)
     shift
     gateway_ip=${1}
     ;;
   --)
     shift
     if [ x"${1}" != x"" ]; then
       echo "Argument ERROR \"${1}\""
       exit 1
     fi
     break
     ;;
 esac
 shift
done

NPU_PATH="${_dir}/../"

if [[ ${master_ip} != "127.0.0.1" && ${master_ip} != "localhost" && ${gateway_ip} != "null" ]]; then
  #Determine if the external LAN is fluent.
  Timeout=0;
  Timeout_limit=200;
  Timeout_flag=0;

  while [ "${Timeout_limit}" -gt "100" ]
  do
    Timeout="`ping ${gateway_ip} -c 3 | grep 'min/avg/max/mdev' | awk '{print $4}' | cut -b '7'`"
    Timeout_limit=${Timeout}
    if [ -z "${Timeout}" ] ; then
      Timeout_limit=300
    else
      Timeout_limit=${Timeout}
    fi
    echo "Timeout limit is : ${Timeout_limit}"
    sleep 1
  done
  echo "The LAN network is fluency!"
fi

function check_dir_size()
{
  _target_dir_=$1
  _max_size_=$2

  echo -n "Check size of \"${_target_dir_}\"..."
  if [ ! -d ${_target_dir_} ]; then
    mkdir -p ${_target_dir_}
  fi
  cd ${_target_dir_}
  _size_=`du -s ${_target_dir_} | awk '{print $1}'`
  [ "x${_size_}" == "x" ] && _size_=0
  if [ ${_size_} -gt ${_max_size_} ] ; then
    echo " $(tput -T xterm setaf 1)It is $_size_ KB(>${_max_size_}), removed it$(tput -T xterm sgr 0)."
    rm -rf ${_target_dir_}/*
  else
    echo " $(tput -T xterm setaf 2)It is $_size_ KB$(tput -T xterm sgr 0)."
  fi
}

max_log_size=$((1024*1024))
max_bag_size=$((3*1024*1024))

check_dir_size ~/.ros/log/ ${max_log_size}
check_dir_size /log/npu ${max_log_size}
check_dir_size /log/bag ${max_bag_size}

rm -rf ~/.bash_history

cd ${NPU_PATH}
source ./setup.bash

export ROS_MASTER_URI=http://${master_ip}:${master_port}
export ROS_HOSTNAME=${master_ip}
export CLIENT_ID=app
export BASE_ID=linkmiao

roslaunch wr_${CLIENT_ID} start_core.launch logger_level:=${logger_level} &
