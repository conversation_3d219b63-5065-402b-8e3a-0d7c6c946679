##! /bin/bash
#case $1 in
#"")
#    navi_type="p2p"
#    #echo "set P2P"
#    ;;
#"--p2p")
#    navi_type="p2p"
#    #echo "set P2P"
#    ;;
#"--pf")
#    navi_type="pf"
#    #echo "set pf"
#    ;;
#*)
#    echo "Arguments error!"
#    ;;
#esac

#case $2 in
#"")
#    slam_type="icpslam"
#    #echo "set icpslam!"
#    ;;
#"--icpslam")
#    slam_type="icpslam"
#    #echo "set icpslam"
#    ;;
#"--pfslam")
#    slam_type="pfslam"
#    #echo "set pfslam"
#    ;;
#*)
#    echo "Arguments error!"
#    ;;
#esac

#export NPU_HOST_NAME=`hostname -I | awk '{print $1}'`

## export NPU_VERSION=*******

## #map id: {...}
## export MAP_ID=map0

## #client id: {wizrobo, zddk, csst, hwl, hhkj...}
## export CLIENT_ID=app

## #mobile base id: {kobuki, csst, zddk...}
## export BASE_ID=linkmiao

## #enable npu mode
## export ENB_SLAVE_MODE=false

## #sensor type: {lidar, rgbd, 2.5D-lidar, 3D-lidar, lidar_imu, lidar_camera...}
## export SENSOR_TYPE=lidar

## #sensor id: {rplidar, lslidar, delidar, lms111, lms151, tim551, tim561, xtion,
## #            kinect, kinect2, hdl16, r2000-20hz, r2000-50hz...}
## export LIDAR_ID=rplidar

## #lidar ip (only works for ethernet lidars)
## export LIDAR_IP=***************

##navi type: {p2p, gpt, cpp}
#if [ ${navi_type} = "p2p" ]; then
#    export NAVI_TYPE=p2p
#    #echo "set navi_tpye-->p2p"
#fi

#if [ ${navi_type} = "pf" ]; then
#    export NAVI_TYPE=pf
#    #echo "set navi_tpye-->pf"
#fi

##slam type: {icpslam, pfslam}
#if [ ${slam_type} = "icpslam" ]; then
#    export SLAM_TYPE=icpslam
#    #echo "set slam_tpye-->icpslam"
#fi

#if [ ${slam_type} = "pfslam" ]; then
#    export SLAM_TYPE=pfslam
#    #echo "set slam_tpye-->pfslam"
#fi
##npu_type:{npu_std,npu_pro}
#export NPU_TYPE=npu_std

##npu_config_file
#export NPU_CONFIG_FILE=npu_config.sh
