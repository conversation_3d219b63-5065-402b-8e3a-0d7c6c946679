#! /bin/bash

NPU_PATH="/npu"

if [ $# -lt 2 ]; then
    echo "Arguments error!"
fi

case $1 in
"")
    action="check_imu:=true check_base:=true check_lidar:=true"
    action_s="all"
    echo "check all"
    ;;
"--all")
    action="check_imu:=true check_base:=true check_lidar:=true"
    action_s="all"
    echo "check all"
    ;;
"--lidar")
    action="check_imu:=false check_base:=false check_lidar:=true"
    action_s="lidar"
    echo "lidar !"
    ;;
"--imu")
    action="check_imu:=true check_base:=false check_lidar:=false"
    action_s="imu"
    echo "imu !"
    ;;
"--bcu")
    action="check_imu:=false check_base:=true check_lidar:=false"
    action_s="odom"
    echo "bcu !"
    ;;
*)
    echo "Arguments error!"
    ;;
esac

cd ${NPU_PATH}

sh ./script/npu_check.sh

echo "$(tput setaf 2)Start...$(tput sgr 0)"
echo "$(tput setaf 2)>> Setup...$(tput sgr 0)"
ROS_MASTER_URI=http://localhost:11311
ROS_HOSTNAME=`hostname -I | awk '{print $1}'`
source ./setup.bash

echo "$(tput setaf 2)>>> roslaunch wr_app check_sensor.launch ${action} logger_level:=debug $2 --screen$(tput sgr 0)"
python /npu/lib/wr_sensor_status/check_port.py ${action_s} $2
