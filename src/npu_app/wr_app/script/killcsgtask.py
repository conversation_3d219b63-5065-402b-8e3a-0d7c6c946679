#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import psutil
import sys

def get_all_children(parent_process):
    children_list = parent_process.get_children()
    if children_list == []:
        return []
    _list = []
    for children in children_list:
        _list.append(children)
        _list += get_all_children(children)
    return _list

def terminate_processes(process_list):
    for process in process_list:
        try:
            print "Process interrupt ", process.pid, process.name, " ".join(process.cmdline)
            #process.terminate()
            process.send_signal(2)
        except exception, e:
            print e
    for process in process_list:
        try:
            process.wait(5)
        except psutil.TimeoutExpired, e:
            print " kill", process.pid, process.name, " ".join(process.cmdline)
            process.kill()

def kill_processes(process_list):
    for process in process_list:
        try:
            print process.pid, process.name, " ".join(process.cmdline)
            process.kill()
        except exception, e:
            print e

if __name__ == "__main__":
    pid_iter = psutil.process_iter()
    regex_pattern = r"bash /npu\.(armhf|x86_64)/script/task_csg_run\.sh"
    parent = None
    for process in pid_iter:
        cmdline = " ".join(process.cmdline)
        if cmdline == "" :
            continue
        if re.match(regex_pattern, cmdline) != None:
            parent = process
            break
    else:
        sys.exit(0)
    children_list = get_all_children(parent)
    #kill_processes(children_list)
    terminate_processes(children_list)
