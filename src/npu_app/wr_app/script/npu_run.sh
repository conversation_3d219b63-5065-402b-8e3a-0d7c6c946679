#! /bin/bash

first_char=${0:0:1}
if [ x"${first_char}" == x"/" ] ; then
  _dir=`dirname $0`
else
  _pwd=`pwd`
  _dir=`dirname ${_pwd}/$0`
fi
_basename=`basename $0`
cd ${_dir}

NPU_PATH="${_dir}/../"

if [ $# -lt 2 ]; then
    echo "Arguments error!"
fi

case $1 in
"--navi")
    action="navi"
    ;;
"--slam")
    action="slam"
    ;;
*)
    echo "Arguments error!"
    ;;
esac

source ${NPU_PATH}/setup.bash

echo "$(tput setaf 2)>>> roslaunch wr_app start_${action}.launch logger_level:=debug $2 --screen$(tput sgr 0)"
roslaunch wr_app start_${action}.launch logger_level:=debug $2 --screen
