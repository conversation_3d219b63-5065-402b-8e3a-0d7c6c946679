#! /bin/bash

#_pwd=`pwd`
_dir=`dirname $0`
_basename=`basename $0`
cd $_dir

if [ x"$#" != x"6" ]
then
    exit 1
fi

platform=$1
if [ "${platform}"x == "armeabi-hf"x ] ; then
    platform="armhf"
fi
release_flag=$2
default_config=$3
default_map=$4
ice_file=$5
default_bag=$6

#cd ${git_repo_dir}

#git status > /dev/null
#if [ x"$?" != x"0" ]
#then
#    cd -
#    exit 1
#fi
#branch_name=`git status | head -n1 | cut -d " " -f 3-`
branch_name="xxxxxxx"
#NPU.v2.2.0.20171104.christmas_x86_64_nightly_20180116101747
sys_version=${branch_name}_${platform}_${release_flag}_`date +"%Y%m%d%H%M%S"`

_tmp=`cat ${ice_file} | grep NPU_API_VERSION | cut -d \" -f 2`
#_tmp=${_tmp##*=}
#_tmp=${_tmp%%;*}
iceapi_version=${_tmp}

rm -rf ./version
echo ${sys_version} > ./version.${platform}
echo ${iceapi_version} >> ./version.${platform}
echo auto >> ./version.${platform}

if [ x"${default_config}" == x"" -o ! -d ./config/user/${default_config} ]
then
    default_config="linkmiao"
fi

rm -rf ./config/factory
rm -rf ./config/user/default 

cp -r ./config/user/${default_config} ./config/factory
cp -r ./config/user/${default_config} ./config/user/default

echo "## default_config: ${default_config}
CONFIG_ID : default
MAP_ID    : ${default_map} 
BAG_ID    : ${default_bag}
##-- debug --##
LOGGER_LEVEL    : \"info\"
##-- system information --##
PLATFORM    : ${platform}
IP          : auto
SYS_VERSION : ${sys_version}
ICE_VERSION : ${iceapi_version}
" > ./config/core.yaml
