#! /bin/bash

# short for "set -o errexit"
#set -e 

platform=`uname -p`
if [ x"${platform}" == x"armv7l" ]; then
    platform="armhf"
fi

first_char=${0:0:1}
if [ x"${first_char}" == x"/" ] ; then
  _dir=`dirname $0`
else
  _pwd=`pwd`
  _dir=`dirname ${_pwd}/$0`
fi
_basename=`basename $0`
cd ${_dir}

sudo ls -l /npu.${platform}/ > /dev/null
if [ x"$?" == x"0" ]; then
    surfix=`ls -1 | grep config_map_backup_ | wc -l`
    backup_name="config_map_backup_${surfix}.tar.gz"
    tar -czf ./${backup_name} ./config ./map

    if [ x"$1" != x"--without-config" ]; then
        rm -rf ./config
        cp -r /npu.${platform}/config ./
    fi
    rm -rf ./map
    cp -r /npu.${platform}/map ./
fi

sudo rm -rf /npu.${platform}
sudo ln -s ${_dir} /npu.${platform}
sudo chown -R linkmiao:linkmiao /npu.${platform}
