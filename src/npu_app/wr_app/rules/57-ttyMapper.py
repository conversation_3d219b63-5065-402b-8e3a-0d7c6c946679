#!/usr/bin/env python
# *-* utf-8 *-*

import os
import re
import sys
import stat

#corresponding command: /sys/`udevadm info -q path -n /dev/ttyUSBX`
device_set = {
    #"/sys/bus/usb/drivers/usb/usb1/1-1/1-1.4/1-1.4:1.0/" : "fslidar",       # faselaser
    "/sys/bus/usb/drivers/usb/usb3/3-1/3-1.1/3-1.1:1.0/" : "bcu",           # odroid without usb hub
    "/sys/bus/usb/drivers/usb/usb3/3-1/3-1.2/3-1.2:1.0/" : "imu",    # odroid without usb hub
}


def GetPortNameFromDevicePath(path):
    if os.path.exists(path) == False:
        return "" 
    object_list = os.listdir(path)
    for obj in object_list:
        if re.match(r"ttyUSB\d+", obj)==None and re.match(r"ttyACM\d+", obj)==None:
            continue
        break
    else:
        return ""
    return obj

def ClearSymbolLink(link_name):    
    if os.path.islink("/dev/"+link_name) == True:
        os.remove("/dev/"+link_name)

def MakeSymbolLink(port_name, link_name):
    if os.path.realpath("/dev/"+link_name) == "/dev/"+port_name:
        return
    if os.path.islink("/dev/"+link_name):
        os.remove("/dev/"+link_name)
    os.symlink("/dev/"+port_name, "/dev/"+link_name)
    os.chmod("/dev/"+link_name, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)

if __name__ == "__main__" :
    for path in device_set:
        link_name = device_set[path]
        if link_name=="bcu" and os.path.islink("/dev/bcu") and re.match(".*ttySAC0", os.path.realpath("/dev/bcu")) != None:
            continue
        port_name = GetPortNameFromDevicePath(path)
        if port_name == "":
            ClearSymbolLink(link_name)
        else:
            MakeSymbolLink(port_name, link_name)
    sys.exit(0)
