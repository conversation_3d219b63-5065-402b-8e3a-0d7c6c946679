<?xml version="1.0"?>
<!-- change log:
created by lawrence.han @2017-04-03;
updated by lhan@2017-04-07;
updated by lhan@2017-05-23;
updated by lhan@2017-06-02;
updated by lhan@2017-06-25;
updated by lhan@2017-08-27;
-->

<launch>
<arg name="launch_dir" value="$(find wr_app)/launch"/>
<arg name="logger_level" default="info"/>

<!-- bag record & play -->
<arg name="bag_id" default="lm_170725_1429"/>
<arg name="bag_topics" default="/scan /imu /odom"/>
<arg name="enb_record" default="false"/>
<arg name="record_args" default=""/>
<arg name="enb_play" default="false"/>
<arg name="play_args" default="-r 1.0"/>

<!-- 1. start npu server -->
<include file="$(arg launch_dir)/include/core/npu_server.launch.xml">
    <arg name="logger_level" value="$(arg logger_level)"/>
    <arg name="version_file" value="$(arg launch_dir)/../../../version"/>
    <arg name="wait_for_param" value="true"/>
    <arg name="server_type" value="ice"/>
    <arg name="serial_port" value="/dev/comi"/>
    <arg name="baud_rate" value="9600"/>
    <arg name="data_bit" value="8"/>
    <arg name="parity" value="NONE"/>
    <arg name="com_type" value="SERIAL_LM"/>
</include>

<!-- 2. start map server -->
<include file="$(arg launch_dir)/include/core/map_server.launch.xml">
    <arg name="logger_level" value="$(arg logger_level)"/>
</include>

<!-- 3. Start tf server -->
<include file="$(arg launch_dir)/include/core/tf_publisher.launch.xml">
    <arg name="logger_level" value="$(arg logger_level)"/>
</include>

<!-- 4. start bag server -->
<include file="$(arg launch_dir)/include/core/bag_server.launch.xml">
    <arg name="logger_level" value="$(arg logger_level)"/>
    <arg name="bag_id" value="$(arg bag_id)"/>
    <arg name="bag_topics" value="$(arg bag_topics)"/>
    <arg name="enb_record" value="$(arg enb_record)"/>
    <arg name="record_args" value="$(arg record_args)"/>
    <arg name="enb_play" value="$(arg enb_play)"/>
    <arg name="play_args" value="$(arg play_args)"/>
</include>


</launch>
