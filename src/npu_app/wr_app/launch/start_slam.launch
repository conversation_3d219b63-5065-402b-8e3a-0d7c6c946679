<?xml version="1.0"?>


<launch>
<arg name="launch_dir" value="$(find wr_app)/launch"/>
<arg name="logger_level" default="info"/>
<arg name="enb_handheld_mode" default="false"/>

<!-- slam args -->
<arg name="slam_mode" default="gh_slam"/><!-- enum {icp_slam, pf_slam, wo_icp_slam, gh_slam} -->

<!-- bag record & play -->
<arg name="bag_id" default=""/>
<arg name="bag_topics" default="/scan /imu/data_raw /wo_odom"/>
<arg name="enb_record" default="true"/>
<arg name="record_args" default=""/>
<arg name="enb_play" default="false"/>
<arg name="play_args" default="-r 1.0"/>
<arg name="bag_dir" default="/log/bag" />

<!-- 1. start slam -->
<include file="$(arg launch_dir)/include/slam/slam.$(arg slam_mode).launch.xml">
    <arg name="launch_dir" value="$(arg launch_dir)"/>
    <arg name="logger_level" value="$(arg logger_level)"/>
    <arg name="enb_handheld_mode" value="$(arg enb_handheld_mode)"/>
</include>

<!-- 2. Start pose monitor -->
<include file="$(arg launch_dir)/include/locl/pose_monitor.launch.xml" />

<!-- 3. start bag server -->
<include file="$(arg launch_dir)/include/core/bag_server.launch.xml">
    <arg name="logger_level" value="$(arg logger_level)"/>
    <arg name="bag_id" value="$(arg bag_id)"/>
    <arg name="bag_topics" value="$(arg bag_topics)"/>
    <arg name="enb_record" value="$(arg enb_record)"/>
    <arg name="record_args" value="$(arg record_args)"/>
    <arg name="enb_play" value="$(arg enb_play)"/>
    <arg name="play_args" value="$(arg play_args)"/>
    <arg name="bag_dir" default="$(arg bag_dir)" />
</include>

</launch>

