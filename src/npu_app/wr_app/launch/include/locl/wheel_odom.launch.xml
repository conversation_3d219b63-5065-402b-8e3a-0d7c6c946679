<?xml version="1.0"?>

<launch>
<arg name="logger_level" default="info"/>
<arg name="enb_imu" default="true"/>
<arg name="pub_odom_tf" default="true"/>

<arg name="odom_topic" default="/odom"/>
<arg name="imu_topic" default="/imu_ftp"/>
<arg name="fusion_pose_est_topic" default=""/>
<arg name="pos_fusion_cov_thrs" default="0.01"/>
<arg name="ori_fusion_cov_thrs" default="0.01"/>
<arg name="pose_fusion_mode"   default="cov_weighting"/>

<arg name="map_frame" default="/map_frame"/>
<arg name="odom_frame" default="/odom_frame"/>
<arg name="base_frame" default="/base_ftp_frame"/>

<arg name="use_wheel_odom" value="false"/>
<arg name="enb_imu_rect" value="true"/>

<!-- 1. start wheel_odom -->
<node if="$(arg use_wheel_odom)" name="wheel_odom_node" pkg="wr_wheel_odom" type="wheel_odom_node" output="screen">
    <param name="logger_level" value="$(arg logger_level)"/>
    <!-- enb -->
    <param name="enb_imu_rect" value="$(arg enb_imu_rect)"/>
    <param name="use_exact_sync" value="false"/>
    <!-- frame -->
    <param name="map_frame" value="$(arg map_frame)"/>
    <param name="odom_frame" value="$(arg odom_frame)"/>
    <param name="base_frame" value="$(arg base_frame)"/>
    <!-- tf -->
    <param name="pub_map_to_odom_tf" value="false"/>
    <param name="pub_odom_to_base_tf" value="$(arg pub_odom_tf)"/>
    <!-- sub -->
    <param name="odom_topic" value="$(arg odom_topic)"/>
    <param name="imu_topic" value="$(arg imu_topic)"/>
    <!-- fusion -->
    <param name="fusion_pose_est_topic" value="$(arg fusion_pose_est_topic)"/>
    <param name="pos_fusion_cov_thrs" value="$(arg pos_fusion_cov_thrs)"/>
    <param name="ori_fusion_cov_thrs" value="$(arg ori_fusion_cov_thrs)"/>
    <param name="pose_fusion_mode"   value="$(arg pose_fusion_mode)"/>
</node>

<!-- 2. input_ekf -->
<node unless="$(arg use_wheel_odom)" name="wo_ekf_node" pkg="robot_pose_ekf" type="robot_pose_ekf" output="screen" >
  <param name="logger_level" value="$(arg logger_level)"/>
  <param name="freq" value="30.0"/>
  <param name="sensor_timeout" value="1.0"/>
  <!-- enb -->
  <param name="imu_used" value="$(arg enb_imu)"/>
  <param name="odom_used" value="true"/>
  <param name="vo_used" value="false"/>
  <!-- frame -->
  <param name="output_frame" value="/odom_frame"/>
  <param name="base_footprint_frame" value="/base_ftp_frame"/>
  <!-- sub -->
  <param name="imu_topic" value="$(arg imu_topic)"/>
  <param name="odom_topic" value="$(arg odom_topic)"/>
  <!--param name="vo_topic" value=""/-->
  <!-- pub -->
  <!--
  <remap from="~/odom_ekf" to="/odom_ekf"/>
  <remap from="~/vo_ekf" to="/vo_ekf"/>
  -->
  <remap from="~/ekf_odom" to="/wo_odom"/>
  <remap from="~/ekf_pose_est" to="/wo_pose_est"/>
  <remap from="~/ekf_pose" to="/wo_pose"/>
  <!-- tf -->
  <param name="pub_output_to_base_tf" value="$(arg pub_odom_tf)"/>
</node>
</launch>
