<?xml version="1.0" ?>
<!-- -*- mode: XML -*- -->
<!-- v1.0 created by tommy @2017.04.24 -->
<launch>
<!-- wizrobo standard launch arg -->
<arg name="logger_level" default="info" />
<!-- Start tf listener -->
    <node pkg="wr_pose_monitor" name="pose_monitor_node" type="pose_monitor_node" respawn="true" output="screen" >
        <!--param name="logger_level" value="$(arg logger_level)"/-->
        <param name="pose_topic" value="wizrobo/pose"/>
        <param name="echo_topic" value="odom"/>
        <param name="source_frame_id" value="map_frame"/>
        <param name="target_frame_id" value="base_frame"/>
        <param name="pose_pub_rate" value="16"/>
        <param name="resample_min_d" value="6.0"/>
        <param name="enb_direct_sensor_trans" value="false"/>
        <param name="enb_correlation" value="false"/>
        <param name="enb_pf_resample" value="false"/>
    </node>
</launch>
