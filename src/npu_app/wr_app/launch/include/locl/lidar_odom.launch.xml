<?xml version="1.0"?>

<launch>
<arg name="logger_level" default="info"/>
<arg name="enb_imu" default="true"/>
<arg name="pub_odom_tf" default="true"/>

<arg name="fusion_pose_est_topic" default=""/>
<arg name="pos_fusion_cov_thrs" default="0.01"/>
<arg name="ori_fusion_cov_thrs" default="0.01"/>
<arg name="pose_fusion_mode"   default="cov_weighting"/>

<!-- 1. start lidar_odom -->
<node name="lidar_odom_node" pkg="wr_lidar_odom" type="lidar_odom_node" output="screen">
	<param name="logger_level" value="$(arg logger_level)"/>
    <param name="pos_x_jump_thrs_m" value="1.0"/>
    <param name="pos_y_jump_thrs_m" value="1.0"/>
    <param name="ori_jump_thrs_rad" value="0.5"/>
    <param name="map_level_num" value="3"/>
    <param name="cov_sigma_scalar" value="0.1"/>
    <param name="use_exact_sync" value="false"/>
    <!-- enb -->
    <param name="pub_scan_pc" value="false"/>
    <!-- frame -->
    <param name="map_frame" value="/map_frame"/>
    <param name="odom_frame" value="/map_frame"/>
    <param name="base_frame" value="/base_ftp_frame"/>
    <!-- tf -->
    <param name="pub_map_to_odom_tf" value="false"/>
    <param name="pub_odom_to_base_tf" value="$(arg pub_odom_tf)"/>
    <!-- fusion -->
    <param name="fusion_pose_est_topic" value="$(arg fusion_pose_est_topic)"/>
    <param name="pos_fusion_cov_thrs" value="$(arg pos_fusion_cov_thrs)"/>
    <param name="ori_fusion_cov_thrs" value="$(arg ori_fusion_cov_thrs)"/>
    <param name="pose_fusion_mode"   value="$(arg pose_fusion_mode)"/>
</node>

</launch>
