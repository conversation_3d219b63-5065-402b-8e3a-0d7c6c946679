<launch>
<arg name="logger_level" />
<arg name="enb_3d_compensation" default="true"/>
<arg name="pose_update_topic" default="~/pose_update"/>

<node name="base_stabilizer_node" pkg="hector_pose_estimation" type="pose_estimation" output="screen">
  <!-- enb -->
  <param name="enb_3d_compensation" value="$(arg enb_3d_compensation)"/>
  <param name="publish_covariances" value="true"/>
  <param name="pub_nav_to_footprint_tf" value="false"/>
  <!-- frame id-->
  <param name="nav_frame" value="/map_frame"/>
  <param name="position_frame" value="/map_frame"/>
  <!-- pub -->
  <remap from="~/imu_ftp" to="/imu_ftp"/>
  <remap from="~/imu_hzt" to="/imu_hzt"/>
  <remap from="~/pose_update" to ="$(arg pose_update_topic)"/>
</node>
<group unless="$(arg enb_3d_compensation)" >
  <node pkg="tf2_ros" type="static_transform_publisher" name="base_ftp_to_base_hzt_tf"
  args="0 0 0 0 0 0 /base_ftp_frame /base_hzt_frame"/>
  <node pkg="tf2_ros" type="static_transform_publisher" name="base_hzt_to_base_tf"
  args="0 0 0 0 0 0 /base_hzt_frame /base_frame"/>
</group>
</launch>
