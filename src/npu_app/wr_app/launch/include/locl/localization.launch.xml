<launch>
<!--    <include file="$(find wr_csml)/launch/csml.launch" />-->
    <node name="initial_node" pkg="wr_csml" type="csml_node" output="screen">
        <param name="base_frame"        value="base_frame"/>
        <param name="odom_frame"        value="odom_frame"/>
        <param name="map_frame"         value="map_frame"/>
        <param name="coarse_position_search_range"             value="10.0"/>
        <param name="coarse_position_search_resolution"        value="0.2"/>
        <param name="coarse_angle_search_range"                value="360"/>
        <param name="coarse_angle_search_resolution"           value="2"/>
        <param name="fine_position_search_range"               value="0.5"/>
        <param name="fine_position_search_resolution"          value="0.05"/>
        <param name="fine_angle_search_range"                  value="5"/>
        <param name="fine_angle_search_resolution"             value="0.5"/>
    </node>
    <node name="localization_node" pkg="wr_localization" type="localization_node" output="screen">
        <param name="base_frame"        value="base_frame"/>
        <param name="odom_frame"        value="odom_frame"/>
        <param name="map_frame"         value="map_frame"/>
        <param name="scan_topic"        value="scan"/>
        <param name="pose_topic"        value="robot_pose"/>
        <param name="map_level"         value="2"/>
        <param name="high_res_iterations"           value="5"/>
        <param name="low_res_iterations"            value="3"/>
        <param name="enable_scan_matching"          value="true"/>
        <param name="enable_gps_initial_pose"       value="false"/>
        <param name="min_scan_distance"             value="0.3"/>
        <param name="vaild_points_num_scale"        value="8"/>
        <param name="accuracy_threshold"            value="0.5"/>
        <param name="tf_time_tolerance"             value="0.2"/>
        <param name="resample_distance"             value="5"/>
        <param name="resample_angle"                value="6.28"/>
        <param name="delay_tolerance_time"                value="0.5"/>
    </node>
</launch>
