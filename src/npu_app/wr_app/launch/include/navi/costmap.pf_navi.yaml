global_frame: map_frame
robot_base_frame: /base_frame
robot_radius: 0.3  # distance a circular robot should be clear of the obstacle (kobuki: 0.18)
# footprint: [[0.22, 0.22], [0.22, -0.22], [-0.22, -0.22], [-0.22, 0.22]]  # if the robot is not circular
map_type: voxel
update_frequency: 5.0
publish_frequency: 2.0
static_map: false
rolling_window: true
width: 4.0
height: 4.0
resolution: 0.05
map_type: costmap
transform_tolerance: 0.5
plugins:
  - {name: obstacle_layer,      type: "costmap_2d::VoxelLayer"}
  - {name: inflation_layer,     type: "costmap_2d::InflationLayer"}
obstacle_layer:
  enabled:              true
  max_obstacle_height:  0.6
  origin_z:             0.0
  z_resolution:         0.2
  z_voxels:             2
  unknown_threshold:    15
  mark_threshold:       0
  combination_method:   1
  track_unknown_space:  true    #true needed for disabling global path planning through unknown space
  obstacle_range: 2.5
  raytrace_range: 3.0
  origin_z: 0.0
  z_resolution: 0.2
  z_voxels: 2
  publish_voxel_map: false
  observation_sources:  scan
  scan:
    data_type: LaserScan
    topic: scan
    marking: true
    clearing: true
    min_obstacle_height: 0.0
    max_obstacle_height: 0.6
   # for debugging only, let's you see the entire voxel grid

#cost_scaling_factor and inflation_radius were now moved to the inflation_layer ns
inflation_layer:
  enabled: true
  cost_scaling_factor: 5.0  # exponential rate at which the obstacle cost drops off (default: 10)
  inflation_radius: 0.35  # max. distance from an obstacle at which costs are incurred for planning paths.

static_layer:
  enabled: true
