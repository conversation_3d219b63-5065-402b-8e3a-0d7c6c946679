<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-04-03 -->

<launch>
<arg name="launch_dir"/>
<arg name="logger_level"/>

<!-- 1. start amcl -->
<include file="$(arg launch_dir)/include/locl/amcl.launch.xml" >
    <arg name="logger_level" value="$(arg logger_level)"/>
</include>

<!-- 2. start localization -->
<include file="$(arg launch_dir)/include/locl/localization.launch.xml" >
    <!--arg name="logger_level" value="$(arg logger_level)"/-->
</include>

<!-- 3. start navi_core -->
<include file="$(arg launch_dir)/include/navi/navi_core.launch.xml">
    <arg name="logger_level" value="$(arg logger_level)"/>
</include>
</launch>

