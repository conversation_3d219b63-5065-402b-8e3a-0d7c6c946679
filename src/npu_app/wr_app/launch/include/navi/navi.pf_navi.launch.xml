<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-05-10 -->
<launch>
<arg name="launch_dir"/>
<arg name="logger_level"/>

<!-- 1. start wr_amcl -->
<include file="$(arg launch_dir)/include/locl/amcl.launch.xml" >
    <arg name="logger_level" value="$(arg logger_level)"/>
</include>

<!-- 2. start localization -->
<include file="$(arg launch_dir)/include/locl/localization.launch.xml" >
    <!--arg name="logger_level" value="$(arg logger_level)"/-->
</include>

<!-- 3. start wr_path_tracker -->
<node pkg="wr_path_follower" type="path_follower_node" name="path_follower_node" output="screen">
    <param name="logger_level" value="$(arg logger_level)"/>
    <param name="enb_dyn_param" value="true"/>
    <rosparam file="$(find wr_path_follower)/param/global_costmap_params.yaml" command="load" />
</node>

<!-- 4. cpp_planner -->
<node pkg="wr_ccp_planner" type="ccp_planner_node.py" name="ccp_planner_node" respawn="false" output="screen">
    <param name="enb_dyn_param" value="true"/>
    <param name="coverage_spacing_m" value="0.3"/>
</node>
</launch>

