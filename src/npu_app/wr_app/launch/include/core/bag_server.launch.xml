<?xml version="1.0"?>
<!-- -*- mode: XML -*- -->
<!-- v1.0 created by lawrence.han @2017-08-13 -->
<launch>
<arg name="logger_level"/>

<arg name="bag_id" default="lm_170725_1429"/>
<arg name="bag_topics" default="/scan"/>

<arg name="enb_record" default="false"/>
<arg name="record_args" default=""/>

<arg name="enb_play" default="false"/>
<arg name="play_args" default="-r 1.0"/>

<arg name="bag_dir" default="$(find wr_app)/../../bag" />

<!-- 1. start play -->
<node if="$(arg enb_play)" pkg="rosbag" type="play" name="bag_play_node"
args="$(arg play_args) -q -k --clock $(arg bag_dir)/$(arg bag_id).bag --topics /imu_data $(arg bag_topics)">
  <remap from="/visp_auto_tracker/object_position" to="/qrcode"/>
  <remap from="/ar_marker_pose" to="/artag"/>
  <remap from="/imu_data" to="/imu"/>
</node>

<!-- 2. start record -->
<group if="$(arg enb_record)">
  <node if="$(arg enb_play)" pkg="rosbag" type="record" name="bag_record_node"
  args="$(arg record_args) -q -j -O $(arg bag_dir)/$(arg bag_id).new.bag $(arg bag_topics)"/>
  <node unless="$(arg enb_play)" pkg="rosbag" type="record" name="bag_record_node"
  args="$(arg record_args) -q -j -O $(arg bag_dir)/$(arg bag_id).bag $(arg bag_topics)"/>
</group>

</launch>
