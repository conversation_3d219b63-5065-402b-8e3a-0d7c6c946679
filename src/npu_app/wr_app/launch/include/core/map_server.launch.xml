<?xml version="1.0"?>
<!-- -*- mode: XML -*- -->
<!-- v1.0 created by lawrence.han @2017-04-02 -->
<!-- v1.0.1 modified by lawrence.han @2017-04-03 -->
<launch>
    <!-- standard launch arg -->
    <arg name="logger_level" default="info"/>
    <!-- custom launch arg -->
    <arg name="map_dir_path" default="$(find wr_map_server)/../../map/" />
    <arg name="map_server_mode" default="NAVI_MODE" />

    <node pkg="wr_map_server" type="map_server_node" name="map_server_node" output="screen">
        <param name="logger_level" value="$(arg logger_level)"/>
        <param name="map_dir_path" value="$(arg map_dir_path)"/>
        <param name="map_server_mode" value="$(arg map_server_mode)"/>
    </node>

</launch>
