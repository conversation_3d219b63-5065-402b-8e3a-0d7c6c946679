<?xml version="1.0"?>
<!-- -*- mode: XML -*- -->
<!-- v1.0 created by lawrence.han @2017-05-10; updated by lhan@2017-05-23-->
<launch>
<!-- standard launch arg -->
<arg name="logger_level" default="debug"/>
<arg name="version_file" default="null"/>
<arg name="wait_for_param" default="true"/>
<arg name="server_type" default="ice"/>
<arg name="serial_port" default="/dev/comi"/>
<arg name="baud_rate" default="9600"/>
<arg name="data_bit" default="8"/>
<arg name="parity" default="NONE"/>
<arg name="com_type" default="SERIAL_LM"/>

<node pkg="wr_npu_server" type="ip_sender" name="ip_sender" args="$(arg version_file)" output="screen" />
<node pkg="wr_npu_server" type="runtime_status_node" name="runtime_status_node" output="screen" />
<node pkg="wr_npu_server" type="npu_server_node" name="npu_server_node" output="screen" >
    <param name="logger_level" value="$(arg logger_level)" />
    <param name="enb_direct_sensor_trans" value="true"/>
    <param name="latch_initial_pose_msg" value="true"/>
    <param name="wait_for_param" value="$(arg wait_for_param)"/>
    <param name="server_type" value="$(arg server_type)"/>
    <param name="serial_port" value="$(arg serial_port)"/>
    <param name="baud_rate" value="$(arg baud_rate)"/>
    <param name="data_bit" value="$(arg data_bit)"/>
    <param name="parity" value="$(arg parity)"/>
    <param name="com_type" value="$(arg com_type)"/>
    <param name="init_pose_area_width" value="0.0"/>
    <param name="init_pose_area_height" value="0.0"/>
    <param name="init_act_pose" value="$(find wr_npu_server)/param/init_act_pose.yaml"/>
    <param name="unfinishpath" value="$(find wr_npu_server)/param/unfinishpath.yaml"/>
    <rosparam file="$(find wr_npu_server)/param/unfinishpath.yaml" command="load" />
    <rosparam file="$(find wr_npu_server)/param/init_act_pose.yaml" command="load"/>
</node>
</launch>
