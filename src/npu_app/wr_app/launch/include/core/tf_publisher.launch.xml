<launch>
<!-- wizrobo standard launch arg -->
<arg name="logger_level" default="info"/>

<!-- 1. Functional node -->
<node pkg="wr_tf_publisher" type="tf_publisher_node" name="tf_publisher_node" output="screen">
    <param name="logger_level" value="$(arg logger_level)"/>
    <param name="loop_freq_hz" value="100" />
</node>
<!--node name="base_frame_to_lidar_frame_0_tf" pkg="tf2_ros" type="static_transform_publisher" 
args="0 0 0 0 0 0 /base_frame /lidar_frame_0"/>

<node name="base_frame_to_imu_frame_0_tf" pkg="tf2_ros" type="static_transform_publisher" 
args="0 0 0 0 0 0 /base_frame /imu_frame_0"/-->
<!--node name="base_ftp_to_footprint_frame_tf" pkg="tf2_ros" type="static_transform_publisher" 
args="0 0 0 0 0 0 /base_ftp_frame /footprint_frame"/-->
</launch>
