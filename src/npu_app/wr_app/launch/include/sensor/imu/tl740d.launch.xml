<?xml version="1.0"?>
<launch>
<arg name="logger_level"  default="info"/>
<arg name="imu_itf_id"    default="/dev/tl740d_imu"/>
<arg name="imu_id"        default="0"/>
<arg name="imu_topic"     default="/imu_$(arg imu_id)"/>
<arg name="frame_id"      default="imu_frame_$(arg imu_id)"/>
  <node name="tl740d_node"   pkg="wr_tl740d"    type="tl740d_node" output="screen">
  	<param name="logger_level"    type="string"  value="$(arg logger_level)" />
    <param name="dev_path"        type="string"  value="$(arg imu_itf_id)"/>
    <param name="imu_frame_id"    type="string"  value="$(arg frame_id)"/>
    <param name="imu_data_topic"  type="string"  value="imu/data_raw"/>
    <param name="yaw_data_topic"  type="string"  value="yaw_data"/>
    <param name="publish_freq"    type="double"  value="20"/>
  </node>
</launch>
