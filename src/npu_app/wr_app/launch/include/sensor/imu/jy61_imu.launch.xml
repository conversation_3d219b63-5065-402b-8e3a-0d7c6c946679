<?xml version="1.0"?>
<launch>
<arg name="logger_level"  default="info"/>
<arg name="imu_itf_id"    default="/dev/jy61_imu"/>
<arg name="imu_id"        default="0"/>
<arg name="imu_topic"     default="/imu_$(arg imu_id)"/>
<arg name="frame_id"      default="/imu_frame_$(arg imu_id)"/>

<node name="jy61_imu_node_$(arg imu_id)" pkg="wr_jy61_imu" type="jy61_imu_node" output="screen">
    <param name="logger_level" type="string" value="$(arg logger_level)" />
    <param name="serial_port" type="string" value="$(arg imu_itf_id)"/>
    <param name="imu_frame_id" type="string" value="/imu_frame_$(arg imu_id)"/>
    <param name="imu_data_topic" value="$(arg imu_topic)"/>
</node>
</launch>
