<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-08-10;
Designed for auto-generation by dynparam_node
-->
<launch>
<arg name="logger_level"        default="info"/>
<arg name="camera_id"           default="0"/>
<arg name="camera_type"         default="ps3eye_small_fov"/>
<arg name="camera_usb_port_id"  default="/dev/ps3eye"/>
<arg name="camera_ethernet_ip"  default=""/>
<arg name="camera_image_width"  default="640"/>
<arg name="camera_image_height" default="480"/>
<arg name="camera_frame_rate"   default="30"/>

<arg name="use_calibration" value="true"/>

<arg name="cam_node_name" value="usb_cam_node_$(arg camera_id)"/>
<node name="usb_cam_node_$(arg camera_id)" pkg="usb_cam" type="usb_cam_node" output="screen" >
  <param name="video_device" value="$(arg camera_usb_port_id)" />
  <param name="image_width" value="$(arg camera_image_width)" />
  <param name="image_height" value="$(arg camera_image_height)" />
  <param name="framerate" type="int" value="$(arg camera_frame_rate)" />
  <param name="pixel_format" value="yuyv" />
  <!--<param name="autofocus" value="false"/>-->

  <param name="camera_frame_id" value="camera_frame_$(arg camera_id)" />
  <param name="camera_name" value="$(arg camera_type)" />
  <param name="camera_info_url" value="package://wr_app/calib/$(arg camera_type).ini" type="string" />

  <remap from="/usb_cam_node_$(arg camera_id)/camera_info" to="/camera_$(arg camera_id)/camera_info" />
  <remap from="/usb_cam_node_$(arg camera_id)/image_raw" to="/camera_$(arg camera_id)/image_raw" />
</node>

<group if="$(arg use_calibration)">
  <node name="image_proc_node" ns="camera_$(arg camera_id)" pkg="image_proc" type="image_proc" output="screen" >
  </node>
</group>
</launch>
