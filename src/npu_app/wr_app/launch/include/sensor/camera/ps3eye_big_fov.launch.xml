<?xml version="1.0"?>
<launch>
<arg name="logger_level"        default="info"/>
<arg name="camera_itf_id"       default=""/>
<arg name="camera_id"           default="0"/>
<arg name="camera_topic"        default="/camera_$(arg camera_id)"/>
<arg name="frame_id"            default="/camera_frame_$(arg camera_id)"/>
<arg name="camera_width_pix"    default="640"/>
<arg name="camera_height_pix"   default="480"/>
<arg name="camera_fps_hz"       default="30"/>

<arg name="use_calibration" value="true"/>
<arg name="camera_name"     value="ps3eye_big_fov"/>

<arg name="cam_node_name" value="usb_cam_node_$(arg camera_id)"/>
<node name="usb_cam_node_$(arg camera_id)" pkg="usb_cam" type="usb_cam_node" output="screen" >
  <param name="video_device" value="$(arg camera_itf_id)" />
  <param name="image_width" value="$(arg camera_width_pix)" />
  <param name="image_height" value="$(arg camera_height_pix)" />
  <param name="framerate" type="int" value="$(arg camera_fps_hz)" />
  <param name="pixel_format" value="yuyv" />
  <!--<param name="autofocus" value="false"/>-->

  <param name="camera_frame_id" value="camera_frame_$(arg camera_id)" />
  <param name="camera_name" value="$(arg camera_name)" />
  <param name="camera_info_url" value="package://wr_app/calib/$(arg camera_name).ini" type="string" />

  <remap from="/usb_cam_node_$(arg camera_id)/camera_info" to="/camera_$(arg camera_id)/camera_info" />
  <remap from="/usb_cam_node_$(arg camera_id)/image_raw" to="/camera_$(arg camera_id)/image_raw" />
</node>

<group if="$(arg use_calibration)">
  <node name="image_proc_node" ns="camera_$(arg camera_id)" pkg="image_proc" type="image_proc" output="screen" >
    <remap from="/camera_$(arg camera_id)/camera_info" to="$(arg camera_topic)/info"/>
    <remap from="/camera_$(arg camera_id)/image_rect" to="$(arg camera_topic)/image"/>
  </node>
</group>
</launch>
