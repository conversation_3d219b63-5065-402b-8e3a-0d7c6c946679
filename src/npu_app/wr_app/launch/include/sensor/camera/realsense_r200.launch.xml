<?xml version='1.0' ?>
<launch>
  <!-- common -->
  <arg name="logger_level"        default="info"/>
  <arg name="camera_id"           default="0"/>
  <arg name="camera_itf_id"       default="/dev/camera"/>
  <arg name="camera_width_pix"    default="480"/>
  <arg name="camera_height_pix"   default="360"/>
  <arg name="camera_fps_hz"       default="30"/>
  <arg name="camera_topic"        default="camera"/>
  <!-- realsense -->
  <arg name="enb_projected"     default="true" />
  <arg name="camera"       		  default="camera" />
  <arg name="camera_type"  		  default="R200" /> <!-- Type of camera -->
  <arg name="serial_no"    		  default="" />
  <arg name="usb_port_id"  		  default="" /> <!-- USB "Bus#-Port#" -->
  <arg name="manager"      		  default="nodelet_manager" />
  <arg name="mode"              default="manual" />
  <arg name="depth_fps"         default="$(arg camera_fps_hz)" />
  <arg name="color_fps"         default="$(arg camera_fps_hz)" />


  <param name="$(arg camera)/driver/mode"              type="str"  value="$(arg mode)" />
  <param name="$(arg camera)/driver/depth_fps"         type="int"  value="$(arg depth_fps)" />
  <param name="$(arg camera)/driver/color_fps"         type="int"  value="$(arg color_fps)" />
  <!-- Laserscan topic -->
  <arg name="scan_topic" 		default="camera_scan"/>
  <arg name="lidar_frame"		default="camera_depth_frame"/>

  <group ns="$(arg camera)">
    <node pkg="nodelet" type="nodelet" name="$(arg manager)" args="manager" output="screen"/>

    <include file="$(find realsense_camera)/wr_launch/includes/nodelet.launch.xml">
      <arg name="manager"      value="$(arg manager)" />
      <arg name="camera"       value="$(arg camera)" />
      <arg name="camera_type"  value="$(arg camera_type)" />
      <arg name="serial_no"    value="$(arg serial_no)" />
      <arg name="depth_fps"    value="$(arg depth_fps)" />
      <arg name="color_fps"    value="$(arg color_fps)" />
      <arg name="depth_width"     value="$(arg camera_width_pix)" />
      <arg name="depth_height"    value="$(arg camera_height_pix)" />
    </include>
  </group> 

  <!-- Start depth image to laserscan conversion nodelet. -->
  <group if="$(arg enb_projected)">
    <node pkg="depthimage_to_laserscan" name="depthimage_to_laserscan" type="depthimage_to_laserscan"
	          respawn="true"  output="screen">
      <param name="scan_height" value="10"/>
      <param name="scan_offset_upward" value="10"/>
      <param name="scan_offset_downward" value="115"/>
      <param name="scan_time" value="0.033"/>
      <param name="output_frame_id" value="/$(arg camera)_depth_frame"/>
      <param name="range_min" value="0.30"/>
      <param name="range_max" value="2"/>
      <param name="depth_image_topic" value="$(arg camera)/depth/image_raw"/>
      <param name="lidar_topic"  value="$(arg scan_topic)"/>
      <param name="frame_id" type="string" value="$(arg lidar_frame)"/>
    </node>
  </group>>

</launch>
