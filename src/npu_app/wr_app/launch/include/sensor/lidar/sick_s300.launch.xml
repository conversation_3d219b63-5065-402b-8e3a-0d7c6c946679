<?xml version="1.0"?>
<launch>
<arg name="logger_level"    default="info"/>
<arg name="lidar_itf_id"	default="/dev/wrsicks300" />
<arg name="lidar_id"		default="0"/>
<arg name="lidar_topic"	default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"		default="lidar_frame_$(arg lidar_id)"/>

<node name="sick_s300_node_$(arg lidar_id)" pkg="wr_s300" type="wr_sick_s300" output="screen" >
    <param name="port"      type="string" value="$(arg lidar_itf_id)"/>
    <param name="baud"      type="int"    value="115200"/>
    <param name="frame_id"  type="string" value="$(arg frame_id)"/>
    <remap from="scan" to="$(arg lidar_topic)" />
</node>
</launch>
