<?xml version="1.0"?>
<launch>
<arg name="logger_level"       default="info"/>
<arg name="lidar_itf_id"	default="************" />
<arg name="lidar_id"		default="0"/>
<arg name="lidar_topic"	default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"		default="lidar_frame_$(arg lidar_id)"/>

<node name="sick_lms511_node_$(arg lidar_id)" pkg="wr_lms5xx" type="lms5xx_node" output="screen">
    <param name="frame_id" value="$(arg frame_id)" />
    <param name="laser_ip" value="$(arg lidar_itf_id)" />
    <param name="scan_topic" value="$(arg lidar_topic)" />
    <param name="scan_freq" value="25" />  <!-- supported 25 35 50 75 100 hz -->
    <param name="scan_res" value="25" />   <!-- supported 17 25 33 50 67 75 100 *0.01deg-->
</node>
</launch>
