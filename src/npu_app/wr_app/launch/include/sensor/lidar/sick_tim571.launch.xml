<?xml version="1.0"?>
<launch>
<arg name="logger_level"       default="info"/>
<arg name="lidar_itf_id"	default="192.168.1.50" />
<arg name="lidar_id"		default="0"/>
<arg name="lidar_topic"	default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"		default="lidar_frame_$(arg lidar_id)"/>

<node name="sick_tim571_$(arg lidar_id)" pkg="wr_timxxx" type="sick_tim561_2050101" respawn="false" output="screen">
    <!-- default values: -->
    <!--
    <param name="min_ang" type="double" value="-2.35619449019" />
    <param name="max_ang" type="double" value="2.35619449019" />
    <param name="intensity" type="bool" value="True" />
    <param name="skip" type="int" value="0" />
    <param name="frame_id" type="str" value="laser" />
    <param name="time_offset" type="double" value="-0.001" />
    <param name="publish_datagram" type="bool" value="False" />
    <param name="subscribe_datagram" type="bool" value="false" />
    <param name="device_number" type="int" value="0" />
    <param name="time_increment" type="double" value="0.000061722" />
    <param name="range_min" type="double" value="0.05" />
    <param name="range_max" type="double" value="10.0" />
    -->
    <param name="time_increment" type="double" value="0.000061722" />
    <param name="frame_id" type="str" value="$(arg frame_id)" />
    <param name="scan_topic" type="str" value="$(arg lidar_topic)" />
    <param name="range_max" type="double" value="25.0" />

    <!-- Uncomment this to enable TCP instead of USB connection; 'hostname' is the host name or IP address of the laser scanner
    In cases where a race condition exists and the computer boots up before the TIM is ready, increase 'timelimit.'-->
    <param name="hostname" type="string" value="$(arg lidar_itf_id)" />
    <param name="port" type="string" value="2112" />
    <param name="timelimit" type="int" value="5" />
</node>
</launch>
