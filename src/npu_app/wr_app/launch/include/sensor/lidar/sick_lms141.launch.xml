<?xml version="1.0"?>
<launch>
<arg name="logger_level"    default="info"/>
<arg name="lidar_itf_id"	default="************" />
<arg name="lidar_id"		default="0"/>
<arg name="lidar_topic"	default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"		default="lidar_frame_$(arg lidar_id)"/>

<node name="sick_lms141_node_$(arg lidar_id)" pkg="wr_lms1xx" type="LMS1xx_node" output="screen">
    <param name="frame_id"      value="$(arg frame_id)" />
    <param name="host"          value="$(arg lidar_itf_id)" />
    <param name="puhlish_freq"  value="30" />
    <remap from="/scan"         to="$(arg lidar_topic)" />
</node>
</launch>
