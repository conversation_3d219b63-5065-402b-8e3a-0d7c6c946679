<?xml version="1.0"?>
<launch>
<arg name="logger_level"       default="info"/>
<arg name="lidar_itf_id"	default="/dev/lslidar" />
<arg name="lidar_id"		default="0"/>
<arg name="lidar_topic"	default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"		default="lidar_frame_$(arg lidar_id)"/>

<node name="lslidar_node_$(arg lidar_id)" pkg="wr_lslidar" type="lslidar_node" output="screen">
    <param name="serial_port"         type="string" value="$(arg lidar_itf_id)"/>
    <param name="serial_baudrate"     type="int"    value="115200"/>
    <param name="frame_id"            type="string" value="$(arg frame_id)"/>
    <param name="inverted"            type="bool"   value="false"/>
    <param name="angle_compensate"    type="bool"   value="true"/>
    <param name="scan_topic" 	      type="string" value="$(arg lidar_topic)"/>
</node>
</launch>
