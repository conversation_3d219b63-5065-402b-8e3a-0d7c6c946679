<?xml version="1.0"?>
<launch>
<arg name="logger_level"   default="info"/>
<arg name="lidar_itf_id"   default="*************" />
<arg name="lidar_id"	   default="0"/>
<arg name="lidar_topic"	   default="/scan_$(arg lidar_id)"/>
<!-- start sensor-->
<!-- declare arguments with default values -->
<arg name="calibration" default="$(find velodyne_pointcloud)/params/VLP16db.yaml"/>
<arg name="device_ip" default="$(arg lidar_itf_id)" />
<arg name="frame_id" default="velodyne" />
<arg name="manager" default="$(arg frame_id)_nodelet_manager" />
<arg name="max_range" default="40.0" />
<arg name="min_range" default="0.15" />
<arg name="pcap" default="" />
<arg name="port" default="2368" />
<arg name="read_fast" default="false" />
<arg name="read_once" default="false" />
<arg name="repeat_delay" default="0.0" />
<arg name="rpm" default="600.0" />
<arg name="cut_angle" default="-0.01" />
<arg name="laserscan_ring" default="-1" />
<arg name="laserscan_resolution" default="0.007" />

<!-- start nodelet manager and driver nodelets -->
<include file="$(find velodyne_driver)/launch/nodelet_manager.launch">
  <arg name="device_ip"    value="$(arg device_ip)"/>
  <arg name="frame_id"     value="$(arg frame_id)"/>
  <arg name="manager"      value="$(arg manager)" />
  <arg name="model"        value="VLP16"/>
  <arg name="pcap"         value="$(arg pcap)"/>
  <arg name="port"         value="$(arg port)"/>
  <arg name="read_fast"    value="$(arg read_fast)"/>
  <arg name="read_once"    value="$(arg read_once)"/>
  <arg name="repeat_delay" value="$(arg repeat_delay)"/>
  <arg name="rpm"          value="$(arg rpm)"/>
  <arg name="cut_angle"    value="$(arg cut_angle)"/>
</include>

<!-- start cloud nodelet -->
<include file="$(find velodyne_pointcloud)/launch/cloud_nodelet.launch">
  <arg name="calibration" value="$(arg calibration)"/>
  <arg name="manager"     value="$(arg manager)" />
  <arg name="max_range"   value="$(arg max_range)"/>
  <arg name="min_range"   value="$(arg min_range)"/>
</include>

<!-- start laserscan nodelet -->
<include file="$(find velodyne_pointcloud)/launch/laserscan_nodelet.launch">
  <arg name="manager"    value="$(arg manager)" />
  <arg name="ring"       value="$(arg laserscan_ring)"/>
  <arg name="resolution" value="$(arg laserscan_resolution)"/>
  <arg name="max_range"   value="$(arg max_range)"/>
  <arg name="min_range"   value="$(arg min_range)"/>
  <remap from="/scan"         to="$(arg lidar_topic)" />
</include>

</launch>
