<?xml version="1.0"?>
<launch>
<arg name="logger_level"       default="info"/>
<arg name="lidar_itf_id"	default="************" />
<arg name="lidar_id"		default="0"/>
<arg name="lidar_topic"	default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"		default="lidar_frame_$(arg lidar_id)"/>

<!-- product-specific lidar args -->
<arg name="scan_frequency" default="50"/>
<arg name="samples_per_scan" default="7200"/>

<!-- R2000 Driver -->
<node name="pf_r2000_50hz_node_$(arg lidar_id)" pkg="wr_r2000" type="r2000_node" output="screen">
    <param name="scanner_ip" value="$(arg lidar_itf_id)"/>
    <param name="frame_id" value="$(arg frame_id)"/>
    <param name="scan_frequency" value="$(arg scan_frequency)"/>
    <param name="samples_per_scan" value="$(arg samples_per_scan)"/>
    <remap from="/scan" to="$(arg lidar_topic)" />
</node>
</launch>
