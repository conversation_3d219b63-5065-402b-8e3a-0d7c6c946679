<?xml version="1.0"?>
<launch>
<arg name="logger_level"       default="info"/>
<arg name="lidar_itf_id"	default="************"/>
<arg name="lidar_id"		default="0"/>
<arg name="lidar_topic"        default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"		default="lidar_frame_$(arg lidar_id)"/>

<node name="hokuyo_ust10lx_node_$(arg lidar_id)" pkg="wr_hokuyo" type="hokuyo_node" output="screen">
    <param name="scan_topic" type="str" value="$(arg lidar_topic)" />
    <param name="ip_address" value="$(arg lidar_itf_id)"/>
    <param name="serial_port" value="$(arg lidar_itf_id)"/>
    <param name="serial_baud" value="115200"/>
    <param name="frame_id" value="$(arg frame_id)"/>
    <param name="calibrate_time" value="true"/>
    <param name="publish_intensity" value="true"/>
    <param name="publish_multiecho" value="false"/>
    <param name="angle_min" value="-1.8"/>
    <param name="angle_max" value="1.8"/>
</node>
</launch>
