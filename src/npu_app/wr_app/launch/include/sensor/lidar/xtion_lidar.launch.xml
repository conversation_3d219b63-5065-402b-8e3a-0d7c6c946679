<?xml version="1.0"?>
<launch>
	
<arg name="logger_level"  default="info"/>
<arg name="lidar_itf_id"  default="/dev/fslidar" />
<arg name="lidar_id"      default="0"/>
<arg name="lidar_topic"   default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"      default="/lidar_frame_$(arg lidar_id)"/>
<arg name="depth_image_topic" default="/camera/depth/image"/>

<include file="$(find openni2_launch)/launch/openni2.launch" />

<node pkg="depthimage_to_laserscan"  type="depthimage_to_laserscan" name="depthimage_to_laserscan" output="screen">
	<param name="depth_image_topic" type="string" value="$(arg depth_image_topic)"/>
	<param name="frame_id" type="string" value="$(arg frame_id)"/>
	<param name="lidar_topic" type="string" value="$(arg lidar_topic)" />
</node>

<node pkg="tf" type="static_transform_publisher" name="base_link_to_lidar_link_static_tf" args="0 0 0 0 0 0 /base_frame /camera_link 100"/>

</launch>
