<?xml version="1.0"?>
<launch>
<arg name="logger_level"  default="info"/>
<arg name="lidar_itf_id"  default="/dev/fslidar" />
<arg name="lidar_id"      default="0"/>
<arg name="lidar_topic"   default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"      default="lidar_frame_$(arg lidar_id)"/>

<node name="fslidar_node_$(arg lidar_id)" pkg="wr_fslidar"  type="fslidar_node" output="screen">
    <param name="scan_topic"        type="string" value="$(arg lidar_topic)"/>
    <param name="dev_path"          type="string" value="$(arg lidar_itf_id)"/>
    <param name="inverted"          type="bool"   value="false"/>
    <param name="frame_id"          type="string" value="$(arg frame_id)"/>
    <param name="sample_rate"       type="int"    value="5000"/>
    <param name="rotational_speed"  type="int"    value="5"/>
</node>
</launch>
