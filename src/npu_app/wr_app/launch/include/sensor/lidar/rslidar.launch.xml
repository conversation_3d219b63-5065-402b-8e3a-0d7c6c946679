<?xml version="1.0"?>
<launch>
<arg name="logger_level"    default="info"/>
<arg name="lidar_itf_id"	default="/dev/rslidar" />
<arg name="lidar_id"		default="0"/>
<arg name="lidar_topic"		default="/scan_$(arg lidar_id)"/>
<arg name="frame_id"		default="lidar_frame_$(arg lidar_id)"/>

<node name="rslidar_node_$(arg lidar_id)" pkg="wr_rslidar" type="rslidar_node" output="screen">
    <param name="port_name" value="$(arg lidar_itf_id)" />
    <param name="scan_topic" value="$(arg lidar_topic)" />
    <param name="lidar_frame_id" value="$(arg frame_id)" />
    <param name="logger_level" value="$(arg logger_level)" />
</node>
</launch>
