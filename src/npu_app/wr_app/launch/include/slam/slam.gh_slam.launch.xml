<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-04-03 
	updated by tommy @2017-11-22
-->
<launch>
<arg name="launch_dir"/>
<arg name="logger_level" default="info"/>
<arg name="enb_handheld_mode" default="false"/>

<arg name="pub_map_to_odom_tf" default="false"/>
<arg name="pub_odom_to_base_tf" default="false"/>
<arg name="pub_necessary_static_tf" default="false"/>

<!-- 1. start necessary_static_tf -->
<group if="$(arg pub_necessary_static_tf)">
  <node if="$(arg pub_map_to_odom_tf)" name="odom_to_base_tf"
  pkg="tf2_ros" type="static_transform_publisher"
  args="0 0 0 0 0 0 /odom_frame /base_frame"/>
  <node if="$(arg pub_odom_to_base_tf)" name="map_to_odom_tf" 
  pkg="tf2_ros" type="static_transform_publisher" 
  args="0 0 0 0 0 0 /map_frame /odom_frame"/>
</group>

<!-- 2. start wheel_odom -->
<include file="$(arg launch_dir)/include/locl/wheel_odom.launch.xml">
  <arg name="logger_level" value="$(arg logger_level)"/>
  <!-- enb -->
  <!-- frame -->
  <arg name="map_frame" value="/map_frame"/>
  <arg name="odom_frame" value="/odom_frame"/>
  <arg name="base_frame" value="/base_ftp_frame"/>
  <!-- tf -->
  <arg name="pub_odom_tf" value="false"/>
  <!-- sub -->
  <arg name="odom_topic" value="/odom"/>
  <arg name="imu_topic" value="/imu/data_raw"/>
  <!-- fusion -->
  <arg name="fusion_pose_est_topic" value=""/>
  <arg name="pos_fusion_cov_thrs" value="0.01"/>
  <arg name="ori_fusion_cov_thrs" value="0.01"/>
  <arg name="pose_fusion_mode"   value="weighting_fusion"/>
</include>

<!-- 2. start gp_mapping -->
<node name="slam_mode3_node" pkg="cartographer_ros"
    type="cartographer_node" args="
    -configuration_directory $(find cartographer_ros)/configuration_files_linkmiao
    -configuration_basename linkmiao_cng3.lua"
    output="screen">
    <remap from="imu" to="imu/data_raw" />
    <remap from="odom" to="wo_odom" />
</node>

<node name="map_node" pkg="cartographer_ros"
    type="cartographer_occupancy_grid_node" args="-resolution 0.1" />
</launch>

