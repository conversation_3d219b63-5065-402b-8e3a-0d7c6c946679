<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-08-10 -->
<launch>
<arg name="launch_dir"/>
<arg name="logger_level"/>
<arg name="enb_camera"/>

<!-- 1. start map_optimizer -->
<!-- node -->
<node name="map_optimizer_node" pkg="wr_map_optimizer" type="map_optimizer_node" output="screen">
  <param name="logger_level" value="$(arg logger_level)"/>
  <!-- enb -->
  <!-- sub -->
  <remap from="/odom" to="/slam_odom"/>
  <!-- misc -->
  <param name="map_pub_frq_hz" value="1.0"/>
</node>

<!-- 2. start artag_tracker -->
<include if="$(arg enb_camera)" file="$(arg launch_dir)/include/locl/artag_tracker.launch.xml">
  <arg name="logger_level" value="$(arg logger_level)"/>
</include>
</launch>

