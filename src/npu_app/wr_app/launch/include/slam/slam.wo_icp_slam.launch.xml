<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-08-10 -->
<launch>
<arg name="launch_dir"/>
<arg name="logger_level" default="info"/>

<arg name="pub_map_to_odom_tf" default="false"/>
<arg name="pub_odom_to_base_tf" default="true"/>
<arg name="pub_necessary_static_tf" default="true"/>

<arg name="pos_fusion_cov_thrs" default="0"/>
<arg name="ori_fusion_cov_thrs" default="0"/>
<arg name="enb_cov_weighting"   default="true"/>
<arg name="fusion_pose_est_topic" default="~/fusion_pose_est"/>

<!-- 1. start necessary_static_tf -->
<group if="$(arg pub_necessary_static_tf)">
  <node if="$(arg pub_map_to_odom_tf)" name="odom_to_base_tf"
  pkg="tf2_ros" type="static_transform_publisher"
  args="0 0 0 0 0 0 /odom_frame /base_frame"/>
  <node if="$(arg pub_odom_to_base_tf)" name="map_to_odom_tf" 
  pkg="tf2_ros" type="static_transform_publisher" 
  args="0 0 0 0 0 0 /map_frame /odom_frame"/>
</group>

<!-- 2. start hector_mapping -->
<node name="hector_mapping_node" pkg="wr_hector_mapping" type="hector_mapping_node" output="screen">
  <param name="logger_level" value="$(arg logger_level)"/>
  <param name="pub_drawings" value="true"/>
  <param name="pub_debug_output" value="true"/>
  <!-- frame -->
  <param name="map_frame" value="/map_frame"/>
  <param name="odom_frame" value="/map_frame"/>
  <param name="base_frame" value="/base_ftp_frame"/>
  <!-- tf -->
  <param name="pub_odom_to_base_tf" value="$(arg pub_odom_to_base_tf)"/>
  <param name="pub_map_to_odom_tf" value="$(arg pub_map_to_odom_tf)"/>
</node>
</launch>

