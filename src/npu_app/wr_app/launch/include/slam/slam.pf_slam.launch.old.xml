<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-09-02 -->
<launch>
<arg name="launch_dir"/>
<arg name="logger_level" default="info"/>
<arg name="enb_handheld_mode" default="false"/>

<arg name="pub_map_to_odom_tf" default="true"/>
<arg name="pub_odom_to_base_tf" default="false"/>
<arg name="pub_necessary_static_tf" default="false"/>

<arg name="pos_fusion_cov_thrs" default="0"/>
<arg name="ori_fusion_cov_thrs" default="0"/>
<arg name="enb_cov_weighting"   default="true"/>
<arg name="fusion_pose_est_topic" default="~/fusion_pose_est"/>

<group unless="$(arg enb_handheld_mode)">
  <!-- 1. start wheel_odom -->
  <include file="$(arg launch_dir)/include/locl/wheel_odom.launch.xml">
    <arg name="logger_level" value="$(arg logger_level)"/>
    <!-- enb -->
    <!-- frame -->
    <arg name="map_frame" value="/map_frame"/>
    <arg name="odom_frame" value="/odom_frame"/>
    <arg name="base_frame" value="/base_ftp_frame"/>
    <!-- tf -->
    <arg name="pub_odom_tf" value="true"/>
    <!-- sub -->
    <arg name="odom_topic" value="/odom"/>
    <arg name="imu_topic" value="/imu/data_raw"/>
    <!-- fusion -->
    <arg name="fusion_pose_est_topic" value=""/>
    <arg name="pos_fusion_cov_thrs" value="0.01"/>
    <arg name="ori_fusion_cov_thrs" value="0.01"/>
    <arg name="pose_fusion_mode"   value="weighting_fusion"/>
  </include>
</group>

  <!-- 3. start gmapping -->
  <node name="pf_node" pkg="wr_gmapping" type="gmapping_node" output="screen">
    <param name="logger_level" value="$(arg logger_level)"/>
    <!-- frame -->
    <param name="map_frame" value="/map_frame" />
    <param name="odom_frame" value="/odom_frame" />
    <param name="base_frame" value="/base_ftp_frame" />
    <!-- sub -->
    <param name="throttle_scans" value="4"/>
    <remap from="scan" to="/scan"/>
    <!-- tf -->
    <param name="pub_odom_to_base_tf" value="$(arg pub_odom_to_base_tf)"/>
    <param name="pub_map_to_odom_tf" value="$(arg pub_map_to_odom_tf)"/>
    
    <param name="transform_publish_period" value="0.05" />

    <!-- map -->
    <param name="map_update_interval" value="5.0" />

    <param name="delta" value="0.12"/><!--resolution-->
    <param name="xmin" value="-10.0"/><!--initial map size x-->
    <param name="ymin" value="-10.0"/><!--initial map size x-->
    <param name="xmax" value="10.0"/><!--initial map size x-->
    <param name="ymax" value="10.0"/><!--initial map size x-->
    <!--
    <param name="occ_thresh" value="0.25"/>
    -->
    <!-- lidar -->
    <!--
    <param name="maxUrange" value="9.0"/>
    <param name="maxRange" value="10.0"/>
    -->
    <!-- slam -->
    <param name="minimum_score" value="100"/>
    
    <param name="sigma" value="0.05"/>
    <param name="kernelSize" value="1"/>
    <param name="lstep" value="0.05"/>
    <param name="astep" value="0.05"/>
    <param name="iterations" value="1"/>
    <param name="lsigma" value="0.075"/>
    <param name="ogain" value="6.0"/>
    <!--param name="lskip" value="4"/-->
    <param name="srr" value="0.1"/>
    <param name="srt" value="0.2"/>
    <param name="str" value="0.1"/>
    <param name="stt" value="0.2"/>

    <param name="linearUpdate" value="1.0"/>
    <param name="angularUpdate" value="0.5"/>
    <param name="temporalUpdate" value="3.0"/>
    <param name="resampleThreshold" value="0.6"/>
    <param name="particles" value="30"/>


    <param name="llsamplerange" value="0.01"/>
    <param name="llsamplestep" value="0.01"/>
    <param name="lasamplerange" value="0.005"/>
    <param name="lasamplestep" value="0.005"/>

    <param name="pub_traj" value="false"/>
    <param name="max_beam_num" value="200"/>
  </node>

</launch>
