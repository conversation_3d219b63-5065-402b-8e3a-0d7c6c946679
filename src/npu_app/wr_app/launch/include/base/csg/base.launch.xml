<?xml version="1.0"?>
<!-- -*- mode: XML -*- -->
<launch>
<arg name="launch_dir"/>
<arg name="logger_level"/>
<arg name="enb_slave_mode"/>
<arg name="enb_odom_tf_pub"/>

<!-- 1. start bcu driver -->
<node unless="$(arg enb_slave_mode)" pkg="wr_bcu_server" type="bcu_server_node" name="bcu_server_node"
output="screen" respawn="false">
    <param name="logger_level" value="$(arg logger_level)"/>
    <param name="enb_dyn_param" value="true"/>
    <param name="main_loop_frq" value="30"/>
    <param name="port_id" value="/dev/bcu"/>
    <param name="cmd_retry_num" value="5"/>
    <param name="cmd_delay_ms" value="1"/>
    <param name="ans_retry_num" value="33"/>
    <param name="ans_delay_ms" value="1"/>
    <param name="enb_com_data_display" value="true"/>
    <param name="model_type" value="CSGDRV"/>
    <param name="driver_type" value="SERIAL_CSG"/>

    <param name="control_mode" value="MOTOR_CONTROL_UNIT"/>
    <param name="enb_imu" value="false"/>
    <param name="enb_gps" value="false"/>
</node>

<!-- 2. start chassis_server -->
<node pkg="wr_chassis_server" type="chassis_server_node" name="chassis_server_node"
output="screen" respawn="false">
    <param name="logger_level" value="$(arg logger_level)"/>
    <param name="enb_dyn_param" value="true"/>
    <param name="model_type" value="CSGDRV"/>
    <!--param name="imu_used" value="false"/-->
    <!--param name="enb_odom_tf_pub" value="false"/-->
</node>

<!-- 3. start cmd_vel_mux -->
<include file="$(find wr_vel_mux)/launch/standalone.launch"/>
</launch>
