<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-04-03 -->
<launch>
<!-- npu_app standard launch-xml arg -->
<arg name="launch_dir" />
<arg name="logger_level" default="info"/>
<arg name="enb_slave_mode" default="false"/>
<!--arg name="enb_imu" default="true"/-->
<arg name="enb_odom_tf_pub" default="true" />

<include file="$(find turtlebot_gazebo)/launch/turtlebot_world_hokuyo.launch">
</include>

<!-- velocity commands multiplexer -->
<include file="$(find wr_vel_mux)/launch/standalone.launch"/>
</launch>



