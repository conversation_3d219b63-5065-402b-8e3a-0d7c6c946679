<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-04-03 -->
<launch>
<!-- npu_app standard launch-xml arg -->
<arg name="launch_dir" />
<arg name="logger_level" default="info"/>
<arg name="enb_slave_mode" default="false"/>
<!--arg name="enb_imu" default="true"/-->
<arg name="enb_odom_tf_pub" default="true" />

<node pkg="nodelet" type="nodelet" name="mobile_base_nodelet_manager" args="manager"/>
<node pkg="nodelet" type="nodelet" name="mobile_base" args="load kobuki_node/KobukiNodelet mobile_base_nodelet_manager">
    <rosparam file="/npu.x86_64/config/kobuki.yaml" command="load"/>
    <param name="publish_tf" value="$(arg enb_odom_tf_pub)"/>
    <!--param name="use_imu_heading" value="$(arg enb_imu)"/-->
    <remap from="mobile_base/odom" to="odom"/>
    <!-- Don't do this - force applications to use a velocity mux for redirection-->
    <remap from="mobile_base/commands/velocity" to="cmd_vel"/>
    <remap from="mobile_base/enable" to="enable"/>
    <remap from="mobile_base/disable" to="disable"/>
    <remap from="mobile_base/joint_states" to="joint_states"/>
</node>

<!-- velocity commands multiplexer -->
<include file="$(find wr_vel_mux)/launch/standalone.launch"/>
  
<!-- bumper/cliff to pointcloud -->
<include file="$(find turtlebot_bringup)/launch/includes/kobuki/bumper2pc.launch.xml"/>

</launch>
