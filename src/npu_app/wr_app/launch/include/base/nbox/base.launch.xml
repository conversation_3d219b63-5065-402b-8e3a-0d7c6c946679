<?xml version="1.0"?>
<!-- -*- mode: XML -*- -->
<launch>
<arg name="launch_dir"/>
<arg name="logger_level"/>
<arg name="enb_slave_mode"/>
<arg name="enb_odom_tf_pub"/>
<arg name="control_mode" default="MOTOR_CONTROL_UNIT"/>

<!-- 1. start bcu driver -->
<node unless="$(arg enb_slave_mode)"
name="bcu_driver_node" pkg="wr_bcu_server" type="bcu_server_node" output="screen" respawn="false">
    <param name="logger_level" value="$(arg logger_level)"/>
    <param name="enb_dyn_param" value="true"/>
    <param name="main_loop_frq" value="30"/>
    <param name="driver_type" value="serial_nbox"/>
    <param name="port_id" value="/dev/bcu"/>
    <param name="cmd_retry_num" value="5"/>
    <param name="cmd_delay_ms" value="200"/>
    <param name="ans_retry_num" value="200"/>
    <param name="ans_delay_ms" value="5"/>
    <param name="enb_com_data_display" value="false"/>

    <param name="control_mode" value="$(arg control_mode)"/>
    <param name="bmp_vol" value="LOW_VALID"/>
    <param name="esb_vol" value="LOW_VALID"/>
    <param name="dac_bit_width" value="12"/>
    <param name="dac_ref_v" value="3.3"/>
    <param name="adc_bit_width" value="12"/>
    <param name="adc_ref_v" value="3.3"/>
    <param name="enb_imu" value="false"/>
    <param name="enb_gps" value="false"/>
</node>

<!-- 2. start chassis_server -->
<node pkg="wr_chassis_server" type="chassis_server_node" name="chassis_server_node"
output="screen" respawn="false">
    <param name="logger_level" value="$(arg logger_level)"/>
    <param name="enb_dyn_param" value="true"/>
    <param name="model_type" value="DIFFDRV"/>
    <!--param name="imu_used" value="false"/-->
    <!--param name="enb_odom_tf_pub" value="false"/-->
</node>

<!-- 3. start cmd_vel_mux -->
<include file="$(find wr_vel_mux)/launch/standalone.launch"/>
</launch>
