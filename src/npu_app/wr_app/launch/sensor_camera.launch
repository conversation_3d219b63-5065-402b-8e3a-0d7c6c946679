<?xml version="1.0"?>
<!-- v1.0 created by armstrong.tu @2018-01-18
To launch camera
-->
<launch>
<arg name="logger_level"/>
<arg name="sid"/>
<arg name="main"/>
<arg name="std_camera_topic" value="/camera"/>
<arg name="launch_dir" value="$(find wr_app)/launch"/>

<arg name="camera_type"/>
<arg name="camera_itf_type"/>
<arg name="camera_itf_id"/>
<arg name="camera_width_pix"/>
<arg name="camera_height_pix"/>
<arg name="camera_fps_hz"/>

<!-- camera_frame_0 -> camera_frame -->
<group if="$(arg main)">
    <node name="camera_frame_0_to_camera_frame_tf" pkg="tf2_ros" type="static_transform_publisher" args="0 0 0 0 0 0 /camera_frame_0 /camera_frame"/>
</group>

<group if="$(arg main)">
    <include file="$(arg launch_dir)/include/sensor/camera/$(arg camera_type).launch.xml">
        <arg name="logger_level" value="$(arg logger_level)"/>
        <arg name="camera_itf_id" value="$(arg camera_itf_id)" />
        <arg name="camera_id" value="$(arg sid)"/>
        <arg name="camera_width_pix" value="$(arg camera_width_pix)" />
        <arg name="camera_height_pix" value="$(arg camera_height_pix)" />
        <arg name="camera_fps_hz" value="$(arg camera_fps_hz)" />
        <arg name="camera_topic" value="$(arg std_camera_topic)"/>
    </include>
</group>
<group unless="$(arg main)">
    <include file="$(arg launch_dir)/include/sensor/camera/$(arg camera_type).launch.xml">
        <arg name="logger_level" value="$(arg logger_level)"/>
        <arg name="camera_itf_id" value="$(arg camera_itf_id)" />
        <arg name="camera_id" value="$(arg sid)"/>
        <arg name="camera_width_pix" value="$(arg camera_width_pix)" />
        <arg name="camera_height_pix" value="$(arg camera_height_pix)" />
        <arg name="camera_fps_hz" value="$(arg camera_fps_hz)" />
        <arg name="camera_topic" value="$(arg std_camera_topic)_$(arg sid)"/>
    </include>
</group>
</launch>