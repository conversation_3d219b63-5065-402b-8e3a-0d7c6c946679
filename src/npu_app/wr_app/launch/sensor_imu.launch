<?xml version="1.0"?>
<!-- v1.0 created by armstrong.tu @2018-01-18
To launch imu
-->
<launch>
<arg name="logger_level"/>
<arg name="sid"/>
<arg name="main"/>
<arg name="std_imu_topic" value="/imu"/>
<arg name="launch_dir" value="$(find wr_app)/launch"/>

<arg name="imu_type"/>
<arg name="imu_itf_type"/>
<arg name="imu_itf_id"/>

<!-- imu_frame_0 -> imu_frame -->
<group if="$(arg main)">
    <node name="imu_frame_0_to_imu_frame_tf" pkg="tf2_ros" type="static_transform_publisher" args="0 0 0 0 0 0 /imu_frame_0 /imu_frame"/>
</group>

<group if="$(arg main)">
    <include file="$(arg launch_dir)/include/sensor/imu/$(arg imu_type).launch.xml">
        <arg name="logger_level" value="$(arg logger_level)"/>
        <arg name="imu_itf_id" value="$(arg imu_itf_id)" />
        <arg name="imu_id" value="$(arg sid)"/>
        <arg name="imu_topic" value="$(arg std_imu_topic)"/>
    </include>
</group>
<group unless="$(arg main)">
    <include file="$(arg launch_dir)/include/sensor/imu/$(arg imu_type).launch.xml">
        <arg name="logger_level" value="$(arg logger_level)"/>
        <arg name="imu_itf_id" value="$(arg imu_itf_id)" />
        <arg name="imu_id" value="$(arg sid)"/>
        <arg name="imu_topic" value="$(arg std_imu_topic)_$(arg sid)"/>
    </include>
</group>
</launch>