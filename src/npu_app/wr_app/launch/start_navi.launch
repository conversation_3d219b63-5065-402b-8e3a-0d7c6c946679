<?xml version="1.0"?>

<launch>
<arg name="launch_dir" value="$(find wr_app)/launch"/>
<arg name="logger_level" default="info"/>
<arg name="enb_kobuki" default="false"/>

<!-- navi args -->
<arg name="navi_mode" default="p2p_navi"/><!-- enum {p2p_navi, pf_navi} -->

<!-- 1. start wheel_odom -->
<include unless="$(arg enb_kobuki)" file="$(arg launch_dir)/include/locl/wheel_odom.launch.xml">
    <arg name="logger_level" value="$(arg logger_level)"/>
    <!-- enb -->
    <!-- frame -->
    <arg name="map_frame" value="/map_frame"/>
    <arg name="odom_frame" value="/odom_frame"/>
    <arg name="base_frame" value="/base_ftp_frame"/>
    <!-- tf -->
    <arg name="pub_odom_tf" value="true"/>
    <!-- sub -->
    <arg name="odom_topic" value="/odom"/>
    <arg name="imu_topic" value="/imu/data_raw"/>
    <!-- fusion -->
    <arg name="fusion_pose_est_topic" value=""/>
    <arg name="pos_fusion_cov_thrs" value="0.01"/>
    <arg name="ori_fusion_cov_thrs" value="0.01"/>
    <arg name="pose_fusion_mode"   value="weighting_fusion"/>
</include>

<!-- 2. Start navi -->
<include file="$(arg launch_dir)/include/navi/navi.$(arg navi_mode).launch.xml" >
    <arg name="launch_dir" value="$(arg launch_dir)"/>
    <arg name="logger_level" value="$(arg logger_level)"/>
</include>

<!-- 3. Start pose monitor -->
<include file="$(arg launch_dir)/include/locl/pose_monitor.launch.xml">
</include>

<!-- 4. Start collision detect -->
<!--include file="$(find wr_collision_detect)/launch/collision_detect.launch">
</include-->

<node pkg="wr_odom_distance" type="odom_distance_node" name="odom_distance_node" output="screen" >
    <param name="logger_level" value="$(arg logger_level)"/>
</node>

</launch>
