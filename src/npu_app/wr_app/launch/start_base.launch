<?xml version="1.0"?>
<!-- change log:
created by lawrence.han @2017-04-03;
updated by lhan@2017-04-07;
updated by lhan@2017-05-23;
updated by lhan@2017-06-02;
updated by lhan@2017-06-25;
updated by lhan@2017-08-27;
updated by armstrong.tu@2018-01-19;
-->

<launch>
<arg name="launch_dir" value="$(find wr_app)/launch"/>
<arg name="logger_level" default="info"/>

<!-- base args -->
<arg name="base_id" default="nbox"/>
<arg name="is_nbox" default="false"/>
<arg name="enb_dau" default="false"/>
<arg name="enb_slave_mode" default="false"/>
<arg name="enb_handheld_mode" default="false"/><!-- ~ no odom -->
<arg name="enb_3d_compensation" default="false"/>

<!-- 1. start base -->
<group if="$(arg is_nbox)">
    <group if="$(arg enb_dau)">
        <include file="$(arg launch_dir)/include/base/nbox/base.launch.xml">
            <arg name="launch_dir" value="$(arg launch_dir)"/>
            <arg name="logger_level" value="$(arg logger_level)"/>
            <arg name="enb_slave_mode" value="$(arg enb_slave_mode)"/>
            <arg name="control_mode" default="INTEGRATED"/>
        </include>
    </group>
    <group unless="$(arg enb_dau)">
        <include file="$(arg launch_dir)/include/base/nbox/base.launch.xml">
            <arg name="launch_dir" value="$(arg launch_dir)"/>
            <arg name="logger_level" value="$(arg logger_level)"/>
            <arg name="enb_slave_mode" value="$(arg enb_slave_mode)"/>
            <arg name="control_mode" default="MOTOR_CONTROL_UNIT"/>
        </include>
    </group>
</group>

<group unless="$(arg is_nbox)">
    <include file="$(arg launch_dir)/include/base/$(arg base_id)/base.launch.xml">
        <arg name="launch_dir" value="$(arg launch_dir)"/>
        <arg name="logger_level" value="$(arg logger_level)"/>
        <arg name="enb_slave_mode" value="$(arg enb_slave_mode)"/>
    </include>
    <group if="$(arg enb_dau)">
        <include file="$(arg launch_dir)/include/base/nbox/data_acquisition.launch.xml">
            <arg name="launch_dir" value="$(arg launch_dir)"/>
            <arg name="logger_level" value="$(arg logger_level)"/>
            <arg name="enb_slave_mode" value="$(arg enb_slave_mode)"/>
        </include>
    </group>
</group>

<!-- 2. start base_stabilizer -->
<include file="$(arg launch_dir)/include/locl/base_stabilizer.launch.xml">
  <arg name="logger_level" value="$(arg logger_level)"/>
  <!-- enb -->
  <arg name="enb_3d_compensation" value="$(arg enb_3d_compensation)"/>
  <!--<arg name="enb_imu" value="$(arg enb_imu)"/>-->
</include>





</launch>
