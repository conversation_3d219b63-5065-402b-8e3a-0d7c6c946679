<?xml version="1.0"?>
<!-- v1.0 created by armstrong.tu @2018-01-18
To launch gps
-->
<launch>
<arg name="logger_level"/>
<arg name="sid"/>
<arg name="main"/>
<arg name="std_gps_topic" value="/gps"/>
<arg name="launch_dir" value="$(find wr_app)/launch"/>

<arg name="gps_type"/>
<arg name="gps_itf_type"/>
<arg name="gps_itf_id"/>

<!-- gps_frame_0 -> gps_frame -->
<group if="$(arg main)">
    <node name="imu_frame_0_to_imu_frame_tf" pkg="tf2_ros" type="static_transform_publisher" args="0 0 0 0 0 0 /imu_frame_0 /imu_frame"/>
</group>

<group if="$(arg main)">
    <include file="$(arg launch_dir)/include/sensor/gps/$(arg gps_type).launch.xml">
        <arg name="logger_level" value="$(arg logger_level)"/>
        <arg name="gps_itf_id" value="$(arg gps_itf_id)" />
        <arg name="gps_id" value="$(arg sid)"/>
        <arg name="gps_topic" value="$(arg std_gps_topic)"/>
    </include>
</group>
<group unless="$(arg main)">
    <include file="$(arg launch_dir)/include/sensor/gps/$(arg gps_type).launch.xml">
        <arg name="logger_level" value="$(arg logger_level)"/>
        <arg name="gps_itf_id" value="$(arg gps_itf_id)" />
        <arg name="gps_id" value="$(arg sid)"/>
        <arg name="gps_topic" value="$(arg std_gps_topic)_$(arg sid)"/>
    </include>
</group>
</launch>