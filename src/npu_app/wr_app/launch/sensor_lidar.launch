<?xml version="1.0"?>
<!-- v1.0 created by armstrong.tu @2018-01-18
To launch lidar
-->
<launch>
<arg name="logger_level"/>
<arg name="sid"/>
<arg name="main"/>
<arg name="std_lidar_topic" value="/scan"/>
<arg name="launch_dir" value="$(find wr_app)/launch"/>

<arg name="lidar_type"/>
<arg name="lidar_itf_type"/>
<arg name="lidar_itf_id"/>
<arg name="lidar_enb_filter" default="false"/>

<!-- lidar_frame_0 -> lidar_frame -->
<group if="$(arg main)">
    <node name="lidar_frame_0_to_lidar_frame_tf" pkg="tf2_ros" type="static_transform_publisher" args="0 0 0 0 0 0 /lidar_frame_0 /lidar_frame"/>
</group>

<!-- if (lidar_enb_filter) -->
<group if="$(arg lidar_enb_filter)">
    <include file="$(arg launch_dir)/include/sensor/lidar/$(arg lidar_type).launch.xml">
        <arg name="logger_level" value="$(arg logger_level)"/>
        <arg name="lidar_itf_id" value="$(arg lidar_itf_id)" />
        <arg name="lidar_id" value="$(arg sid)"/>
        <!-- scan->scan_raw -->
        <arg name="lidar_topic" value="$(arg std_lidar_topic)_raw_$(arg sid)"/>
    </include>
    <node name="lidar_filters_$(arg sid)" pkg="wr_lidar_filters" type="scan_to_scan_filter_chain"  output="screen" >
        <rosparam command="load" file="/npu.x86_64/config/user/default/lidar_filters.yaml" />
        <remap from="scan" to="$(arg std_lidar_topic)_raw_$(arg sid)" />
        <remap from="scan_filtered" to="scan" />
    </node>
</group>
<!-- else -->
<group unless="$(arg lidar_enb_filter)">
    <group if="$(arg main)">
        <include file="$(arg launch_dir)/include/sensor/lidar/$(arg lidar_type).launch.xml">
            <arg name="logger_level" value="$(arg logger_level)"/>
            <arg name="lidar_itf_id" value="$(arg lidar_itf_id)" />
            <arg name="lidar_id" value="$(arg sid)"/>
            <arg name="lidar_topic" value="$(arg std_lidar_topic)"/>
        </include>
    </group>
    <group unless="$(arg main)">
        <include file="$(arg launch_dir)/include/sensor/lidar/$(arg lidar_type).launch.xml">
            <arg name="logger_level" value="$(arg logger_level)"/>
            <arg name="lidar_itf_id" value="$(arg lidar_itf_id)" />
            <arg name="lidar_id" value="$(arg sid)"/>
            <arg name="lidar_topic" value="$(arg std_lidar_topic)_$(arg sid)"/>
        </include>
    </group>
</group>
</launch>
