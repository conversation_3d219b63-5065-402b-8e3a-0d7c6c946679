#!/usr/bin/env python
# *-* utf-8 *-*

import os
import sys
import shutil
import xlrd

def travel_one_floor(start_index, param_depth_list, param_name_list):
    floor_index = 0
    travel_index = start_index
    current_depth = param_depth_list[travel_index].count("-")
    travel_seq = { "name" : param_name_list[travel_index], "seq" : {}}
    current_depth += 1
    floor_index += 1
    travel_index += 1
    while travel_index < len(param_depth_list)-1:
        next_depth = param_depth_list[travel_index+1].count("-")
        if next_depth == current_depth:
            travel_seq["seq"][floor_index] = param_name_list[travel_index]
        elif next_depth > current_depth:
            travel_index, travel_seq["seq"][floor_index] = travel_one_floor(travel_index, param_depth_list, param_name_list)
        else:
            travel_seq["seq"][floor_index] = param_name_list[travel_index]
            break
        floor_index += 1
        travel_index += 1
    return travel_index, travel_seq
    

def generate_travel_list(param_depth_list, param_name_list):
    travel_seq = { "name" : "npu_param", "seq" : {}}
    travel_index = 1
    floor_index = 1
    while travel_index < len(param_depth_list)-1:
        travel_index, travel_seq["seq"][floor_index] = travel_one_floor(travel_index, param_depth_list, param_name_list)
        travel_index += 1
        floor_index += 1
    return travel_seq

def print_travel_seq(travel_seq):
    print travel_seq["name"]
    index = 1
    param_name = ""
    while True:
        param_name = travel_seq["seq"].get(index, None)
        if type(param_name) == type({}):
            print_travel_seq(param_name)
        elif param_name == None:
            return
        else:
            print param_name
        index += 1


if __name__ == "__main__" :
    pwd = os.path.dirname(sys.argv[0])
    shutil.rmtree("%s/config"%pwd, True)
    os.mkdir("%s/config/"%pwd)
    os.mkdir("%s/config/user"%pwd)
    data = xlrd.open_workbook("%s/NpuParam.xlsx"%pwd)
    table = data.sheets()[0]
    param_depth = table.col_values(0)
    param_name = table.col_values(1)
    #travel_seq = generate_travel_list(param_depth, param_name)
    #print_travel_seq(travel_seq)
    config_list = table.row_values(0)
    for config_index in range(2, len(config_list)):
        config_id = config_list[config_index]
        os.mkdir("%s/config/user/%s"%(pwd, config_id))
        param_value = table.col_values(config_index)
        content = ""
        file_name = ""
        for i in range(1, len(param_depth)):
            depth = param_depth[i].count("-")                
            name = param_name[i].lstrip().rstrip()
            value = param_value[i]
            if depth == 0:
                if content != "":
                    fd = open("%s/config/user/%s/%s.yaml"%(pwd, config_id, file_name), "w")
                    fd.write(content)
                    fd.close()
                    content = ""
                file_name = name
                continue
            if value!="NA":
                content += "%(indent)s%(param_name)s%(value)s\n" % {
                    "indent" : "  "*(depth-1),
                    "param_name" : "%s : "%name if name[0]!="-" else "- ",
                    "value" : value if value!="NA" else "\"0\""
            }

