/****************************************
*Maintainer status: developed
*Maintainer: <PERSON> (<EMAIL>)
*Date: 2016-10-11
*Copyright(c) 2016-2017 LinkMiao CO., Ltd.
*All rights reserved
****************************************/

#include <iostream>
#include <time.h>

#include "npu_icei.h"

namespace wizrobo_npu {

typedef struct _NpuClient
{
    std::string ip;
    int         port;
    time_t      active_time;
}NpuClient;

const int CLIENT_ACT = 1;
const int TIME_OUT = 10;

static NpuClient npu_client = {"", -1, 0};
#define ICEI_COUT 0

static bool CheckNpuClient(const ::Ice::Current& cur_client)
{
    return true;
    const Ice::ConnectionInfo *p_conn_info = cur_client.con->getInfo()._ptr;
    const Ice::IPConnectionInfo *p_ip_conn_info = reinterpret_cast<const Ice::IPConnectionInfo *>(p_conn_info);

    const std::string &ip = p_ip_conn_info->remoteAddress;
    int port = p_ip_conn_info->remotePort;
    time_t now = time(0);
    int interval = now - npu_client.active_time;

#if ICEI_COUT
    std::cout << "CheckNpuClient: client("
              << ip << ":" << port << "/"
              << npu_client.ip << ":"<<  npu_client.port << ") "
              << interval ;
#endif
    if (ip == npu_client.ip && port == npu_client.port)
    {
        npu_client.active_time = now;
#if ICEI_COUT
        std::cout << " accept" << std::endl;
#endif
        return true;
    }
#if ICEI_COUT
    std::cout << " reject" << std::endl;
#endif
    cur_client.con->close(false);
    return false;
}

static bool AddNpuClient(const ::Ice::Current& cur_client)
{
    const Ice::ConnectionInfo *p_conn_info = cur_client.con->getInfo()._ptr;
    const Ice::IPConnectionInfo *p_ip_conn_info = reinterpret_cast<const Ice::IPConnectionInfo *>(p_conn_info);
    const std::string &ip = p_ip_conn_info->remoteAddress;
    int port = p_ip_conn_info->remotePort;
    time_t now = time(0);
    int interval = now - npu_client.active_time;

#if ICEI_COUT
    std::cout << "AddNpuClient: client("
              << ip << ":" << port << "/"
              << npu_client.ip << ":"<<  npu_client.port << ") "
              << interval;
#endif
    if (npu_client.ip == "" || ip == npu_client.ip
            || interval > TIME_OUT)
    {
#if ICEI_COUT
        std::cout << " accept" << std::endl;
#endif
        npu_client.ip = ip;
        npu_client.port = port;
        npu_client.active_time = now;
        return true;
    }
#if ICEI_COUT
    std::cout << " reject" << std::endl;
#endif
    cur_client.con->close(false);
    return false;
}


NpuIceI::NpuIceI(int argc, char **argv, NpuServer* ptr)
{
    //p_npu_server = new NpuServer(argc, argv);
    p_npu_server = ptr;
}

NpuIceI::~NpuIceI()
{
}

//// connection
string NpuIceI::GetServerVersion(const ::Ice::Current&)
{
    return p_npu_server->GetServerVersion();
}

void NpuIceI::Connect(const string& version, const ::Ice::Current& cur_client)
{
    if (AddNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::Connect() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call Connect() failed! There is another client connected!");
        return;
    }
    return p_npu_server->Connect(version);
}

////exce_info
void NpuIceI::CheckAbnormalInfo(const ::Ice::Current& )
{
    return p_npu_server->CheckAbnormalInfo();
}

//// connection
ServerState NpuIceI::GetServerState(const ::Ice::Current& cur_client)
{
    ServerState rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetServerState() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetServerState() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetServerState();
}

SystemDiagInfo NpuIceI::GetSystemDiagInfo(const ::Ice::Current& cur_client)
{
    SystemDiagInfo rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetSystemDiagInfo() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetSystemDiagInfo() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetSystemDiagInfo();
}

ActionState NpuIceI::GetActionState(const ::Ice::Current& cur_client)
{
    ActionState rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetActionState() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetActionState() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetActionState();
}

NpuState NpuIceI::GetNpuState(const ::Ice::Current& cur_client)
{
    NpuState rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetNpuState() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetNpuState() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetNpuState();
}


//// power
void NpuIceI::Shutdown(const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::Shutdown() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::Shutdown() failed! There is another client connected");
        return;
    }
    return p_npu_server->Shutdown();
}

void NpuIceI::Reboot(const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::Reboot() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::Reboot() failed! There is another client connected");
        return;
    }
    return p_npu_server->Reboot();
}


//// config management
StringArray NpuIceI::GetConfigIdList(const ::Ice::Current& cur_client)
{
    StringArray rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetConfigIdList() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetConfigIdList() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetConfigIdList();
}

void NpuIceI::SelectConfig(const string& id, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SelectConfig() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SelectConfig() failed! There is another client connected");
        return;
    }
    return p_npu_server->SelectConfig(id);
}

void NpuIceI::DeleteConfig(const string& id, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::DeleteConfig() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::DeleteConfig() failed! There is another client connected");
        return;
    }
    return p_npu_server->DeleteConfig(id);
}

void NpuIceI::AddConfig(const string& id, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::AddConfig() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::AddConfig() failed! There is another client connected");
        return;
    }
    return p_npu_server->AddConfig(id);
}


// npu
CoreParam NpuIceI::GetCoreParam(const ::Ice::Current& cur_client)
{
    CoreParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCoreParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCoreParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCoreParam();
}

void NpuIceI::SetCoreParam(const CoreParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetCoreParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetCoreParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetCoreParam(param);
}


// motor
MotorParam NpuIceI::GetMotorParam(const ::Ice::Current& cur_client)
{
    MotorParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetMotorParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetMotorParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetMotorParam();
}

void NpuIceI::SetMotorParam(const MotorParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetMotorParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetMotorParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetMotorParam(param);
}


// pid
PidParam NpuIceI::GetPidParam(const ::Ice::Current& cur_client)
{
    PidParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetPidParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetPidParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetPidParam();
}

void NpuIceI::SetPidParam(const PidParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetPidParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetPidParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetPidParam(param);
}


// chassis
ChassisParam NpuIceI::GetChassisParam(const ::Ice::Current& cur_client)
{
    ChassisParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetChassisParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetChassisParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetChassisParam();
}

void NpuIceI::SetChassisParam(const ChassisParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetChassisParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetChassisParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetChassisParam(param);
}


// footprint
FootprintParam NpuIceI::GetFootprintParam(const ::Ice::Current& cur_client)
{
    FootprintParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetFootprintParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetFootprintParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetFootprintParam();
}

void NpuIceI::SetFootprintParam(const FootprintParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetFootprintParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetFootprintParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetFootprintParam(param);
}


// base
BaseParam NpuIceI::GetBaseParam(const ::Ice::Current& cur_client)
{
    BaseParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetBaseParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetBaseParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetBaseParam();
}

void NpuIceI::SetBaseParam(const BaseParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetBaseParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetBaseParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetBaseParam(param);
}


// sensor
SensorParam NpuIceI::GetSensorParam(const ::Ice::Current& cur_client)
{
    SensorParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetSensorParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetSensorParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetSensorParam();
}

void NpuIceI::SetSensorParam(const SensorParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetSensorParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetSensorParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetSensorParam(param);
}


// teleop
TeleopParam NpuIceI::GetTeleopParam(const ::Ice::Current& cur_client)
{
    TeleopParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetTeleopParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetTeleopParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetTeleopParam();
}

void NpuIceI::SetTeleopParam(const TeleopParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetTeleopParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetTeleopParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetTeleopParam(param);
}


// navi
NaviParam NpuIceI::GetNaviParam(const ::Ice::Current& cur_client)
{
    NaviParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetNaviParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetNaviParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetNaviParam();
}

void NpuIceI::SetNaviParam(const NaviParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetNaviParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetNaviParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetNaviParam(param);
}


// slam
SlamParam NpuIceI::GetSlamParam(const ::Ice::Current& cur_client)
{
    SlamParam rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetSlamParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetSlamParam() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetSlamParam();
}

void NpuIceI::SetSlamParam(const SlamParam& param, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetSlamParam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetSlamParam() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetSlamParam(param);
}


//// slave mode
void NpuIceI::FeedMotorEnc(const MotorEnc& enc, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::FeedMotorEnc() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::FeedMotorEnc() failed! There is another client connected");
        return;
    }
    return p_npu_server->FeedMotorEnc(enc);
}

void NpuIceI::FeedActMotorSpd(const MotorSpd& spd, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::FeedActMotorSpd() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::FeedActMotorSpd() failed! There is another client connected");
        return;
    }
    return p_npu_server->FeedActMotorSpd(spd);
}


//// motor data

// enc
MotorEnc NpuIceI::GetMotorEnc(const ::Ice::Current& cur_client)
{
    MotorEnc rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetMotorEnc() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetMotorEnc() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetMotorEnc();
}

void NpuIceI::ClearMotorEnc(const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::ClearMotorEnc() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::ClearMotorEnc() failed! There is another client connected");
        return;
    }
    return p_npu_server->ClearMotorEnc();
}


// spd
MotorSpd NpuIceI::GetCmdMotorSpd(const ::Ice::Current& cur_client)
{
    MotorSpd rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCmdMotorSpd() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCmdMotorSpd() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCmdMotorSpd();
}

MotorSpd NpuIceI::GetActMotorSpd(const ::Ice::Current& cur_client)
{
    MotorSpd rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetActMotorSpd() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetActMotorSpd() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetActMotorSpd();
}


//// sensor data

// lidar
LidarScan NpuIceI::GetLidarScan(const ::Ice::Current& cur_client)
{
    LidarScan rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetLidarScan() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetLidarScan() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetLidarScan();
}

ImgLidarScan NpuIceI::GetImgLidarScan(const ::Ice::Current& cur_client)
{
    ImgLidarScan rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetImgLidarScan() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetImgLidarScan() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetImgLidarScan();
}


// imu
ImuData NpuIceI::GetImuData(const ::Ice::Current& cur_client)
{
    ImuData rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetImuData() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetImuData() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetImuData();
}


// sonar
SonarScan NpuIceI::GetSonarScan(const ::Ice::Current& cur_client)
{
    SonarScan rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetSonarScan() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetSonarScan() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetSonarScan();
}

ImgSonarScan NpuIceI::GetImgSonarScan(const ::Ice::Current& cur_client)
{
    ImgSonarScan rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetImgSonarScan() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetImgSonarScan() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetImgSonarScan();
}


// infrd
InfrdScan NpuIceI::GetInfrdScan(const ::Ice::Current& cur_client)
{
    InfrdScan rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetInfrdScan() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetInfrdScan() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetInfrdScan();
}

ImgInfrdScan NpuIceI::GetImgInfrdScan(const ::Ice::Current& cur_client)
{
    ImgInfrdScan rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetImgInfrdScan() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetImgInfrdScan() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetImgInfrdScan();
}


// bumper
BumperArray NpuIceI::GetBumperArray(const ::Ice::Current& cur_client)
{
    BumperArray rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetBumperArray() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetBumperArray() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetBumperArray();
}


// battery
BatteryStatus NpuIceI::GetBatteryStatus(const ::Ice::Current& cur_client)
{
    BatteryStatus rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetBatteryStatus() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetBatteryStatus() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetBatteryStatus();
}


//// manul control
void NpuIceI::SetManualCmd(const ManualCmdType cmd, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetManualCmd() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetManualCmd() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetManualCmd(cmd);
}

void NpuIceI::SetManualVel(const float lin_scale, const float ang_scale, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetManualVel() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetManualVel() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetManualVel(lin_scale, ang_scale);
}


//// map management

// map
MapInfoList NpuIceI::GetMapInfos(const ::Ice::Current& cur_client)
{
    MapInfoList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetMapInfos() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetMapInfos() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetMapInfos();
}

void NpuIceI::SetMapInfos(const MapInfoList& list, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetMapInfos() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetMapInfos() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetMapInfos(list);
}

void NpuIceI::SelectMap(const string& id, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SelectMap() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SelectMap() failed! There is another client connected");
        return;
    }
    return p_npu_server->SelectMap(id);
}


// station
StationList NpuIceI::GetStations(const string& map_id, const ::Ice::Current& cur_client)
{
    StationList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetStations() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetStations() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetStations(map_id);
}

void NpuIceI::SetStations(const string& map_id, const StationList& list, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetStations() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetStations() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetStations(map_id, list);
}

ImgStationList NpuIceI::GetImgStations(const string& map_id, const ::Ice::Current& cur_client)
{
    ImgStationList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetImgStations() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetImgStations() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetImgStations(map_id);
}

void NpuIceI::SetImgStations(const string& map_id, const ImgStationList& list, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetImgStations() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetImgStations() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetImgStations(map_id, list);
}

GeoStationList NpuIceI::GetGeoStations(const string& map_id, const ::Ice::Current& cur_client)
{
    GeoStationList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetGeoStations() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetGeoStations() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetGeoStations(map_id);
}

void NpuIceI::SetGeoStations(const string& map_id, const GeoStationList& list, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetGeoStations() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetGeoStations() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetGeoStations(map_id, list);
}


// task
TaskList NpuIceI::GetTaskList(const string& map_id, const ::Ice::Current& cur_client)
{
    TaskList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetTaskList() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetTaskList() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetTaskList(map_id);
}

void NpuIceI::ExecuteTask(const TaskList& list, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::ExecuteTask() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::ExecuteTask() failed! There is another client connected");
        return;
    }
    return p_npu_server->ExecuteTask(list);
}

void NpuIceI::SetTasks(const string& map_id, const TaskList& list, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetTasks() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetTasks() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetTasks(map_id, list);
}


// path
PathList NpuIceI::GetPaths(const string& map_id, const ::Ice::Current& cur_client)
{
    PathList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetPaths() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetPaths() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetPaths(map_id);
}

void NpuIceI::SetPaths(const string& map_id, const PathList& list, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetPaths() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetPaths() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetPaths(map_id, list);
}

ImgPathList NpuIceI::GetImgPaths(const string& map_id, const ::Ice::Current& cur_client)
{
    ImgPathList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetImgPaths() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetImgPaths() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetImgPaths(map_id);
}

void NpuIceI::SetImgPaths(const string& map_id, const ImgPathList& list, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetImgPaths() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetImgPaths() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetImgPaths(map_id, list);
}

GeoPathList NpuIceI::GetGeoPaths(const string& map_id, const ::Ice::Current& cur_client)
{
    GeoPathList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetGeoPaths() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetGeoPaths() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetGeoPaths(map_id);
}

GeoPoseList NpuIceI::GetGeoPath(const ::Ice::Current& cur_client)
{
    GeoPoseList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetGeoPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetGeoPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetGeoPath();
}

void NpuIceI::SetGeoPaths(const string& map_id, const GeoPathList& list, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetGeoPaths() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetGeoPaths() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetGeoPaths(map_id, list);
}

void NpuIceI::SetGeoPath(const GeoPath& geo_path, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetGeoPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetGeoPath() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetGeoPath(geo_path);
}


// virtual wall
VirtualWallList NpuIceI::GetVirtualWalls(const string& map_id, const ::Ice::Current& cur_client)
{
    VirtualWallList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetVirtualWalls() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetVirtualWalls() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetVirtualWalls(map_id);
}

void NpuIceI::SetVirtualWalls(const string& map_id, const VirtualWallList& virtual_walls, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetVirtualWalls() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetVirtualWalls() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetVirtualWalls(map_id, virtual_walls);
}

ImgVirtualWallList NpuIceI::GetImgVirtualWalls(const string& map_id, const ::Ice::Current& cur_client)
{
    ImgVirtualWallList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetImgVirtualWalls() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetImgVirtualWalls() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetImgVirtualWalls(map_id);
}

void NpuIceI::SetImgVirtualWalls(const string& map_id, const ImgVirtualWallList& virtual_walls, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetImgVirtualWalls() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetImgVirtualWalls() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetImgVirtualWalls(map_id, virtual_walls);
}

GeoVirtualWallList NpuIceI::GetGeoVirtualWalls(const string& map_id, const ::Ice::Current& cur_client)
{
    GeoVirtualWallList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetGeoVirtualWalls() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetGeoVirtualWalls() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetGeoVirtualWalls(map_id);
}

void NpuIceI::SetGeoVirtualWalls(const string& map_id, const GeoVirtualWallList& virtual_walls, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetGeoVirtualWalls() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetGeoVirtualWalls() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetGeoVirtualWalls(map_id, virtual_walls);
}


//// common runtime data

// vel
Vel3D NpuIceI::GetCmdVel(const ::Ice::Current& cur_client)
{
    Vel3D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCmdVel() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCmdVel() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCmdVel();
}

Vel3D NpuIceI::GetActVel(const ::Ice::Current& cur_client)
{
    Vel3D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetActVel() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetActVel() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetActVel();
}


// TODEL: be replaced by GetActVel()
Vel3D NpuIceI::GetCurrentVel(const ::Ice::Current& cur_client)
{
    Vel3D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCurrentVel() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCurrentVel() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCurrentVel();
}


// acc
Acc3D NpuIceI::GetAcc(const ::Ice::Current& cur_client)
{
    Acc3D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetAcc() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetAcc() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetAcc();
}


// TODEL: be replaced by GetAcc
Acc3D NpuIceI::GetCurrentAcc(const ::Ice::Current& cur_client)
{
    Acc3D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCurrentAcc() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCurrentAcc() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCurrentAcc();
}


// pose
Pose3D NpuIceI::GetCmdPose(const ::Ice::Current& cur_client)
{
    Pose3D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCmdPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCmdPose() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCmdPose();
}

Pose3D NpuIceI::GetActPose(const ::Ice::Current& cur_client)
{
    Pose3D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetActPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetActPose() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetActPose();
}

ImgPose NpuIceI::GetCmdImgPose(const ::Ice::Current& cur_client)
{
    ImgPose rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCmdImgPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCmdImgPose() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCmdImgPose();
}

ImgPose NpuIceI::GetActImgPose(const ::Ice::Current& cur_client)
{
    ImgPose rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetActImgPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetActImgPose() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetActImgPose();
}

GeoPose NpuIceI::GetCmdGeoPose(const ::Ice::Current& cur_client)
{
    GeoPose rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCmdGeoPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCmdGeoPose() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCmdGeoPose();
}

GeoPose NpuIceI::GetActGeoPose(const ::Ice::Current& cur_client)
{
    GeoPose rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetActGeoPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetActGeoPose() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetActGeoPose();
}


// TODEL: be replaced by GetActPose()
Pose3D NpuIceI::GetCurrentPose(const ::Ice::Current& cur_client)
{
    Pose3D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCurrentPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCurrentPose() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCurrentPose();
}


// TODEL: be replaced by GetActImgPose()
ImgPose NpuIceI::GetCurrentImgPose(const ::Ice::Current& cur_client)
{
    ImgPose rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCurrentImgPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCurrentImgPose() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCurrentImgPose();
}


// path
Path NpuIceI::GetGlobalPath(const ::Ice::Current& cur_client)
{
    Path rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetGlobalPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetGlobalPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetGlobalPath();
}

Path NpuIceI::GetLocalPath(const ::Ice::Current& cur_client)
{
    Path rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetLocalPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetLocalPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetLocalPath();
}

Path NpuIceI::GetActPath(const ::Ice::Current& cur_client)
{
    Path rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetActPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetActPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetActPath();
}

ImgPath NpuIceI::GetGlobalImgPath(const ::Ice::Current& cur_client)
{
    ImgPath rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetGlobalImgPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetGlobalImgPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetGlobalImgPath();
}

ImgPath NpuIceI::GetLocalImgPath(const ::Ice::Current& cur_client)
{
    ImgPath rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetLocalImgPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetLocalImgPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetLocalImgPath();
}

ImgPath NpuIceI::GetActImgPath(const ::Ice::Current& cur_client)
{
    ImgPath rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetActImgPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetActImgPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetActImgPath();
}

GeoPath NpuIceI::GetGlobalGeoPath(const ::Ice::Current& cur_client)
{
    GeoPath rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetGlobalGeoPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetGlobalGeoPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetGlobalGeoPath();
}

GeoPath NpuIceI::GetLocalGeoPath(const ::Ice::Current& cur_client)
{
    GeoPath rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetLocalGeoPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetLocalGeoPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetLocalGeoPath();
}

GeoPath NpuIceI::GetActGeoPath(const ::Ice::Current& cur_client)
{
    GeoPath rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetActGeoPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetActGeoPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetActGeoPath();
}


// TODEL: replaced by GetGlobalPath()
Path NpuIceI::GetCurrentPath(const ::Ice::Current& cur_client)
{
    Path rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCurrentPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCurrentPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCurrentPath();
}


// TODEL: replaced by GetGlobalImgPath()
ImgPath NpuIceI::GetCurrentImgPath(const ::Ice::Current& cur_client)
{
    ImgPath rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCurrentImgPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCurrentImgPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCurrentImgPath();
}


// map
Map2D NpuIceI::GetMap(const ::Ice::Current& cur_client)
{
    Map2D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetMap() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetMap() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetMap();
}

ImgMap NpuIceI::GetImgMap(const ::Ice::Current& cur_client)
{
    ImgMap rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetImgMap() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetImgMap() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetImgMap();
}

GeoMap NpuIceI::GetGeoMap(const ::Ice::Current& cur_client)
{
    GeoMap rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetGeoMap() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetGeoMap() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetGeoMap();
}


// TODEL: be replaced by GetMap()
Map2D NpuIceI::GetCurrentMap(const ::Ice::Current& cur_client)
{
    Map2D rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCurrentMap() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCurrentMap() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCurrentMap();
}


// TODEL: be replaced by GetImgMap()
ImgMap NpuIceI::GetCurrentImgMap(const ::Ice::Current& cur_client)
{
    ImgMap rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetCurrentImgMap() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetCurrentImgMap() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetCurrentImgMap();
}


// footprint
Point3DList NpuIceI::GetFootprintVertices(const ::Ice::Current& cur_client)
{
    Point3DList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetFootprintVertices() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetFootprintVertices() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetFootprintVertices();
}

ImgPointList NpuIceI::GetFootprintImgVertices(const ::Ice::Current& cur_client)
{
    ImgPointList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetFootprintImgVertices() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetFootprintImgVertices() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetFootprintImgVertices();
}

GeoPointList NpuIceI::GetFootprintGeoVertices(const ::Ice::Current& cur_client)
{
    GeoPointList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetFootprintGeoVertices() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetFootprintGeoVertices() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetFootprintGeoVertices();
}


//// telop
void NpuIceI::StartTelop(const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::StartTelop() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::StartTelop() failed! There is another client connected");
        return;
    }
    return p_npu_server->StartTelop();
}

void NpuIceI::StopTelop(const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::StopTelop() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::StopTelop() failed! There is another client connected");
        return;
    }
    return p_npu_server->StopTelop();
}


//// navi

/// navi.common

// initial pose
void NpuIceI::SetInitPose(const Pose3D& pose, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetInitPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetInitPose() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetInitPose(pose);
}

void NpuIceI::SetInitPoseArea(const InitPoseArea& pose, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetInitPoseArea() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetInitPoseArea() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetInitPoseArea(pose);
}

void NpuIceI::SetInitImgPose(const ImgPose& pose, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetInitImgPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetInitImgPose() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetInitImgPose(pose);
}

void NpuIceI::SetInitImgPoseArea(const InitImgPoseArea& pose, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetInitImgPoseArea() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetInitImgPoseArea() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetInitImgPoseArea(pose);
}

void NpuIceI::SetInitGeoPose(const GeoPose& pose, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetInitGeoPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetInitGeoPose() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetInitGeoPose(pose);
}

void NpuIceI::SetInitGeoPoseArea(const InitGeoPoseArea& pose, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SetInitGeoPoseArea() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SetInitGeoPoseArea() failed! There is another client connected");
        return;
    }
    return p_npu_server->SetInitGeoPoseArea(pose);
}

float NpuIceI::GetMatchingScore(const ::Ice::Current& cur_client)
{
    float rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetMatchingScore() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetMatchingScore() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetMatchingScore();
}


// start & stop
NaviMode NpuIceI::GetNaviMode(const ::Ice::Current& cur_client)
{
    NaviMode rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetNaviMode() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetNaviMode() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetNaviMode();
}

void NpuIceI::StartNavi(const NaviMode mode, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::StartNavi() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::StartNavi() failed! There is another client connected");
        return;
    }
    return p_npu_server->StartNavi(mode);
}

void NpuIceI::StopNavi(const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::StopNavi() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::StopNavi() failed! There is another client connected");
        return;
    }
    return p_npu_server->StopNavi();
}


// task control
void NpuIceI::PauseTask(const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::PauseTask() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::PauseTask() failed! There is another client connected");
        return;
    }
    return p_npu_server->PauseTask();
}

void NpuIceI::ContinueTask(const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::ContinueTask() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::ContinueTask() failed! There is another client connected");
        return;
    }
    return p_npu_server->ContinueTask();
}

void NpuIceI::CancelTask(const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::CancelTask() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::CancelTask() failed! There is another client connected");
        return;
    }
    return p_npu_server->CancelTask();
}

float NpuIceI::GetTaskProgress(const ::Ice::Current& cur_client)
{
    float rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetTaskProgress() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetTaskProgress() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetTaskProgress();
}


// status
NaviState NpuIceI::GetNaviState(const ::Ice::Current& cur_client)
{
    NaviState rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetNaviState() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetNaviState() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetNaviState();
}


/// navi.p2p
void NpuIceI::GotoPose(const Pose3D& pose, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GotoPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GotoPose() failed! There is another client connected");
        return;
    }
    return p_npu_server->GotoPose(pose);
}

void NpuIceI::GotoImgPose(const ImgPose& pose, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GotoImgPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GotoImgPose() failed! There is another client connected");
        return;
    }
    return p_npu_server->GotoImgPose(pose);
}

void NpuIceI::GotoGeoPose(const GeoPose& pose, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GotoGeoPose() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GotoGeoPose() failed! There is another client connected");
        return;
    }
    return p_npu_server->GotoGeoPose(pose);
}


//TODEL: replaced by GotoPose(Pose3D pose);
void NpuIceI::GotoGoal(const Pose3D& goal, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GotoGoal() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GotoGoal() failed! There is another client connected");
        return;
    }
    return p_npu_server->GotoGoal(goal);
}


// TODEL: replaced by GotoImgPose(Pose3D pose);
void NpuIceI::GotoImgGoal(const ImgPose& goal, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GotoImgGoal() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GotoImgGoal() failed! There is another client connected");
        return;
    }
    return p_npu_server->GotoImgGoal(goal);
}

void NpuIceI::GotoStation(const string& map_id, const string& station_id, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GotoStation() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GotoStation() failed! There is another client connected");
        return;
    }
    return p_npu_server->GotoStation(map_id, station_id);
}


/// navi.pf
void NpuIceI::FollowTempPath(const Pose3DList& poses, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::FollowTempPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::FollowTempPath() failed! There is another client connected");
        return;
    }
    return p_npu_server->FollowTempPath(poses);
}

void NpuIceI::FollowTempImgPath(const ImgPoseList& poses, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::FollowTempImgPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::FollowTempImgPath() failed! There is another client connected");
        return;
    }
    return p_npu_server->FollowTempImgPath(poses);
}

void NpuIceI::FollowTempGeoPath(const GeoPoseList& poses, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::FollowTempGeoPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::FollowTempGeoPath() failed! There is another client connected");
        return;
    }
    return p_npu_server->FollowTempGeoPath(poses);
}

void NpuIceI::FollowPath(const string& map_id, const string& path_id, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::FollowPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::FollowPath() failed! There is another client connected");
        return;
    }
    return p_npu_server->FollowPath(map_id, path_id);
}


//// navi.ccp
Pose3DList NpuIceI::PlanCoveragePath(const Point3DList& vertices, const ::Ice::Current& cur_client)
{
    Pose3DList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::PlanCoveragePath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::PlanCoveragePath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->PlanCoveragePath(vertices);
}

ImgPoseList NpuIceI::PlanCoverageImgPath(const ImgPointList& vertices, const ::Ice::Current& cur_client)
{
    ImgPoseList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::PlanCoverageImgPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::PlanCoverageImgPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->PlanCoverageImgPath(vertices);
}

GeoPoseList NpuIceI::PlanCoverageGeoPath(const GeoPointList& vertices, const ::Ice::Current& cur_client)
{
    GeoPoseList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::PlanCoverageGeoPath() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::PlanCoverageGeoPath() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->PlanCoverageGeoPath(vertices);
}


//// slam
SlamMode NpuIceI::GetSlamMode(const ::Ice::Current& cur_client)
{
    SlamMode rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetSlamMode() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetSlamMode() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetSlamMode();
}

void NpuIceI::StartSlam(const SlamMode mode, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::StartSlam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::StartSlam() failed! There is another client connected");
        return;
    }
    return p_npu_server->StartSlam(mode);
}

void NpuIceI::StopSlam(const string& map_id, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::StopSlam() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::StopSlam() failed! There is another client connected");
        return;
    }
    return p_npu_server->StopSlam(map_id);
}


/// save map
void NpuIceI::SaveMapImg(const string& map_id, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SaveMapImg() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SaveMapImg() failed! There is another client connected");
        return;
    }
    return p_npu_server->SaveMapImg(map_id);
}


//// file
ZipFile NpuIceI::ExportConfigFile(const string& file_name, const ::Ice::Current& cur_client)
{
    ZipFile rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::ExportConfigFile() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::ExportConfigFile() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->ExportConfigFile(file_name);
}

void NpuIceI::ImportConfigFile(const ZipFile& file, const string& file_name, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::ImportConfigFile() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::ImportConfigFile() failed! There is another client connected");
        return;
    }
    return p_npu_server->ImportConfigFile(file, file_name);
}

ZipFile NpuIceI::ExportMapFile(const string& file_name, const ::Ice::Current& cur_client)
{
    ZipFile rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::ExportMapFile() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::ExportMapFile() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->ExportMapFile(file_name);
}

void NpuIceI::ImportMapFile(const ZipFile& file, const string& file_name, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::ImportMapFile() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::ImportMapFile() failed! There is another client connected");
        return;
    }
    return p_npu_server->ImportMapFile(file, file_name);
}

void NpuIceI::GetExportFileInfo_async(const ::wizrobo_npu::AMD_NpuIce_GetExportFileInfoPtr& ptr, const string& fileName, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetExportFileInfo_async() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetExportFileInfo_async() failed! There is another client connected");
        return;
    }
    return p_npu_server->GetExportFileInfo_async(ptr, fileName);
}

FileData NpuIceI::GetExportFiledata(const string& fileName, const int chunk_index, const ::Ice::Current& cur_client)
{
    FileData rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetExportFiledata() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetExportFiledata() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetExportFiledata(fileName, chunk_index);
}

void NpuIceI::SendImportFileData(const FileData& data, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::SendImportFileData() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::SendImportFileData() failed! There is another client connected");
        return;
    }
    return p_npu_server->SendImportFileData(data);
}


////sensorstatus
void NpuIceI::CheckSensorStatus_async(const ::wizrobo_npu::AMD_NpuIce_CheckSensorStatusPtr& ptr, const CheckMode mode, const ::Ice::Current& cur_client)
{
    
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::CheckSensorStatus_async() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::CheckSensorStatus_async() failed! There is another client connected");
        return;
    }
    return p_npu_server->CheckSensorStatus_async(ptr, mode);
}

SensorStatus NpuIceI::GetSensorStatus(const ::Ice::Current& cur_client)
{
    SensorStatus rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetSensorStatus() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetSensorStatus() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetSensorStatus();
}


////exce_info

////task_system

//void StopTaskSystem() throws NpuException;
RuntimeStatusList NpuIceI::GetRuntimeStatus(const ::Ice::Current& cur_client)
{
    RuntimeStatusList rtn;
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::GetRuntimeStatus() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::GetRuntimeStatus() failed! There is another client connected");
        return rtn;
    }
    return p_npu_server->GetRuntimeStatus();
}



}
