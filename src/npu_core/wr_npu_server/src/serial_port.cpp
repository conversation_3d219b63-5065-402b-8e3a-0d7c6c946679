#include "com/serial_port.h"

namespace wizrobo { namespace serial_port {
SerialPort::SerialPort() : is_open(false) ,file_dev(-1)
{
}
SerialPort::~SerialPort()
{
    Close();
}

bool SerialPort::Open(std::string port_name, int baud_rate, int data_bits, std::string parity)
{
    if (IsOpen())
    {
        Close();
    }
#if SERIAL_DEBUG_PORT
    std::cout << "Opening..." << std::endl;
#endif
    // Open serial port: Read-and-write, Non-blocking, Non-process-controlling
    std::cout << "Opening..." << port_name.c_str() << std::endl;

    file_dev = open(port_name.c_str(), O_RDWR | O_NDELAY | O_NOCTTY);
    std::cout << "file_dev: " << file_dev << std::endl;

    if (file_dev < 0)
    {
        std::cerr << "Open: Unable to open port " << port_name << std::endl;
        return FALSE;
    }

    // Get attribute
    struct termios old_options;
    if (tcgetattr(file_dev, &old_options) != 0)
    {
        std::cerr << "Open: Failed to get old options." << std::endl;
        return FALSE;
    }
    else
    {
        std::cout << "Old options:" << std::endl;
        PrintOption(old_options);
#if SERIAL_DEBUG_PORT
        std::cout << "Old options:" << std::endl;
        PrintOption(old_options);
#endif
    }

    struct termios new_options;
    bzero(&new_options, sizeof(new_options));
    cfmakeraw(&new_options);

    //Baud rate
    cfsetispeed(&new_options, baud_rate);
    cfsetospeed(&new_options, baud_rate);
//    //Baud rate = 115200
    //new_options.c_cflag = B9600;

    PrintOption(new_options);

    // Ignore CDC & Enable receiving
    new_options.c_cflag |= CLOCAL | CREAD;

    //Data bits = 8
    new_options.c_cflag &= ~CSIZE;

    if (data_bits == 7)
    {
        new_options.c_cflag |= CS7;
        std::cout << "data_bits:7" << std::endl;
    }
    else
    {
        new_options.c_cflag |= CS8;
    }

    //Stop bits = 1
    new_options.c_cflag &= ~CSTOPB;

    //parity

    if (parity == "ODD")
    {
        new_options.c_cflag |= (PARODD | PARENB);
        new_options.c_iflag |= INPCK;             //Disnable parity checking
        std::cout << "parity:ODD" << std::endl;
    }
    else if(parity == "EVEN")
    {
        //new_options.c_cflag &= PARENB;
        new_options.c_cflag |= PARENB;     //Enable parity
        new_options.c_cflag &= ~PARODD;
        new_options.c_iflag |= INPCK;       //Disnable parity checking
        std::cout << "parity:EVEN" << std::endl;
    }
    else
    {
        new_options.c_cflag &= ~PARENB;
        new_options.c_iflag &= ~INPCK;       //Disnable parity checking
    }

    // Min byte num = 0, waiting time = 0
    new_options.c_cc[VMIN] = 0;
    new_options.c_cc[VTIME] = 0;

    tcflush(file_dev, TCIOFLUSH);

    if (tcsetattr(file_dev, TCSANOW, &new_options) != 0)
    {
        std::cerr << "Open: Failed to set new options" << std::endl;
        return FALSE;
    }
    else
    {
        std::cout << "New options:" << std::endl;
        PrintOption(new_options);
#if SERIAL_DEBUG_PORT
        std::cout << "New options:" << std::endl;
        PrintOption(new_options);
#endif
    }

    // Clear IO buffer
    is_open = true;
    return is_open;
}
void SerialPort::Close()
{
    if (file_dev >= 0)
    {
        close(file_dev);
        file_dev = -1;
    }
    ClearReadBuffer();
    is_open= false;
}

int SerialPort::Write(const char* p_buf, int buf_len)
{
#if SERIAL_DEBUG_PORT
    std::cout << "\t[Write] ";
#endif
    for (int i = 0; i < buf_len; i++)
    {
#if SERIAL_DEBUG_PORT
        std::cout << std::hex << std::setw(2) << std::setfill('0')\
            << (int)(unsigned char)p_buf[i] << ' ';
#endif
        if (WriteByte(p_buf[i]) == FALSE)
        {
#if SERIAL_DEBUG_PORT
            std::cout << std::endl;
#endif
            return i;
        }
    }
#if SERIAL_DEBUG_PORT
    std::cout << std::endl;
#endif
    return buf_len;
}
int SerialPort::WriteByte(char ch)
{
    if (file_dev < 0 || !is_open)
    {
        return 0;
    }
    int res = write(file_dev, &ch, 1);
    if (res < 1)
    {
        std::cerr << "Write: Failed." << std::endl;
        return 0;
    }
    return res;
}

int SerialPort::BytesWaiting()
{
    if (file_dev < 0 || !is_open)
    {
        return 0;
    }
    int bytes;
    if (ioctl(file_dev, FIONREAD, &bytes) < 0)
    {
        return 0;
    }
    else
    {
        return bytes;
    }
}
int SerialPort::Read(char* p_buf, int buf_len)
{
    for (int i = 0; i < buf_len; i++)
    {
        if (ReadByte(p_buf[i]) == FALSE)
        {
            return i;
        }
    }
    return buf_len;
}
int SerialPort::ReadByte(char& ch)
{
    if (file_dev < 0 || !is_open)
    {
        return 0;
    }
    int res = read(file_dev, &ch, 1);
    //std::cout << "res: " << res << "ch: " << ch << std::endl;
    if (res < 0)
    {
        std::cerr << "read: Failed." << std::endl;
        return 0;
    }
    return res;
}

void SerialPort::PrintOption(const struct termios &opt) const
{
    std::cout << "\tispeed=" << std::dec << GetBaud(opt.c_ispeed) << std::endl;
    std::cout << "\tospeed=" << std::dec << GetBaud(opt.c_ospeed) << std::endl;
    std::cout << "\ti:" << std::hex << std::setw(8) << std::setfill('0')
        << opt.c_iflag << std::endl;
    std::cout << "\to:" << std::hex << std::setw(8) << std::setfill('0')
        << opt.c_oflag << std::endl;
    std::cout << "\tc:" << std::hex << std::setw(8) << std::setfill('0')
        << opt.c_lflag << std::endl;
    std::cout << "\tl:" << std::hex << std::setw(8) << std::setfill('0')
        << opt.c_cflag << std::endl;
    std::cout << "\tcc:" << std::endl;
    for (int i = 0; i < NCCS; i++)
    {
        std::cout << std::dec << "\t[" << std::setw(2) << std::setfill('0') << i << "]";
        std::cout << std::hex << std::setw(2) << std::setfill('0')
            << (int)opt.c_cc[i];
        if ( (i+1) % 10 == 0)
        {
            std::cout << std::endl;
        }
    }
    std::cout << std::endl;
}
}}// namespace
