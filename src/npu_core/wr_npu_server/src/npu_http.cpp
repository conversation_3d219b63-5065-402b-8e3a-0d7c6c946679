#include "npu_http.h"

#include <boost/archive/iterators/base64_from_binary.hpp>
#include <boost/archive/iterators/binary_from_base64.hpp>
#include <boost/archive/iterators/transform_width.hpp>

#include "lodepng.h"
#include "utility.hpp"

#include <time.h>
#include <fstream>
#include <sstream>

#include <wr_npu_msgs/Station.h>
#include <wr_npu_msgs/StationArray.h>

#define NHP_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("NHP", "NpuHttpI::%s() " fmt, __FUNCTION__, ##arg)
#define NHP_INFO(fmt, arg...)  ROS_INFO_NAMED ("NHP", "NpuHttpI::%s() " fmt, __FUNCTION__, ##arg)
#define NHP_WARN(fmt, arg...)  ROS_WARN_NAMED ("NHP", "NpuHttpI::%s() " fmt, __FUNCTION__, ##arg)
#define NHP_ERROR(fmt, arg...) ROS_ERROR_NAMED("NHP", "NpuHttpI::%s() " fmt, __FUNCTION__, ##arg)

#define CheckAndParse_(response, request) \
    do \
    { \
        NHP_DEBUG("path: %s?%s", request->path.c_str(), request->query_string.c_str()); \
        if (CheckNpuClient(request)!=true) \
        { \
            if (AddNpuClient(request)!=true) \
            { \
                GetTypicalResponse(response, response_fail, "CheckNpuClient failed!"); \
                return; \
            } \
        } \
    } while(0)

namespace wizrobo_npu {

using namespace std;
using namespace boost::property_tree;
using namespace boost::property_tree::json_parser;

template <class Type>
Type stringToNum(const string& str)
{
    istringstream iss(str);
    Type num;
    iss >> num;
    return num;
}

template<typename T>
static bool Base64Encode(const T& input, string* output)
{
    typedef boost::archive::iterators::base64_from_binary<boost::archive::iterators::transform_width<typename T::const_iterator, 6, 8> > Base64EncodeIterator;
    stringstream result;
    copy(Base64EncodeIterator(input.begin()) , Base64EncodeIterator(input.end()), ostream_iterator<char>(result));
    size_t equal_count = (3 - input.size() % 3) % 3;
    for (size_t i = 0; i < equal_count; i++) {
        result.put('=');
    }
    *output = result.str();
    return output->empty() == false;
}

template<typename T>
static bool Base64Decode(const T& input, string* output)
{
    typedef boost::archive::iterators::transform_width<boost::archive::iterators::binary_from_base64<typename T::const_iterator>, 8, 6> Base64DecodeIterator;
    stringstream result;
    try {
        copy(Base64DecodeIterator(input.begin()) , Base64DecodeIterator(input.end()), ostream_iterator<char>(result));
    } catch(...) {
        return false;
    }
    *output = result.str();
    return output->empty() == false;
}

NpuHttpI::NpuHttpI() : NpuClientManagerMixin()
{
    SetHttpServerPort(8080);
    InitHttpServerUrl();
    p_npu_server = NULL;
    slam_map_name = "";
    current_map_name = "";
    p_png_data = NULL;
    pnd_data_length = 0;
    pnd_map_name = "";
}

void NpuHttpI::ParseQueryString(std::string& query_string, ParamMap& param_map)
{
    string str = query_string;
    string key, value;
    int count_key=0, count_value=0;
    int i = 0;
    NHP_DEBUG("query_string:  %s", str.c_str());
    while(str[i] != '\0') {
        if(str[i] != '&') {
            if(str[i] != '=') {
                if(count_key == count_value) {
                    key += str[i];
                } else {
                    value += str[i];
                }
            } else {
                ++count_key;
            }
        } else {
            ++count_value;
            NHP_DEBUG("key: %s / value: %s", key.c_str(), value.c_str());
            param_map.insert({key, value});
            key = "\0";
            value = "\0";
        }
        ++i;
    }
    NHP_DEBUG("key: %s / value: %s", key.c_str(), value.c_str());
    param_map.insert(make_pair(key,value));
    key = "\0";
    value = "\0";
}

NpuHttpI::ClientAddress NpuHttpI::GetClientAddress(RequestPtr request)
{
    NpuHttpI::ClientAddress client_address;
    client_address.ip = request->remote_endpoint_address();
    client_address.port = request->remote_endpoint_port();
    return client_address;
}

void NpuHttpI::InitHttpServerUrl()
{

#define _set_resource(regex, method, function) \
    do \
    { \
        http_server.resource[regex][method] = \
            std::bind(&function, this, \
            std::placeholders::_1, std::placeholders::_2); \
    } while(0)

    _set_resource("^/string$", "POST", NpuHttpI::PostString);
    _set_resource("^/info$", "GET", NpuHttpI::GetHttpInfo);
    _set_resource("^/status$", "GET", NpuHttpI::DataDeviceStatus);

//    _set_resource("^/gs-robot/real_time_data/laser_raw$", "GET", NpuHttpI::RealTimeDataLaserRaw);
//    _set_resource("^/gs-robot/real_time_data/laser_phit$", "GET", NpuHttpI::RealTimeDataLaserPhit);
//    _set_resource("^/gs-robot/real_time_data/ultrasonic_raw$", "GET", NpuHttpI::RealTimeDataUltrasonicRaw);
//    _set_resource("^/gs-robot/real_time_data/ultrasonic_phit$", "GET", NpuHttpI::RealTimeDataUltrasonicPhit);
//    _set_resource("^/gs-robot/real_time_data/protector$", "GET", NpuHttpI::RealTimeDataProtector);
//    _set_resource("^/gs-robot/real_time_data/mobile_data$", "GET", NpuHttpI::RealTimeDataMobileData);
//    _set_resource("^/gs-robot/real_time_data/non_map_data$", "GET", NpuHttpI::RealTimeDataNonMapData);
    _set_resource("^/gs-robot/data/device_status$", "GET", NpuHttpI::DataDeviceStatus);
//    _set_resource("^/gs-robot/real_time_data/footprint$", "GET", NpuHttpI::RealTimeDataFootprint);
//    _set_resource("^/gs-robot/data/positions$", "GET", NpuHttpI::DataPositions);
//    _set_resource("^/gs-robot/cmd/add_position$", "GET", NpuHttpI::CmdAddPosition);
//    _set_resource("^/gs-robot/cmd/delete_position$", "GET", NpuHttpI::CmdDeletePosition);
//    _set_resource("^/gs-robot/cmd/rename_position$", "GET", NpuHttpI::CmdRenamePosition);
//    _set_resource("^/gs-robot/cmd/start_scan_map$", "GET", NpuHttpI::CmdStartScanMap);
//    _set_resource("^/gs-robot/cmd/stop_scan_map$", "GET", NpuHttpI::CmdStopScanMap);
//    _set_resource("^/gs-robot/cmd/cancel_scan_map$", "GET", NpuHttpI::CmdCancelScanMap);
//    _set_resource("^/gs-robot/cmd/async_stop_scan_map$", "GET", NpuHttpI::CmdAsyncStopScanMap);
//    _set_resource("^/gs-robot/cmd/is_stop_scan_finished$", "GET", NpuHttpI::CmdIsStopScanFinished);
//    _set_resource("^/gs-robot/data/map_png$", "GET", NpuHttpI::DataMapPng);
//    _set_resource("^/gs-robot/data/maps$", "GET", NpuHttpI::DataMaps);
//    _set_resource("^/gs-robot/cmd/delete_map$", "GET", NpuHttpI::CmdDeleteMap);
//    _set_resource("^/gs-robot/cmd/rename_map$", "GET", NpuHttpI::CmdRenameMap);
//    _set_resource("^/gs-robot/cmd/load_map$", "GET", NpuHttpI::CmdLoadMap);
//    _set_resource("^/gs-robot/cmd/initialize_directly$", "GET", NpuHttpI::CmdInitializeDirectly);
//    _set_resource("^/gs-robot/cmd/initialize$", "GET", NpuHttpI::CmdInitialize);
//    _set_resource("^/gs-robot/cmd/initialize_customized$", "POST", NpuHttpI::CmdInitializeCustomized);
//    _set_resource("^/gs-robot/cmd/is_initialize_finished$", "GET", NpuHttpI::CmdIsInitializeFinished);
//    _set_resource("^/gs-robot/real_time_data/current_initialize_status$", "GET", NpuHttpI::RealTimeDataCurrentInitializeStatus);
//    _set_resource("^/gs-robot/real_time_data/position$", "GET", NpuHttpI::RealTimeDataPosition);
//    _set_resource("^/gs-robot/real_time_data/gps$", "GET", NpuHttpI::RealTimeDataGps);
//    _set_resource("^/gs-robot/cmd/position/navigate$", "GET", NpuHttpI::CmdPositionNavigate);
//    _set_resource("^/gs-robot/cmd/navigate$", "POST", NpuHttpI::CmdNavigate);
//    _set_resource("^/gs-robot/cmd/pause_navigate$", "GET", NpuHttpI::CmdPauseNavigate);
//    _set_resource("^/gs-robot/cmd/resume_navigate$", "GET", NpuHttpI::CmdResumeNavigate);
//    _set_resource("^/gs-robot/cmd/cancel_navigate$", "GET", NpuHttpI::CmdCancelNavigate);
//    _set_resource("^/gs-robot/real_time_data/navigation_path$", "GET", NpuHttpI::RealTimeDataNavigationPath);
//    _set_resource("^/gs-robot/data/navigation_path$", "GET", NpuHttpI::DataNavigationPath);
//    _set_resource("^/gs-robot/data/paths$", "GET", NpuHttpI::DataPaths);
//    _set_resource("^/gs-robot/cmd/delete_path$", "GET", NpuHttpI::CmdDeletePath);
//    _set_resource("^/gs-robot/cmd/rename_path$", "GET", NpuHttpI::CmdRenamePath);
//    _set_resource("^/gs-robot/cmd/start_record_path$", "GET", NpuHttpI::CmdStartRecordPath);
//    _set_resource("^/gs-robot/cmd/stop_record_path$", "GET", NpuHttpI::CmdStopRecordPath);
//    _set_resource("^/gs-robot/cmd/cancel_record_path$", "GET", NpuHttpI::CmdCancelRecordPath);
//    _set_resource("^/gs-robot/cmd/start_record_area$", "GET", NpuHttpI::CmdStartRecordArea);
//    _set_resource("^/gs-robot/cmd/stop_record_area$", "GET", NpuHttpI::CmdStopRecordArea);
//    _set_resource("^/gs-robot/cmd/cancel_record_area$", "GET", NpuHttpI::CmdCancelRecordArea);
//    _set_resource("^/gs-robot/cmd/add_path_action$", "POST", NpuHttpI::CmdAddPathAction);
//    _set_resource("^/gs-robot/data/path_data_list$", "GET", NpuHttpI::DataPathDataList);
//    _set_resource("^/gs-robot/cmd/generate_graph_path$", "POST", NpuHttpI::CmdGenerateGraphPath);
//    _set_resource("^/gs-robot/cmd/verify_graph_line$", "POST", NpuHttpI::CmdVerifyGraphLine);
//    _set_resource("^/gs-robot/cmd/verify_graph_path$", "POST", NpuHttpI::CmdVerifyGraphPath);
//    _set_resource("^/gs-robot/data/action_list$", "GET", NpuHttpI::DataActionList);
//    _set_resource("^/gs-robot/cmd/update_graph_path$", "POST", NpuHttpI::CmdUpdateGraphPath);
//    _set_resource("^/gs-robot/cmd/verify_graph$", "POST", NpuHttpI::CmdVerifyGraph);
//    _set_resource("^/gs-robot/data/graph_paths$", "GET", NpuHttpI::DataGraphPaths);
//    _set_resource("^/gs-robot/cmd/delete_graph_path$", "GET", NpuHttpI::CmdDeleteGraphPath);
//    _set_resource("^/gs-robot/cmd/rename_graph_path$", "GET", NpuHttpI::CmdRenameGraphPath);
//    _set_resource("^/gs-robot/real_time_data/work_status$", "GET", NpuHttpI::RealTimeDataWorkStatus);
//    _set_resource("^/gs-robot/cmd/save_task_queue$", "POST", NpuHttpI::CmdSaveTaskQueue);
//    _set_resource("^/gs-robot/data/task_queues$", "GET", NpuHttpI::DataTaskQueues);
//    _set_resource("^/gs-robot/cmd/delete_task_queue$", "GET", NpuHttpI::CmdDeleteTaskQueue);
//    _set_resource("^/gs-robot/cmd/start_task_queue$", "POST", NpuHttpI::CmdStartTaskQueue);
//    _set_resource("^/gs-robot/cmd/stop_task_queue$", "GET", NpuHttpI::CmdStopTaskQueue);
//    _set_resource("^/gs-robot/cmd/pause_task_queue$", "GET", NpuHttpI::CmdPauseTaskQueue);
//    _set_resource("^/gs-robot/cmd/resume_task_queue$", "GET", NpuHttpI::CmdResumeTaskQueue);
//    _set_resource("^/gs-robot/cmd/stop_current_task$", "GET", NpuHttpI::CmdStopCurrentTask);
//    _set_resource("^/gs-robot/cmd/is_task_queue_finished$", "GET", NpuHttpI::CmdIsTaskQueueFinished);
//    _set_resource("^/gs-robot/data/virtual_obstacles$", "GET", NpuHttpI::DataVirtual_obstacles);
//    _set_resource("^/gs-robot/cmd/update_virtual_obstacles$", "POST", NpuHttpI::CmdUpdateVirtualObstacles);
//    _set_resource("^/gs-robot/cmd/position/add_position$", "POST", NpuHttpI::CmdPositionAddPosition);
//    _set_resource("^/gs-robot/cmd/move$", "POST", NpuHttpI::CmdMove);
//    _set_resource("^/gs-robot/cmd/rotate$", "POST", NpuHttpI::CmdRotate);
//    _set_resource("^/gs-robot/cmd/is_rotate_finished$", "GET", NpuHttpI::CmdIsRotateFinished);
//    _set_resource("^/gs-robot/cmd/ping$", "GET", NpuHttpI::CmdPing);
//    _set_resource("^/gs-robot/cmd/set_heart$", "GET", NpuHttpI::CmdSetQHeart);
//    _set_resource("^/gs-robot/cmd/start_charge$", "GET", NpuHttpI::CmdStartCharge);
//    _set_resource("^/gs-robot/cmd/stop_charge$", "GET", NpuHttpI::CmdStopCharge);
//    _set_resource("^/gs-robot/data/charge_status$", "GET", NpuHttpI::DataChargeStatus);
//    _set_resource("^/gs-robot/cmd/power_off$", "GET", NpuHttpI::CmdPowerOff);
//    _set_resource("^/gs-robot/cmd/update_laser_param$", "POST", NpuHttpI::CmdUpdateLaserParam);
//    _set_resource("^/gs-robot/info/version$", "GET", NpuHttpI::InfoVersion);
//    _set_resource("^/gs-robot/cmd/set_navigation_speed_level$", "GET", NpuHttpI::CmdSetNavigationSpeedLevel);
//    _set_resource("^/gs-robot/cmd/set_speed_level$", "GET", NpuHttpI::CmdSetSpeedLevel);

    _set_resource("^/lm/bag_opt$", "POST", NpuHttpI::BagOpt);

    _set_resource("^/.*\\.gif$", "GET", NpuHttpI::GetWebFile);
    _set_resource("^/.*\\.png$", "GET", NpuHttpI::GetWebFile);
    _set_resource("^/.*\\.json$", "GET", NpuHttpI::GetWebFile);
    _set_resource("^/.*\\.js$", "GET", NpuHttpI::GetJs);
    _set_resource("^/.*\\.css$", "GET", NpuHttpI::GetJs);
    _set_resource("^/.*\\.html$", "GET", NpuHttpI::GetHtml);
    _set_resource("^/.*\\.ico$", "GET", NpuHttpI::GetIco);
    _set_resource("^/.*\\.bag$", "GET", NpuHttpI::GetBagData);
    _set_resource("^/lm/main$", "GET", NpuHttpI::GetMainPage);

    //connect
    _set_resource("^/lm/get_server_version$", "GET", NpuHttpI::GetServerVersion);
    _set_resource("^/lm/connect$", "GET", NpuHttpI::Connect);
    _set_resource("^/lm/get_server_state$", "GET", NpuHttpI::GetServerState);
    _set_resource("^/lm/get_system_diag_info$", "GET", NpuHttpI::GetSystemDiagInfo);
    _set_resource("^/lm/get_action_state$", "GET", NpuHttpI::GetActionState);
    _set_resource("^/lm/get_npu_state$", "GET", NpuHttpI::GetNpuState);

    //power
    _set_resource("^/lm/shutdown$", "GET", NpuHttpI::Shutdown);
    _set_resource("^/lm/reboot$", "GET", NpuHttpI::Reboot);

    _set_resource("^/lm/device_status$", "GET", NpuHttpI::DataDeviceStatus);
    // config management
    _set_resource("^/lm/get_config_id_list$", "GET", NpuHttpI::GetConfigIdList);
    _set_resource("^/lm/select_config$", "GET", NpuHttpI::SelectConfig);
    _set_resource("^/lm/delete_config$", "GET", NpuHttpI::DeleteConfig);
    _set_resource("^/lm/add_config$", "GET", NpuHttpI::AddConfig);

    _set_resource("^/lm/get_core_param$", "GET", NpuHttpI::GetCoreParam);
    _set_resource("^/lm/set_core_param$", "POST", NpuHttpI::SetCoreParam);
    _set_resource("^/lm/get_motor_param$", "GET", NpuHttpI::GetMotorParam);
    _set_resource("^/lm/set_motor_param$", "POST", NpuHttpI::SetMotorParam);
    _set_resource("^/lm/get_pid_param$", "GET", NpuHttpI::GetPidParam);
    _set_resource("^/lm/set_pid_param$", "POST", NpuHttpI::SetPidParam);
    _set_resource("^/lm/get_chassis_param$", "GET", NpuHttpI::GetChassisParam);
    _set_resource("^/lm/set_chassis_param$", "POST", NpuHttpI::SetChassisParam);
    _set_resource("^/lm/get_footprint_param$", "GET", NpuHttpI::GetFootprintParam);
    _set_resource("^/lm/set_footprint_param$", "POST", NpuHttpI::SetFootprintParam);
    _set_resource("^/lm/get_base_param$", "GET", NpuHttpI::GetBaseParam);
    _set_resource("^/lm/set_base_param$", "POST", NpuHttpI::SetBaseParam);
    _set_resource("^/lm/get_sensor_param$", "GET", NpuHttpI::GetSensorParam);
    _set_resource("^/lm/set_sensor_param$", "POST", NpuHttpI::SetSensorParam);
    _set_resource("^/lm/get_teleop_param$", "GET", NpuHttpI::GetTeleopParam);
    _set_resource("^/lm/set_teleop_param$", "POST", NpuHttpI::SetTeleopParam);
    _set_resource("^/lm/get_param$", "GET", NpuHttpI::GetParamByPtree);
    _set_resource("^/lm/set_param$", "POST", NpuHttpI::SetParamByPtree);
    _set_resource("^/lm/get_navi_param$", "GET", NpuHttpI::GetNaviParam);
    _set_resource("^/lm/set_navi_param$", "POST", NpuHttpI::SetNaviParam);
    _set_resource("^/lm/get_slam_param$", "GET", NpuHttpI::GetSlamParam);
    _set_resource("^/lm/set_slam_param$", "POST", NpuHttpI::SetSlamParam);

    _set_resource("^/lm/feed_motor_enc$", "GET", NpuHttpI::FeedMotorEnc);
    _set_resource("^/lm/feed_act_motor_spd$", "GET", NpuHttpI::FeedActMotorSpd);
    _set_resource("^/lm/get_motor_enc$", "GET", NpuHttpI::GetMotorEnc);
    _set_resource("^/lm/clear_motor_enc$", "GET", NpuHttpI::ClearMotorEnc);
    _set_resource("^/lm/get_cmd_motor_spd$", "GET", NpuHttpI::GetCmdMotorSpd);
    _set_resource("^/lm/get_act_motor_spd$", "GET", NpuHttpI::GetActMotorSpd);
    _set_resource("^/lm/get_lidar_scan$", "GET", NpuHttpI::GetLidarScan);
//    _set_resource("^/lm/GetImgLidarScan$", "GET", NpuHttpI::GetImgLidarScan);
    _set_resource("^/lm/get_imu_data$", "GET", NpuHttpI::GetImuData);
    _set_resource("^/lm/get_sonar_scan$", "GET", NpuHttpI::GetSonarScan);
//    _set_resource("^/lm/GetImgSonarScan$", "GET", NpuHttpI::GetImgSonarScan);
    _set_resource("^/lm/get_infrd_scan$", "GET", NpuHttpI::GetInfrdScan);
//    _set_resource("^/lm/GetImgInfrdScan$", "GET", NpuHttpI::GetImgInfrdScan);
    _set_resource("^/lm/get_bumper_array$", "GET", NpuHttpI::GetBumperArray);
    _set_resource("^/lm/get_battery_status$", "GET", NpuHttpI::GetBatteryStatus);
    _set_resource("^/lm/set_manual_cmd$", "GET", NpuHttpI::SetManualCmd);
    _set_resource("^/lm/set_manual_vel$", "GET", NpuHttpI::SetManualVel);
    _set_resource("^/lm/get_map_infos$", "GET", NpuHttpI::GetMapInfos);
    _set_resource("^/lm/set_map_infos$", "POST", NpuHttpI::SetMapInfos);
    _set_resource("^/lm/select_map$", "GET", NpuHttpI::SelectMap);
    _set_resource("^/lm/get_stations$", "GET", NpuHttpI::GetStations);
    _set_resource("^/lm/set_stations$", "POST", NpuHttpI::SetStations);
//    _set_resource("^/lm/GetImgStations$", "GET", NpuHttpI::GetImgStations);
//    _set_resource("^/lm/SetImgStations$", "GET", NpuHttpI::SetImgStations);
    _set_resource("^/lm/get_task_list$", "GET", NpuHttpI::GetTaskList);
    _set_resource("^/lm/execute_task$", "GET", NpuHttpI::ExecuteTask);
    _set_resource("^/lm/set_tasks$", "POST", NpuHttpI::SetTasks);
    _set_resource("^/lm/get_paths$", "GET", NpuHttpI::GetPaths);
    _set_resource("^/lm/set_paths$", "POST", NpuHttpI::SetPaths);
//    _set_resource("^/lm/GetImgPaths$", "GET", NpuHttpI::GetImgPaths);
//    _set_resource("^/lm/SetImgPaths$", "GET", NpuHttpI::SetImgPaths);
    _set_resource("^/lm/get_virtualwalls$", "GET", NpuHttpI::GetVirtualWalls);
    _set_resource("^/lm/set_virtualwalls$", "POST", NpuHttpI::SetVirtualWalls);
//    _set_resource("^/lm/GetImgVirtualWalls$", "GET", NpuHttpI::GetImgVirtualWalls);
//    _set_resource("^/lm/SetImgVirtualWalls$", "GET", NpuHttpI::SetImgVirtualWalls);
    _set_resource("^/lm/get_cmd_vel$", "GET", NpuHttpI::GetCmdVel);
    _set_resource("^/lm/get_act_vel$", "GET", NpuHttpI::GetActVel);
//    _set_resource("^/lm/GetCurrentVel$", "GET", NpuHttpI::GetCurrentVel);
//    _set_resource("^/lm/GetAcc$", "GET", NpuHttpI::GetAcc);
//    _set_resource("^/lm/GetCurrentAcc$", "GET", NpuHttpI::GetCurrentAcc);
    _set_resource("^/lm/get_cmd_pose$", "GET", NpuHttpI::GetCmdPose);
//    _set_resource("^/lm/GetCmdImgPose$", "GET", NpuHttpI::GetCmdImgPose);
    _set_resource("^/lm/get_act_pose$", "GET", NpuHttpI::GetActPose);
//    _set_resource("^/lm/GetActImgPose$", "GET", NpuHttpI::GetActImgPose);
//    _set_resource("^/lm/GetCurrentPose$", "GET", NpuHttpI::GetCurrentPose);
//    _set_resource("^/lm/GetCurrentImgPose$", "GET", NpuHttpI::GetCurrentImgPose);
    _set_resource("^/lm/get_global_path$", "GET", NpuHttpI::GetGlobalPath);
//    _set_resource("^/lm/GetGlobalImgPath$", "GET", NpuHttpI::GetGlobalImgPath);
    _set_resource("^/lm/get_local_path$", "GET", NpuHttpI::GetLocalPath);
//    _set_resource("^/lm/GetLocalImgPath$", "GET", NpuHttpI::GetLocalImgPath);
    _set_resource("^/lm/get_act_path$", "GET", NpuHttpI::GetActPath);
//    _set_resource("^/lm/GetActImgPath$", "GET", NpuHttpI::GetActImgPath);
//    _set_resource("^/lm/GetCurrentPath$", "GET", NpuHttpI::GetCurrentPath);
//    _set_resource("^/lm/GetCurrentImgPath$", "GET", NpuHttpI::GetCurrentImgPath);
    _set_resource("^/lm/get_map$", "GET", NpuHttpI::GetMap);
//    _set_resource("^/lm/GetImgMap$", "GET", NpuHttpI::GetImgMap);
//    _set_resource("^/lm/GetCurrentMap$", "GET", NpuHttpI::GetCurrentMap);
//    _set_resource("^/lm/GetCurrentImgMap$", "GET", NpuHttpI::GetCurrentImgMap);
    _set_resource("^/lm/get_footprint_vertices$", "GET", NpuHttpI::GetFootprintVertices);
//    _set_resource("^/lm/GetFootprintImgVertices$", "GET", NpuHttpI::GetFootprintImgVertices);
//    _set_resource("^/lm/StartTelop$", "GET", NpuHttpI::StartTelop);
//    _set_resource("^/lm/StopTelop$", "GET", NpuHttpI::StopTelop);
    _set_resource("^/lm/set_init_pose$", "POST", NpuHttpI::SetInitPose);
//    _set_resource("^/lm/SetInitImgPose$", "GET", NpuHttpI::SetInitImgPose);
    _set_resource("^/lm/set_init_pose_area$", "POST", NpuHttpI::SetInitPoseArea);
//    _set_resource("^/lm/SetInitImgPoseArea$", "GET", NpuHttpI::SetInitImgPoseArea);
    _set_resource("^/lm/get_matching_score$", "GET", NpuHttpI::GetMatchingScore);
//    _set_resource("^/lm/GetNaviMode$", "GET", NpuHttpI::GetNaviMode);
    _set_resource("^/lm/start_navi$", "GET", NpuHttpI::StartNavi);
    _set_resource("^/lm/stop_navi$", "GET", NpuHttpI::StopNavi);
    _set_resource("^/lm/pause_task$", "GET", NpuHttpI::PauseTask);
    _set_resource("^/lm/continue_task$", "GET", NpuHttpI::ContinueTask);
    _set_resource("^/lm/cancel_task$", "GET", NpuHttpI::CancelTask);
    _set_resource("^/lm/get_task_progress$", "GET", NpuHttpI::GetTaskProgress);
    _set_resource("^/lm/get_navi_state$", "GET", NpuHttpI::GetNaviState);
    _set_resource("^/lm/goto_pose$", "GET", NpuHttpI::GotoPose);
//    _set_resource("^/lm/GotoImgPose$", "GET", NpuHttpI::GotoImgPose);
//    _set_resource("^/lm/GotoGoal$", "GET", NpuHttpI::GotoGoal);
//    _set_resource("^/lm/GotoImgGoal$", "GET", NpuHttpI::GotoImgGoal);
    _set_resource("^/lm/goto_station$", "GET", NpuHttpI::GotoStation);
    _set_resource("^/lm/follow_temp_path$", "GET", NpuHttpI::FollowTempPath);
//    _set_resource("^/lm/FollowTempImgPath$", "GET", NpuHttpI::FollowTempImgPath);
    _set_resource("^/lm/follow_path$", "GET", NpuHttpI::FollowPath);
    _set_resource("^/lm/plan_coverage_path$", "POST", NpuHttpI::PlanCoveragePath);
//    _set_resource("^/lm/PlanCoverageImgPath$", "GET", NpuHttpI::PlanCoverageImgPath);
//    _set_resource("^/lm/get_slam_mode$", "GET", NpuHttpI::GetSlamMode);
    _set_resource("^/lm/start_slam$", "GET", NpuHttpI::StartSlam);
    _set_resource("^/lm/stop_slam$", "GET", NpuHttpI::StopSlam);
    _set_resource("^/lm/save_map_img$", "GET", NpuHttpI::SaveMapImg);
//    _set_resource("^/lm/export_config_file$", "GET", NpuHttpI::ExportConfigFile);
//    _set_resource("^/lm/import_config_file$", "GET", NpuHttpI::ImportConfigFile);
    _set_resource("^/lm/export_map_file$", "GET", NpuHttpI::ExportMapFile);
    _set_resource("^/lm/import_map_file$", "POST", NpuHttpI::ImportMapFile);
//    _set_resource("^/lm/GetExportFileInfo$", "GET", NpuHttpI::GetExportFileInfo);
//    _set_resource("^/lm/GetExportFiledata$", "GET", NpuHttpI::GetExportFiledata);
//    _set_resource("^/lm/SendImportFileData$", "GET", NpuHttpI::SendImportFileData);
//    _set_resource("^/lm/CheckSensorStatus$", "GET", NpuHttpI::CheckSensorStatus);
//    _set_resource("^/lm/GetSensorStatus$", "GET", NpuHttpI::GetSensorStatus);
//    _set_resource("^/lm/CheckAbnormalInfo$", "GET", NpuHttpI::CheckAbnormalInfo);
    _set_resource("^/lm/get_runtime_status$", "GET", NpuHttpI::GetRuntimeStatus);

    _set_resource("^/lm/map.png$", "GET", NpuHttpI::GetPngMap);
    _set_resource("^/lm/get_png_map$", "GET", NpuHttpI::GetPngMap);
    _set_resource("^/lm/get_current_map_info$", "GET", NpuHttpI::GetCurrentMapInfo);

    _set_resource("^/lm/get_csg_stations$", "GET", NpuHttpI::GetCsgStations);
    _set_resource("^/lm/csg_station_opt$", "GET", NpuHttpI::CsgStationOpt);
    _set_resource("^/lm/csg_path_opt$", "GET", NpuHttpI::CsgPathOpt);
    _set_resource("^/lm/csg_enable_auto_mark$", "GET", NpuHttpI::CsgEnableAutoMark);
    _set_resource("^/lm/csg_enable_auto_connect$", "GET", NpuHttpI::CsgEnableAutoConnect);
    _set_resource("^/lm/csg_do_connect_once$", "GET", NpuHttpI::CsgConnectOnce);
#undef _set_resource
    http_server.default_resource["GET"] = [](ResponsePtr response, RequestPtr request) {
        NHP_ERROR("GET request: %s", request->path.c_str());
    };
}

void NpuHttpI::SetNpuServer(NpuServer* ptr)
{
    p_npu_server = ptr;
}

void NpuHttpI::SetHttpServerPort(int port)
{
    http_server.config.port = port;
    //NHP_ERROR("max_request_streambuf_size: %d", static_cast<int>(http_server.config.max_request_streambuf_size));
    //http_server.config.max_request_streambuf_size = 8192;
    return;
}

void NpuHttpI::GetTypicalResponse(ResponsePtr response, ResponseType type, const char* message)
{
    std::string _message = std::string(message);
    return GetTypicalResponse(response, type, _message);
}

void NpuHttpI::GetTypicalResponse(ResponsePtr response, ResponseType type, std::string& message)
{
    ptree data;
    data.add("message", message);
    return GetTypicalResponse(response, type, data);
}

void NpuHttpI::GetTypicalResponse(ResponsePtr response, ResponseType type, ptree& data)
{
    std::string msg = "";
    int errorCode = 0;
    bool successed = false;
    SimpleWeb::StatusCode code = SimpleWeb::StatusCode::client_error_not_found;

    switch (type)
    {
    case response_success:
        code = SimpleWeb::StatusCode::success_ok;
        msg = "successed";
        errorCode = 200;
        successed = true;
        break;
    case response_fail:
        code = SimpleWeb::StatusCode::success_ok;
        msg = "failed";
        errorCode = 200;
        successed = false;
        break;
    case response_error:
        code = SimpleWeb::StatusCode::client_error_bad_request;
        msg = "Bad Request";
        errorCode = 400;
        successed = false;
        break;
    case response_not_found:
    default:
        code = SimpleWeb::StatusCode::client_error_not_found;
        msg = "Not Found";
        errorCode = 404;
        successed = false;
        break;
    }

    ptree pt;
    pt.add_child("data", data);
    pt.add("errorCode", errorCode);
    pt.add("msg", msg);
    pt.add("successed", successed);
    std::stringstream ss;
    write_json(ss, pt);
    response->write(code, ss.str().c_str());
    return;
}

void NpuHttpI::Run()
{
    boost::thread server_thread([&]() {
        // Start server
        http_server.start();
    });

    while(ros::ok() && !server_thread.timed_join(boost::posix_time::millisec(200)))
    {

    }
    http_server.stop();
    server_thread.join();
}

void NpuHttpI::PostString(ResponsePtr response, RequestPtr request)
{
    // Retrieve string:
    auto content = request->content.string();
    // request->content.string() is a convenience function for:
    // stringstream ss;
    // ss << request->content.rdbuf();
    // auto content=ss.str();
    *response << "HTTP/1.1 200 OK\r\nContent-Length: " << content.length() << "\r\n\r\n"
              << content;


    // Alternatively, use one of the convenience functions, for instance:
    // response->write(content);
    GetTypicalResponse(response, response_success, "");
}
#include <iostream>
void NpuHttpI::GetHttpInfo(ResponsePtr response, RequestPtr request)
{
    stringstream stream;
//    stream << "<h1>Request from " << request->remote_endpoint_address() << ":" << request->remote_endpoint_port() << "</h1>";

//    stream << request->method << " " << request->path << " HTTP/" << request->http_version;

//    stream << "<h2>Query Fields</h2>";
//    auto query_fields = request->parse_query_string();
//    for(auto &field : query_fields)
//        stream << field.first << ": " << field.second << "<br>";

//    stream << "<h2>Header Fields</h2>";
//    for(auto &field : request->header)
//        stream << field.first << ": " << field.second << "<br>";

    ptree root, arr,object;
    arr.push_back(std::make_pair("", object));
    //arr.push_back(std::make_pair("","2"));
    //arr.push_back(std::make_pair("","3"));

    object.put("a","b");
    object.put("c","d");
    object.put("e","f");

    root.add_child("array", arr);
    bool boolvalue = true;
    root.put("boolean",boolvalue);
    root.put("null","null");
    int num = 123;
    root.put("number",num);
    root.add_child("object",object);
    root.put("string","Hello World");
    write_json(stream, root);
    response->write(stream.str());
    //GetTypicalResponse(response, response_success, stream.str().c_str());
}

void NpuHttpI::RealTimeDataLaserRaw(ResponsePtr response, RequestPtr request)
{
//    CheckAndParse_(response, request);
//    ParamMap param_map;
//    ParseQueryString(request->path, param_map);
//    try
//    {
//        ptree data,point;
//        point.put("stamp",0);
//        point.put("frame_id","base_laser");
//        data.add_child("header",point);
//        //data.add("header/stamp", 0);
//        //data.add("header/frame_id", "base_laser");

//        data.add("angle_min", p_npu_server->lidar_scan_msg_.angle_min);
//        data.add("angle_max", p_npu_server->lidar_scan_msg_.angle_max);
//        data.add("angle_increment", p_npu_server->lidar_scan_msg_.angle_increment);
//        data.add("range_min", p_npu_server->lidar_scan_msg_.range_min);
//        data.add("range_max", p_npu_server->lidar_scan_msg_.range_max);
//        data.add("range", p_npu_server->lidar_scan_msg_.ranges);
//        data.add("intensities", p_npu_server->lidar_scan_msg_.intensities);

//        std::stringstream ss;
//        write_json(ss, data);
//        response->write(ss.str());
//        //GetTypicalResponse(response, response_success, data);
//    }
//    catch(exception& e)
//    {
//        GetTypicalResponse(response, response_error, e.what());
//    }
    return;
}

void NpuHttpI::RealTimeDataLaserPhit(ResponsePtr response, RequestPtr request)
{
    /*http://127.0.0.1:8080/gs-robot/cmd/start_scan_map?map_name=linkmiao_map&index=100*/
    //request->path; // /gs-robot/cmd/start_scan_map
    //request->query_string; // map_name=linkmiao_map&index=100
    CheckAndParse_(response, request);
    ParamMap param_map; // param_map["map_name"] => "linkmiao_map", param_map["index"] => "100"
    ParseQueryString(request->query_string, param_map);
    try
    {
        ptree data;
        data.add("header/stamp", 0);
        data.add("header/frame_id", "base_laser");

        ImgMap img_map = p_npu_server->GetImgMap();
        data.add("mapInfo/gridHeight", img_map.mat.height);
        data.add("mapInfo/gridWidth", img_map.mat.width);
        data.add("mapInfo/originX", img_map.info.offset.x);
        data.add("mapInfo/originY", img_map.info.offset.y);
        data.add("mapInfo/resolution", img_map.info.resolution);

        ImgLidarScan img_lidar_scan = p_npu_server->GetImgLidarScan();
        LidarScan lidar_scan = p_npu_server->GetLidarScan();

        ptree grid_phits, grid_point;
        for (int i=0;i<img_lidar_scan.points.size();i++)
        {
            grid_point.put("x", img_lidar_scan.points[i].u);
            grid_point.put("y", img_lidar_scan.points[i].v);
            grid_point.put("z", 0);
            grid_phits.push_back(std::make_pair("", grid_point));
        }
        data.add_child("gridPhits" , grid_phits);

        ptree world_phits, world_point;
        for (int i=0;i<lidar_scan.points.size();i++)
        {
            world_point.put("x", lidar_scan.points[i].x);
            world_point.put("y", lidar_scan.points[i].y);
            world_point.put("z", 0);
            world_phits.push_back(std::make_pair("", world_point));
        }
        data.add_child("worldPhits" , world_phits);
        std::stringstream ss;
        write_json(ss, data);
        response->write(ss.str());
        //GetTypicalResponse(response, response_success, data);
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::RealTimeDataUltrasonicRaw(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::RealTimeDataUltrasonicPhit(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::RealTimeDataProtector(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::RealTimeDataMobileData(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::RealTimeDataNonMapData(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::DataDeviceStatus(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        ptree data, point_l, point_a, linear, angular, imu, point_i;
        NpuState npu_state = p_npu_server->GetNpuState();
        NaviMode navimode = p_npu_server->GetNaviMode();
        NaviState navi_state = p_npu_server->GetNaviState();
        Vel3D act_speed = p_npu_server->GetActVel();
        ImuData imu_data = p_npu_server->GetImuData();
        data.add("npuState", npu_state);
        data.add("naviMode",navimode);
        data.add("naviState", navi_state);
        point_l.put("x", act_speed.v_x);
        point_l.put("y", act_speed.v_y);
        point_l.put("z", act_speed.v_z);
        linear.push_back(std::make_pair("", point_l));
        data.add_child("linear" , linear);
        point_a.put("roll", act_speed.v_roll);
        point_a.put("pitch", act_speed.v_pitch);
        point_a.put("yaw", act_speed.v_yaw);
        angular.push_back(std::make_pair("", point_a));
        data.add_child("angular" , angular);
        //data.add("linear/x", act_speed.v_x);
        //data.add("linear/y", act_speed.v_y);
        //data.add("linear/z", act_speed.v_z);
        //data.add("angular/roll", act_speed.v_roll);
        //data.add("angular/pitch", act_speed.v_pitch);
        //data.add("angular/yaw", act_speed.v_yaw);
        point_i.put("roll", imu_data.roll_deg);
        point_i.put("pitch", imu_data.pitch_deg);
        point_i.put("yaw", imu_data.yaw_deg);
        imu.push_back(std::make_pair("", point_i));
        data.add_child("imu_data" , imu);
        GetTypicalResponse(response, response_success, data);
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::RealTimeDataFootprint(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        ptree data;
        ImgMap img_map = p_npu_server->GetImgMap();
        data.add("mapInfo/gridHeight", img_map.mat.height);
        data.add("mapInfo/gridWidth", img_map.mat.width);
        data.add("mapInfo/originX", img_map.info.offset.x);
        data.add("mapInfo/originY", img_map.info.offset.y);
        data.add("mapInfo/resolution", img_map.info.resolution);

        ImgPointList img_point_list = p_npu_server->GetFootprintImgVertices();
        Point3DList point_3d_list = p_npu_server->GetFootprintVertices();

        ptree grid_phits, grid_point;
        for (int i=0;i<img_point_list.size();i++)
        {
            grid_point.put("x", img_point_list[i].u);
            grid_point.put("y", img_point_list[i].v);
            grid_phits.push_back(std::make_pair("", grid_point));
        }
        data.add_child("gridPhits" , grid_phits);

        ptree world_phits, world_point;
        for (int i=0;i<point_3d_list.size();i++)
        {
            world_point.put("x", point_3d_list[i].x);
            world_point.put("y", point_3d_list[i].y);
            world_phits.push_back(std::make_pair("", world_point));
        }
        data.add_child("worldPhits" , world_phits);
        std::stringstream ss;
        write_json(ss, data);
        response->write(ss.str());
        //GetTypicalResponse(response, response_success, data);
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::DataPositions(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        ptree data, root, station, worldPose, orientation, position;
        std::string mapName, type_s;
        int isGetType = 1;
        mapName = param_map["map_name"];
        if(param_map.find("type") == param_map.end())
            isGetType = 0;
        type_s = param_map["type"];
        int type_i = atoi(type_s.c_str());
        p_npu_server->SelectMap(mapName);
        StationList stations = p_npu_server->GetStations(mapName);
        ImgStationList imgstations = p_npu_server->GetImgStations(mapName);
//        ROS_INFO("333");
//        ROS_INFO("mapName: %s",mapName.c_str());
//        ROS_INFO("stations.size: %d",stations.size());
//        ROS_INFO("imgstations.size: %d",imgstations.size());
//        for(int i=0;i<2;i++)
//        {
//            if(isGetType)
//                if(type_i != stations.at(i).info.type)
//                    continue;
//            station.add("angle","11");
//            station.add("artag_id","stations[i].info.artag_id");
//            station.add("gridX","imgstations[i].pose.u");
//            station.add("gridY","imgstations[i].pose.v");
//            station.add("id","i");
//            station.add("mapId","stations[i].info.map_id");
//            station.add("mapName","mapName");
//            station.add("name","stations[i].info.id");
//            station.add("type","stations[i].info.type");
//            //tf::Quaternion quat;
//            //quat.setRPY(stations[i].pose.roll, stations[i].pose.pitch, stations[i].pose.yaw);
//            orientation.put("w","quat.w()");
//            orientation.put("x","quat.x()");
//            orientation.put("y","quat.y()");
//            orientation.put("z","quat.z()");
//            //worldPose.add_child("worldPose",orientation);
//            worldPose.push_back(std::make_pair("orientation",orientation));
//            position.put("x","stations[i].pose.x");
//            position.put("y","stations[i].pose.y");
//            position.put("z","stations[i].pose.z");
//            //worldPose.add_child("position",position);
//            //station.add_child("worldPose",worldPose);
//            worldPose.push_back(std::make_pair("position", position));
//            station.push_back(std::make_pair("worldPose", worldPose));
//            data.push_back(std::make_pair("", station));
//            station.clear();
//        }

        for(int i=0;i<stations.size();i++)
        {
            if(isGetType)
                if(type_i != stations.at(i).info.type)
                    continue;
            station.add("angle",imgstations[i].pose.theta);
            station.add("artag_id",stations[i].info.artag_id);
            station.add("gridX",imgstations[i].pose.u);
            station.add("gridY",imgstations[i].pose.v);
            station.add("id",i);
            station.add("mapId",stations[i].info.map_id);
            station.add("mapName",mapName);
            station.add("name",stations[i].info.id);
            station.add("type",stations[i].info.type);
            tf::Quaternion quat;
            quat.setRPY(stations[i].pose.roll, stations[i].pose.pitch, stations[i].pose.yaw);
            orientation.put("w",quat.w());
            orientation.put("x",quat.x());
            orientation.put("y",quat.y());
            orientation.put("z",quat.z());
            worldPose.push_back(std::make_pair("orientation",orientation));
            position.put("x",stations[i].pose.x);
            position.put("y",stations[i].pose.y);
            position.put("z",stations[i].pose.z);
            worldPose.push_back(std::make_pair("position", position));
            station.push_back(std::make_pair("worldPose", worldPose));
            data.push_back(std::make_pair("", station));
            station.clear();
        }
//        ROS_INFO("555");
        GetTypicalResponse(response, response_success, data);

    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::CmdAddPosition(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        std::string mapName, type_s, position_name;
        wizrobo_npu::StationInfo station_info;

        if(current_map_name == "")
            ROS_ERROR("CmdAddPositon() : no current map");
        else
            mapName = current_map_name;
        position_name = param_map["position_name"];
        type_s = param_map["type"];
        int type_i = atoi(type_s.c_str());

        p_npu_server->StartNavi(P2P_NAVI);//test
        ros::Duration duration(8);
        duration.sleep();

        station_info.map_id = mapName;
        station_info.id = position_name;
        station_info.type = wizrobo_npu::StationType(type_i);
        p_npu_server->AddStation(mapName,station_info);

        GetTypicalResponse(response, response_success, "");

        p_npu_server->StopNavi();//test
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::CmdDeletePosition(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        std::string mapName, position_name;

        mapName = param_map["map_name"];
        position_name = param_map["position_name"];

        if(p_npu_server->DeleteStation(mapName,position_name))
            GetTypicalResponse(response, response_success, "");
        else
            GetTypicalResponse(response, response_fail, "");

    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::CmdRenamePosition(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        std::string mapName, origin_name, new_name;

        mapName = param_map["map_name"];
        origin_name = param_map["origin_name"];
        new_name = param_map["new_name"];

        if(p_npu_server->RenameStation(mapName,origin_name,new_name))
            GetTypicalResponse(response, response_success, "");
        else
            GetTypicalResponse(response, response_fail, "");

    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdStartScanMap(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    slam_map_name = param_map["map_name"];
    try
    {
        p_npu_server->StartSlam(SlamMode::GH_SLAM);
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        slam_map_name = "";
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdStopScanMap(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        p_npu_server->StopSlam(slam_map_name);
        slam_map_name = "";
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        slam_map_name = "";
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdCancelScanMap(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        p_npu_server->StopSlam("");
        slam_map_name = "";
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        slam_map_name = "";
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdAsyncStopScanMap(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        p_npu_server->StopSlam(slam_map_name);
        slam_map_name = "";
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        slam_map_name = "";
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdIsStopScanFinished(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        NpuState state = p_npu_server->GetNpuState();
        if(state == NpuState::IDLE_STATE)
            GetTypicalResponse(response, response_success, "true");
        else
            GetTypicalResponse(response, response_success, "false");

    }
    catch(exception& e)
    {
        slam_map_name = "";
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::DataMapPng(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    std::string map_name = param_map["map_name"];
    try
    {
        MapInfoList map_info_list = p_npu_server->GetMapInfos();
        int i;
        for(i=0;i<map_info_list.size();i++)
        {
            if (map_info_list[i].id == map_name)
            {
                break;
            }
        }
        if (i == map_info_list.size() )
        {
            GetTypicalResponse(response, response_fail, "map not found");
            return;
        }
        std::string platform = "x86_64";
        ros::NodeHandle nh;
        nh.param<std::string>(STD_CORE_PARAM_NS+"PLATFORM", platform, "x86_64");
        std::string pgm_file_path = "/npu." + platform + "/map/" + map_name + ".pgm";
        std::string png_file_path = "/npu." + platform + "/map/" + map_name + ".png";
        std::string cmd = "/usr/bin/convert " + pgm_file_path + " -flatten png:- > " + png_file_path;
        system(cmd.c_str());

        ifstream fin(png_file_path);
        if(!fin)
        {
            GetTypicalResponse(response, response_fail, "convert png file failed");
            return;
        }
        fin.seekg(0, std::ios::end);
        int length = fin.tellg();
        fin.seekg(0, std::ios::beg);
        char* ptr_buffer = new char[length];
        fin.read(ptr_buffer, length);
        response->write(ptr_buffer);
        delete ptr_buffer;
        ptr_buffer = NULL;
        fin.close();
        //GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        slam_map_name = "";
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::DataMaps(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        ptree data, map_info;
        MapInfoList map_info_list = p_npu_server->GetMapInfos();
        for(int i=0;i<map_info_list.size();i++)
        {
            map_info.put("createdAt", map_info_list[i].creation_time);
            map_info.put("dataFileName", map_info_list[i].id);
            map_info.put("id", map_info_list[i].id);
            map_info.put("mapInfo/gridHeight", map_info_list[i].dimension.x);
            map_info.put("mapInfo/gridWidth", map_info_list[i].dimension.y);
            map_info.put("mapInfo/originX", map_info_list[i].offset.x);
            map_info.put("mapInfo/originY", map_info_list[i].offset.y);
            map_info.put("mapInfo/resolution", map_info_list[i].resolution);
            map_info.put("name", map_info_list[i].id);
            map_info.put("obstacleFileName", "");
            map_info.put("pgmFileName", map_info_list[i].id);
            map_info.put("pngFileName", "");
            map_info.put("yamlFileName", map_info_list[i].id);
            data.push_back(std::make_pair("", map_info));
        }
        GetTypicalResponse(response, response_success, data);
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdDeleteMap(ResponsePtr response, RequestPtr request)
{
    return;
}
void NpuHttpI::CmdRenameMap(ResponsePtr response, RequestPtr request) { return; }

void NpuHttpI::CmdLoadMap(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    current_map_name = param_map["map_name"];
    try
    {
        p_npu_server->SelectMap(current_map_name);
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdInitializeDirectly(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdInitialize(ResponsePtr response, RequestPtr request) { return; }

void NpuHttpI::CmdInitializeCustomized(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {

        std::string mapName, u, v, theta;
        ImgPose img_pose;
        mapName = param_map["mapName"];
        ROS_INFO("mapName: %s",mapName.c_str());
        u = param_map["point.gridPosition.x"];
        v = param_map["point.gridPosition.y"];
        theta = param_map["point.angle"];
        img_pose.u = atoi(u.c_str());
        img_pose.v = atoi(v.c_str());
        img_pose.theta = atof(theta.c_str());
        ROS_INFO("u: %d",img_pose.u);
        ROS_INFO("v: %d",img_pose.v);
        ROS_INFO("theta: %f",img_pose.theta);
        NpuState state = p_npu_server->GetNpuState();
        if (state != NpuState::NAVI_STATE)
        {
            p_npu_server->SelectMap(mapName);
            p_npu_server->StartNavi(P2P_NAVI);
            ros::Duration duration(5);
            duration.sleep();
        }
        p_npu_server->SetInitImgPose(img_pose);
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdIsInitializeFinished(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::RealTimeDataCurrentInitializeStatus(ResponsePtr response, RequestPtr request) { return; }

void NpuHttpI::RealTimeDataPosition(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        ptree root;
        ImgPose img_pose = p_npu_server->GetActImgPose();
        root.add("angle" ,img_pose.theta);
        root.add("gridPosition/gridPosition/x", img_pose.u);
        root.add("gridPosition/gridPosition/y", img_pose.v);
        Pose3D pose_3d = p_npu_server->GetActPose();
        tf::Quaternion quat;
        quat.setRPY(0.0, 0.0, pose_3d.yaw);
        root.add("worldPosition/orientation/w", quat.w());
        root.add("worldPosition/orientation/x", quat.x());
        root.add("worldPosition/orientation/y", quat.y());
        root.add("worldPosition/orientation/z", quat.z());
        root.add("worldPosition/position/x" , pose_3d.x);
        root.add("worldPosition/position/y" , pose_3d.y);
        root.add("worldPosition/position/z" , pose_3d.z);
        ImgMap img_map = p_npu_server->GetImgMap();
        root.add("mapInfo/gridHeight", img_map.mat.height);
        root.add("mapInfo/gridWidth", img_map.mat.width);
        root.add("mapInfo/originX", img_map.info.offset.x);
        root.add("mapInfo/originY", img_map.info.offset.y);
        root.add("mapInfo/resolution", img_map.info.resolution);
        std::stringstream ss;
        write_json(ss, root);
        response->write(ss.str().c_str());
        //GetTypicalResponse(response, response_success, ss.str());
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::RealTimeDataGps(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdPositionNavigate(ResponsePtr response, RequestPtr request) { return; }

void NpuHttpI::CmdNavigate(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        ImgPose img_pose;
//        ptree pt;
//        read_json(request->content, pt);
//        img_pose.u = pt.get_child("destination").get_child("gridPosition").get<int>("x");
//        img_pose.v = pt.get_child("destination").get_child("gridPosition").get<int>("y");
//        img_pose.theta = pt.get_child("destination").get<double>("angle");
        std::string u, v, theta;
        u = param_map["destination.gridPosition.x"];
        v = param_map["destination.gridPosition.y"];
        theta = param_map["destination.angle"];
        img_pose.u = atoi(u.c_str());
        img_pose.v = atoi(v.c_str());
        img_pose.theta = atof(theta.c_str());
        p_npu_server->GotoImgPose(img_pose);
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdPauseNavigate(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        p_npu_server->PauseTask();
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdResumeNavigate(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        p_npu_server->ContinueTask();
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdCancelNavigate(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        p_npu_server->StopNavi();
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::RealTimeDataNavigationPath(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        ptree data, gridPhits, point;
        ImgPath img_path = p_npu_server->GetGlobalImgPath();
        for (int i=0;i<img_path.poses.size();i++)
        {
            point.put("x", img_path.poses[i].u);
            point.put("y", img_path.poses[i].v);
            gridPhits.push_back(std::make_pair("", point));
        }
        data.add_child("gridPhits" , gridPhits);
        ImgMap img_map = p_npu_server->GetImgMap();
        data.add("mapInfo/gridHeight", img_map.mat.height);
        data.add("mapInfo/gridWidth", img_map.mat.width);
        data.add("mapInfo/originX", img_map.info.offset.x);
        data.add("mapInfo/originY", img_map.info.offset.y);
        data.add("mapInfo/resolution", img_map.info.resolution);
        GetTypicalResponse(response, response_success, data);
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::DataNavigationPath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::DataPaths(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdDeletePath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdRenamePath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdStartRecordPath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdStopRecordPath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdCancelRecordPath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdStartRecordArea(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdStopRecordArea(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdCancelRecordArea(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdAddPathAction(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::DataPathDataList(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdGenerateGraphPath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdVerifyGraphLine(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdVerifyGraphPath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::DataActionList(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdUpdateGraphPath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdVerifyGraph(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::DataGraphPaths(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdDeleteGraphPath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdRenameGraphPath(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::RealTimeDataWorkStatus(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdSaveTaskQueue(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::DataTaskQueues(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdDeleteTaskQueue(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdStartTaskQueue(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdStopTaskQueue(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdPauseTaskQueue(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdResumeTaskQueue(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdStopCurrentTask(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdIsTaskQueueFinished(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::DataVirtual_obstacles(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdUpdateVirtualObstacles(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdPositionAddPosition(ResponsePtr response, RequestPtr request) { return; }

void NpuHttpI::CmdMove(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        //ptree pt;
        //read_json(request->content, pt);
        std::string linear, angular;
        linear = param_map["speed.linearSpeed"];
        angular = param_map["speed.angularSpeed"];
        float line_v = atof(linear.c_str());
        float angu_v = atof(angular.c_str());
        p_npu_server->SetManualVel(line_v, angu_v);
        GetTypicalResponse(response, response_success, "");
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CmdRotate(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdIsRotateFinished(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdPing(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdSetQHeart(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdStartCharge(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdStopCharge(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::DataChargeStatus(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdPowerOff(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdUpdateLaserParam(ResponsePtr response, RequestPtr request) { return; }

void NpuHttpI::InfoVersion(ResponsePtr response, RequestPtr request)
{
    CheckAndParse_(response, request);
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try
    {
        std::string version;
        version = p_npu_server->GetServerVersion();
        NHP_INFO("%s", version.c_str());
        p_npu_server->Connect(version);
        ptree data;
        data.add("version", version);
        GetTypicalResponse(response, response_success, data);
    }
    catch(exception& e)
    {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::CmdSetNavigationSpeedLevel(ResponsePtr response, RequestPtr request) { return; }
void NpuHttpI::CmdSetSpeedLevel(ResponsePtr response, RequestPtr request) { return; }

static int GetFile(const char* path, char** pp_content)
{
    ifstream f_index(path);
    f_index.seekg(0, ios::end);
    int l = f_index.tellg();
    f_index.seekg(0, ios::beg);
    (*pp_content) = new char[l+1];
    f_index.read((*pp_content), l);
    (*pp_content)[l] = '\0';
    return (l+1);
}

void NpuHttpI::GetMainPage(ResponsePtr response, RequestPtr request)
{
    string path = "/npu.x86_64/web-app/index.html";
    char* p_content;
    GetFile(path.c_str(), &p_content);
    SimpleWeb::CaseInsensitiveMultimap header;
    header.insert(std::make_pair("Cache-Control", "no-cache"));
    response->write(SimpleWeb::StatusCode::success_ok, p_content);
    delete p_content;
}

void NpuHttpI::GetWebFile(ResponsePtr response, RequestPtr request)
{
    LM_DEBUG("");
    string path = "/npu.x86_64/web-app" + request->path;
    GetGeneralFile(path, response);
    LM_DEBUG("<");
    return;
}

void NpuHttpI::GetJs(ResponsePtr response, RequestPtr request)
{
    string path = "/npu.x86_64/web-app" + request->path;
    NHP_DEBUG("%s", path.c_str());
    char* p_content;
    GetFile(path.c_str(), &p_content);
    response->write(SimpleWeb::StatusCode::success_ok, p_content);
    delete p_content;
}

void NpuHttpI::GetHtml(ResponsePtr response, RequestPtr request)
{
    string path = "/npu.x86_64/web-app" + request->path;
    NHP_DEBUG("%s", path.c_str());
    char* p_content;
    GetFile(path.c_str(), &p_content);
    SimpleWeb::CaseInsensitiveMultimap header;
    header.insert(std::make_pair("Cache-Control", "no-cache"));
    response->write(SimpleWeb::StatusCode::success_ok, p_content);
    delete p_content;
}

void NpuHttpI::GetCss(ResponsePtr response, RequestPtr request)
{
    string path = "/npu.x86_64/web-app" + request->path;
    NHP_DEBUG("%s", path.c_str());
    char* p_content;
    GetFile(path.c_str(), &p_content);
    response->write(SimpleWeb::StatusCode::success_ok, p_content);
    delete p_content;
}

void NpuHttpI::GetIco(ResponsePtr response, RequestPtr request)
{
#if 0
    string path = "/npu.x86_64/web-app" + request->path;
    NHP_DEBUG("%s", path.c_str());
    char* p_content;
    GetFile(path.c_str(), &p_content);
    response->write(SimpleWeb::StatusCode::success_ok, p_content);
    delete p_content;
#endif
    response->write(SimpleWeb::StatusCode::client_error_not_found, "");
}

void NpuHttpI::GetBagData(ResponsePtr response, RequestPtr request)
{
    LM_DEBUG("");
    string path = "/npu.x86_64/bag/" + request->path;
    GetGeneralFile(path, response);
    LM_DEBUG("<");
}

//connect
void NpuHttpI::GetServerVersion(ResponsePtr response, RequestPtr request)
{
    string str_version = p_npu_server->GetServerVersion();
    try {
        ptree data;
        data.add("version", str_version);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;

}

void NpuHttpI::Connect(ResponsePtr response, RequestPtr request)
{
    string str_version = p_npu_server->GetServerVersion();
    p_npu_server->Connect(str_version);
    GetTypicalResponse(response, response_success, "");
}

void NpuHttpI::GetServerState(ResponsePtr response, RequestPtr request)
{
    ServerState rtn;
    try {
        ptree data;
        rtn = p_npu_server->GetServerState();
        data.add("server_state", rtn);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetSystemDiagInfo(ResponsePtr response, RequestPtr request)
{
    SystemDiagInfo rtn;
    try {
        ptree data;
        rtn = p_npu_server->GetSystemDiagInfo();
        data.add("system_diag_info", rtn.todo);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetActionState(ResponsePtr response, RequestPtr request)
{
    ActionState rtn;
    try {
        ptree data;
        rtn = p_npu_server->GetActionState();
        data.add("action_state", rtn);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetNpuState(ResponsePtr response, RequestPtr request)
{
    NpuState rtn;
    try {
        ptree data;
        rtn = p_npu_server->GetNpuState();
        data.add("npu_state", rtn);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//power
void NpuHttpI::Shutdown(ResponsePtr response, RequestPtr request)
{
    try {
        p_npu_server->Shutdown();
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::Reboot(ResponsePtr response, RequestPtr request)
{
    try {
        p_npu_server->Reboot();
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// config management
void NpuHttpI::GetConfigIdList(ResponsePtr response, RequestPtr request)
{
    try {
        StringArray configIdList = p_npu_server->GetConfigIdList();
        ptree pt_Config_list;
        for (auto& config : configIdList) {
            ptree pt_config;
            pt_config.put_value(config);
            pt_Config_list.push_back(std::make_pair("", pt_config));
        }
        GetTypicalResponse(response, response_success, pt_Config_list);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::SelectConfig(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        p_npu_server->SelectConfig(param_map["configName"]);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::DeleteConfig(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        p_npu_server->DeleteConfig(param_map["configName"]);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::AddConfig(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        p_npu_server->AddConfig(param_map["configName"]);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//core
void NpuHttpI::GetCoreParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        CoreParam param = p_npu_server->GetCoreParam();
        ptree data;
        data.add("npu_mode", param.npu_mode);
        data.add("config_id", param.config_id);
        data.add("map_id", param.map_id);
        data.add("record_id", param.record_id);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetCoreParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        CoreParam param;
        param.npu_mode = wizrobo_npu::NpuMode(pt_data.get_child("npu_mode").get_value<Ice::Int>());
        param.config_id = pt_data.get_child("config_id").get_value<std::string>();
        param.map_id = pt_data.get_child("map_id").get_value<std::string>();
        param.record_id = pt_data.get_child("record_id").get_value<std::string>();
        p_npu_server->SetCoreParam(param);

        GetTypicalResponse(response, response_success, "data");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//motor
void NpuHttpI::GetMotorParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        MotorParam param = p_npu_server->GetMotorParam();
        ptree data;
        data.add("motor_num", param.motor_num);
        data.add("motor_dir_str", param.motor_dir_str);
        data.add("motor_max_spd_rpm", param.motor_max_spd_rpm);
        data.add("motor_rdc_ratio", param.motor_rdc_ratio);
        data.add("motor_enc_res_ppr", param.motor_enc_res_ppr);
        data.add("motor_pwm_frq_hz", param.motor_pwm_frq_hz);
        data.add("motor_brk_type", param.motor_brk_type);
        data.add("brk_vol", param.brk_vol);
        data.add("enb_vol", param.enb_vol);
        data.add("dir_vol", param.dir_vol);
        data.add("pwm_vol", param.pwm_vol);
        data.add("enb_auxdir_mode", param.enb_auxdir_mode);
        data.add("enb_throttle_mode", param.enb_throttle_mode);
        data.add("throttle_zero_pos_fac", param.throttle_zero_pos_fac);
        data.add("throttle_zero_neg_fac", param.throttle_zero_neg_fac);
        data.add("end_left_right_switch", param.end_left_right_switch);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetMotorParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        MotorParam param;
        param.motor_num = pt_data.get_child("motor_num").get_value<Ice::Int>();
        param.motor_dir_str = pt_data.get_child("motor_dir_str").get_value<std::string>();
        param.motor_max_spd_rpm = pt_data.get_child("motor_max_spd_rpm").get_value<Ice::Int>();
        param.motor_rdc_ratio = pt_data.get_child("motor_rdc_ratio").get_value<Ice::Float>();
        param.motor_enc_res_ppr = pt_data.get_child("motor_enc_res_ppr").get_value<Ice::Int>();
        param.motor_pwm_frq_hz = pt_data.get_child("motor_pwm_frq_hz").get_value<Ice::Int>();
        param.motor_brk_type = wizrobo_npu::BrakeType(pt_data.get_child("motor_brk_type").get_value<Ice::Int>());
        param.brk_vol = wizrobo_npu::ValidOutputLevel(pt_data.get_child("brk_vol").get_value<Ice::Int>());
        param.enb_vol = wizrobo_npu::ValidOutputLevel(pt_data.get_child("enb_vol").get_value<Ice::Int>());
        param.dir_vol = wizrobo_npu::ValidOutputLevel(pt_data.get_child("dir_vol").get_value<Ice::Int>());
        param.pwm_vol = wizrobo_npu::ValidOutputLevel(pt_data.get_child("pwm_vol").get_value<Ice::Int>());
        param.enb_auxdir_mode = pt_data.get_child("enb_auxdir_mode").get_value<bool>();
        param.enb_throttle_mode = pt_data.get_child("enb_throttle_mode").get_value<bool>();
        param.throttle_zero_pos_fac = pt_data.get_child("throttle_zero_pos_fac").get_value<Ice::Float>();
        param.throttle_zero_neg_fac = pt_data.get_child("throttle_zero_neg_fac").get_value<Ice::Float>();
        param.end_left_right_switch = pt_data.get_child("end_left_right_switch").get_value<bool>();
        p_npu_server->SetMotorParam(param);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//pid
void NpuHttpI::GetPidParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        PidParam param = p_npu_server->GetPidParam();
        ptree data;
        data.add("enb_pid", param.enb_pid);
        data.add("kp_acc", param.kp_acc);
        data.add("kp_dec", param.kp_dec);
        data.add("ki_acc", param.ki_acc);
        data.add("ki_dec", param.ki_dec);
        data.add("kd_acc", param.kd_acc);
        data.add("kd_dec", param.kd_dec);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetPidParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        PidParam param;
        param.enb_pid = pt_data.get_child("enb_pid").get_value<bool>();
        param.kp_acc = pt_data.get_child("kp_acc").get_value<Ice::Float>();
        param.kp_dec = pt_data.get_child("kp_dec").get_value<Ice::Float>();
        param.ki_acc = pt_data.get_child("ki_acc").get_value<Ice::Float>();
        param.ki_dec = pt_data.get_child("ki_dec").get_value<Ice::Float>();
        param.kd_acc = pt_data.get_child("kd_acc").get_value<Ice::Float>();
        param.kd_dec = pt_data.get_child("kd_dec").get_value<Ice::Float>();
        p_npu_server->SetPidParam(param);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//chassis
void NpuHttpI::GetChassisParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        ChassisParam param = p_npu_server->GetChassisParam();
        ptree data;
        data.add("model_type", param.model_type);
        data.add("wheel_rdc_ratio", param.wheel_rdc_ratio);
        data.add("wheel_radius_m", param.wheel_radius_m);
        data.add("wheel_span_m", param.wheel_span_m);
        data.add("max_lin_acc_time_s", param.max_lin_acc_time_s);
        data.add("max_ang_acc_time_s", param.max_ang_acc_time_s);
        data.add("autoset_max_lin_spd_mps", param.autoset_max_lin_spd_mps);
        data.add("autoset_max_ang_spd_rps", param.autoset_max_ang_spd_rps);
        data.add("carlike_steer_enc_location", param.carlike_steer_enc_location);
        data.add("carlike_axle_dist_m", param.carlike_axle_dist_m);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetChassisParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        ChassisParam param;
        param.model_type = wizrobo_npu::ChassisModelType(pt_data.get_child("model_type").get_value<Ice::Int>());
        param.wheel_rdc_ratio = pt_data.get_child("wheel_rdc_ratio").get_value<Ice::Float>();
        param.wheel_radius_m = pt_data.get_child("wheel_radius_m").get_value<Ice::Float>();
        param.wheel_span_m = pt_data.get_child("wheel_span_m").get_value<Ice::Float>();
        param.max_lin_acc_time_s = pt_data.get_child("max_lin_acc_time_s").get_value<Ice::Float>();
        param.max_ang_acc_time_s = pt_data.get_child("max_ang_acc_time_s").get_value<Ice::Float>();
        param.autoset_max_lin_spd_mps = pt_data.get_child("autoset_max_lin_spd_mps").get_value<Ice::Float>();
        param.autoset_max_ang_spd_rps = pt_data.get_child("autoset_max_ang_spd_rps").get_value<Ice::Float>();
        param.carlike_steer_enc_location = wizrobo_npu::SteerEncLocation(pt_data.get_child("carlike_steer_enc_location").get_value<Ice::Int>());
        param.carlike_axle_dist_m = pt_data.get_child("carlike_axle_dist_m").get_value<Ice::Float>();
        p_npu_server->SetChassisParam(param);
        GetTypicalResponse(response, response_success, pt_data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//footprint
void NpuHttpI::GetFootprintParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        FootprintParam param = p_npu_server->GetFootprintParam();
        ptree data, pt_vertices;
        data.add("shape_type", param.shape_type);
        data.add("rot_center_offset_m", param.rot_center_offset_m);
        data.add("round_radius_m", param.round_radius_m);
        data.add("rectangle_width_m", param.rectangle_width_m);
        data.add("rectangle_length_m", param.rectangle_length_m);
        data.add("polygon_vertex_num", param.polygon_vertex_num);
        for (auto& point : param.polygon_vertices) {
            ptree pt_point;
            pt_point.put("x", point.x);
            pt_point.put("y", point.y);
            pt_point.put("z", point.z);
            pt_vertices.push_back(std::make_pair("", pt_point));
        }
        data.add_child("polygon_vertices", pt_vertices);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetFootprintParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        FootprintParam param;
        param.shape_type = wizrobo_npu::ShapeType(pt_data.get_child("shape_type").get_value<Ice::Int>());
        param.rot_center_offset_m = pt_data.get_child("rot_center_offset_m").get_value<Ice::Float>();
        param.round_radius_m = pt_data.get_child("round_radius_m").get_value<Ice::Float>();
        param.rectangle_width_m = pt_data.get_child("rectangle_width_m").get_value<Ice::Float>();
        param.rectangle_length_m = pt_data.get_child("rectangle_length_m").get_value<Ice::Float>();
        param.polygon_vertex_num = pt_data.get_child("polygon_vertex_num").get_value<Ice::Int>();
        ptree pt_vertices = pt_data.get_child("polygon_vertices");
        for (ptree::iterator po_iter = pt_vertices.begin();po_iter != pt_vertices.end();po_iter++) {
            Point3D point;
            point.x = po_iter->second.get_child("x").get_value<float>();
            point.y = po_iter->second.get_child("y").get_value<float>();
            point.z = po_iter->second.get_child("z").get_value<float>();
            param.polygon_vertices.push_back(point);
        }
        p_npu_server->SetFootprintParam(param);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//base
void NpuHttpI::GetBaseParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        BaseParam param = p_npu_server->GetBaseParam();
        ptree data, pt_motor_param, pt_pid_param, pt_chassis_param, pt_footprint_param, pt_vertices;
        data.add("base_id", param.base_id);
        data.add("enb_slave_mode", param.enb_slave_mode);
        pt_motor_param.add("motor_num", param.motor_param.motor_num);
        pt_motor_param.add("motor_dir_str", param.motor_param.motor_dir_str);
        pt_motor_param.add("motor_max_spd_rpm", param.motor_param.motor_max_spd_rpm);
        pt_motor_param.add("motor_rdc_ratio", param.motor_param.motor_rdc_ratio);
        pt_motor_param.add("motor_enc_res_ppr", param.motor_param.motor_enc_res_ppr);
        pt_motor_param.add("motor_pwm_frq_hz", param.motor_param.motor_pwm_frq_hz);
        pt_motor_param.add("motor_brk_type", param.motor_param.motor_brk_type);
        pt_motor_param.add("brk_vol", param.motor_param.brk_vol);
        pt_motor_param.add("enb_vol", param.motor_param.enb_vol);
        pt_motor_param.add("dir_vol", param.motor_param.dir_vol);
        pt_motor_param.add("pwm_vol", param.motor_param.pwm_vol);
        pt_motor_param.add("enb_auxdir_mode", param.motor_param.enb_auxdir_mode);
        pt_motor_param.add("enb_throttle_mode", param.motor_param.enb_throttle_mode);
        pt_motor_param.add("throttle_zero_pos_fac", param.motor_param.throttle_zero_pos_fac);
        pt_motor_param.add("throttle_zero_neg_fac", param.motor_param.throttle_zero_neg_fac);
        pt_motor_param.add("end_left_right_switch", param.motor_param.end_left_right_switch);
        data.add_child("motor_param",pt_motor_param);
        pt_pid_param.add("enb_pid", param.pid_param.enb_pid);
        pt_pid_param.add("kp_acc", param.pid_param.kp_acc);
        pt_pid_param.add("kp_dec", param.pid_param.kp_dec);
        pt_pid_param.add("ki_acc", param.pid_param.ki_acc);
        pt_pid_param.add("ki_dec", param.pid_param.ki_dec);
        pt_pid_param.add("kd_acc", param.pid_param.kd_acc);
        pt_pid_param.add("kd_dec", param.pid_param.kd_dec);
        data.add_child("pid_param",pt_pid_param);
        pt_chassis_param.add("model_type", param.chassis_param.model_type);
        pt_chassis_param.add("wheel_rdc_ratio", param.chassis_param.wheel_rdc_ratio);
        pt_chassis_param.add("wheel_radius_m", param.chassis_param.wheel_radius_m);
        pt_chassis_param.add("wheel_span_m", param.chassis_param.wheel_span_m);
        pt_chassis_param.add("max_lin_acc_time_s", param.chassis_param.max_lin_acc_time_s);
        pt_chassis_param.add("max_ang_acc_time_s", param.chassis_param.max_ang_acc_time_s);
        pt_chassis_param.add("autoset_max_lin_spd_mps", param.chassis_param.autoset_max_lin_spd_mps);
        pt_chassis_param.add("autoset_max_ang_spd_rps", param.chassis_param.autoset_max_ang_spd_rps);
        pt_chassis_param.add("carlike_steer_enc_location", param.chassis_param.carlike_steer_enc_location);
        pt_chassis_param.add("carlike_axle_dist_m", param.chassis_param.carlike_axle_dist_m);
        data.add_child("chassis_param",pt_chassis_param);
        pt_footprint_param.add("shape_type", param.footprint_param.shape_type);
        pt_footprint_param.add("rot_center_offset_m", param.footprint_param.rot_center_offset_m);
        pt_footprint_param.add("round_radius_m", param.footprint_param.round_radius_m);
        pt_footprint_param.add("rectangle_width_m", param.footprint_param.rectangle_width_m);
        pt_footprint_param.add("rectangle_length_m", param.footprint_param.rectangle_length_m);
        pt_footprint_param.add("polygon_vertex_num", param.footprint_param.polygon_vertex_num);
        for (auto& point : param.footprint_param.polygon_vertices) {
            ptree pt_point;
            pt_point.put("x", point.x);
            pt_point.put("y", point.y);
            pt_point.put("z", point.z);
            pt_vertices.push_back(std::make_pair("", pt_point));
        }
        pt_footprint_param.add_child("polygon_vertices", pt_vertices);
        data.add_child("footprint_param",pt_footprint_param);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetBaseParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data, pt_motor_param, pt_pid_param, pt_chassis_param, pt_footprint_param;
        read_json(request->content, pt_data);
        BaseParam param;
        param.base_id = pt_data.get_child("base_id").get_value<std::string>();
        param.enb_slave_mode = pt_data.get_child("enb_slave_mode").get_value<bool>();
        pt_motor_param = pt_data.get_child("motor_param");
        param.motor_param.motor_num = pt_motor_param.get_child("motor_num").get_value<Ice::Int>();
        param.motor_param.motor_dir_str = pt_motor_param.get_child("motor_dir_str").get_value<std::string>();
        param.motor_param.motor_max_spd_rpm = pt_motor_param.get_child("motor_max_spd_rpm").get_value<Ice::Int>();
        param.motor_param.motor_rdc_ratio = pt_motor_param.get_child("motor_rdc_ratio").get_value<Ice::Float>();
        param.motor_param.motor_enc_res_ppr = pt_motor_param.get_child("motor_enc_res_ppr").get_value<Ice::Int>();
        param.motor_param.motor_pwm_frq_hz = pt_motor_param.get_child("motor_pwm_frq_hz").get_value<Ice::Int>();
        param.motor_param.motor_brk_type = wizrobo_npu::BrakeType(pt_motor_param.get_child("motor_brk_type").get_value<Ice::Int>());
        param.motor_param.brk_vol = wizrobo_npu::ValidOutputLevel(pt_motor_param.get_child("brk_vol").get_value<Ice::Int>());
        param.motor_param.enb_vol = wizrobo_npu::ValidOutputLevel(pt_motor_param.get_child("enb_vol").get_value<Ice::Int>());
        param.motor_param.dir_vol = wizrobo_npu::ValidOutputLevel(pt_motor_param.get_child("dir_vol").get_value<Ice::Int>());
        param.motor_param.pwm_vol = wizrobo_npu::ValidOutputLevel(pt_motor_param.get_child("pwm_vol").get_value<Ice::Int>());
        param.motor_param.enb_auxdir_mode = pt_motor_param.get_child("enb_auxdir_mode").get_value<bool>();
        param.motor_param.enb_throttle_mode = pt_motor_param.get_child("enb_throttle_mode").get_value<bool>();
        param.motor_param.throttle_zero_pos_fac = pt_motor_param.get_child("throttle_zero_pos_fac").get_value<Ice::Float>();
        param.motor_param.throttle_zero_neg_fac = pt_motor_param.get_child("throttle_zero_neg_fac").get_value<Ice::Float>();
        param.motor_param.end_left_right_switch = pt_motor_param.get_child("end_left_right_switch").get_value<bool>();
        pt_pid_param = pt_data.get_child("pid_param");
        param.pid_param.enb_pid = pt_pid_param.get_child("enb_pid").get_value<bool>();
        param.pid_param.kp_acc = pt_pid_param.get_child("kp_acc").get_value<Ice::Float>();
        param.pid_param.kp_dec = pt_pid_param.get_child("kp_dec").get_value<Ice::Float>();
        param.pid_param.ki_acc = pt_pid_param.get_child("ki_acc").get_value<Ice::Float>();
        param.pid_param.ki_dec = pt_pid_param.get_child("ki_dec").get_value<Ice::Float>();
        param.pid_param.kd_acc = pt_pid_param.get_child("kd_acc").get_value<Ice::Float>();
        param.pid_param.kd_dec = pt_pid_param.get_child("kd_dec").get_value<Ice::Float>();
        pt_chassis_param = pt_data.get_child("chassis_param");
        param.chassis_param.model_type = wizrobo_npu::ChassisModelType(pt_chassis_param.get_child("model_type").get_value<Ice::Int>());
        param.chassis_param.wheel_rdc_ratio = pt_chassis_param.get_child("wheel_rdc_ratio").get_value<Ice::Float>();
        param.chassis_param.wheel_radius_m = pt_chassis_param.get_child("wheel_radius_m").get_value<Ice::Float>();
        param.chassis_param.wheel_span_m = pt_chassis_param.get_child("wheel_span_m").get_value<Ice::Float>();
        param.chassis_param.max_lin_acc_time_s = pt_chassis_param.get_child("max_lin_acc_time_s").get_value<Ice::Float>();
        param.chassis_param.max_ang_acc_time_s = pt_chassis_param.get_child("max_ang_acc_time_s").get_value<Ice::Float>();
        param.chassis_param.autoset_max_lin_spd_mps = pt_chassis_param.get_child("autoset_max_lin_spd_mps").get_value<Ice::Float>();
        param.chassis_param.autoset_max_ang_spd_rps = pt_chassis_param.get_child("autoset_max_ang_spd_rps").get_value<Ice::Float>();
        param.chassis_param.carlike_steer_enc_location = wizrobo_npu::SteerEncLocation(pt_chassis_param.get_child("carlike_steer_enc_location").get_value<Ice::Int>());
        param.chassis_param.carlike_axle_dist_m = pt_chassis_param.get_child("carlike_axle_dist_m").get_value<Ice::Float>();
        pt_footprint_param = pt_data.get_child("footprint_param");
        param.footprint_param.shape_type = wizrobo_npu::ShapeType(pt_footprint_param.get_child("shape_type").get_value<Ice::Int>());
        param.footprint_param.rot_center_offset_m = pt_footprint_param.get_child("rot_center_offset_m").get_value<Ice::Float>();
        param.footprint_param.round_radius_m = pt_footprint_param.get_child("round_radius_m").get_value<Ice::Float>();
        param.footprint_param.rectangle_width_m = pt_footprint_param.get_child("rectangle_width_m").get_value<Ice::Float>();
        param.footprint_param.rectangle_length_m = pt_footprint_param.get_child("rectangle_length_m").get_value<Ice::Float>();
        param.footprint_param.polygon_vertex_num = pt_footprint_param.get_child("polygon_vertex_num").get_value<Ice::Int>();
        ptree pt_vertices = pt_footprint_param.get_child("polygon_vertices");
        for (ptree::iterator po_iter = pt_vertices.begin();po_iter != pt_vertices.end();po_iter++) {
            Point3D point;
            point.x = po_iter->second.get_child("x").get_value<float>();
            point.y = po_iter->second.get_child("y").get_value<float>();
            point.z = po_iter->second.get_child("z").get_value<float>();
            param.footprint_param.polygon_vertices.push_back(point);
        }
        p_npu_server->SetBaseParam(param);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//sensor
void NpuHttpI::GetSensorParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        SensorParam param = p_npu_server->GetSensorParam();
        ptree data, pt_lidar_param_list;
        data.add("lidar_num", param.lidar_num);
        for (auto& lidar_param : param.lidar_params) {
            ptree pt_lidar_param, pt_pose;
            pt_lidar_param.put("enb_itl_filter", lidar_param.enb_itl_filter);
            pt_lidar_param.put("ethernet_ip", lidar_param.ethernet_ip);
            pt_pose.put("x",lidar_param.install_pose.x);
            pt_pose.put("y",lidar_param.install_pose.y);
            pt_pose.put("z",lidar_param.install_pose.z);
            pt_pose.put("roll",lidar_param.install_pose.roll);
            pt_pose.put("pitch",lidar_param.install_pose.pitch);
            pt_pose.put("yaw",lidar_param.install_pose.yaw);
            pt_lidar_param.add_child("install_pose", pt_pose);
            pt_lidar_param.put("itf_type", lidar_param.itf_type);
            pt_lidar_param.put("max_angle", lidar_param.max_angle);
            pt_lidar_param.put("min_angle", lidar_param.min_angle);
            pt_lidar_param.put("record_file_id", lidar_param.record_file_id);
            pt_lidar_param.put("serial_port_id", lidar_param.serial_port_id);
            pt_lidar_param.put("type", lidar_param.type);
            pt_lidar_param_list.push_back(std::make_pair("", pt_lidar_param));
        }
        data.add_child("lidar_params",pt_lidar_param_list);

        ptree pt_sonar_param_list;
        data.add("sonar_num", param.sonar_num);
        for (auto& sonar_param : param.sonar_params) {
            ptree pt_sonar_param, pt_pose;
            pt_sonar_param.put("fov_deg", sonar_param.fov_deg);
            pt_pose.put("x",sonar_param.install_pose.x);
            pt_pose.put("y",sonar_param.install_pose.y);
            pt_pose.put("z",sonar_param.install_pose.z);
            pt_pose.put("roll",sonar_param.install_pose.roll);
            pt_pose.put("pitch",sonar_param.install_pose.pitch);
            pt_pose.put("yaw",sonar_param.install_pose.yaw);
            pt_sonar_param.add_child("install_pose", pt_pose);
            pt_sonar_param.put("max_range_m", sonar_param.max_range_m);
            pt_sonar_param.put("min_range_m", sonar_param.min_range_m);
            pt_sonar_param.put("scan_freq_hz", sonar_param.scan_freq_hz);
            pt_sonar_param_list.push_back(std::make_pair("", pt_sonar_param));
        }
        data.add_child("sonar_params",pt_sonar_param_list);

        ptree pt_infrd_param_list;
        data.add("infrd_num", param.infrd_num);
        for (auto& infrd_param : param.infrd_params) {
            ptree pt_infrd_param, pt_pose;
            pt_infrd_param.put("fov_deg", infrd_param.fov_deg);
            pt_pose.put("x",infrd_param.install_pose.x);
            pt_pose.put("y",infrd_param.install_pose.y);
            pt_pose.put("z",infrd_param.install_pose.z);
            pt_pose.put("roll",infrd_param.install_pose.roll);
            pt_pose.put("pitch",infrd_param.install_pose.pitch);
            pt_pose.put("yaw",infrd_param.install_pose.yaw);
            pt_infrd_param.add_child("install_pose", pt_pose);
            pt_infrd_param.put("max_range_m", infrd_param.max_range_m);
            pt_infrd_param.put("min_range_m", infrd_param.min_range_m);
            pt_infrd_param_list.push_back(std::make_pair("", pt_infrd_param));
        }
        data.add_child("infrd_params",pt_infrd_param_list);

        ptree pt_bumper_param_list;
        data.add("bumper_num", param.bumper_num);
        for (auto& bumper_param : param.bumper_params) {
            ptree pt_bumper_param;
            pt_bumper_param.put("location", bumper_param.location);
            pt_bumper_param_list.push_back(std::make_pair("", pt_bumper_param));
        }
        data.add_child("bumper_params",pt_bumper_param_list);

        ptree pt_imu_param_list;
        data.add("imu_num", param.imu_num);
        for (auto& imu_param : param.imu_params) {
            ptree pt_imu_param, pt_pose;
            pt_pose.put("x",imu_param.install_pose.x);
            pt_pose.put("y",imu_param.install_pose.y);
            pt_pose.put("z",imu_param.install_pose.z);
            pt_pose.put("roll",imu_param.install_pose.roll);
            pt_pose.put("pitch",imu_param.install_pose.pitch);
            pt_pose.put("yaw",imu_param.install_pose.yaw);
            pt_imu_param.add_child("install_pose", pt_pose);
            pt_imu_param_list.push_back(std::make_pair("", pt_imu_param));
        }
        data.add_child("imu_params",pt_imu_param_list);

        ptree pt_gps_param_list;
        data.add("gps_num", param.gps_num);
        for (auto& gps_param : param.gps_params) {
            ptree pt_gps_param, pt_pose;
            pt_pose.put("x",gps_param.install_pose.x);
            pt_pose.put("y",gps_param.install_pose.y);
            pt_pose.put("z",gps_param.install_pose.z);
            pt_pose.put("roll",gps_param.install_pose.roll);
            pt_pose.put("pitch",gps_param.install_pose.pitch);
            pt_pose.put("yaw",gps_param.install_pose.yaw);
            pt_gps_param.add_child("install_pose", pt_pose);
            pt_gps_param_list.push_back(std::make_pair("", pt_gps_param));
        }
        data.add_child("gps_params",pt_gps_param_list);

        ptree pt_camera_param_list;
        data.add("camera_num", param.camera_num);
        for (auto& camera_param : param.camera_params) {
            ptree pt_camera_param, pt_pose;
            pt_pose.put("x",camera_param.install_pose.x);
            pt_pose.put("y",camera_param.install_pose.y);
            pt_pose.put("z",camera_param.install_pose.z);
            pt_pose.put("roll",camera_param.install_pose.roll);
            pt_pose.put("pitch",camera_param.install_pose.pitch);
            pt_pose.put("yaw",camera_param.install_pose.yaw);
            pt_camera_param.add_child("install_pose", pt_pose);
            pt_camera_param_list.push_back(std::make_pair("", pt_camera_param));
        }
        data.add_child("camera_params",pt_camera_param_list);

        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetSensorParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data, pt_lidar_param_list;
        read_json(request->content, pt_data);
        SensorParam param;
        param.lidar_num = pt_data.get_child("lidar_num").get_value<Ice::Int>();
        pt_lidar_param_list = pt_data.get_child("lidar_params");
        for (ptree::iterator pt_iter = pt_lidar_param_list.begin();pt_iter!=pt_lidar_param_list.end();pt_iter++) {
            LidarParam param_iter;
            param_iter.type = wizrobo_npu::LidarType(pt_iter->second.get_child("type").get_value<Ice::Int>());
            param_iter.itf_type = wizrobo_npu::InterfaceType(pt_iter->second.get_child("itf_type").get_value<Ice::Int>());
            param_iter.serial_port_id = pt_iter->second.get_child("serial_port_id").get_value<std::string>();
            param_iter.ethernet_ip = pt_iter->second.get_child("ethernet_ip").get_value<std::string>();
            param_iter.enb_itl_filter = pt_iter->second.get_child("enb_itl_filter").get_value<bool>();
            ptree pt_pose = pt_iter->second.get_child("install_pose");
            param_iter.install_pose.x = pt_pose.get_child("x").get_value<Ice::Float>();
            param_iter.install_pose.y = pt_pose.get_child("y").get_value<Ice::Float>();
            param_iter.install_pose.z = pt_pose.get_child("z").get_value<Ice::Float>();
            param_iter.install_pose.roll = pt_pose.get_child("roll").get_value<Ice::Float>();
            param_iter.install_pose.pitch = pt_pose.get_child("pitch").get_value<Ice::Float>();
            param_iter.install_pose.yaw = pt_pose.get_child("yaw").get_value<Ice::Float>();
            param_iter.record_file_id = pt_iter->second.get_child("record_file_id").get_value<std::string>();
            param_iter.max_angle = pt_iter->second.get_child("max_angle").get_value<Ice::Double>();
            param_iter.min_angle = pt_iter->second.get_child("min_angle").get_value<Ice::Double>();
            param.lidar_params.push_back(param_iter);
        }

        param.sonar_num = pt_data.get_child("sonar_num").get_value<Ice::Int>();
        ptree pt_sonar_param_list = pt_data.get_child("sonar_params");
        for (ptree::iterator pt_iter = pt_sonar_param_list.begin();pt_iter!=pt_sonar_param_list.end();pt_iter++) {
            SonarParam param_iter;
            param_iter.min_range_m = pt_iter->second.get_child("min_range_m").get_value<Ice::Float>();
            param_iter.max_range_m = pt_iter->second.get_child("max_range_m").get_value<Ice::Float>();
            param_iter.scan_freq_hz = pt_iter->second.get_child("scan_freq_hz").get_value<Ice::Float>();
            param_iter.fov_deg = pt_iter->second.get_child("fov_deg").get_value<Ice::Float>();
            ptree pt_pose = pt_iter->second.get_child("install_pose");
            param_iter.install_pose.x = pt_pose.get_child("x").get_value<Ice::Float>();
            param_iter.install_pose.y = pt_pose.get_child("y").get_value<Ice::Float>();
            param_iter.install_pose.z = pt_pose.get_child("z").get_value<Ice::Float>();
            param_iter.install_pose.roll = pt_pose.get_child("roll").get_value<Ice::Float>();
            param_iter.install_pose.pitch = pt_pose.get_child("pitch").get_value<Ice::Float>();
            param_iter.install_pose.yaw = pt_pose.get_child("yaw").get_value<Ice::Float>();
            param.sonar_params.push_back(param_iter);
        }

        param.infrd_num = pt_data.get_child("infrd_num").get_value<Ice::Int>();
        ptree pt_infrd_param_list = pt_data.get_child("infrd_params");
        for (ptree::iterator pt_iter = pt_infrd_param_list.begin();pt_iter!=pt_infrd_param_list.end();pt_iter++) {
            InfrdParam param_iter;
            param_iter.min_range_m = pt_iter->second.get_child("min_range_m").get_value<Ice::Float>();
            param_iter.max_range_m = pt_iter->second.get_child("max_range_m").get_value<Ice::Float>();
            param_iter.fov_deg = pt_iter->second.get_child("scan_freq_hz").get_value<Ice::Float>();
            ptree pt_pose = pt_iter->second.get_child("install_pose");
            param_iter.install_pose.x = pt_pose.get_child("x").get_value<Ice::Float>();
            param_iter.install_pose.y = pt_pose.get_child("y").get_value<Ice::Float>();
            param_iter.install_pose.z = pt_pose.get_child("z").get_value<Ice::Float>();
            param_iter.install_pose.roll = pt_pose.get_child("roll").get_value<Ice::Float>();
            param_iter.install_pose.pitch = pt_pose.get_child("pitch").get_value<Ice::Float>();
            param_iter.install_pose.yaw = pt_pose.get_child("yaw").get_value<Ice::Float>();
            param.infrd_params.push_back(param_iter);
        }

        param.bumper_num = pt_data.get_child("bumper_num").get_value<Ice::Int>();
        ptree  pt_bumper_param_list = pt_data.get_child("bumper_params");
        for (ptree::iterator pt_iter = pt_bumper_param_list.begin();pt_iter!=pt_bumper_param_list.end();pt_iter++) {
            BumperParam param_iter;
            param_iter.location = wizrobo_npu::BumperLocation(pt_iter->second.get_child("location").get_value<Ice::Int>());
            param.bumper_params.push_back(param_iter);
        }

        param.imu_num = pt_data.get_child("imu_num").get_value<Ice::Int>();
        ptree pt_imu_param_list = pt_data.get_child("imu_params");
        for (ptree::iterator pt_iter = pt_imu_param_list.begin();pt_iter!=pt_imu_param_list.end();pt_iter++) {
            ImuParam param_iter;
            ptree pt_pose = pt_iter->second.get_child("install_pose");
            param_iter.install_pose.x = pt_pose.get_child("x").get_value<Ice::Float>();
            param_iter.install_pose.y = pt_pose.get_child("y").get_value<Ice::Float>();
            param_iter.install_pose.z = pt_pose.get_child("z").get_value<Ice::Float>();
            param_iter.install_pose.roll = pt_pose.get_child("roll").get_value<Ice::Float>();
            param_iter.install_pose.pitch = pt_pose.get_child("pitch").get_value<Ice::Float>();
            param_iter.install_pose.yaw = pt_pose.get_child("yaw").get_value<Ice::Float>();
            param.imu_params.push_back(param_iter);
        }

        param.gps_num = pt_data.get_child("gps_num").get_value<Ice::Int>();
        ptree pt_gps_param_list = pt_data.get_child("gps_params");
        for (ptree::iterator pt_iter = pt_gps_param_list.begin();pt_iter!=pt_gps_param_list.end();pt_iter++) {
            GpsParam param_iter;
            ptree pt_pose = pt_iter->second.get_child("install_pose");
            param_iter.install_pose.x = pt_pose.get_child("x").get_value<Ice::Float>();
            param_iter.install_pose.y = pt_pose.get_child("y").get_value<Ice::Float>();
            param_iter.install_pose.z = pt_pose.get_child("z").get_value<Ice::Float>();
            param_iter.install_pose.roll = pt_pose.get_child("roll").get_value<Ice::Float>();
            param_iter.install_pose.pitch = pt_pose.get_child("pitch").get_value<Ice::Float>();
            param_iter.install_pose.yaw = pt_pose.get_child("yaw").get_value<Ice::Float>();
            param.gps_params.push_back(param_iter);
        }

        param.camera_num = pt_data.get_child("camera_num").get_value<Ice::Int>();
        ptree pt_camera_param_list = pt_data.get_child("camera_params");
        for (ptree::iterator pt_iter = pt_camera_param_list.begin();pt_iter!=pt_camera_param_list.end();pt_iter++) {
            CameraParam param_iter;
            ptree pt_pose = pt_iter->second.get_child("install_pose");
            param_iter.install_pose.x = pt_pose.get_child("x").get_value<Ice::Float>();
            param_iter.install_pose.y = pt_pose.get_child("y").get_value<Ice::Float>();
            param_iter.install_pose.z = pt_pose.get_child("z").get_value<Ice::Float>();
            param_iter.install_pose.roll = pt_pose.get_child("roll").get_value<Ice::Float>();
            param_iter.install_pose.pitch = pt_pose.get_child("pitch").get_value<Ice::Float>();
            param_iter.install_pose.yaw = pt_pose.get_child("yaw").get_value<Ice::Float>();
            param.camera_params.push_back(param_iter);
        }
        p_npu_server->SetSensorParam(param);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//teleop
void NpuHttpI::GetTeleopParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        TeleopParam param = p_npu_server->GetTeleopParam();
        ptree data;
        data.add("lin_spd_lmt_ratio", param.lin_spd_lmt_ratio);
        data.add("ang_spd_lmt_ratio", param.ang_spd_lmt_ratio);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::SetTeleopParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        TeleopParam param;
        param.lin_spd_lmt_ratio = pt_data.get_child("lin_spd_lmt_ratio").get_value<Ice::Float>();
        param.ang_spd_lmt_ratio = pt_data.get_child("ang_spd_lmt_ratio").get_value<Ice::Float>();
        p_npu_server->SetTeleopParam(param);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetParamByPtree(ResponsePtr response, RequestPtr request)
{
    LM_DEBUG("");
    try {
        ptree pt_data;
        p_npu_server->GetParamByPtree(pt_data);
        GetTypicalResponse(response, response_success, pt_data);
    } catch(exception& e) {
        LM_ERROR("%s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }
    LM_DEBUG("<");
    return;
}

void NpuHttpI::SetParamByPtree(ResponsePtr response, RequestPtr request)
{
    LM_DEBUG("");
    try {
        ptree pt_data;
        read_json(request->content, pt_data);
        p_npu_server->SetParamByPtree(pt_data);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        LM_ERROR("%s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }
    LM_DEBUG("<");
    return;
}

//navi
void NpuHttpI::GetNaviParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        NaviParam param = p_npu_server->GetNaviParam();
        ptree data, pt_p2p_planner_param, pt_pf_planner_param, pt_ccp_planner_param;
        data.add("lin_spd_lmt_ratio", param.lin_spd_lmt_ratio);
        data.add("ang_spd_lmt_ratio", param.ang_spd_lmt_ratio);
        data.add("x_err_tolr_m", param.x_err_tolr_m);
        data.add("y_err_tolr_m", param.y_err_tolr_m);
        data.add("yaw_err_tolr_rad", param.yaw_err_tolr_rad);
        pt_p2p_planner_param.add("acc_lim_x_mps2", param.p2p_planner_param.acc_lim_x_mps2);
        pt_p2p_planner_param.add("acc_lim_theta_rps2", param.p2p_planner_param.acc_lim_theta_rps2);
        pt_p2p_planner_param.add("max_vel_x_mps", param.p2p_planner_param.max_vel_x_mps);
        pt_p2p_planner_param.add("min_vel_x_mps", param.p2p_planner_param.min_vel_x_mps);
        pt_p2p_planner_param.add("max_rot_vel_rps", param.p2p_planner_param.max_rot_vel_rps);
        pt_p2p_planner_param.add("min_rot_vel_rps", param.p2p_planner_param.min_rot_vel_rps);
        pt_p2p_planner_param.add("plan_strictness", param.p2p_planner_param.plan_strictness);
        pt_p2p_planner_param.add("path_approach_avenue_factor", param.p2p_planner_param.path_approach_avenue_factor);
        pt_p2p_planner_param.add("safety_inflation_factor", param.p2p_planner_param.safety_inflation_factor);
        data.add_child("p2p_planner_param",pt_p2p_planner_param);
        pt_pf_planner_param.add("waypoint_mode", param.pf_planner_param.waypoint_mode);
        pt_pf_planner_param.add("look_ahead_dist_m", param.pf_planner_param.look_ahead_dist_m);
        pt_pf_planner_param.add("position_control_tolerance_m", param.pf_planner_param.position_control_tolerance_m);
        pt_pf_planner_param.add("heading_control_tolerance_rad", param.pf_planner_param.heading_control_tolerance_rad);
        pt_pf_planner_param.add("max_lin_vel_mps", param.pf_planner_param.max_lin_vel_mps);
        pt_pf_planner_param.add("max_ang_vel_rps", param.pf_planner_param.max_ang_vel_rps);
        pt_pf_planner_param.add("heading_kp", param.pf_planner_param.heading_kp);

        pt_pf_planner_param.add("forward_max_lin_vel", param.pf_planner_param.forward_max_lin_vel);
        pt_pf_planner_param.add("forward_min_lin_vel", param.pf_planner_param.forward_min_lin_vel);
        pt_pf_planner_param.add("forward_max_ang_vel", param.pf_planner_param.forward_max_ang_vel);
        pt_pf_planner_param.add("spin_max_ang_vel", param.pf_planner_param.spin_max_ang_vel);
        pt_pf_planner_param.add("spin_min_ang_vel", param.pf_planner_param.spin_min_ang_vel);
        pt_pf_planner_param.add("spin_deflection_kp", param.pf_planner_param.spin_deflection_kp);
        pt_pf_planner_param.add("parking_distence_k", param.pf_planner_param.parking_distance_k);
        pt_pf_planner_param.add("parking_offset", param.pf_planner_param.parking_offset);
        pt_pf_planner_param.add("decelerate_distence_k", param.pf_planner_param.decelerate_distence_k);
        pt_pf_planner_param.add("decelerate_offset", param.pf_planner_param.decelerate_offset);
        pt_pf_planner_param.add("ang_limit_kp", param.pf_planner_param.ang_limit_kp);
        pt_pf_planner_param.add("forward_deflection_kp", param.pf_planner_param.forward_deflection_kp);
        pt_pf_planner_param.add("forward_deflection_ki", param.pf_planner_param.forward_deflection_ki);
        pt_pf_planner_param.add("forward_deflection_kd", param.pf_planner_param.forward_deflection_kd);

        data.add_child("pf_planner_param",pt_pf_planner_param);
        pt_ccp_planner_param.add("coverage_spacing_m", param.ccp_planner_param.coverage_spacing_m);
        data.add_child("ccp_planner_param",pt_ccp_planner_param);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetNaviParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data, pt_p2p_planner_param, pt_pf_planner_param, pt_ccp_planner_param;
        read_json(request->content, pt_data);
        NaviParam param;
        param.lin_spd_lmt_ratio = pt_data.get_child("lin_spd_lmt_ratio").get_value<Ice::Float>();
        param.ang_spd_lmt_ratio = pt_data.get_child("ang_spd_lmt_ratio").get_value<Ice::Float>();
        param.x_err_tolr_m = pt_data.get_child("x_err_tolr_m").get_value<Ice::Float>();
        param.y_err_tolr_m = pt_data.get_child("y_err_tolr_m").get_value<Ice::Float>();
        param.yaw_err_tolr_rad = pt_data.get_child("yaw_err_tolr_rad").get_value<Ice::Float>();
        pt_p2p_planner_param = pt_data.get_child("p2p_planner_param");
        param.p2p_planner_param.acc_lim_x_mps2 = pt_p2p_planner_param.get_child("acc_lim_x_mps2").get_value<Ice::Float>();
        param.p2p_planner_param.acc_lim_theta_rps2 = pt_p2p_planner_param.get_child("acc_lim_theta_rps2").get_value<Ice::Float>();
        param.p2p_planner_param.max_vel_x_mps = pt_p2p_planner_param.get_child("max_vel_x_mps").get_value<Ice::Float>();
        param.p2p_planner_param.min_vel_x_mps = pt_p2p_planner_param.get_child("min_vel_x_mps").get_value<Ice::Float>();
        param.p2p_planner_param.max_rot_vel_rps = pt_p2p_planner_param.get_child("max_rot_vel_rps").get_value<Ice::Float>();
        param.p2p_planner_param.min_rot_vel_rps = pt_p2p_planner_param.get_child("min_rot_vel_rps").get_value<Ice::Float>();
        param.p2p_planner_param.plan_strictness = pt_p2p_planner_param.get_child("plan_strictness").get_value<Ice::Float>();
        param.p2p_planner_param.path_approach_avenue_factor = pt_p2p_planner_param.get_child("path_approach_avenue_factor").get_value<Ice::Float>();
        param.p2p_planner_param.safety_inflation_factor = pt_p2p_planner_param.get_child("safety_inflation_factor").get_value<Ice::Float>();
        pt_pf_planner_param = pt_data.get_child("pf_planner_param");
        param.pf_planner_param.waypoint_mode = pt_pf_planner_param.get_child("waypoint_mode").get_value<bool>();
        param.pf_planner_param.look_ahead_dist_m = pt_pf_planner_param.get_child("look_ahead_dist_m").get_value<Ice::Float>();
        param.pf_planner_param.position_control_tolerance_m = pt_pf_planner_param.get_child("position_control_tolerance_m").get_value<Ice::Float>();
        param.pf_planner_param.heading_control_tolerance_rad = pt_pf_planner_param.get_child("heading_control_tolerance_rad").get_value<Ice::Float>();
        param.pf_planner_param.max_lin_vel_mps = pt_pf_planner_param.get_child("max_lin_vel_mps").get_value<Ice::Float>();
        param.pf_planner_param.max_ang_vel_rps = pt_pf_planner_param.get_child("max_ang_vel_rps").get_value<Ice::Float>();
        param.pf_planner_param.heading_kp = pt_pf_planner_param.get_child("heading_kp").get_value<Ice::Float>();

        param.pf_planner_param.forward_max_lin_vel = pt_pf_planner_param.get_child("forward_max_lin_vel").get_value<Ice::Float>();
        param.pf_planner_param.forward_min_lin_vel = pt_pf_planner_param.get_child("forward_min_lin_vel").get_value<Ice::Float>();
        param.pf_planner_param.forward_max_ang_vel = pt_pf_planner_param.get_child("forward_max_ang_vel").get_value<Ice::Float>();
        param.pf_planner_param.spin_max_ang_vel = pt_pf_planner_param.get_child("spin_max_ang_vel").get_value<Ice::Float>();
        param.pf_planner_param.spin_min_ang_vel = pt_pf_planner_param.get_child("spin_min_ang_vel").get_value<Ice::Float>();
        param.pf_planner_param.spin_deflection_kp = pt_pf_planner_param.get_child("spin_deflection_kp").get_value<Ice::Float>();
        param.pf_planner_param.parking_distance_k = pt_pf_planner_param.get_child("parking_distence_k").get_value<Ice::Float>();
        param.pf_planner_param.parking_offset = pt_pf_planner_param.get_child("parking_offset").get_value<Ice::Float>();
        param.pf_planner_param.decelerate_distence_k = pt_pf_planner_param.get_child("decelerate_distence_k").get_value<Ice::Float>();
        param.pf_planner_param.decelerate_offset = pt_pf_planner_param.get_child("decelerate_offset").get_value<Ice::Float>();
        param.pf_planner_param.ang_limit_kp = pt_pf_planner_param.get_child("ang_limit_kp").get_value<Ice::Float>();
        param.pf_planner_param.forward_deflection_kp = pt_pf_planner_param.get_child("forward_deflection_kp").get_value<Ice::Float>();
        param.pf_planner_param.forward_deflection_ki = pt_pf_planner_param.get_child("forward_deflection_ki").get_value<Ice::Float>();
        param.pf_planner_param.forward_deflection_kd = pt_pf_planner_param.get_child("forward_deflection_kd").get_value<Ice::Float>();

        pt_ccp_planner_param = pt_data.get_child("ccp_planner_param");
        param.ccp_planner_param.coverage_spacing_m = pt_ccp_planner_param.get_child("coverage_spacing_m").get_value<Ice::Float>();
        p_npu_server->SetNaviParam(param);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//slam
void NpuHttpI::GetSlamParam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        SlamParam param = p_npu_server->GetSlamParam();
        ptree data, pt_optimizer_param;
        data.add("map_res_mpp", param.map_res_mpp);
        data.add("map_size_m", param.map_size_m);
        data.add("update_dist_thrs_m", param.update_dist_thrs_m);
        data.add("update_ori_thrs_rad", param.update_ori_thrs_rad);
        data.add("max_lin_spd_mps", param.max_lin_spd_mps);
        data.add("max_ang_spd_rps", param.max_ang_spd_rps);
        pt_optimizer_param.add("enb_map_opt", param.optimizer_param.enb_map_opt);
        pt_optimizer_param.add("map_res_mpp", param.optimizer_param.map_res_mpp);
        pt_optimizer_param.add("update_dist_thrs_m", param.optimizer_param.update_dist_thrs_m);
        pt_optimizer_param.add("update_ori_thrs_rad", param.optimizer_param.update_ori_thrs_rad);
        pt_optimizer_param.add("enb_trace_clearing", param.optimizer_param.enb_trace_clearing);
        pt_optimizer_param.add("enb_filtering", param.optimizer_param.enb_filtering);
        pt_optimizer_param.add("filter_size", param.optimizer_param.filter_size);
        data.add_child("optimizer_param",pt_optimizer_param);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetSlamParam(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data, pt_optimizer_param;
        read_json(request->content, pt_data);
        SlamParam param;
        param.map_res_mpp = pt_data.get_child("map_res_mpp").get_value<Ice::Float>();
        param.map_size_m = pt_data.get_child("map_size_m").get_value<Ice::Int>();
        param.update_dist_thrs_m = pt_data.get_child("update_dist_thrs_m").get_value<Ice::Float>();
        param.update_ori_thrs_rad = pt_data.get_child("update_ori_thrs_rad").get_value<Ice::Float>();
        param.max_lin_spd_mps = pt_data.get_child("max_lin_spd_mps").get_value<Ice::Float>();
        param.max_ang_spd_rps = pt_data.get_child("max_ang_spd_rps").get_value<Ice::Float>();
        pt_optimizer_param = pt_data.get_child("optimizer_param");
        param.optimizer_param.enb_map_opt = pt_optimizer_param.get_child("enb_map_opt").get_value<bool>();
        param.optimizer_param.map_res_mpp = pt_optimizer_param.get_child("map_res_mpp").get_value<Ice::Float>();
        param.optimizer_param.update_dist_thrs_m = pt_optimizer_param.get_child("update_dist_thrs_m").get_value<Ice::Float>();
        param.optimizer_param.update_ori_thrs_rad = pt_optimizer_param.get_child("update_ori_thrs_rad").get_value<Ice::Float>();
        param.optimizer_param.enb_trace_clearing = pt_optimizer_param.get_child("enb_trace_clearing").get_value<bool>();
        param.optimizer_param.enb_filtering = pt_optimizer_param.get_child("enb_filtering").get_value<bool>();
        param.optimizer_param.filter_size = pt_optimizer_param.get_child("filter_size").get_value<Ice::Int>();
        p_npu_server->SetSlamParam(param);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
//// slave mode
void NpuHttpI::FeedMotorEnc(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        MotorEnc enc;
        enc.motor_num = pt_data.get_child("motor_num").get_value<Ice::Int>();
        ptree pt_ticks = pt_data.get_child("ticks");
        for (ptree::iterator pt_iter = pt_ticks.begin();pt_iter!=pt_ticks.end();pt_iter++) {
            Ice::Float param_iter;
            enc.ticks.push_back(param_iter);
        }
        enc.steer_angle_deg = pt_data.get_child("steer_angle_deg").get_value<Ice::Float>();
        p_npu_server->FeedMotorEnc(enc);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::FeedActMotorSpd(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        MotorSpd spd;
        spd.motor_num = pt_data.get_child("motor_num").get_value<Ice::Int>();
        ptree pt_rpms = pt_data.get_child("rpms");
        for (ptree::iterator pt_iter = pt_rpms.begin();pt_iter!=pt_rpms.end();pt_iter++) {
            Ice::Float param_iter;
            spd.rpms.push_back(param_iter);
        }
        spd.steer_angle_deg = pt_data.get_child("steer_angle_deg").get_value<Ice::Float>();
        p_npu_server->FeedActMotorSpd(spd);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//// motor data

// enc
void NpuHttpI::GetMotorEnc(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_ticks;
        MotorEnc Enc = p_npu_server->GetMotorEnc();
        double secs = ros::Time::now().toSec();
        data.add("time", secs);
        data.add("motor_num", Enc.motor_num);
        for (auto& tick : Enc.ticks) {
            ptree pt_tick;
            pt_tick.put_value(tick);
            pt_ticks.push_back(std::make_pair("", pt_tick));
        }
        data.add_child("ticks", pt_ticks);
        data.add("steer_angle_deg", Enc.steer_angle_deg);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::ClearMotorEnc(ResponsePtr response, RequestPtr request)
{
    try{
        p_npu_server->ClearMotorEnc();
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// spd
void NpuHttpI::GetCmdMotorSpd(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_rpms;
        MotorSpd rtn = p_npu_server->GetCmdMotorSpd();
        data.add("motor_num", rtn.motor_num);
        for (auto& rpm : rtn.rpms) {
            ptree pt_rpm;
            pt_rpm.put_value(rpm);
            pt_rpms.push_back(std::make_pair("", pt_rpm));
        }
        data.add_child("rpms", pt_rpms);
        data.add("steer_angle_deg", rtn.steer_angle_deg);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::GetActMotorSpd(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_rpms;
        MotorSpd rtn = p_npu_server->GetActMotorSpd();
        data.add("motor_num", rtn.motor_num);
        for (auto& rpm : rtn.rpms) {
            ptree pt_rpm;
            pt_rpm.put_value(rpm);
            pt_rpms.push_back(std::make_pair("", pt_rpm));
        }
        data.add_child("rpms", pt_rpms);
        data.add("steer_angle_deg", rtn.steer_angle_deg);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//// sensor data

// lidar
void NpuHttpI::GetLidarScan(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_points, pt_intensities;
        LidarScan rtn = p_npu_server->GetLidarScan();
        for (auto& point : rtn.points) {
            ptree pt_point;
            pt_point.put("x", point.x);
            pt_point.put("y", point.y);
            pt_point.put("z", point.z);
            pt_points.push_back(std::make_pair("", pt_point));
        }
        data.add_child("points", pt_points);
        for (auto& intensitie : rtn.intensities) {
            ptree pt_intensitie;
            pt_intensitie.put_value(intensitie);
            pt_intensities.push_back(std::make_pair("", pt_intensitie));
        }
        data.add_child("intensities", pt_intensities);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetImgLidarScan(ResponsePtr response, RequestPtr request)
{
    const int normalization_cnt = 50;
    try {
        ptree data, grid_phits, grid_point;
        ImgLidarScan img_lidar_scan = p_npu_server->GetImgLidarScan();
        int step = img_lidar_scan.points.size() / normalization_cnt;
        for (int i=0;i<img_lidar_scan.points.size();i+=step) {
            grid_point.put("x", img_lidar_scan.points[i].u);
            grid_point.put("y", img_lidar_scan.points[i].v);
            grid_point.put("z", 0);
            grid_phits.push_back(std::make_pair("", grid_point));
        }
        data.add_child("gridPhits" , grid_phits);
        std::stringstream ss;
        write_json(ss, data);
        response->write(ss.str());
        //GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//imu
void NpuHttpI::GetImuData(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data;
        ImuData rtn = p_npu_server->GetImuData();
        data.add("roll_deg", rtn.roll_deg);
        data.add("pitch_deg", rtn.pitch_deg);
        data.add("yaw_deg", rtn.yaw_deg);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//sonar
void NpuHttpI::GetSonarScan(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_points;
        SonarScan rtn = p_npu_server->GetSonarScan();
        for (auto& point : rtn.points) {
            ptree pt_point;
            pt_point.put("x", point.x);
            pt_point.put("y", point.y);
            pt_point.put("z", point.z);
            pt_points.push_back(std::make_pair("", pt_point));
        }
        data.add_child("points", pt_points);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
//void NpuHttpI::GetImgSonarScan(ResponsePtr response, RequestPtr request)
//{
//    try{
//        ptree data, pt_points;
//        ImgSonarScan rtn = p_npu_server->GetImgSonarScan();
//        for (auto& point : rtn.points) {
//            ptree pt_point;
//            pt_point.put("u", point.u);
//            pt_point.put("v", point.v);
//            pt_points.push_back(std::make_pair("", pt_point));
//        }
//        data.add_child("points", pt_points);
//        GetTypicalResponse(response, response_success, data);
//    } catch(exception& e) {
//        GetTypicalResponse(response, response_error, e.what());
//    }
//    return;
//}

//infrd
void NpuHttpI::GetInfrdScan(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_points;
        InfrdScan rtn = p_npu_server->GetInfrdScan();
        for (auto& point : rtn.points) {
            ptree pt_point;
            pt_point.put("x", point.x);
            pt_point.put("y", point.y);
            pt_point.put("z", point.z);
            pt_points.push_back(std::make_pair("", pt_point));
        }
        data.add_child("points", pt_points);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
//void NpuHttpI::GetImgInfrdScan(ResponsePtr response, RequestPtr request)
//{
//    try{
//        ptree data, pt_points;
//        ImgInfrdScan rtn = p_npu_server->GetImgInfrdScan();
//        for (auto& point : rtn.points) {
//            ptree pt_point;
//            pt_point.put("u", point.u);
//            pt_point.put("v", point.v);
//            pt_points.push_back(std::make_pair("", pt_point));
//        }
//        data.add_child("points", pt_points);
//        GetTypicalResponse(response, response_success, data);
//    } catch(exception& e) {
//        GetTypicalResponse(response, response_error, e.what());
//    }
//    return;
//}

//bumper
void NpuHttpI::GetBumperArray(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_states;
        BumperArray rtn = p_npu_server->GetBumperArray();
        for (auto& state : rtn.states) {
            ptree pt_state;
            pt_state.put_value(state.state);
            pt_state.put_value(state.location);
            pt_states.push_back(std::make_pair("", pt_state));
        }
        data.add_child("states", pt_states);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//battery
void NpuHttpI::GetBatteryStatus(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data;
        BatteryStatus rtn = p_npu_server->GetBatteryStatus();
        data.add("current_a", rtn.current_a);
        data.add("voltage_v", rtn.voltage_v);
        data.add("temperature_deg", rtn.temperature_deg);
        data.add("capacity_level", rtn.capacity_level);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//// manul control
void NpuHttpI::SetManualCmd(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        ManualCmdType cmd = ManualCmdType(stringToNum<int>(param_map["cmd"]));
        printf("TYPE***%d***",cmd);
        p_npu_server->SetManualCmd(cmd);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetManualVel(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        float lin_scale, ang_scale;
        lin_scale = stringToNum<float>(param_map["lin_scale"]);
        ang_scale = stringToNum<float>(param_map["ang_scale"]);
        printf("LIN***%f***",lin_scale);
        printf("ANG***%f***",ang_scale);
        p_npu_server->SetManualVel(lin_scale, ang_scale);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//// map management

// map
void NpuHttpI::GetMapInfos(ResponsePtr response, RequestPtr request)
{
    try {
        ptree data, pt_map_info_list;
        MapInfoList map_infos = p_npu_server->GetMapInfos();
        for (auto& map_info : map_infos) {
            ptree pt_map_info;
            pt_map_info.add("id", map_info.id);
            pt_map_info.add("creation_time", map_info.creation_time);
            pt_map_info.add("resolution", map_info.resolution);
            pt_map_info.add("dimension_x", map_info.dimension.x);
            pt_map_info.add("dimension_y", map_info.dimension.y);
            pt_map_info.add("origin_x", map_info.offset.x);
            pt_map_info.add("origin_y", map_info.offset.y);
            pt_map_info_list.push_back(std::make_pair("", pt_map_info));
        }
        data.add_child("mapInfoList", pt_map_info_list);
        //std::stringstream ss;
        //write_json(ss, data);
        //response->write(ss.str());
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::SetMapInfos(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        MapInfoList map_info_list;
        for (ptree::iterator pt_iter = pt_data.begin();pt_iter!=pt_data.end();pt_iter++) {
            MapInfo mapInfo;
            mapInfo.index = pt_iter->second.get_child("index").get_value<Ice::Int>();
            mapInfo.id = pt_iter->second.get_child("id").get_value<std::string>();
            mapInfo.creation_time = pt_iter->second.get_child("creation_time").get_value<std::string>();
            mapInfo.resolution = pt_iter->second.get_child("resolution").get_value<Ice::Float>();
            ptree pt_dimension = pt_iter->second.get_child("dimension");
            mapInfo.dimension.x = pt_dimension.get_child("x").get_value<Ice::Double>();
            mapInfo.dimension.y = pt_dimension.get_child("y").get_value<Ice::Double>();
            mapInfo.dimension.z = pt_dimension.get_child("z").get_value<Ice::Double>();
            ptree pt_offset = pt_iter->second.get_child("offset");
            mapInfo.offset.x = pt_offset.get_child("x").get_value<Ice::Double>();
            mapInfo.offset.y = pt_offset.get_child("y").get_value<Ice::Double>();
            mapInfo.offset.z = pt_offset.get_child("z").get_value<Ice::Double>();
            ptree pt_thumbnail = pt_iter->second.get_child("thumbnail");
            mapInfo.thumbnail.width = pt_thumbnail.get_child("width").get_value<Ice::Int>();
            mapInfo.thumbnail.height = pt_thumbnail.get_child("height").get_value<Ice::Int>();
            mapInfo.thumbnail.ratio = pt_thumbnail.get_child("ratio").get_value<Ice::Float>();
//            ptree pt_data = pt_thumbnail->second.get_child("data");
//            for (ptree::iterator pt_iter = pt_data.begin();pt_iter != pt_data.end();pt_iter++) {
//                Ice::Byte byte;
//                byte = pt_iter->second.get_child("x").get_value<float>();
//                param.polygon_vertices.push_back(point);
//            }
            mapInfo.station_num = pt_iter->second.get_child("station_num").get_value<Ice::Int>();
            mapInfo.path_num = pt_iter->second.get_child("path_num").get_value<Ice::Int>();
            mapInfo.task_num = pt_iter->second.get_child("task_num").get_value<Ice::Int>();
            map_info_list.push_back(mapInfo);
        }
        p_npu_server->SetMapInfos(map_info_list);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::SelectMap(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    p_npu_server->SelectMap(param_map["mapName"]);
    GetTypicalResponse(response, response_success, "");
}


// station

void NpuHttpI::GetStations(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    NHP_DEBUG("map name: %s", param_map["mapName"].c_str());
    try {
        StationList station_list = p_npu_server->GetStations(param_map["mapName"]);
        ptree pt_station_list;
        for (auto& station : station_list) {
            ptree pt_station;
            NHP_DEBUG("station.info.id: %s", station.info.id.c_str());
            pt_station.add("id", station.info.id);
            pt_station.add("type", station.info.type);
            pt_station.add("x", station.pose.x);
            pt_station.add("y", station.pose.y);
            pt_station.add("yaw", station.pose.yaw);
            pt_station_list.push_back(std::make_pair("", pt_station));
        }
        GetTypicalResponse(response, response_success, pt_station_list);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::SetStations(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        ptree pt_data;
        read_json(request->content, pt_data);
        string& mapName = param_map["mapName"];
        StationList station_list;
        for (ptree::iterator s_iter = pt_data.begin();s_iter!=pt_data.end();s_iter++) {
            Station station;
            station.info.id = s_iter->second.get_child("id").get_value<std::string>();
            station.info.map_id = mapName;
            station.info.artag_id = 0;
            station.info.type = USER_DEFINED;
            station.pose.x = s_iter->second.get_child("x").get_value<float>();
            station.pose.y = s_iter->second.get_child("y").get_value<float>();
            station.pose.z = 0;
            station.pose.roll = 0;
            station.pose.pitch = 0;
            station.pose.yaw = s_iter->second.get_child("yaw").get_value<float>();
            station_list.push_back(station);
        }
        p_npu_server->SetStations(mapName, station_list);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        NHP_ERROR("e.what() ==> %s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// task
void NpuHttpI::GetTaskList(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        TaskList tasklist = p_npu_server->GetTaskList(param_map["mapName"]);
        ptree data, pt_tasklist;
        for (auto& task : tasklist) {
            ptree pt_task,pt_info,pt_action_list;
            pt_info.add("map_id", task.info.map_id);
            pt_info.add("task_id", task.info.task_id);
            for (auto& action : task.info.action_list) {
                ptree pt_action;
                pt_action.add("action_name", action.action_name);
                pt_action.add("action_args", action.action_args);
                pt_action.add("duration", action.duration);
                pt_action_list.push_back(std::make_pair("", pt_action));
            }
            pt_info.add_child("action_list",pt_action_list);
            pt_task.add_child("info",pt_info);
            pt_task.add("enb_taskloop", task.enb_taskloop);
            pt_task.add("task_loop_times", task.task_loop_times);
            pt_tasklist.push_back(std::make_pair("", pt_task));
        }
        data.add_child("tasklist", pt_tasklist);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::ExecuteTask(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        TaskList tasklist;
        ptree pt_task_list = pt_data.get_child("tasklist");
        for (ptree::iterator pt_iter = pt_task_list.begin();pt_iter!=pt_task_list.end();pt_iter++) {
            Task task;
            task.enb_taskloop = pt_iter->second.get_child("enb_taskloop").get_value<bool>();
            task.task_loop_times = pt_iter->second.get_child("task_loop_times").get_value<Ice::Int>();
            ptree pt_info = pt_iter->second.get_child("info");
            task.info.map_id = pt_info.get_child("map_id").get_value<std::string>();
            task.info.task_id = pt_info.get_child("task_id").get_value<std::string>();
            ptree pt_action_list = pt_info.get_child("action_list");
            for (ptree::iterator pt_iter1 = pt_action_list.begin();pt_iter1!=pt_action_list.end();pt_iter1++) {
                TaskAction action;
                action.action_args = pt_iter1->second.get_child("action_args").get_value<std::string>();
                action.duration = pt_iter1->second.get_child("duration").get_value<Ice::Int>();
                action.action_name = wizrobo_npu::actionname(pt_iter1->second.get_child("action_name").get_value<Ice::Int>());
                task.info.action_list.push_back(action);
            }
            tasklist.push_back(task);
        }
        p_npu_server->ExecuteTask(tasklist);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::SetTasks(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        TaskList tasklist;
        ptree pt_task_list = pt_data.get_child("tasklist");
        for (ptree::iterator pt_iter = pt_task_list.begin();pt_iter!=pt_task_list.end();pt_iter++) {
            Task task;
            task.enb_taskloop = pt_iter->second.get_child("enb_taskloop").get_value<bool>();
            task.task_loop_times = pt_iter->second.get_child("task_loop_times").get_value<Ice::Int>();
            ptree pt_info = pt_iter->second.get_child("info");
            task.info.map_id = pt_info.get_child("map_id").get_value<std::string>();
            task.info.task_id = pt_info.get_child("task_id").get_value<std::string>();
            ptree pt_action_list = pt_info.get_child("action_list");
            for (ptree::iterator pt_iter1 = pt_action_list.begin();pt_iter1!=pt_action_list.end();pt_iter1++) {
                TaskAction action;
                action.action_args = pt_iter1->second.get_child("action_args").get_value<std::string>();
                action.duration = pt_iter1->second.get_child("duration").get_value<Ice::Int>();
                action.action_name = wizrobo_npu::actionname(pt_iter1->second.get_child("action_name").get_value<Ice::Int>());
                task.info.action_list.push_back(action);
            }
            tasklist.push_back(task);
        }
        p_npu_server->SetTasks(param_map["mapName"], tasklist);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// path
void NpuHttpI::GetPaths(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        PathList path_list = p_npu_server->GetPaths(param_map["mapName"]);
        ptree pt_path_list;
        for (auto& path : path_list) {
            ptree pt_path, pt_poses;
            pt_path.add("id", path.info.id);
            pt_path.add("lenght", path.info.length);
            pt_path.add("pose_num", path.info.pose_num);
            for (auto& pose : path.poses) {
                ptree pt_pose;
                pt_pose.add("x", pose.x);
                pt_pose.add("y", pose.y);
                pt_pose.add("yaw", pose.yaw);
                pt_poses.push_back(std::make_pair("", pt_pose));
            }
            pt_path.add_child("poses", pt_poses);
            pt_path_list.push_back(std::make_pair("", pt_path));
        }
        GetTypicalResponse(response, response_success, pt_path_list);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::SetPaths(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        ptree pt_data;
        read_json(request->content, pt_data);
        string& mapName = param_map["mapName"];
        PathList path_list;
        for (ptree::iterator path_iter = pt_data.begin();path_iter!=pt_data.end();path_iter++) {
            Path path;
            path.info.id = path_iter->second.get_child("id").get_value<std::string>();
            path.info.map_id = mapName;
            path.info.length = 0;
            path.info.pose_num = path_iter->second.get_child("pose_num").get_value<int>();
            ptree pt_poses = path_iter->second.get_child("poses");
            for (ptree::iterator po_iter = pt_poses.begin();po_iter != pt_poses.end();po_iter++) {
                Pose3D pose;
                pose.x = po_iter->second.get_child("x").get_value<float>();
                pose.y = po_iter->second.get_child("y").get_value<float>();
                pose.z = 0;
                pose.roll = 0;
                pose.pitch = 0;
                pose.yaw = po_iter->second.get_child("yaw").get_value<float>();
                path.poses.push_back(pose);
            }
            path_list.push_back(path);
        }
        p_npu_server->SetPaths(mapName, path_list);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        NHP_ERROR("e.what() ==> %s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// virtual wall
void NpuHttpI::GetVirtualWalls(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        VirtualWallList vw_list = p_npu_server->GetVirtualWalls(param_map["mapName"]);
        ptree pt_vw_list;
        for (auto& vw : vw_list) {
            ptree pt_vm, pt_points;
            pt_vm.add("id", vw.info.id);
            pt_vm.add("lenght", vw.info.length);
            for (auto& point : vw.points) {
                ptree pt_point;
                pt_point.add("x", point.x);
                pt_point.add("y", point.y);
                pt_points.push_back(std::make_pair("", pt_point));
            }
            pt_vm.add_child("points", pt_points);
            //pt_vm.get_child();
            pt_vw_list.push_back(std::make_pair("", pt_vm));
        }
        GetTypicalResponse(response, response_success, pt_vw_list);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::SetVirtualWalls(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        ptree pt;
        read_json(request->content, pt);
        string& mapName = param_map["mapName"];
        ptree& pt_vw_data = pt;
        VirtualWallList vw_list;
        for (ptree::iterator vw_iter = pt_vw_data.begin();vw_iter!=pt_vw_data.end();vw_iter++) {
            VirtualWall vw;
            vw.info.id = vw_iter->second.get_child("id").get_value<std::string>();
            vw.info.map_id = mapName;
            vw.info.length = 0;
            vw.info.closed = false;
            ptree pt_points = vw_iter->second.get_child("points");
            for (ptree::iterator po_iter = pt_points.begin();po_iter != pt_points.end();po_iter++) {
                Point3D point;
                point.x = po_iter->second.get_child("x").get_value<float>();
                point.y = po_iter->second.get_child("y").get_value<float>();
                point.z = 0;
                vw.points.push_back(point);
            }
            vw_list.push_back(vw);
        }
        p_npu_server->SetVirtualWalls(mapName, vw_list);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        NHP_ERROR("e.what() ==> %s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}


//// common runtime data

// vel
void NpuHttpI::GetCmdVel(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data;
        Vel3D rtn = p_npu_server->GetCmdVel();
        double secs = ros::Time::now().toSec();
        data.add("time", secs);
        data.add("v_x", rtn.v_x);
        data.add("v_y", rtn.v_y);
        data.add("v_z", rtn.v_z);
        data.add("v_roll", rtn.v_roll);
        data.add("v_pitch", rtn.v_pitch);
        data.add("v_yaw", rtn.v_yaw);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::GetActVel(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data;
        Vel3D rtn = p_npu_server->GetActVel();
        double secs = ros::Time::now().toSec();
        data.add("time", secs);
        data.add("v_x", rtn.v_x);
        data.add("v_y", rtn.v_y);
        data.add("v_z", rtn.v_z);
        data.add("v_roll", rtn.v_roll);
        data.add("v_pitch", rtn.v_pitch);
        data.add("v_yaw", rtn.v_yaw);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// pose
void NpuHttpI::GetCmdPose(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data;
        Pose3D rtn = p_npu_server->GetCmdPose();
        data.add("x", rtn.x);
        data.add("y", rtn.y);
        data.add("z", rtn.z);
        data.add("roll", rtn.roll);
        data.add("pitch", rtn.pitch);
        data.add("yaw", rtn.yaw);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetActPose(ResponsePtr response, RequestPtr request)
{
    try {
        ptree data, position;
        Pose3D pose_3d = p_npu_server->GetActPose();
        double secs = ros::Time::now().toSec();
        data.add("time", secs);
        position.add("x", pose_3d.x);
        position.add("y", pose_3d.y);
        position.add("theta", pose_3d.yaw);
        data.add_child("actPose", position);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// path
void NpuHttpI::GetGlobalPath(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_info, pt_poses;
        Path rtn = p_npu_server->GetGlobalPath();
        pt_info.add("map_id", rtn.info.map_id);
        pt_info.add("id", rtn.info.id);
        pt_info.add("length", rtn.info.length);
        pt_info.add("pose_num", rtn.info.pose_num);
        data.add_child("info", pt_info);
        for (auto& pose : rtn.poses) {
            ptree pt_pose;
            pt_pose.add("x", pose.x);
            pt_pose.add("y", pose.y);
            pt_pose.add("z", pose.z);
            pt_pose.add("roll", pose.roll);
            pt_pose.add("pitch", pose.pitch);
            pt_pose.add("yaw", pose.yaw);
            pt_poses.push_back(std::make_pair("", pt_pose));
        }
        data.add_child("poses", pt_poses);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetLocalPath(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_info, pt_poses;
        Path rtn = p_npu_server->GetLocalPath();
        pt_info.add("map_id", rtn.info.map_id);
        pt_info.add("id", rtn.info.id);
        pt_info.add("length", rtn.info.length);
        pt_info.add("pose_num", rtn.info.pose_num);
        data.add_child("info", pt_info);
        for (auto& pose : rtn.poses) {
            ptree pt_pose;
            pt_pose.add("x", pose.x);
            pt_pose.add("y", pose.y);
            pt_pose.add("z", pose.z);
            pt_pose.add("roll", pose.roll);
            pt_pose.add("pitch", pose.pitch);
            pt_pose.add("yaw", pose.yaw);
            pt_poses.push_back(std::make_pair("", pt_pose));
        }
        data.add_child("poses", pt_poses);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetActPath(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_info, pt_poses;
        Path rtn = p_npu_server->GetActPath();
        pt_info.add("map_id", rtn.info.map_id);
        pt_info.add("id", rtn.info.id);
        pt_info.add("length", rtn.info.length);
        pt_info.add("pose_num", rtn.info.pose_num);
        data.add_child("info", pt_info);
        for (auto& pose : rtn.poses) {
            ptree pt_pose;
            pt_pose.add("x", pose.x);
            pt_pose.add("y", pose.y);
            pt_pose.add("z", pose.z);
            pt_pose.add("roll", pose.roll);
            pt_pose.add("pitch", pose.pitch);
            pt_pose.add("yaw", pose.yaw);
            pt_poses.push_back(std::make_pair("", pt_pose));
        }
        data.add_child("poses", pt_poses);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// map
void NpuHttpI::GetMap(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data;
        Map2D rtn = p_npu_server->GetMap();
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}


void NpuHttpI::GetPngMap(ResponsePtr response, RequestPtr request)
{
    if (current_map_name == "") {
        GetTypicalResponse(response, response_error, "current_map_name is NOT avaliable.");
    }
    Map2D map_2d = p_npu_server->GetMap();
    NHP_DEBUG("current_map_name: \"%s\" / \"%s\"", pnd_map_name.c_str(), map_2d.info.id.c_str());
    if (pnd_map_name != map_2d.info.id || (pnd_map_name == map_2d.info.id && p_png_data == NULL)) {
        try {
            pnd_map_name = map_2d.info.id;
            int width = map_2d.mat.width;
            int height = map_2d.mat.height;
            NHP_DEBUG("width: %d, height: %d, length: %d", width, height, static_cast<int>(map_2d.mat.data.size()));
            std::vector<unsigned char> pixel;
            std::vector<unsigned char> png;
            pixel.resize(width * height * 4);
            for(int y = 0; y < height; y++) {
                for(int x = 0; x < width; x++) {
                    int p_index = 4 * width * y + 4 * x;
                    int m_index = width * (height - 1 - y) + x;
                    unsigned char p;
                    if (map_2d.mat.data[m_index] == OCCUPIED_CELL) {
                        p = 0;
                    } else if (map_2d.mat.data[m_index] == FREE_CELL) {
                        p = 254;
                    } else {
                        p = 205;
                    }
                    pixel[p_index + 0] = p;
                    pixel[p_index + 1] = p;
                    pixel[p_index + 2] = p;
                    pixel[p_index + 3] = 255;
                }
            }
            NHP_DEBUG("pixel.size(): %d", static_cast<int>(pixel.size()));
            int error = lodepng::encode(png, pixel, width, height);
            NHP_DEBUG("error: %d", error);
            if(error) {
                GetTypicalResponse(response, response_error, "Make png image failed!");
                return;
            }
            if (p_png_data != NULL) {
                delete p_png_data;
                p_png_data = NULL;
                pnd_data_length = 0;
            }
            p_png_data = new char [png.size()];
            pnd_data_length = png.size();
            char *p = p_png_data;
            for (auto c : png) {
                (*p) = static_cast<char>(c);
                ++p;
            }
            NHP_DEBUG("pnd_data_length: %d", pnd_data_length);
#if 0
            string base64_png;
            Base64Encode(png, &base64_png);
#endif
        } catch(exception& e) {
            GetTypicalResponse(response, response_error, e.what());
        }
    }
    stringstream ss;
    ss << "HTTP/1.1 200\r\n";
    ss << "Cache-Control: no-cache\r\n";
    ss << "Content-Length: " << pnd_data_length << "\r\n\r\n";
    response->write(ss.str().c_str(), ss.str().length());
    response->write(p_png_data, pnd_data_length);
    NHP_DEBUG("Done");
    return;
}

void NpuHttpI::GetCurrentMapInfo(ResponsePtr response, RequestPtr request)
{
    try {
        ptree data, map_info;
        Map2D map_2d = p_npu_server->GetMap();
        map_info.add("id_", map_2d.info.id);
        current_map_name = map_2d.info.id;
        map_info.add("height", map_2d.info.dimension.x);
        map_info.add("width", map_2d.info.dimension.y);
        map_info.add("originX", map_2d.info.offset.x);
        map_info.add("originY", map_2d.info.offset.y);
        map_info.add("resolution", map_2d.info.resolution);
        map_info.add("gridHeight", map_2d.mat.height);
        map_info.add("gridWidth", map_2d.mat.width);
        data.add_child("mapInfo", map_info);
        //std::stringstream ss;
        //write_json(ss, data);
        //response->write(ss.str());
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::BagOpt(ResponsePtr response, RequestPtr request)
{
    LM_DEBUG("");
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        ptree pt;
        read_json(request->content, pt);
        const string& opt = pt.get<string>("opt");
        const string& args = pt.get<string>("args", "");
        string res;
        p_npu_server->DoBagOpt(opt, args, res);
        GetTypicalResponse(response, response_success, res);
    } catch(exception& e) {
        LM_ERROR("%s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }
    LM_DEBUG("<");
    return;
}

void NpuHttpI::GetFootprintVertices(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_pointlist;
        Point3DList pointlist = p_npu_server->GetFootprintVertices();
        for (auto& point : pointlist) {
            ptree pt_point;
            pt_point.add("x",point.x);
            pt_point.add("y",point.y);
            pt_point.add("z",point.z);
            pt_pointlist.push_back(std::make_pair("", pt_point));
        }
        data.add_child("pointlist",pt_pointlist);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::SetInitPose(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        Pose3D pose;
        ptree pt_data;
        read_json(request->content, pt_data);
        pose.x = pt_data.get_child("x").get_value<Ice::Float>();
        pose.y = pt_data.get_child("y").get_value<Ice::Float>();
        //pose.z = pt_data.get_child("z").get_value<Ice::Float>();
        //pose.roll = pt_data.get_child("roll").get_value<Ice::Float>();
        //pose.pitch = pt_data.get_child("pitch").get_value<Ice::Float>();
        pose.yaw = pt_data.get_child("yaw").get_value<Ice::Float>();
        p_npu_server->SetInitPose(pose);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::SetInitPoseArea(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try{
        InitPoseArea pose;
        ptree pt_data;
        read_json(request->content, pt_data);
        pose.height = pt_data.get_child("height").get_value<Ice::Float>();
        pose.width = pt_data.get_child("width").get_value<Ice::Float>();
        ptree pt_pose = pt_data.get_child("pose");
        pose.pose.x = pt_pose.get_child("x").get_value<Ice::Float>();
        pose.pose.y = pt_pose.get_child("y").get_value<Ice::Float>();
        pose.pose.z = pt_pose.get_child("z").get_value<Ice::Float>();
        pose.pose.roll = pt_pose.get_child("roll").get_value<Ice::Float>();
        pose.pose.pitch = pt_pose.get_child("pitch").get_value<Ice::Float>();
        pose.pose.yaw = pt_pose.get_child("yaw").get_value<Ice::Float>();
        p_npu_server->SetInitPoseArea(pose);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetMatchingScore(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data;
        float rtn = p_npu_server->GetMatchingScore();
        data.add("matching_score",rtn);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// start & stop
void NpuHttpI::StartNavi(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    if (param_map["mode"] == "PF_NAVI") {
        p_npu_server->StartNavi(NaviMode::PF_NAVI);
    } else {
        p_npu_server->StartNavi(NaviMode::P2P_NAVI);
    }
    GetTypicalResponse(response, response_success, "");
    return;
}

void NpuHttpI::StopNavi(ResponsePtr response, RequestPtr request)
{
    p_npu_server->StopNavi();
    GetTypicalResponse(response, response_success, "");
    return;
}

// task control
void NpuHttpI::PauseTask(ResponsePtr response, RequestPtr request)
{
    try{
        p_npu_server->PauseTask();
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::ContinueTask(ResponsePtr response, RequestPtr request)
{
    try{
        p_npu_server->ContinueTask();
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CancelTask(ResponsePtr response, RequestPtr request)
{
    NHP_DEBUG("");
    p_npu_server->CancelTask();
    GetTypicalResponse(response, response_success, "");
    return;
}

void NpuHttpI::GetTaskProgress(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data;
        float rtn = p_npu_server->GetTaskProgress();
        data.add("task_progress",rtn);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

// status
void NpuHttpI::GetNaviState(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data;
        NaviState rtn = p_npu_server->GetNaviState();
        data.add("navi_state",rtn);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

/// navi.p2p
void NpuHttpI::GotoPose(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try {
        ptree pt_data;
        read_json(request->content, pt_data);
        Pose3D pose;
        pose.x = pt_data.get_child("x").get_value<float>();
        pose.y = pt_data.get_child("y").get_value<float>();
        pose.z = 0;
        pose.roll = 0;
        pose.pitch = 0;
        pose.yaw = pt_data.get_child("yaw").get_value<float>();
        NHP_DEBUG("x: %.2f, y: %.2f, yaw: %.2f", pose.x, pose.y, pose.yaw);
        p_npu_server->GotoPose(pose);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        NHP_ERROR("e.what() ==> %s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}
void NpuHttpI::GotoStation(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        p_npu_server->GotoStation(param_map["mapName"], param_map["station_id"]);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

/// navi.pf
void NpuHttpI::FollowTempPath(ResponsePtr response, RequestPtr request)
{
    GetTypicalResponse(response, response_success, "");
    try {
        ptree pt_data;
        read_json(request->content, pt_data);
        Pose3DList path;
        for (ptree::iterator po_iter = pt_data.begin();po_iter != pt_data.end();po_iter++) {
            Pose3D pose;
            pose.x = po_iter->second.get_child("x").get_value<float>();
            pose.y = po_iter->second.get_child("y").get_value<float>();
            pose.z = 0;
            pose.roll = 0;
            pose.pitch = 0;
            pose.yaw = po_iter->second.get_child("yaw").get_value<float>();
            NHP_DEBUG("x: %.2f, y: %.2f, yaw: %.2f", pose.x, pose.y, pose.yaw);
            path.push_back(pose);
        }
        p_npu_server->FollowTempPath(path);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        NHP_ERROR("e.what() ==> %s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::FollowPath(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        p_npu_server->FollowPath(param_map["mapName"], param_map["path_id"]);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//// navi.ccp
void NpuHttpI::PlanCoveragePath(ResponsePtr response, RequestPtr request)
{
    try{
        ptree pt_data;
        read_json(request->content, pt_data);
        Point3DList vertices;
        ptree pt_vertices = pt_data.get_child("vertices");
        for (ptree::iterator po_iter = pt_vertices.begin();po_iter != pt_vertices.end();po_iter++) {
            Point3D point;
            point.x = po_iter->second.get_child("x").get_value<float>();
            point.y = po_iter->second.get_child("y").get_value<float>();
            point.z = po_iter->second.get_child("z").get_value<float>();
            vertices.push_back(point);
        }
        Pose3DList rtn = p_npu_server->PlanCoveragePath(vertices);
        ptree pt_pointlist;
        for (auto& point : rtn) {
            ptree pt_point;
            pt_point.add("x",point.x);
            pt_point.add("y",point.y);
            pt_point.add("z",point.z);
            pt_pointlist.push_back(std::make_pair("", pt_point));
        }
        ptree data;
        data.add_child("point_list",pt_pointlist);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

//// slam
void NpuHttpI::StartSlam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    if (param_map["mode"] == "GH_SLAM") {
        p_npu_server->StartSlam(SlamMode::GH_SLAM);
    } else if (param_map["mode"] == "WARE_SLAM") {
        p_npu_server->StartSlam(SlamMode::WARE_SLAM);
    } else if (param_map["mode"] == "PF_SLAM") {
        p_npu_server->StartSlam(SlamMode::PF_SLAM);
    } else {
        p_npu_server->StartSlam(SlamMode::ICP_SLAM);
    }
    GetTypicalResponse(response, response_success, "");
    return;
}

void NpuHttpI::StopSlam(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    p_npu_server->StopSlam(param_map["mapName"]);
    GetTypicalResponse(response, response_success, "");
    return;
}

//// save map
void NpuHttpI::SaveMapImg(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try{
        p_npu_server->SaveMapImg(param_map["mapName"]);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::GetGeneralFile(const string& path, ResponsePtr& response)
{
    LM_DEBUG("%s", path.c_str());
    char* p_content;
    int length = GetFile(path.c_str(), &p_content);
    stringstream ss;
    ss << "HTTP/1.1 200\r\n";
    ss << "Cache-Control: no-cache\r\n";
    ss << "Content-Length: " << length << "\r\n\r\n";
    response->write(ss.str().c_str(), ss.str().length());
    response->write(p_content, length);
    delete p_content;
    LM_DEBUG("<");
    return;
}

//// file
//void NpuHttpI::ExportConfigFile(ResponsePtr response, RequestPtr request)
//{
//    ParamMap param_map;
//    ParseQueryString(request->query_string, param_map);
//    try{
//        ptree data;
//        ZipFile file = p_npu_server->ExportConfigFile(param_map["file_name"]);

//        GetTypicalResponse(response, response_success, data);
//    } catch(exception& e) {
//        GetTypicalResponse(response, response_error, e.what());
//    }
//    return;
//}
//void NpuHttpI::ImportConfigFile(ResponsePtr response, RequestPtr request)
//{
//    ParamMap param_map;
//    ParseQueryString(request->query_string, param_map);
//    try{
//        ptree pt_data;
//        read_json(request->content, pt_data);
//        ZipFile file;

//        p_npu_server->ImportConfigFile(file, param_map["file_name"]);
//        GetTypicalResponse(response, response_success, "");
//    } catch(exception& e) {
//        GetTypicalResponse(response, response_error, e.what());
//    }
//    return;
//}

void NpuHttpI::ExportMapFile(ResponsePtr response, RequestPtr request)
{
    LM_DEBUG("");
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    string& map_id = param_map["map_id"];
    p_npu_server->PackMapFile(map_id);
    string tar_path = "/npu.x86_64/map/" + map_id + ".zip";
    GetGeneralFile(tar_path, response);
    LM_DEBUG("<");
    return;
}

void NpuHttpI::ImportMapFile(ResponsePtr response, RequestPtr request)
{
    LM_DEBUG("");
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    try {
        string& map_id = param_map["map_id"];
        LM_DEBUG("map_id: %s, request->content: %ld", map_id.c_str(), request->content.size());
        ofstream of("/npu.x86_64/map/"+map_id+".zip");
        of << request->content.string();
        of.close();
        p_npu_server->UnPackMapFile(map_id);
        GetTypicalResponse(response, response_success, "");
    } catch(exception& e) {
        LM_ERROR("%s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }

#if 0
    try {
        ptree pt_data;
        read_json(request->content, pt_data);
        std::string file_name = pt_data.get_child("map_data").get_value<std::string>();
        ZipFile file;
        ptree pt_file = pt_data.get_child("file");
        for(ptree::iterator by_iter = pt_file.begin(); by_iter != pt_file.end(); by_iter++)
        {
            char byte;
            byte = by_iter->second.get_child("byte").get_value<char>();
            file.push_back(byte);
        }
        p_npu_server->ImportMapFile(file, file_name);
        GetTypicalResponse(response, response_success, pt_data);
    } catch(exception& e) {
        LM_ERROR("%s", e.what());
        GetTypicalResponse(response, response_error, e.what());
    }
#endif
    LM_DEBUG("<");
    return;
}

////exce_info

////task_system
void NpuHttpI::GetRuntimeStatus(ResponsePtr response, RequestPtr request)
{
    try{
        ptree data, pt_runtime_status;
        RuntimeStatusList rtn = p_npu_server->GetRuntimeStatus();
        for (auto& runtime_status : rtn) {
            ptree pt_runtime_state;
            pt_runtime_state.put_value(runtime_status);
            pt_runtime_status.push_back(std::make_pair("", pt_runtime_state));
        }
        data.add_child("runtime_status_list",pt_runtime_status);
        GetTypicalResponse(response, response_success, data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}


void NpuHttpI::GetCsgStations(ResponsePtr response, RequestPtr request)
{
    wr_npu_msgs::Station current_station;
    wr_npu_msgs::StationArray station_list;
    current_station = p_npu_server->GetCsgCurrentStation();
    if (current_station.d2s < 0) {
        GetTypicalResponse(response, response_success, "Get current station failed!");
        return;
    }
    station_list = p_npu_server->GetCsgStationList();
    if (station_list.station_array.size() <= 0) {
        GetTypicalResponse(response, response_success, "This no station list exist!");
        return;
    }
    try {
        ptree pt_data, pt_cur_station, pt_station_list;
        pt_cur_station.add("id_", current_station.id);
        int type = 0;
        type += (current_station.inflection_point == true)?1:0;
        type += (current_station.csg_station == true)?2:0;
        type += (current_station.enb_navi_arrived == true)?4:0;
        type += (current_station.is_charge_station == true)?8:0;
        type += (current_station.is_charger == true)?16:0;
        pt_cur_station.add("type", type);
        pt_cur_station.add("x", current_station.x);
        pt_cur_station.add("y", current_station.y);
        pt_cur_station.add("theta", current_station.theta);
        pt_data.add_child("current_station", pt_cur_station);
        for (auto station : station_list.station_array) {
            ptree pt_station;
            pt_station.add("id_", station.id);
            int type = 0;
            type += (station.inflection_point == true)?1:0;
            type += (station.csg_station == true)?2:0;
            type += (station.enb_navi_arrived == true)?4:0;
            type += (station.is_charge_station == true)?8:0;
            type += (station.is_charger == true)?16:0;
            pt_station.add("type", type);
            pt_station.add("x", station.x);
            pt_station.add("y", station.y);
            pt_station.add("theta", station.theta);
            NHP_DEBUG("id: %d, x: %.2f, y: %.2f, theta: %.2f, type: %d",
                       station.id, station.x, station.x, station.theta, type);
            ptree pt_adjcent;
            for (auto adj_index : station.adjacent_id) {
                ptree pt_adj_index;
                pt_adj_index.put("", adj_index);
                pt_adjcent.push_back(std::make_pair("", pt_adj_index));
            }
            pt_station.add_child("adjacents", pt_adjcent);
            pt_station_list.push_back(std::make_pair("", pt_station));
        }
        pt_data.add_child("stations", pt_station_list);
        GetTypicalResponse(response, response_success, pt_data);
    } catch(exception& e) {
        GetTypicalResponse(response, response_error, e.what());
    }
    return;
}

void NpuHttpI::CsgStationOpt(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    int opt = atoi(param_map["opt"].c_str());
    int id = -1, type = -1;
    double x, y, theta;

    id = atoi(param_map["id"].c_str());
    x = atof(param_map["x"].c_str());
    y = atof(param_map["y"].c_str());
    theta = atof(param_map["theta"].c_str());
    type = atoi(param_map["type"].c_str());
    NHP_DEBUG("opt: %d id: %d x: %.2f y: %.2f theta: %.2f type: %d", opt, id, x, y, theta, type);
    if (p_npu_server->CsgChangeStation(opt, id, x, y, theta, type)) {
        GetTypicalResponse(response, response_success, "True");
    } else {
        GetTypicalResponse(response, response_success, "False");
    }
    return;
}

void NpuHttpI::CsgPathOpt(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    int opt = atoi(param_map["opt"].c_str());
    int s_id = atof(param_map["s_id"].c_str());
    int e_id = atof(param_map["e_id"].c_str());
    NHP_DEBUG("opt: %d s_id: %d e_id: %d", opt, s_id, e_id);
    if (p_npu_server->CsgChangePath(opt, s_id, e_id)) {
        GetTypicalResponse(response, response_success, "True");
    } else {
        GetTypicalResponse(response, response_success, "False");
    }
    return;
}

void NpuHttpI::CsgEnableAutoMark(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    int opt = atoi(param_map["opt"].c_str());
    NHP_DEBUG("opt: %d", opt);
    if (p_npu_server->CsgEnableAutoMark(opt)) {
        GetTypicalResponse(response, response_success, "True");
    } else {
        GetTypicalResponse(response, response_success, "False");
    }
    return;
}

void NpuHttpI::CsgEnableAutoConnect(ResponsePtr response, RequestPtr request)
{
    ParamMap param_map;
    ParseQueryString(request->query_string, param_map);
    int opt = atoi(param_map["opt"].c_str());
    NHP_DEBUG("opt: %d", opt);
    if (p_npu_server->CsgEnableAutoConnect(opt)) {
        GetTypicalResponse(response, response_success, "True");
    } else {
        GetTypicalResponse(response, response_success, "False");
    }
    return;
}

void NpuHttpI::CsgConnectOnce(ResponsePtr response, RequestPtr request)
{
    NHP_DEBUG("");
    if (p_npu_server->CsgDoConnectOnce()) {
        GetTypicalResponse(response, response_success, "True");
    } else {
        GetTypicalResponse(response, response_success, "False");
    }
    return;
}

} //namespace wizrobo_npu

