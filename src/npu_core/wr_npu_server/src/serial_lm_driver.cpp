#include "com/serial_lm_driver.h"
#define SERIAL_LM_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("SERIAL_LM::", "ComLMDriver::%s() " fmt, __FUNCTION__,##arg)
#define SERIAL_LM_INFO(fmt, arg...)  ROS_INFO_NAMED("SERIAL_LM::",  "ComLMDriver::%s() " fmt, __FUNCTION__,##arg)
#define SERIAL_LM_WARN(fmt, arg...)  ROS_WARN_NAMED("SERIAL_LM::",  "ComLMDriver::%s() " fmt, __FUNCTION__,##arg)
#define SERIAL_LM_ERROR(fmt, arg...) ROS_ERROR_NAMED("SERIAL_LM::", "ComLMDriver::%s() " fmt, __FUNCTION__,##arg)
#define SERIAL_LM_FATAL(fmt, arg...) ROS_FATAL_NAMED("SERIAL_LM::", "ComLMDriver::%s() " fmt, __FUNCTION__,##arg)

#define ENDIAN_EXCHANGE_HALFWORD(x) ((((x)&0xFF00)>>8)+(((x)&0x00FF)<<8))
#define ENDIAN_EXCHANGE_WORD(x) ((((x)&0xFF000000)>>24)+(((x)&0x00FF0000)>>8)+(((x)&0x0000FF00)<<8)+(((x)&0x000000FF)<<24))

namespace wizrobo { namespace icom_driver { namespace serial_lm {

ComLMDriver::ComLMDriver()
    : p_func_map_(NULL)
    , receive_buffer_rear_(0)
{
    SERIAL_LM_INFO("Init done");
}

ComLMDriver::~ComLMDriver()
{
//    if(p_func_map_){
//        delete p_func_map_;
//        p_func_map_ = NULL;
//    }
    port_.Close();
}

void ComLMDriver::SetFuncPtr(funcmap::FuncMap *p_func_map)
{
    p_func_map_ = p_func_map;
}

int ComLMDriver::SetPortParam(string port, int baudrate, int databit, string parity)
{
    if(!port_.Open(port, baudrate, databit, parity)) {
        SERIAL_LM_ERROR("failed to open port(%s)",port.c_str());
        return 0;
    }
    if(port_.IsOpen()){
        std::cout<<"is open"<<std::endl;
        return 0;
    }
    port_.ClearReadBuffer();
    return 1;
}

void ComLMDriver::Run()
{
    if(!ReceiveCmd()){

    }

}

bool ComLMDriver::ReceiveCmd()
{
    int byte_cnt = port_.BytesWaiting();
    if(byte_cnt <= 0)
        return false;
    if((byte_cnt + receive_buffer_rear_) > receive_buffer_length_) {
        byte_cnt = receive_buffer_length_ - receive_buffer_rear_;
    }
    unsigned char *p_buffer_rear = &(receive_buffer_[receive_buffer_rear_]);
    byte_cnt = port_.Read((char *)p_buffer_rear,byte_cnt);
    if(byte_cnt < 0) {
        SERIAL_LM_DEBUG("receive_buffer_rear_: %d, byte_cnt: %d.", receive_buffer_rear_, byte_cnt);
        SERIAL_LM_ERROR("serial port read failed.");
        return false;
    }
    receive_buffer_rear_ += byte_cnt;
#if 0
    SERIAL_LM_INFO("data is :");
    for(int i = 0;i<byte_cnt;i++){
        printf("%02X ",receive_buffer_[i]);
    }
    printf("\n");
#endif
    ParseCmd();
    return true;
}

void ComLMDriver::ParseCmd()
{
    SERIAL_LM_DEBUG("");
    int cnt_available_char = receive_buffer_rear_;
    unsigned char *p_available_char = &(receive_buffer_[0]);
    Header *p_head;

    while(cnt_available_char >= HEADER_LENGTH) {
        p_head = (Header *)p_available_char;
//        SERIAL_LM_INFO("flag %04X", p_head->flag);
//        p_head->flag = ENDIAN_EXCHANGE_HALFWORD(p_head->flag);
        SERIAL_LM_DEBUG("flag %04X", p_head->flag);
        if(p_head->flag != ENDIAN_EXCHANGE_HALFWORD(FLAG_AAAA)) {
            p_available_char++;
            cnt_available_char--;
            continue;
        }
        int frame_length = p_head->data_length + HEADER_LENGTH;
        int total_length = frame_length + 1;
        if (cnt_available_char < total_length) {
            break;
        }
        unsigned char org_sum = p_available_char[total_length-1];
        unsigned char cal_sum = CheckSum(p_available_char, frame_length);
        SERIAL_LM_INFO("org_sum/cal_sum: %02X/%02X", org_sum, cal_sum);

#if 0
        SERIAL_LM_INFO("flag: %04X", p_head->flag);
        SERIAL_LM_INFO(" fun: %02X", p_head->function);
        SERIAL_LM_INFO(" len: %02X", p_head->data_length);
        SERIAL_LM_INFO("org_sum/cal_sum: %02X/%02X", org_sum, cal_sum);
        SERIAL_LM_INFO("frame_length: %d", frame_length);

        for (int i=0;i<frame_length;i++) {
            SERIAL_LM_INFO("byte: %02X", p_available_char[i]);
        }
#endif

        if (org_sum == cal_sum){
            if(p_head->data_length == 0x00 && p_head->cmd == 0x01) {
                ReadFunc(p_head->function);
            } else if(p_head->cmd == 0x02) {
                p_write_buffer_char_ = &(receive_buffer_[HEADER_LENGTH]);
                WriteFunc(p_head->function,p_head->data_length);
            } else{
                SERIAL_LM_ERROR("Invalid command (%02X)",p_head->cmd);
            }
        } else{
            SERIAL_LM_ERROR("Verification failed");
        }
        p_available_char += total_length;
        cnt_available_char -= total_length;
    }

    unsigned char *_ptr = &(receive_buffer_[0]);
    int offset = p_available_char - &(receive_buffer_[0]);
    receive_buffer_rear_ -= offset;
    for (int i=0;i<cnt_available_char;i++) {
        (*_ptr) = (*(_ptr + offset));
        _ptr++;
    }
    (*_ptr) = 0x00;

    return;
}

void ComLMDriver::ReadFunc(unsigned char func)
{
    SERIAL_LM_INFO("");
    Header *p_header = (Header *)(&(send_buffer_[0]));
    p_header->flag = ENDIAN_EXCHANGE_HALFWORD(FLAG_AAAA);
    p_header->cmd = 0x01;
    p_header->function = func;
    unsigned int function = static_cast<unsigned int>(func);

//    FrameData _frame_data;
    if(p_func_map_->lm_read_func_map_.find(function) != p_func_map_->lm_read_func_map_.end()){
        unsigned char send_char[256];
        int lenth = p_func_map_->lm_read_func_map_[function](send_char);
        p_header->data_length = static_cast<unsigned char>(lenth);
        SERIAL_LM_INFO("01");
        unsigned char *p_frame_char = (unsigned char *)&send_char[0];
        unsigned int len = lenth;
        unsigned char *p_char = &(send_buffer_[HEADER_LENGTH]);
        for(int i = 0;i < len;i++){
            (*p_char) = (*p_frame_char);
            p_char++;
            p_frame_char++;
        }
        int frame_length = HEADER_LENGTH + len;
        int total_length = frame_length + 1;
        (*p_char) = CheckSum(&(send_buffer_[0]),frame_length);
        port_.Write((char*)send_buffer_,total_length);
    } else {
        p_header->cmd = 0x04;
        unsigned char error_code[2] = {0x00,0xe4};
        p_header->data_length = 0x02;
        unsigned char *p_frame_char = &(error_code[0]);
        unsigned char *p_char = &(send_buffer_[HEADER_LENGTH]);
        for(int i = 0;i < 2;i++){
            (*p_char) = (*p_frame_char);
            p_char++;
            p_frame_char++;
        }
        int frame_length = HEADER_LENGTH + 2;
        int total_length = frame_length + 1;
        (*p_char) = CheckSum(&(send_buffer_[0]),frame_length);
        port_.Write((char*)send_buffer_,total_length);
    }
}

void ComLMDriver::WriteFunc(unsigned char func, int data_len)
{
    Header *p_header = (Header *)(&(send_buffer_[0]));
    p_header->flag = ENDIAN_EXCHANGE_HALFWORD(FLAG_AAAA);
    p_header->cmd = 0x01;
    p_header->function = func;
    p_header->data_length = 0x02;
    unsigned int function = static_cast<unsigned int>(func);
    unsigned char data[data_len];
    unsigned char *p_data = &data[0];
    unsigned char *p_buffer = &(receive_buffer_[HEADER_LENGTH]);
    for(int i = 0;i < data_len;i++){
        (*p_data) = (*p_buffer);
        p_data++;
        p_buffer++;
    }
    p_buffer-=data_len;
    int frame_length = HEADER_LENGTH + 2;
    int total_length = frame_length + 1;
    unsigned char *p_char = &(send_buffer_[HEADER_LENGTH]);
    if(p_func_map_->lm_write_func_map_.find(function) != p_func_map_->lm_write_func_map_.end()){
        if(p_func_map_->lm_write_func_map_[function](data)){
            (*p_char) = 0x01;
            (*(++p_char)) = 0x00;
        } else {
            (*p_char) = 0x00;
            (*(++p_char)) = 0x03;
        }
    } else {
        (*p_char) = 0x00;
        (*(++p_char)) = 0x02;
    }
    (*(++p_char)) = CheckSum((&send_buffer_[0]),frame_length);
    port_.Write((char*)send_buffer_,total_length);
    return;
}

unsigned char ComLMDriver::CheckSum(unsigned char *ptr, int length)
{
    unsigned char sum = 0;
    for (int i=0;i<length;i++) {
        sum += ptr[i];
    }
    return sum;
}

}//namespace lm_server
}//namespace icom_driver
}//namespace wizrobo
