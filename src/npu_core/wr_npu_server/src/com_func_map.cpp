#include "com/com_func_map.h"

#define FUNC_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("FUNC::", "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define FUNC_INFO(fmt, arg...)  ROS_INFO_NAMED("FUNC::",  "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define FUNC_WARN(fmt, arg...)  ROS_WARN_NAMED("FUNC::",  "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define FUNC_ERROR(fmt, arg...) ROS_ERROR_NAMED("FUNC::", "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define FUNC_FATAL(fmt, arg...) ROS_FATAL_NAMED("FUNC::", "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define FUNC_API_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("FUNC_API::", "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define FUNC_API_INFO(fmt, arg...)  ROS_INFO_NAMED("FUNC_API::",  "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define FUNC_API_WARN(fmt, arg...)  ROS_WARN_NAMED("FUNC_API::",  "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define FUNC_API_ERROR(fmt, arg...) ROS_ERROR_NAMED("FUNC_API::", "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define FUNC_API_FATAL(fmt, arg...) ROS_FATAL_NAMED("FUNC_API::", "FuncMap::%s() " fmt, __FUNCTION__,##arg)
#define ENDIAN_EXCHANGE_HALFWORD(x) ((((x)&0xFF00)>>8)+(((x)&0x00FF)<<8))
#define ENDIAN_EXCHANGE_WORD(x) ((((x)&0xFF000000)>>24)+(((x)&0x00FF0000)>>8)+(((x)&0x0000FF00)<<8)+(((x)&0x000000FF)<<24))

namespace wizrobo { namespace funcmap {
using namespace std;

FuncMap::FuncMap():
    ang_vel_(0.0)
  , goal_id_(0)
  , map_id_no_(0)
  , is_active_(0)
  , is_forward_(0)
  , line_vel_(0.0)
  , is_backward_(0)
  , cmd_ang_vel_(0)
  , is_turn_left_(0)
  , cmd_line_vel_(0)
  , task_goal_id_(0)
  , max_ang_vel_(0.0)
  , is_turn_right_(0)
  , max_line_vel_(0.0)
  , normal_goal_id_(0)
  , teach_mode_(false)
  , set_cmd_vel_(false)
  , p_npu_server_(NULL)
  , is_task_goal_(false)
  , map_save_done_(false)
  , update_npu_data_(NULL)
  , speed_gear_level_(0.3)
  , ang_gear_level_(0.3)
  , left_motor_current_(0.0)
  , safe_button_is_on_(true)
  , right_motor_current_(0.0)
  , station_setting_done_(false)
  , myo_started_(false)
  , voice_started_(false)
  , follow_started_(false)
  , slam_mode_(SlamMode::PF_SLAM)
  , navi_mode_(NaviMode::P2P_NAVI)
{
    ros::NodeHandle n;
    cmd_pub_ = n.advertise<geometry_msgs::Twist>("/manual_cmd_vel",1,true);
    goal_cb_ = n.subscribe<geometry_msgs::PoseStamped>("/navi_core_simple/goal",1, &FuncMap::CurrentGoalCallBack, this);
    n.param<float>("/npu_param/teleop/npu_server_param/comi/modbus_rtu_zx/max_ang_vel_scale",max_ang_vel_,4.237);
    n.param<float>("/npu_param/teleop/npu_server_param/comi/modbus_rtu_zx/max_line_vel_scale",max_line_vel_,3.14);
    FUNC_INFO("max_line_vel_scale is %.2f, max_ang_vel_scale is %.2f", max_line_vel_, max_ang_vel_);
    voice_enb_pub_ = n.advertise<std_msgs::Bool>("/enable_voice_control",10);
    follow_enb_pub_ = n.advertise<std_msgs::Bool>("/enable_follow_control",10);
    gesture_enb_pub_ = n.advertise<std_msgs::Bool>("/enable_gesture_control",10);
    motor_enb_pub_ = n.advertise<std_msgs::Bool>("/enable_motor_control",10);
    motor_power_pub_ = n.advertise<std_msgs::Bool>("/motor_power_control",10);
}

FuncMap::~FuncMap()
{
    if (update_npu_data_)
    {
        update_npu_data_->interrupt();
        update_npu_data_->join();
        delete update_npu_data_;
        update_npu_data_ = NULL;
    }
}
void FuncMap::CurrentGoalCallBack(const geometry_msgs::PoseStampedConstPtr &msg)
{
    int j = 0;
    for(Station p : station_list_){
        if(msg->pose.position.x == p.pose.x && msg->pose.position.y == p.pose.y){
            boost::unique_lock<boost::mutex> lock(goal_cb_mutex_);
            task_goal_id_ = j;
            lock.unlock();
        }
        j++;
    }
}
void FuncMap::UpdateRobotStatus()
{
    ros::Rate r(2);
    while (ros::ok()) {
        if(p_npu_server_ == NULL){
            r.sleep();
            continue;
        }
        r.sleep();
    }
}

void FuncMap::SetNpuServer(NpuServer* ptr)
{
    p_npu_server_ = ptr;

    std::string version;
    version = p_npu_server_->GetServerVersion();
    ROS_INFO("%s", version.c_str());
    p_npu_server_->Connect(version);
    map_id_ = p_npu_server_->GetMapInfos()[map_id_no_].id;
}

void FuncMap::InitFuncMap()
{
#define _set_read_func(add, function) \
    do \
    { \
        read_func_map_[add] = std::bind(&function, this);\
    } while(0)
#define _set_write_func(add, function) \
    do \
    { \
        write_func_map_[add] = std::bind(&function, this, std::placeholders::_1);\
    } while(0)
#define _set_lm_read_func(add, function) \
    do \
    { \
        lm_read_func_map_[add] = std::bind(&function, this, std::placeholders::_1);\
    } while(0)
#define _set_lm_write_func(add, function) \
    do \
    { \
        lm_write_func_map_[add] = std::bind(&function, this, std::placeholders::_1);\
    } while(0)

#if 0
    _set_write_func(0, FuncMap::SetRobotState);//40001
    _set_write_func(1, FuncMap::SafeButtonStatus);//40002
    _set_write_func(2, FuncMap::SetSpeedGearLevel);//40002
    _set_write_func(3, FuncMap::SetLineSpd);//40002
    _set_write_func(4, FuncMap::SetAngSpd);//40003
    _set_write_func(3, FuncMap::SetCurrentMapId);//40004
    _set_write_func(4, FuncMap::GotoStation);//40005

    _set_read_func(5,FuncMap::NormalLampStatus);
    _set_read_func(0, FuncMap::GetRobotState);//40001
    _set_read_func(3, FuncMap::GetCurrentMapId);//40004
    _set_read_func(9, FuncMap::GetNaviStatus);//40010
    _set_read_func(10, FuncMap::GetActLineSpd);//40011
    _set_read_func(11, FuncMap::GetActAngSpd);//40012
    _set_read_func(12, FuncMap::GetAngle);//40013
    _set_read_func(16, FuncMap::GetLeftMotorCurrent);//40017
    _set_read_func(17, FuncMap::GetRightMotorCurrent);//40018
    _set_read_func(18, FuncMap::GetCurrentGoalId);//40019
    _set_read_func(19, FuncMap::GetSumOfStation);//40020
#endif
    int read_func = 0, write_func = 0;
    ////read func
    ////1--5
    _set_lm_read_func(++read_func,FuncMap::LMGetNpuVersion);//ok
    _set_lm_read_func(++read_func,FuncMap::LMGetRobotState);
    _set_lm_read_func(++read_func,FuncMap::LMGetCurrentMapId);
    _set_lm_read_func(++read_func,FuncMap::LMGetNaviStatus);
    _set_lm_read_func(++read_func,FuncMap::LMGetNaviMode);

    ////6--10
    _set_lm_read_func(++read_func,FuncMap::LMGetSlamMode);
    _set_lm_read_func(++read_func,FuncMap::LMGetSystemDiagInfo);
    _set_lm_read_func(++read_func,FuncMap::LMGetActionState);
    _set_lm_read_func(++read_func,FuncMap::LMGetConfigIDList);
    _set_lm_read_func(++read_func,FuncMap::LMGetActPose);

    ////11--15
    _set_lm_read_func(++read_func,FuncMap::LMGetActVel);
    _set_lm_read_func(++read_func,FuncMap::LMGetCmdVel);
    _set_lm_read_func(++read_func,FuncMap::LMGetCmdPose);
    _set_lm_read_func(++read_func,FuncMap::LMGetCoreParam);
    _set_lm_read_func(++read_func,FuncMap::LMGetMotorParam);

    ////16--20
    _set_lm_read_func(++read_func,FuncMap::LMGetPidParam);
    _set_lm_read_func(++read_func,FuncMap::LMGetChassisParam);
    _set_lm_read_func(++read_func,FuncMap::LMGetFootprintParam);
    _set_lm_read_func(++read_func,FuncMap::LMGetBaseParam);
    _set_lm_read_func(++read_func,FuncMap::LMGetSensorParam);

    ////21--25
    _set_lm_read_func(++read_func,FuncMap::LMGetTeleopParam);
    _set_lm_read_func(++read_func,FuncMap::LMGetNaviParam);
    _set_lm_read_func(++read_func,FuncMap::LMGetSlamParam);
    _set_lm_read_func(++read_func,FuncMap::LMGetMotorEnc);
    _set_lm_read_func(++read_func,FuncMap::LMGetRuntimeStatus);

    ////26--30
    _set_lm_read_func(++read_func,FuncMap::LMGetCmdMotorSpd);
    _set_lm_read_func(++read_func,FuncMap::LMGetActMotorSpd);
    _set_lm_read_func(++read_func,FuncMap::LMGetImuData);
    _set_lm_read_func(++read_func,FuncMap::LMGetSonarScan);
    _set_lm_read_func(++read_func,FuncMap::LMGetInfrdScan);

    ////31--35
    _set_lm_read_func(++read_func,FuncMap::LMGetBumperArray);
    _set_lm_read_func(++read_func,FuncMap::LMGetBatteryStatus);
    _set_lm_read_func(++read_func,FuncMap::LMGetMapInfos);
    _set_lm_read_func(++read_func,FuncMap::LMGetStations);
    _set_lm_read_func(++read_func,FuncMap::LMGetTaskList);

    ////36--40
    _set_lm_read_func(++read_func,FuncMap::LMGetPaths);
    _set_lm_read_func(++read_func,FuncMap::LMGetVirtualWalls);
    _set_lm_read_func(++read_func,FuncMap::LMGetGlobalPath);
    _set_lm_read_func(++read_func,FuncMap::LMGetLocalPath);
    _set_lm_read_func(++read_func,FuncMap::LMGetActPath);

    ////41--43
    _set_lm_read_func(++read_func,FuncMap::LMGetFootprintVertices);
    _set_lm_read_func(++read_func,FuncMap::LMGetMatchingScore);
    _set_lm_read_func(++read_func,FuncMap::LMGetTaskProgress);


    ////write func
    ////1--5
    _set_lm_write_func(++write_func,FuncMap::LMConnect);
    _set_lm_write_func(++write_func,FuncMap::LMShutDown);
    _set_lm_write_func(++write_func,FuncMap::LMReBoot);
    _set_lm_write_func(++write_func,FuncMap::LMStartNavi);
    _set_lm_write_func(++write_func,FuncMap::LMStopNavi);

    ////6--10
    _set_lm_write_func(++write_func,FuncMap::LMStartSlam);
    _set_lm_write_func(++write_func,FuncMap::LMStopSlam);
    _set_lm_write_func(++write_func,FuncMap::LMSelectMap);
    _set_lm_write_func(++write_func,FuncMap::LMSelectConfig);
    _set_lm_write_func(++write_func,FuncMap::LMDeleteConfig);

    ////11--15
    _set_lm_write_func(++write_func,FuncMap::LMAddConfig);
    _set_lm_write_func(++write_func,FuncMap::LMSetCoreParam);
    _set_lm_write_func(++write_func,FuncMap::LMSetMotorParam);
    _set_lm_write_func(++write_func,FuncMap::LMSetPidParam);
    _set_lm_write_func(++write_func,FuncMap::LMSetChassisParam);

    ////16--20
    _set_lm_write_func(++write_func,FuncMap::LMSetFootprintParam);
    _set_lm_write_func(++write_func,FuncMap::LMSetBaseParam);
    _set_lm_write_func(++write_func,FuncMap::LMSetSensorParam);
    _set_lm_write_func(++write_func,FuncMap::LMSetTeleopParam);
    _set_lm_write_func(++write_func,FuncMap::LMSetNaviParam);

    ////21--25
    _set_lm_write_func(++write_func,FuncMap::LMSetSlamParam);
    _set_lm_write_func(++write_func,FuncMap::LMFeedMotorEnc);
    _set_lm_write_func(++write_func,FuncMap::LMFeedActMotorSpd);
    _set_lm_write_func(++write_func,FuncMap::LMClearMotorEnc);
    _set_lm_write_func(++write_func,FuncMap::LMSetManualCmd);

    ////26--30
    _set_lm_write_func(++write_func,FuncMap::LMSetManualVel);
    _set_lm_write_func(++write_func,FuncMap::LMSetMapInfos);
    _set_lm_write_func(++write_func,FuncMap::LMSetStations);
    _set_lm_write_func(++write_func,FuncMap::LMExecuteTask);
    _set_lm_write_func(++write_func,FuncMap::LMSetTasks);

    ////31--35
    _set_lm_write_func(++write_func,FuncMap::LMSetPaths);
    _set_lm_write_func(++write_func,FuncMap::LMSetInitPose);
    _set_lm_write_func(++write_func,FuncMap::LMSetInitArea);
    _set_lm_write_func(++write_func,FuncMap::LMPauseTask);
    _set_lm_write_func(++write_func,FuncMap::LMContinueTask);

    ////36--40
    _set_lm_write_func(++write_func,FuncMap::LMCancelTask);
    _set_lm_write_func(++write_func,FuncMap::LMGotoPose);
    _set_lm_write_func(++write_func,FuncMap::LMGotoStation);
    _set_lm_write_func(++write_func,FuncMap::LMFollowTempPath);
    _set_lm_write_func(++write_func,FuncMap::LMFollowPath);

    ////41--44
    _set_lm_write_func(++write_func,FuncMap::LMPlanCoveragePath);
    _set_lm_write_func(++write_func,FuncMap::LMGotoGeoPose);
    _set_lm_write_func(++write_func,FuncMap::LMDeletStation);
    _set_lm_write_func(++write_func,FuncMap::LMSetPathPoint);
}


////lm func
/// read
int FuncMap::LMGetNpuVersion(unsigned char *data)
{
    FUNC_INFO("");
    string version = p_npu_server_->GetServerVersion();
    FUNC_API_INFO("get server version is %s",version.c_str());
    unsigned char* p_char = (unsigned char*)version.c_str();
    int len = static_cast<int>(version.size());
    for(int i = 0;i < len;i++){
        data[i] = *p_char;
        p_char++;
    }
    return len;
}

int FuncMap::LMGetNaviStatus(unsigned char *data)
{
    FUNC_INFO("");
    try{
        NaviState state = p_npu_server_->GetNaviState();
        FUNC_API_DEBUG("current navi status is : %d", state);
        data[0] = static_cast<unsigned char>(state);
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return 1;
}

int FuncMap::LMGetCurrentMapId(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        string map_id = map_id_;
        FUNC_DEBUG(" current map id is : %s", map_id_.c_str());
        unsigned char* p_char = (unsigned char*)map_id.c_str();
        len = static_cast<int>(map_id.size());
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetRobotState(unsigned char *data)
{
    FUNC_INFO("");
    try{
        NpuState state = p_npu_server_->GetNpuState();
        data[0] = static_cast<unsigned char>(state);
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return 1;
}

int FuncMap::LMGetNaviMode(unsigned char *data)
{
    FUNC_INFO("");
    try{
        NaviMode mode = p_npu_server_->GetNaviMode();
        data[0] = static_cast<unsigned char>(mode);
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return 1;
}

int FuncMap::LMGetSlamMode(unsigned char *data)
{
    FUNC_INFO("");
    try{
        SlamMode mode = p_npu_server_->GetSlamMode();
        data[0] = static_cast<unsigned char>(mode);
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return 1;
}

int FuncMap::LMGetSystemDiagInfo(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        string info = p_npu_server_->GetSystemDiagInfo().todo;
        unsigned char* p_char = (unsigned char*)info.c_str();
        len = static_cast<int>(info.size());
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetActionState(unsigned char *data)
{
    FUNC_INFO("");
    try{
        ActionState state = p_npu_server_->GetActionState();
        data[0] = static_cast<unsigned char>(state);
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return 1;
}

int FuncMap::LMGetConfigIDList(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        string send_buf;
        StringArray list = p_npu_server_->GetConfigIdList();
        uint list_size = static_cast<uint>(list.size());
        for(int i = 0;i < list_size;i++){
            if(i == (list_size - 1)){
                send_buf += list[i];
            } else{
                send_buf += list[i] + ",";
            }
        }
        unsigned char* p_char = (unsigned char*)send_buf.c_str();
        len = static_cast<int>(send_buf.size());
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetActPose(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        Pose3D pose = p_npu_server_->GetActPose();
        unsigned char *p_char = (unsigned char*)(&pose);
        len = 24;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetCmdPose(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        Pose3D pose = p_npu_server_->GetCmdPose();
        unsigned char *p_char = (unsigned char*)(&pose);
        len = 24;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetActVel(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        Vel3D vel = p_npu_server_->GetActVel();
        unsigned char *p_char = (unsigned char*)(&vel);
        len = 24;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetCmdVel(unsigned char *data)
{
    FUNC_INFO("");

    int len = 1;
    try{
        Vel3D vel = p_npu_server_->GetCmdVel();
        unsigned char *p_char = (unsigned char*)(&vel);
        len = 24;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetCoreParam(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        CoreParam param = p_npu_server_->GetCoreParam();
        unsigned char npu_mode = (unsigned char)(param.npu_mode);
        data[0] = npu_mode;
        string core = param.config_id + "," + param.map_id + "," + param.record_id;
        unsigned char* p_char = (unsigned char*)core.c_str();
        len = static_cast<int>(core.size());
        for(int i = 0;i < len;i++){
            data[i+1] = *p_char;
            p_char++;
        }
        len +=1;
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetMotorParam(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        ComMotorParam param = ComMotorParam(p_npu_server_->GetMotorParam());
        MotorParam send = param.CreatMotorParam();
        unsigned char* p_char = (unsigned char*)(&send);
        len = 37;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetPidParam(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        wizrobo_npu::PidParam param = p_npu_server_->GetPidParam();
#if 0
        printf("%d\n", param.enb_pid);
        cout<<param.kp_acc<<endl;
        cout<<param.kp_dec<<endl;
        cout<<param.ki_acc<<endl;
        cout<<param.kp_acc<<endl;
        cout<<param.ki_dec<<endl;
        cout<<param.kd_acc<<endl;
        cout<<param.kd_dec<<endl;
#endif
        len = 28;
        unsigned char* p_char = (unsigned char*)(&param);
        printf("param is");
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            printf(" %02X(%02X)", *p_char, data[i]);
            p_char++;
        }
        printf("\n");
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetChassisParam(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        ChassisParam param = p_npu_server_->GetChassisParam();
        len = 40;
        unsigned char* p_char = (unsigned char*)(&param);
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetFootprintParam(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        FootprintParam param = p_npu_server_->GetFootprintParam();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetBaseParam(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetBaseParam();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetSensorParam(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetSensorParam();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetTeleopParam(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        TeleopParam param = p_npu_server_->GetTeleopParam();
        len = 8;
        unsigned char* p_char = (unsigned char*)(&param);
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetNaviParam(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        NaviParam param = p_npu_server_->GetNaviParam();
        len = sizeof(param);
        unsigned char* p_char = (unsigned char*)(&param);
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetSlamParam(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        SlamParam param = p_npu_server_->GetSlamParam();
        len = sizeof(param);
        unsigned char* p_char = (unsigned char*)(&param);
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetMotorEnc(unsigned char* data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        ComMotorEnc enc = ComMotorEnc(p_npu_server_->GetMotorEnc());
        FUNC_INFO("motor num is %d, steer_angle_deg is %.3f",
                  enc.motor_num, enc.steer_angle_deg);
        len = 37;
        unsigned char* _enc = (unsigned char*)&enc;
        printf("data is");
        for(int i = 0;i < len;i++){
            data[i] = *_enc;
            printf(" %02X(%02X)", *_enc, data[i]);
            _enc++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}


int FuncMap::LMGetCmdMotorSpd(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        ComMotorSpd spd = ComMotorSpd(p_npu_server_->GetCmdMotorSpd());
        len = 5 + (4 * spd.motor_num_);
        unsigned char* p_char = (unsigned char*)&spd;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetActMotorSpd(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        ComMotorSpd spd = ComMotorSpd(p_npu_server_->GetActMotorSpd());
        len = 5 + (4 * spd.motor_num_);
        unsigned char* p_char = (unsigned char*)&spd;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetImuData(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        ImuData imudata = p_npu_server_->GetImuData();
        len = 12;
        unsigned char* p_char = (unsigned char*)&imudata;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetSonarScan(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetSonarScan();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetInfrdScan(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetInfrdScan();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetBumperArray(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        BumperArray _bumper = p_npu_server_->GetBumperArray();
        ComBumperArray bumper = ComBumperArray(_bumper);
        len = static_cast<int>(_bumper.states.size()) * 2;
        unsigned char* p_char = (unsigned char*)&bumper;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetBatteryStatus(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        BatteryStatus state = p_npu_server_->GetBatteryStatus();
        FUNC_API_INFO("battery state :\n\tcurrent is : %.3f\n\tvoltage is : %.3f\n\ttemp     is : %.3f\n\tcap      is : %.3f\n\t",
                      state.current_a, state.voltage_v, state.temperature_deg, state.capacity_level);
        unsigned char* p_char = (unsigned char*)&state;
        len = 16;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetMapInfos(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        MapInfoList list = p_npu_server_->GetMapInfos();
        string _id;
        cout<<list.size()<<endl;
        unsigned int size = RANGE(list.size(),1,10);
        cout<<size<<endl;
        if(size > 1){
            for(int i = 0;i < size - 1;i++){
                cout<<"times"<<i<<endl;
                _id+=list[i].id;
                cout<<_id.size()<<endl;
                _id+=",";
                cout<<_id.size()<<endl;
            }
        }
        cout<<_id.size()<<endl;
        _id+=list[size-1].id;
        FUNC_INFO("_id(%ld) is : %s", _id.size(), _id.c_str());
        unsigned char* p_char = (unsigned char*)_id.c_str();
        len = static_cast<int>(_id.size());
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetStations(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetStations();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetTaskList(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
        string map_id;
        wizrobo_npu::TaskList list = p_npu_server_->GetTaskList(map_id);
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetPaths(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetPaths();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetVirtualWalls(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetVirtualWalls();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetGlobalPath(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetGlobalPath();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetLocalPath(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetLocalPath();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetActPath(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetActPath();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetFootprintVertices(unsigned char *data)
{
    ///TODO
    FUNC_INFO("");
    int len = 1;
    try{
//        p_npu_server_->GetFootprintVertices();
    } catch(exception& e){
        data[0] = 0xe1;
    }
    data[0] = 0xf1;
    return len;
}

int FuncMap::LMGetMatchingScore(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        float score = p_npu_server_->GetMatchingScore();
        unsigned char* p_char = (unsigned char*)&score;
        len = 4;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetTaskProgress(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        float task_progress = p_npu_server_->GetTaskProgress();
        unsigned char* p_char = (unsigned char*)&task_progress;
        len = 4;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}

int FuncMap::LMGetRuntimeStatus(unsigned char *data)
{
    FUNC_INFO("");
    int len = 1;
    try{
        ComRunTimeStatus status = ComRunTimeStatus(p_npu_server_->GetRuntimeStatus());
        unsigned char* p_char = (unsigned char*)&status;
        len = 12;
        for(int i = 0;i < len;i++){
            data[i] = *p_char;
            p_char++;
        }
    } catch(exception& e){
        data[0] = 0xe1;
    }
    return len;
}


/// write
bool FuncMap::LMConnect(unsigned char *data)
{
    FUNC_INFO("");
    try{
        string version = (char *)data;
        p_npu_server_->Connect(version);
        MapInfoList infos = p_npu_server_->GetMapInfos();
        map_id_ = infos[0].id;
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMShutDown(unsigned char *data)
{
    FUNC_INFO("");
    try{
        p_npu_server_->Shutdown();
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMReBoot(unsigned char *data)
{
    FUNC_INFO("");
    try{
        p_npu_server_->Reboot();
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMStartNavi(unsigned char *data)
{
    FUNC_INFO("");
    try{
        NaviMode mode = (NaviMode)(*data);
        p_npu_server_->StartNavi(mode);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMStopNavi(unsigned char *data)
{
    FUNC_INFO("");
    try{
        p_npu_server_->StopNavi();
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMStartSlam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        SlamMode mode = (SlamMode)(*data);
        p_npu_server_->StartSlam(mode);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMStopSlam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        string map_id = (char*)data;
        if(map_id.size() != 5){
            return false;
        }
        if(*data == 0x00){
            p_npu_server_->StopSlam("");
        } else{
            p_npu_server_->StopSlam(map_id);
        }
        return true;
    } catch(exception &e){
        return false;
    }
}

//bool FuncMap::LMSelectMap(unsigned char *data)
//{
//    try{
//        string id = (char*)data;
//        p_npu_server_->SelectMap(id);
//        return true;
//    } catch(exception &e){
//        return false;
//    }
//}

bool FuncMap::LMSelectConfig(unsigned char *data)
{
    FUNC_INFO("");
    try{
        string id = (char*)data;
        p_npu_server_->SelectConfig(id);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMDeleteConfig(unsigned char *data)
{
    FUNC_INFO("");
    try{
        string id = (char*)data;
        p_npu_server_->DeleteConfig(id);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMAddConfig(unsigned char *data)
{
    FUNC_INFO("");
    try{
        string id = (char*)data;
        p_npu_server_->AddConfig(id);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetCoreParam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        CoreParam _param;
        _param.npu_mode = (NpuMode)*data;
        string set_id = (char*)(data + 1);
        int index = 0,last_index = 0;
        index = set_id.find(",",0);
        _param.config_id = set_id.substr(last_index,index - last_index);
        last_index = index;
        index = set_id.find(",",index + 1);
        _param.map_id = set_id.substr(last_index + 1,index - last_index - 1);
        last_index = index;
        _param.record_id = set_id.substr(index + 1,static_cast<int>(set_id.size()) - last_index - 1);
        p_npu_server_->SetCoreParam(_param);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetMotorParam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        ComMotorParam param = *((ComMotorParam*)data);
        p_npu_server_->SetMotorParam(param.CreatMotorParam());
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetPidParam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        wizrobo_npu::PidParam _param = *((wizrobo_npu::PidParam*)data);
        p_npu_server_->SetPidParam(_param);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetChassisParam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        wizrobo_npu::ChassisParam _param = *((wizrobo_npu::ChassisParam*)data);
        p_npu_server_->SetChassisParam(_param);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetFootprintParam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        FootprintParam *_param = (FootprintParam*)data;
        p_npu_server_->SetFootprintParam(*_param);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetBaseParam(unsigned char *data)
{
    FUNC_INFO("");
//    try{
//        BaseParam *_param = (BaseParam*)data;
//        p_npu_server_->SetBaseParam(*_param);
//        return true;
//    } catch(exception &e){
//        return false;
//    }
}

bool FuncMap::LMSetSensorParam(unsigned char *data)
{
    FUNC_INFO("");
//    try{
//        SensorParam *_param = (BaseParam*)data;
//        p_npu_server_->SetBaseParam(*_param);
//        return true;
//    } catch(exception &e){
//        return false;
//    }
}

bool FuncMap::LMSetTeleopParam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        TeleopParam *_param = (TeleopParam*)data;
        p_npu_server_->SetTeleopParam(*_param);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetNaviParam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        NaviParam *_param = (NaviParam*)data;
        p_npu_server_->SetNaviParam(*_param);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetSlamParam(unsigned char *data)
{
    FUNC_INFO("");
    try{
        SlamParam *_param = (SlamParam*)data;
        p_npu_server_->SetSlamParam(*_param);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMFeedMotorEnc(unsigned char *data)
{
    FUNC_INFO("");
    try{
        MotorEnc enc;
        unsigned char *motor_num = new unsigned char;
        char *motor_enc = new char;
        for(int i = 0;i < 4;i++){
            *motor_num = *data;
            motor_num++;
            data++;
        }
        enc.motor_num = *((unsigned int*)motor_num);
        for(int i = 0;i < enc.motor_num;i++){
            for(int i = 0;i < 4;i++){
                *motor_enc = *data;
                motor_enc++;
                data++;
            }
            motor_enc-=4;
            enc.ticks.push_back(*((int*)motor_enc));
        }
        enc.steer_angle_deg = *((float*)data);
        p_npu_server_->FeedMotorEnc(enc);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMFeedActMotorSpd(unsigned char *data)
{
    FUNC_INFO("");
    try{
        wizrobo_npu::MotorSpd spd;
        unsigned char *motor_num = new unsigned char;
        char *motor_spd = new char;
        for(int i = 0;i < 4;i++){
            *motor_num = *data;
            motor_num++;
            data++;
        }
        spd.motor_num = *((unsigned int*)motor_num);
        for(int i = 0;i < spd.motor_num;i++){
            for(int i = 0;i < 4;i++){
                *motor_spd = *data;
                motor_spd++;
                data++;
            }
            motor_spd-=4;
            spd.rpms.push_back(*((int*)motor_spd));
        }
        spd.steer_angle_deg = *((float*)data);
        p_npu_server_->FeedActMotorSpd(spd);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMClearMotorEnc(unsigned char *data)
{
    FUNC_INFO("");
    try{
        p_npu_server_->ClearMotorEnc();
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetManualCmd(unsigned char *data)
{
    FUNC_INFO("");
    try{
        ManualCmdType type = *((ManualCmdType*)data);
        p_npu_server_->SetManualCmd(type);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetManualVel(unsigned char *data)
{
    FUNC_INFO("");
    try{
        ManualVel vel = *((ManualVel*)data);
        FUNC_INFO("manual vel is :(%.3f,%.3f)", vel.v_lin, vel.v_ang);
        p_npu_server_->SetManualVel(vel.v_lin, vel.v_ang);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetMapInfos(unsigned char *data)
{
    FUNC_INFO("");
    //TO DO
    try{
        MapInfoList infos;
        p_npu_server_->SetMapInfos(infos);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSelectMap(unsigned char *data)
{
    FUNC_INFO("");
    try{
        string map_id = (char*)data;
        if(map_id.size() != 5){
            return false;
        }
        p_npu_server_->SelectMap(map_id);
        map_id_ = map_id;
        return true;
    } catch(exception &e){
    FUNC_INFO("");
        return false;
    }
}

bool FuncMap::LMSetStations(unsigned char *data)
{
    FUNC_INFO("");
    string id = (char*)data;
    if(CreatStation(id)){
        return true;
    } else{
        return false;
    }
}

bool FuncMap::LMDeletStation(unsigned char *data)
{
    FUNC_INFO("");
    string id = (char*)data;
    if(DeletStation(id)){
        return true;
    } else{
        return false;
    }
}

bool FuncMap::LMExecuteTask(unsigned char *data)
{
    FUNC_INFO("");
    //TO DO
    try{
        TaskList list;
        p_npu_server_->ExecuteTask(list);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetTasks(unsigned char *data)
{
    FUNC_INFO("");
    //TO DO
    try{
        string map_id;
        TaskList list;
        p_npu_server_->SetTasks(map_id, list);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetPaths(unsigned char *data)
{
    FUNC_INFO("");
    //TO DO
    try{
        if(path_pose_.size() == 0){
            FUNC_API_WARN("There are no points that can be set as paths");
            return false;
        }
        string id = (char*)data;
        Path _path;
        _path.info.id = id;
        _path.info.map_id = map_id_;
        _path.info.pose_num = static_cast<int>(path_pose_.size());
        _path.poses = path_pose_;
        paths_ = p_npu_server_->GetPaths(map_id_);
        paths_.push_back(_path);
        p_npu_server_->SetPaths(map_id_, paths_);
        path_pose_.clear();
        path_pose_.resize(0);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetPathPoint(unsigned char *data)
{
    FUNC_INFO("");
    try{
        Pose3D _pose = p_npu_server_->GetActPose();
        path_pose_.push_back(_pose);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetInitPose(unsigned char *data)
{
    FUNC_INFO("");
    try{
        Pose3D pose = *((Pose3D*)data);
        p_npu_server_->SetInitPose(pose);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMSetInitArea(unsigned char *data)
{
    FUNC_INFO("");
    try{
        InitPoseArea area = *((InitPoseArea*)data);
        p_npu_server_->SetInitPoseArea(area);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMPauseTask(unsigned char *data)
{
    FUNC_INFO("");
    try{
        p_npu_server_->PauseTask();
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMContinueTask(unsigned char *data)
{
    FUNC_INFO("");
    try{
        p_npu_server_->ContinueTask();
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMCancelTask(unsigned char *data)
{
    FUNC_INFO("");
    try{
        p_npu_server_->CancelTask();
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMGotoPose(unsigned char *data)
{
    FUNC_INFO("");
    try{
        Pose3D pose = *((Pose3D*)data);
        p_npu_server_->GotoPose(pose);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMGotoStation(unsigned char *data)
{
    FUNC_INFO("");
    try{
        string cmd = (char*)data;
        int index = cmd.find(",",0);
        int last_index = 0;
        string map_id = cmd.substr(index, index - last_index);
        last_index = index;
        index = cmd.find(",",index + 1);
        string station_id = cmd.substr(last_index + 1,index - last_index - 1 );
        p_npu_server_->GotoStation(map_id, station_id);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMFollowTempPath(unsigned char *data)
{
    FUNC_INFO("");//TODO
    try{
        Pose3DList poses;
        p_npu_server_->FollowTempPath(poses);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMFollowPath(unsigned char *data)
{
    FUNC_INFO("");
    try{
        string path_id = (char*)data;
        p_npu_server_->FollowPath(map_id_, path_id);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMPlanCoveragePath(unsigned char *data)
{
    FUNC_INFO("");
    try{
        Point3DList vertices;
        p_npu_server_->PlanCoveragePath(vertices);
        return true;
    } catch(exception &e){
        return false;
    }
}

bool FuncMap::LMGotoGeoPose(unsigned char *data)
{
    FUNC_INFO("");
    try{
        GeoPose pose = *((GeoPose *)data);
        p_npu_server_->GotoGeoPose(pose);
        return true;
    } catch(exception &e){
        return false;
    }
}


/*===========================================================*/

short FuncMap::NormalLampStatus()
{
//    FUNC_ERROR(" ");
    short lamp = 0;
    if(safe_button_is_on_){
        lamp |= 1;
    } else{
        lamp |= 0;
    }
    if(d20_1_on_){
        lamp |= 2;
    }
    if(d20_2_on_){
        lamp |= 4;
    }
    FUNC_DEBUG("lamp status is %d",lamp);
    return lamp;
}

short FuncMap::GetRobotState()//40001
{
    return com_status_;
}

short FuncMap::GetCurrentMapId()//40004
{
    short _id = map_id_no_;
    return _id;
}

short FuncMap::GetActPose()
{
    return 0;
}

short FuncMap::GetNaviStatus()//40010
{
    short _navi_status = 0;
    _navi_status |= is_active_;
    _navi_status |= is_forward_;
    _navi_status |= is_backward_;
    _navi_status |= is_turn_left_;
    _navi_status |= is_turn_right_;
    _navi_status |= GetStationSettingDoneStatus();
    _navi_status |= GetSaveMapDoneStatus();
    return _navi_status;
}

short FuncMap::GetStationSettingDoneStatus()//40010.9 TODO
{
    if(station_setting_done_){
        station_setting_done_ = false;
        return 512;
    } else{
        return 0;
    }
}
short FuncMap::GetSaveMapDoneStatus()//40010.10 TODO
{
    if(map_save_done_){
        return 1024;
    } else{
        return 0;
    }
}
////end

short FuncMap::GetActLineSpd()//40011
{
    short _line_vel = static_cast<int>(cmd_line_vel_ / 0.4) * 1000;
    return _line_vel;
}

short FuncMap::GetActAngSpd()//40012
{
    short _ang_vel = static_cast<int>(cmd_ang_vel_ / 0.4) * 1000;
    return _ang_vel;
}

short FuncMap::GetAngle()//40013
{
    try{
        act_pose_ = p_npu_server_->GetActPose();
        short angle = static_cast<short>(act_pose_.yaw / M_PI * 180);
        return angle;
    } catch(exception& e){
        return 0;
    }
}

short FuncMap::GetLeftMotorCurrent()//40017
{
    short current = static_cast<short>(left_motor_current_ * 100);
    return current;
}

short FuncMap::GetRightMotorCurrent()//40018
{
    short current = static_cast<short>(right_motor_current_ * 100);
    return current;
}

short FuncMap::GetCurrentGoalId()//40019
{
    if(is_task_goal_){
        boost::unique_lock<boost::mutex> lock(goal_cb_mutex_);
        goal_id_ = task_goal_id_;
        lock.unlock();
    } else{
        goal_id_ = normal_goal_id_;
    }
    return goal_id_;
}

short FuncMap::GetSumOfStation()//40020
{
    try{
        FUNC_API_DEBUG("GetSumOfStation()");
        StationList list = p_npu_server_->GetStations(map_id_);
        return static_cast<short>(list.size());
    } catch(exception& e){
        FUNC_API_DEBUG("StartTask() failed call npusever api");
        return 0;
    }
}

short FuncMap::SetRobotState(unsigned char *data)
{
    FUNC_API_DEBUG("SetRobotState()");
    static char state[2];

    static short last_cmd = 0;
    unsigned short cmd = Byte2UShort(data[0],data[1]);
    FUNC_DEBUG("cmd is %d",cmd);
    string map_name = "0" + std::to_string(static_cast<int>(p_npu_server_->GetMapInfos().size()));

    switch (static_cast<unsigned int>(cmd)) {
    case STARTNAVITASK:
    {
        if(!StartTask()){
            return last_cmd;
        }
        break;
    }
    case MANUALCONTROL:{
        if(!ManualControl()){
            return last_cmd;
        }
        break;
    }
    case SETCHARGEPOSE:{
        FUNC_API_DEBUG("SETCHARGEPOSE");//TODO
        break;
    }
    case STARTCHARGE:{
        FUNC_API_DEBUG("STARTCHARGE");//TODO
        break;
    }
    case CHARGEDONE:{
        FUNC_API_DEBUG("CHARGEDONE");//TODO
        break;
    }
    case STARTTEACHMODE:{
        FUNC_API_DEBUG("STARTTEACHMODE");
        if(!teach_mode_){
            teach_mode_ = true;
        }
        break;
    }
    case SETTASKPOSE:{
        if(!SetTaskPose(last_cmd)){
            return last_cmd;
        }
        break;
    }
    case STOPTEACHMODE:{
        if(!StopTeachMode()){
            return last_cmd;
        }
        break;
    }
    case RESETNPU:{
        FUNC_DEBUG("RESETNPU");//TO DO
        break;
    }
    case REACHEDCHARGE:{
        FUNC_DEBUG("REACHEDCHARGE");//TO DO
        break;
    }
    case STARTP2PNAVI:{
        if(SelectMap() && StartNavi(navi_mode_)){
            FUNC_DEBUG("starting p2p navi......");
        } else{
            FUNC_API_ERROR("SelectMap or start p2p navi failed");
            return last_cmd;
        }
        break;
    }
    case STARTPFNAVI:{
        if(SelectMap() && StartNavi(navi_mode_)){
            FUNC_INFO("starting pf navi......");
        } else{
            FUNC_API_ERROR("SelectMap or start pf navi failed");
            return last_cmd;
        }
        break;
    }
    case STOPNAVI:{
        try{
            p_npu_server_->StopNavi();
            FUNC_DEBUG("stop navi......");
        } catch(exception& e){
            FUNC_API_ERROR("failed to stop navi");
            return last_cmd;
        }
        break;
    }
    case STARTSLAM:{
        try{
            map_save_done_ = false;
            p_npu_server_->StartSlam(slam_mode_);
            FUNC_DEBUG("starting slam......");
        } catch(exception& e){
            FUNC_API_ERROR("starting slam failed");
            return last_cmd;
        }
        break;
    }
    case STOPSLAMSAVEMAP:{
        try{
            p_npu_server_->StopSlam(map_name);
            map_save_done_ = true;
            FUNC_DEBUG("stop slam and save map......");
        } catch(exception& e){
            return last_cmd;
        }
        break;
    }
    case STOPSLAMONLY:{
        try{
            map_save_done_ = true;
            p_npu_server_->StopSlam("");
            FUNC_DEBUG("stop slam without save......");
        } catch(exception& e){
            return last_cmd;
        }
        break;
    }
    default:{
        FUNC_DEBUG("Instruction error, not found");
        return 0;
    }
    }
    last_cmd = cmd;
    com_status_ = cmd;
    FUNC_DEBUG("set robot state done return current state");
    return cmd;
}
///set robot state func 40001

bool FuncMap::StartTask()//40001.0
{
    try{
        FUNC_API_DEBUG("StartTask");
        is_task_goal_ = true;
        p_npu_server_->ExecuteTask(navi_task_list_);
        return true;
    } catch(exception& e){
        FUNC_API_DEBUG("failed call npusever api");
        return false;
    }
}

bool FuncMap::ManualControl()//40001.1
{
    try{
        FUNC_API_DEBUG("ManualControl");
        p_npu_server_->CancelTask();
        return true;
    } catch(exception& e){
        FUNC_API_DEBUG("ManualControl() failed call npusever api");
        return false;
    }
}

bool FuncMap::SetTaskPose(int last_cmd)//40001.6
{
    try{
        FUNC_API_DEBUG("SetTaskPose(%d)", last_cmd);
        if(last_cmd == SETTASKPOSE){
            FUNC_DEBUG("This point has been added");
            return false;
        }
        if(teach_mode_){
            station_list_ = p_npu_server_->GetStations(map_id_);
            CreatAction();
            navi_task_action_list_.push_back(action_);
            return true;
        } else{
            FUNC_DEBUG("current state is not teach mode");
            return false;
        }
    } catch(exception& e){
        FUNC_API_DEBUG("SetTaskPose(%d) is failed call npusever api", last_cmd);
    }
}

bool FuncMap::StopTeachMode()
{
    try{
        FUNC_API_DEBUG("StopTeachMode()");
        if(teach_mode_){
            navi_task_list_ = p_npu_server_->GetTaskList(map_id_);
            static int _task_id = static_cast<int>(navi_task_list_.size());
            teach_mode_ = false;
            task_info_.action_list = navi_task_action_list_;
            task_info_.map_id = map_id_;
            task_info_.task_id = "0" + std::to_string(_task_id);

            Task _task;
            _task.enb_taskloop = false;
            _task.info = task_info_;
            _task.task_loop_times = 0;
            navi_task_list_.push_back(_task);
            p_npu_server_->SetTasks(map_id_,navi_task_list_);
            _task_id++;

            navi_task_action_list_.clear();
            navi_task_list_.clear();

            return true;
        } else{
            FUNC_DEBUG("current state is not teach mode");
            return false;
        }
    } catch(exception& e){
        FUNC_API_WARN("failed to stop teachmode");
        return false;
    }

}
///end

short FuncMap::SafeButtonStatus(unsigned char *data)
{
    FUNC_DEBUG("data[0] = %02X, data[1] = %02X",data[0], data[1]);
    FUNC_DEBUG("data[0] & 8 = %02X, data[1] & 8 = %02X",data[0] & 8, data[1] & 8);
    FUNC_DEBUG("data[0] & 40 = %02X, data[1] & 40 = %02X",data[0] & 0x40, data[1] & 0x40);
    FUNC_DEBUG("data[0] & 80 = %02X, data[1] & 80 = %02X",data[0] & 0x80, data[1] & 0x80);
    FUNC_DEBUG("data[0] & 10 = %02X, data[1] & 10 = %02X",data[0] & 0x10, data[1] & 0x10);
    FUNC_DEBUG("data[0] & 4 = %02X, data[1] & 4 = %02X",data[0] & 0x04, data[1] & 0x04);
    FUNC_DEBUG("data[0] & 8 = %02X, data[1] & 8 = %02X",data[0] & 8, data[1] & 8);
    geometry_msgs::Twist t;
    try{
        if((data[1] & 0x04) == 0x04){
            p_npu_server_->Shutdown();
        }
        if((data[1] & 0x04) == 0x04){
            p_npu_server_->Reboot();
        }
        if((data[0] & 0x01) == 0x01){
            FUNC_DEBUG("level is 1");
            ang_gear_level_ = 1.0;
        } else if((data[0] & 0x02) == 0x02){
            ang_gear_level_ = 0.3;
            FUNC_DEBUG("level is 0.3");
        } else{
            FUNC_DEBUG("level is 0.6");
            ang_gear_level_ = 0.6;
        }
        std_msgs::Bool msg;
        if((data[1] & 0x01) == 0x01){
            FUNC_INFO("start enb motor");
            if(!safe_button_is_on_){
                msg.data = false;
                motor_enb_pub_.publish(msg);
                safe_button_is_on_ = true;
            }
            d20_2_on_ = true;
        } else if((data[1] & 0x08) == 0x08 /*&& (data[1] & 0x80) == 0x00*/){
            FUNC_INFO("stop enb motor");
            if(safe_button_is_on_){
                msg.data = true;
                motor_enb_pub_.publish(msg);
                safe_button_is_on_ = false;
            }
            ////stop enb motor
        }
        if((data[1] & 0x08) == 0x08 || (data[1] & 0x02) == 0x02 || (data[1] & 0x04) == 0x04){
            safe_button_is_on_ = false;
            d20_2_on_ = false;
        }
        if((data[1] & 0x40) == 0x00 && (data[1] & 0x80) == 0x00){

        } else if((data[1] & 0x40) == 0x40 && (data[1] & 0x80) == 0x00){
            ///pub manual mode stop voice/follow and gesture
            FUNC_INFO("stop all");
            msg.data = false;
            voice_enb_pub_.publish(msg);
            follow_enb_pub_.publish(msg);
            gesture_enb_pub_.publish(msg);
            t.linear.x = 0;
            t.angular.z = 0;
            cmd_pub_.publish(t);
    //        if(follow_started_){
                system("bash /npu.x86_64/script/zx/follow_stop.sh");
                follow_started_ = false;
    //        }
    //        if(myo_started_){
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/myo_stop.sh");
                myo_started_ = false;
    //        }
    //        if(voice_started_){
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/voice_stop.sh");
                voice_started_ = false;
    //        }
            d20_1_on_ = false;
        } else if((data[1] & 0x40) == 0x00 && (data[1] & 0x80) == 0x80){
            FUNC_INFO("start follow");
            d20_1_on_ = true;
            msg.data = true;
            follow_enb_pub_.publish(msg);
            if(!follow_started_){
                t.linear.x = 0;
                t.angular.z = 0;
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/voice_stop.sh");
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/myo_stop.sh");
                voice_started_ = false;
                myo_started_ = false;
                stringstream ss;
                ss << "bash /npu.x86_64/script/zx/npu_run_follow.sh \""
                   << "\"&\n";
                system(ss.str().c_str());
                follow_started_ = true;
            }
        }
        if((data[0] & 0x04) == 0x04){
            FUNC_INFO("start voice");
            d20_1_on_ = true;
            msg.data = true;
            voice_enb_pub_.publish(msg);
            if(!voice_started_){
                t.linear.x = 0;
                t.angular.z = 0;
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/myo_stop.sh");
                myo_started_ = false;
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/follow_stop.sh");
                follow_started_ = false;
                stringstream ss;
                ss << "bash /npu.x86_64/script/zx/npu_run_voice.sh \""
                   << "\"&\n";
                system(ss.str().c_str());
                voice_started_ = true;
                stringstream sss;
                sss << "bash /npu.x86_64/script/zx/npu_run_follow.sh \""
                   << "\"&\n";
                system(sss.str().c_str());
                follow_started_ = true;
            }
            ///start voice
        } else{
            d20_1_on_ = false;
            msg.data = false;
            voice_enb_pub_.publish(msg);
            FUNC_DEBUG("stop voice");
            if(voice_started_){
                t.linear.x = 0;
                t.angular.z = 0;
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/voice_stop.sh");
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/follow_stop.sh");
                voice_started_ = false;
                follow_started_ = false;
            }
            ///stop voice
        }
        if(/*(data[0] & 0x10) == 0x10*/(data[0] & 0x08) == 0x08){
            FUNC_INFO("start gesture");
            d20_1_on_ = true;
            msg.data = true;
            gesture_enb_pub_.publish(msg);
            if(!myo_started_){
                t.linear.x = 0;
                t.angular.z = 0;
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/voice_stop.sh");
                voice_started_ = false;
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/follow_stop.sh");
                follow_started_ = false;
                stringstream ss;
                ss << "bash /npu.x86_64/script/zx/npu_run_myo.sh \""
                   << "\"&\n";
                system(ss.str().c_str());
                myo_started_ = true;
                stringstream sss;
                sss << "bash /npu.x86_64/script/zx/npu_run_follow.sh \""
                   << "\"&\n";
                system(sss.str().c_str());
                follow_started_ = true;

            }
            ///start gesture
        } else{
            FUNC_DEBUG("stop gesture");
            d20_1_on_ = false;
            msg.data = false;
            gesture_enb_pub_.publish(msg);
            if(myo_started_){
                t.linear.x = 0;
                t.angular.z = 0;
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/myo_stop.sh");
                myo_started_ = false;
                cmd_pub_.publish(t);
                system("bash /npu.x86_64/script/zx/follow_stop.sh");
                follow_started_ = false;
            }
            ///stop gesture
        }
    } catch(exception& e){
        FUNC_API_ERROR("");
    }
    return 0;
}

short FuncMap::SetSpeedGearLevel(unsigned char *data)
{
    FUNC_DEBUG("data[0] = %02X, data[1] = %02X",data[0], data[1]);
    FUNC_DEBUG("data[0] & 1 = %02X, data[1] & 2 = %02X",data[0] & 1, data[1] & 2);
    if(((data[1] & 1) == 0x01) && ((data[1] & 2) == 0x00)){
        speed_gear_level_ = 0.238;
        FUNC_DEBUG("speed_gear_level_ = %.2f", speed_gear_level_);
    } else if(((data[1] & 1) == 0x00) && ((data[1] & 2) == 0x00)){
        speed_gear_level_ = 0.636;
        FUNC_DEBUG("speed_gear_level_ = %.2f", speed_gear_level_);
    } else if(((data[1] & 1) == 0x00) && ((data[1] & 2) == 0x02)){
        speed_gear_level_ = 1;
        FUNC_DEBUG("speed_gear_level_ = %.2f", speed_gear_level_);
    }

    if((data[1] & 0x04) == 0x04){
        std_msgs::Bool power_on;
        power_on.data = false;
        motor_power_pub_.publish(power_on);
    } else{
        std_msgs::Bool power_on;
        power_on.data = true;
        motor_power_pub_.publish(power_on);
    }
    return 0;
}

short FuncMap::SetLineSpd(unsigned char *data) //40002
{
    if(!safe_button_is_on_){
        return 0;
    }
    short linevel = Byte2Short(data[0], data[1]);
    FUNC_DEBUG("cmd linevel is %d",linevel);
    float k = 0.0;
    if(linevel > ROCKERFORWARDSTART){
        linevel -= ROCKERFORWARDSTART;
        k = RANGE((static_cast<float>(linevel)/ROCKERRANGE),0,1);
    } else if(linevel < ROCKERBACKWARDSTART){
        linevel -= ROCKERBACKWARDSTART;
        k = RANGE((static_cast<float>(linevel)/ROCKERRANGE),-1,0);
    } else{
        linevel = 0;
        k = 0;
    }
    //Divide by 1.25(Unit is meter) is to become full speed
    line_vel_ = (k * max_line_vel_ * speed_gear_level_)/1.25;
    is_forward_ = (line_vel_ > 1e-6)?2:0; //40010.1
    is_backward_ = (line_vel_ > 1e-6)?0:4; //40010.2
    FUNC_DEBUG("speed_gear_level_ = %.2f", speed_gear_level_);
    FUNC_DEBUG("cal k = %.2f", k);
    FUNC_DEBUG("cal line_vel = %d", linevel);
    FUNC_DEBUG("final line_vel = %.2f", line_vel_);
    FUNC_DEBUG("max line_vel = %.2f", max_line_vel_);
    set_cmd_vel_ = true;
    return line_vel_;
}

short FuncMap::SetAngSpd(unsigned char *data) //40003
{
    if(!safe_button_is_on_){
            return 0;
    }
    short angvel = Byte2Short(data[0], data[1]);
    FUNC_DEBUG("cmd angvel is %d",angvel);
    float k = 0.0;
    if(angvel > ROCKERLEFTSTART){
        angvel -= ROCKERLEFTSTART;
        k = RANGE((static_cast<float>(angvel)/ROCKERRANGE),0,1);
    } else if(angvel < ROCKERRIGHTSTART){
        angvel -= ROCKERRIGHTSTART;
        k = RANGE((static_cast<float>(angvel)/ROCKERRANGE),-1,0);
    } else{
        angvel = 0;
        k = 0;
    }
    //Divide by 1.25(Unit is meter) is to become full speed
    //0.741(Unit is meter) Is the wheel spacing
    ang_vel_ = k * max_ang_vel_ * speed_gear_level_ * ang_gear_level_/1.25/0.741;
    is_turn_left_ = (ang_vel_ > 1e-6)?8:0;//40010.3
    is_turn_right_ = (ang_vel_ < 1e-6)?16:0;//40010.4
    FUNC_DEBUG("speed_gear_level_ = %.2f", speed_gear_level_);
    FUNC_DEBUG("ang_gear_level_ = %.2f", ang_gear_level_);
    FUNC_DEBUG("cal k = %.2f", k);
    FUNC_DEBUG("cal ang_vel_ = %d", angvel);
    FUNC_DEBUG("final ang_vel_ = %.2f", ang_vel_);
    FUNC_DEBUG("max ang_vel_ = %.2f", max_ang_vel_);
    set_cmd_vel_ = true;
    return ang_vel_;
}

void FuncMap::SetManualSpd()
{
    try{
        int times = 0;
        FUNC_DEBUG("SetManualSpd()");
        if(fabs(line_vel_ * ang_vel_) < 1e-6){
            is_active_ = 0;
        } else{
            is_active_ = 1;
        }
        if(set_cmd_vel_){
            p_npu_server_->SetManualVel(line_vel_,ang_vel_);
            set_cmd_vel_ = false;
        } else{
            times++;
        }
        if(times == 10){
            line_vel_ = 0;
            ang_vel_ = 0;
            times = 0;
        }
    } catch(exception& e){
        FUNC_API_DEBUG("SetManualSpd() is failed call npusever api");
    }
}

short FuncMap::SetCurrentMapId(unsigned char *data)//40004
{
    unsigned short _id = Byte2UShort(data[0],data[1]);
    if(SetMapId(static_cast<int>(_id))){
        return _id;
    } else{
        return 0;
    }
}

short FuncMap::GotoStation(unsigned char *data)//40005
{
    try{
        FUNC_DEBUG("GotoStation()");
        is_task_goal_ = true;
        unsigned short _id = Byte2UShort(data[0],data[1]);
        if(_id < station_list_.size()){
            p_npu_server_->GotoPose(station_list_[_id].pose);
        } else{
            return 0;
        }
        normal_goal_id_ = static_cast<int>(_id);
        return _id;
    } catch(exception& e){
        FUNC_API_DEBUG("GotoStation() failed call npusever api");
        return 0;
    }
}

bool FuncMap::SetMapId(int id)
{
    try{
        FUNC_DEBUG("SetMapId()");
        MapInfoList map_info_list = p_npu_server_->GetMapInfos();
        if(id >= static_cast<int>(map_info_list.size())){
            FUNC_API_DEBUG("no such of map");
            return false;
        }
        map_id_no_ = id;
        map_id_ = map_info_list[id].id;
        FUNC_API_DEBUG("set map %d is :%s", id, map_id_.c_str());
        return true;
    } catch(exception& e){
        FUNC_API_DEBUG("SetMapId() failed call npusever api");
        return false;
    }
}

bool FuncMap::StartNavi(NaviMode mode)
{
    try{
        FUNC_DEBUG("StartNavi()");
        p_npu_server_->StartNavi(mode);
        return true;
    } catch(exception& e){
        FUNC_API_DEBUG("StartNavi() failed call npusever api");
        return false;
    }
}

bool FuncMap::SelectMap()
{
    try{
        p_npu_server_->SelectMap(map_id_);
        return true;
    } catch(exception& e){
        FUNC_API_DEBUG("SelectMap() failed call npusever api");
        return false;
    }
}

void FuncMap::CreatStation()
{
    try{
        FUNC_DEBUG("CreatStation()");
        act_pose_ = p_npu_server_->GetActPose();
        station_list_ = p_npu_server_->GetStations(map_id_);
        static int num = static_cast<int>(station_list_.size());
        station_.info.map_id = map_id_;
        station_.info.type = StationType::START;
        station_.info.id = "0"+std::to_string(num);
        station_.pose = act_pose_;
        station_list_.push_back(station_);
        p_npu_server_->SetStations(map_id_,station_list_);
        num++;
        station_setting_done_ = true;
    } catch(exception& e){
        station_setting_done_ = false;
        FUNC_API_DEBUG("CreatStation() failed call npusever api");
    }
}

bool FuncMap::CreatStation(string id)
{
    try{
        FUNC_DEBUG("(id)");
        act_pose_ = p_npu_server_->GetActPose();
        station_list_ = p_npu_server_->GetStations(map_id_);
        static int num = static_cast<int>(station_list_.size());
        station_.info.map_id = map_id_;
        station_.info.type = StationType::START;
        station_.info.id = id;
        station_.pose = act_pose_;
        station_list_.push_back(station_);
        p_npu_server_->SetStations(map_id_,station_list_);
        num++;
        station_setting_done_ = true;
        return true;
    } catch(exception& e){
        station_setting_done_ = false;
        FUNC_API_DEBUG("CreatStation() failed call npusever api");
        return false;
    }
}

bool FuncMap::DeletStation(string id)
{
    try{
        FUNC_DEBUG("(id)");
        int index = 0;
        station_list_ = p_npu_server_->GetStations(map_id_);
        p_npu_server_->SetStations(map_id_,station_list_);
        for(int i = 0;i < station_list_.size();i++){
            if(station_list_[i].info.id == id){
                index = i;
                break;
            }
        }
        StationList::iterator it = station_list_.begin() + index;
        station_list_.erase(it);
        p_npu_server_->SetStations(map_id_, station_list_);
        return true;
    } catch(exception& e){
        station_setting_done_ = false;
        FUNC_API_DEBUG("CreatStation() failed call npusever api");
        return false;
    }
}


void FuncMap::CreatAction()
{
    FUNC_DEBUG("CreatAction()");
    CreatStation();
    action_.action_name = actionname::navi;
    action_.action_args = station_.info.id;
    action_.duration = 4;
}

short FuncMap::Byte2Short(unsigned char h, unsigned char l)
{
    FUNC_DEBUG("start");
    short n = 0;
    short temp = 0;
    temp = temp | h;
    temp = temp << 8;
    n = n | temp;
    temp = 0;
    temp = temp | l;
    n = n | temp;
    FUNC_DEBUG("done");
    return n;
}

unsigned short FuncMap::Byte2UShort(unsigned char h, unsigned char l)
{
    unsigned short n = 0;
    unsigned short temp = 0;
    temp = temp | h;
    temp = temp << 8;
    n = n | temp;
    temp = 0;
    temp = temp | l;
    n = n | temp;
    return n;
}

}//namespace funcmap
}//namespace wizrobo
