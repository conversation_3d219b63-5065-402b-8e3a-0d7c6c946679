#include <iostream>  
#include <fstream>
#include <stdio.h>  
#include <sys/socket.h>  
#include <unistd.h>  
#include <sys/types.h>  
#include <netdb.h>  
#include <netinet/in.h>  
#include <arpa/inet.h>  
#include <string>  
#include <sstream>
#include <Ice/Ice.h>
#include <npuice_api.h>


using namespace std; 

/************ get ip ****************/

::wizrobo_npu::NpuIcePrx NPUICE = 0;
Ice::CommunicatorPtr ic;
Ice::ObjectPrx base;
stringstream ss;
const char *c;

#define NPU_INFO(s,...) fprintf(stdout, "[NPU_INFO]-> "s, ##__VA_ARGS__)
#define NPU_ERROR(s,...) fprintf(stderr, "[NPU_ERROR]-> "s, ##__VA_ARGS__)


void ImportMapFile(string& file_name)
{
    int length = 0;
    ifstream _input("./"+file_name, ios::in|ios::binary);
    _input.seekg(0, ios::end);
    length = _input.tellg();
    _input.seekg(0, ios::beg);
    wizrobo_npu::ZipFile _file;
    _file.resize(length);
    char *_buffer = new char[_file.size()];
    _input.read(_buffer, length);
    for (int i=0;i<_file.size();i++) {
        _file[i] = _buffer[i];
    }
    _input.close();
    delete [] _buffer;
    NPUICE->ImportMapFile(_file, file_name);
}

void ExportMapFile(string& file_name)
{
    wizrobo_npu::ZipFile _file = NPUICE->ExportMapFile(file_name);
    char *_buffer = new char[_file.size()];
    ofstream _output("./"+file_name, ios::out|ios::binary);
    printf("size: %d\n", static_cast<int>(_file.size()));
    for (int i=0;i<_file.size();i++) {
        _buffer[i] = _file[i];
    }
    _output.write(_buffer, _file.size());
    _output.close();
    delete [] _buffer;
}

void TestCase011(int argc, char *argv[])
{
    if (argc != 3) {
        return;
    }
    string map_name(argv[2]);
    string pgm = map_name + ".pgm";
    string yaml = map_name + ".yaml";
    ImportMapFile(pgm);
    ImportMapFile(yaml);
    return;
}

void TestCase010(int argc, char *argv[])
{
    if (argc != 3) {
        return;
    }
    string map_name(argv[2]);
    string pgm = map_name + ".pgm";
    string yaml = map_name + ".yaml";
    ExportMapFile(pgm);
    ExportMapFile(yaml);
    return;
}

void TestCase009()
{
    NPUICE->StartNavi(wizrobo_npu::P2P_NAVI);

    return;
}

void TestCase008()
{
    NPUICE->StartNavi(wizrobo_npu::P2P_NAVI);

    return;
}

void TestCase007()
{
    ::wizrobo_npu::ImgMap m = NPUICE->GetCurrentImgMap();

    cout << m.info.id << endl;

    return;
}

void TestCase006()
{
    NPUICE->StartNavi(::wizrobo_npu::NaviMode::P2P_NAVI);
    return;
}

void TestCase005()
{
    NPUICE->GetCurrentPath();
    return;
}

void TestCase004()
{
    NPUICE->GetPaths("map1");
    return;
}

void TestCase003()
{
    NPUICE->SelectMap("123");
    return;
}

void TestCase002()
{
    ::wizrobo_npu::Map2D map2d = NPUICE->GetCurrentMap();
    if (map2d.info.id == "")
    {
        cout << "id = \"\"" << endl;
        return;
    }
    cout << "id             : " << map2d.info.id << endl;
    cout << "creation_time  : " << map2d.info.creation_time << endl;
    cout << "resolution     : " << map2d.info.resolution << endl;
    cout << "dimension.x    : " << map2d.info.dimension.x << endl;
    cout << "dimension.y    : " << map2d.info.dimension.y << endl;
    cout << "dimension.z    : " << map2d.info.dimension.z << endl;
    cout << "offset.x       : " << map2d.info.offset.x << endl;
    cout << "offset.y       : " << map2d.info.offset.y << endl;
    cout << "offset.z       : " << map2d.info.offset.z << endl;
    cout << "thumbnail.widt : " << map2d.info.thumbnail.width << endl;
    cout << "thumbnail.heigh: " << map2d.info.thumbnail.height << endl;
    cout << "thumbnail.data: " << map2d.info.thumbnail.data.size() << endl;
    cout << "station_num    : " << map2d.info.station_num << endl;
    cout << "path_num       : " << map2d.info.path_num << endl;
    cout << "mat.width      : " << map2d.mat.width << endl;
    cout << "mat.height     : " << map2d.mat.height << endl;
    cout << "mat.data       : " << map2d.mat.data.size() << endl;
    cout << "stations       : " << endl;

    for(int i=0;i<map2d.stations.size();i++)
    {
        cout << "map_id   : " << map2d.stations[i].info.map_id << endl;
        cout << "id       : " << map2d.stations[i].info.id << endl;
        cout << "type     : " << map2d.stations[i].info.type << endl;
        cout << "artag_id : " << map2d.stations[i].info.artag_id << endl;
        cout << "pose.x   : " << map2d.stations[i].pose.x << endl;
        cout << "pose.y   : " << map2d.stations[i].pose.y << endl;
        cout << "pose.z   : " << map2d.stations[i].pose.z << endl;
        cout << "pose.roll   : " << map2d.stations[i].pose.roll << endl;
        cout << "pose.pitch   : " << map2d.stations[i].pose.pitch << endl;
        cout << "pose.yaw   : " << map2d.stations[i].pose.yaw << endl;
    }

    for(int i=0;i<map2d.paths.size();i++)
    {
        cout << "map_id   : " << map2d.paths[i].info.map_id << endl;
        cout << "id       : " << map2d.paths[i].info.id << endl;
        cout << "length   : " << map2d.paths[i].info.length << endl;
        cout << "pose_num : " << map2d.paths[i].info.pose_num << endl;
        for (int j=0;j<map2d.paths[i].poses.size();j++)
        {
            cout << "pose.x   : " << map2d.paths[i].poses[j].x << endl;
            cout << "pose.y   : " << map2d.paths[i].poses[j].y << endl;
            cout << "pose.z   : " << map2d.paths[i].poses[j].z << endl;
            cout << "pose.roll   : " << map2d.paths[i].poses[j].roll << endl;
            cout << "pose.pitch   : " << map2d.paths[i].poses[j].pitch << endl;
            cout << "pose.yaw   : " << map2d.paths[i].poses[j].yaw << endl;
        }
    }
}

void TestCase001()
{
    ::wizrobo_npu::MapInfoList m_list = NPUICE->GetMapInfos();
    for(int i=0;i<m_list.size();i++)
    {
        cout << endl;
        cout << "id             : " << m_list[i].id << endl;
        cout << "creation_time  : " << m_list[i].creation_time << endl;
        cout << "resolution     : " << m_list[i].resolution << endl;
        cout << "dimension.x    : " << m_list[i].dimension.x << endl;
        cout << "dimension.y    : " << m_list[i].dimension.y << endl;
        cout << "dimension.z    : " << m_list[i].dimension.z << endl;
        cout << "offset.x       : " << m_list[i].offset.x << endl;
        cout << "offset.y       : " << m_list[i].offset.y << endl;
        cout << "offset.z       : " << m_list[i].offset.z << endl;
        cout << "station_num    : " << m_list[i].station_num << endl;
        cout << "path_num       : " << m_list[i].path_num << endl;
        cout << "thumbnail.widt : " << m_list[i].thumbnail.width << endl;
        cout << "thumbnail.heigh: " << m_list[i].thumbnail.height << endl;
        cout << "thumbnail.data : " << m_list[i].thumbnail.data.size() << endl;
//        cout << "thumbnail.data : ";
//        for (int j=0;j<m_list[i].thumbnail.data.size();j++)
//        {
//            if (j % m_list[i].thumbnail.width == 0)
//            {
//                cout << endl;
//            }
//            cout << (int)(m_list[i].thumbnail.data[j]) << " ";
//        }
        cout << endl;
    }
}

int main(int argc, char *argv[])
{
    int cmd;
    cmd = atoi(argv[1]);
    setvbuf(stdout, NULL, _IONBF, 0);   
    fflush(stdout);   

    struct sockaddr_in addr; 
    bzero(&addr, sizeof(struct sockaddr_in));    
    addr.sin_family =AF_INET;  
    addr.sin_addr.s_addr = htonl(INADDR_ANY); 
    addr.sin_port = htons(5188);  

    int sock = -1;  
    if ((sock = socket(AF_INET, SOCK_DGRAM, 0)) == -1)   
    {     
        cout<<"socket error"<<endl;   
        return false;  
    }     

    if(bind(sock,(struct sockaddr *)&(addr), sizeof(struct sockaddr_in)) == -1)   
    {     
        cout<<"bind error..."<<endl;  
        return false;  
    }  

    int len = sizeof(sockaddr_in);  
    char smsg[100] = {0};  
/*
    while(1)  
    {  
        int ret=recvfrom(sock, smsg, 100, 0, (struct sockaddr*)&addr,(socklen_t*)&len);
        cout << smsg << endl;
          
        if(ret<=0)  
        {  
            cout<<"read error...."<<sock<<endl;  
            return 0;
        }  
        break;
    }  

    char serverIp[16];
    for(int i=0;i<15;i++)
    {
        serverIp[i] = *(smsg+6+i);
    } 
    serverIp[16] = 0x00;
    cout << "IP: " << serverIp << endl;
*/
/***********ICE***********/
    int status = 0;
    try
    {
        Ice::PropertiesPtr props = Ice::createProperties();
        props->setProperty("Ice.MessageSizeMax",  "51200");
        cout << "01 " << endl;
        Ice::InitializationData initicedata;

        initicedata.properties = props;
        ic = Ice::initialize(initicedata);
        ss << "Npu:tcp -h ";
        //ss << string(serverIp);
        ss << "127.0.0.1";
        ss << " -p 10190";
        ss << " -t 5000";
        cout << "ss: " << ss.str() << endl;
        c = ss.str().c_str();
        base = ic -> stringToProxy(c);

        NPUICE = ::wizrobo_npu::NpuIcePrx::checkedCast(base);
        cout << "Check case success." << endl;
        if(!NPUICE)
        {
            throw "/Invalib proxy";  
        }
    }
    catch(const Ice::Exception& ex)
    {
        cout << "error 01" << endl;
        cerr << ex << endl;
        return -1;
    }
    catch (const char* msg)
    {
        cout << "error 02" << endl;
        cerr << msg << endl;
        return -2;
    }

   // Init();
    cout << "IceInit is ok" << endl;
    string v = NPUICE->GetServerVersion();
    NPUICE->Connect(v);

    switch (cmd)
    {
    case 1:
        TestCase001();
        break;
    case 2:
        TestCase002();
        break;
    case 3:
        TestCase003();
        break;
    case 4:
        TestCase004();
        break;
    case 5:
        TestCase005();
        break;
    case 6:
        TestCase006();
        break;
    case 7:
        TestCase007();
        break;
    case 8:
        TestCase008();
        break;
    case 9:
        TestCase009();
        break;
    case 10:
        TestCase010(argc, argv);
        break;
    case 11:
        TestCase011(argc, argv);
        break;
    default:
        break;
    }
    ic -> destroy();
    return 0;
}







