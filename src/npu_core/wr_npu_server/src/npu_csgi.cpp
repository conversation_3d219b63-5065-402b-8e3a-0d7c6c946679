#include "npu_csgi.h"

namespace wizrobo_npu {

NpuCsgI::NpuCsgI()
{
    ros::NodeHandle n;
    server_ = n.advertiseService("/csg_transfer_npu", &NpuCsgI::CsgTransferNpu, this);
}

bool NpuCsgI::CsgTransferNpu(wr_npu_msgs::CsgTsNpu::Request &req, wr_npu_msgs::CsgTsNpu::Response &rep)
{
    switch(req.mode) {
    ROS_INFO("res: %d", req.mode);
    case 0:
        if(ConnectNpu()) {
            rep.status = 0;
        } else {
            rep.status = 1;
        }
    case 1:
        if(GetCurrentMapName()) {
            rep.status = 0;
            rep.current_map_name = current_map_name_;
        } else {
            rep.status = 1;
        }
        break;
    case 2:
        if(StartNavi()) {
            rep.status = 0;
        } else {
            rep.status = 1;
        }
        break;
    case 3:
        if(<PERSON>Navi()) {
            rep.status = 0;
        } else {
            rep.status = 1;
        }
        break;
    case 4:
        if(SelectMap(req.switch_map_name)) {
            rep.status = 0;
        } else {
            rep.status = 1;
        }
        break;
    case 5:
        if(GetNpuState()) {
            rep.status = 0;
            rep.npu_state = npu_state_d_;
        } else {
            rep.status = 1;
        }
        break;
    case 6:
        if(CancelTask()) {
            rep.status = 0;
        } else {
            rep.status = 1;
        }
        break;
    default:
        break;
    }
    return true;
}

void NpuCsgI::SetNpuServer(NpuServer* ptr)
{
    p_npu_server = ptr;
    return;
}

bool NpuCsgI::ConnectNpu()
{
    v = p_npu_server->GetServerVersion();
    p_npu_server->Connect(v);
    return true;
}

bool NpuCsgI::GetCurrentMapName()
{
    Map2D map_;
    map_ = p_npu_server->GetMap();
    current_map_name_ = map_.info.id;
    return true;
}

bool NpuCsgI::StartNavi()
{
    p_npu_server->StartNavi(NaviMode::P2P_NAVI);
    return true;
}

bool NpuCsgI::StopNavi()
{
    p_npu_server->StopNavi();
    return true;
}

bool NpuCsgI::SelectMap(string ss)
{
    p_npu_server->SelectMap(ss);
    return true;
}

bool NpuCsgI::GetNpuState()
{
    npu_state_ = p_npu_server->GetNpuState();
    if(npu_state_ == NpuState::IDLE_STATE) {
        npu_state_d_ = 0;
        return true;
    } else if(npu_state_ == NpuState::SLAM_STATE) {
        npu_state_d_ = 1;
        return true;
    } else if(npu_state_ == NpuState::NAVI_STATE) {
        npu_state_d_ = 2;
        return true;
    }
}

bool NpuCsgI::CancelTask()
{
    p_npu_server->CancelTask();
    return true;
}
}
