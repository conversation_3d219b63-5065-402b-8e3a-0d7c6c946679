#include "runtime_status.h"

#define RTS_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("rts", "RuntimeStatus::%s() " fmt, __FUNCTION__,##arg)
#define RTS_INFO(fmt, arg...)  ROS_INFO_NAMED("rts",  "RuntimeStatus::%s() " fmt, __FUNCTION__,##arg)
#define RTS_WARN(fmt, arg...)  ROS_WARN_NAMED("rts",  "RuntimeStatus::%s() " fmt, __FUNCTION__,##arg)
#define RTS_ERROR(fmt, arg...) ROS_ERROR_NAMED("rts", "RuntimeStatus::%s() " fmt, __FUNCTION__,##arg)
#define RTS_FATAL(fmt, arg...) ROS_FATAL_NAMED("rts", "RuntimeStatus::%s() " fmt, __FUNCTION__,##arg)

namespace wizrobo {

RuntimeStatus::RuntimeStatus(int argc, char** argv)
    : is_inited_(false)
    , ptr_spin_thread_(NULL)
    , lidar_data_checker_(wr_npu_msgs::RuntimeStatus::RT0_DATA_LIDAR, STD_SCAN_TOPIC_NAME)
    , imu_data_checker_(wr_npu_msgs::RuntimeStatus::RT0_DATA_IMU, STD_IMU_TOPIC_NAME)
    , encoder_data_checker_(wr_npu_msgs::RuntimeStatus::RT0_DATA_ENCODER, STD_ENCODER_TOPIC_NAME)
    , gps_data_checker_(wr_npu_msgs::RuntimeStatus::RT0_DATA_GPS, STD_GPS_TOPIC_NAME)
    , map_data_checker_(wr_npu_msgs::RuntimeStatus::RT0_DATA_MAP, STD_MAP_TOPIC_NAME)
    , emb_triggered_checker_()
    , bumper_triggered_checker_()
    , entrp_detected_checker_()
    , pf_collision_checker_()
    , keya_checker_()
{

}

void RuntimeStatus::Init()
{
    ros::NodeHandle nh;
    ros::NodeHandle prv_nh("~");
    is_inited_ = false;

    std::string logger_level;
    prv_nh.param<std::string>("logger_level", logger_level, "info");
    if (logger_level.compare("debug") == 0) {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Debug);
    } else if (logger_level.compare("info") == 0) {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Info);
    } else if (logger_level.compare("warn") == 0) {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Warn);
    }

    runtime_status_pub_ = nh.advertise<wr_npu_msgs::RuntimeStatus>("/runtime_status", 1, true);
    std::string voltage_data_topic;
    prv_nh.param<std::string>("voltage_data_topic_", voltage_data_topic, "/voltage_data");
    power_voltage_sub_ = \
        nh.subscribe<wr_npu_msgs::VoltageData>(
            voltage_data_topic, 1, &RuntimeStatus::PowerVoltageCallback, this);

    nh.param<float>(STD_SENSOR_PARAM_NS+"/battery/trigger_level_1_range_m", battery_threshold_, -1.0);
    nh.param<float>(STD_SENSOR_PARAM_NS+"/power_supply/trigger_level_1_range_m", power_threshold_, -1.0);

    if (ptr_spin_thread_ != NULL) {
        ptr_spin_thread_->interrupt();
        ptr_spin_thread_->join();
        delete ptr_spin_thread_;
        ptr_spin_thread_ = NULL;
    }
    ptr_spin_thread_ = new boost::thread(boost::bind(&RuntimeStatus::SpinThreadLoop, this));

    is_inited_ = true;
}

void RuntimeStatus::Run()
{
    if (!is_inited_)
    {
        ROS_ERROR("Not inited");
        return;
    }

    ros::Rate loop_rate(1);
    while (ros::ok())
    {
        runtime_status_msg_.header.stamp = ros::Time::now();
        runtime_status_msg_.header.frame_id = "base_frame";
        runtime_status_msg_.runtime_status_0 = 0;
        runtime_status_msg_.runtime_status_1 = 0;
        runtime_status_msg_.runtime_status_2 = 0;
        runtime_status_msg_.runtime_status_0 |= lidar_data_checker_.GetStatus();
        runtime_status_msg_.runtime_status_0 |= imu_data_checker_.GetStatus();
        runtime_status_msg_.runtime_status_0 |= encoder_data_checker_.GetStatus();
        runtime_status_msg_.runtime_status_0 |= gps_data_checker_.GetStatus();
        runtime_status_msg_.runtime_status_0 |= map_data_checker_.GetStatus();
        runtime_status_msg_.runtime_status_1 |= pf_collision_checker_.GetStatus();
        runtime_status_msg_.runtime_status_1 |= emb_triggered_checker_.GetStatus();
        runtime_status_msg_.runtime_status_1 |= bumper_triggered_checker_.GetStatus();
        runtime_status_msg_.runtime_status_1 |= entrp_detected_checker_.GetStatus();
        if(battery_vol_ < battery_threshold_)
        {
            runtime_status_msg_.runtime_status_1 |= wr_npu_msgs::RuntimeStatus::RT1_BATTERY_LOW;
        }
        runtime_status_msg_.runtime_status_2 = keya_checker_.GetStatus();
        runtime_status_pub_.publish(runtime_status_msg_);
        loop_rate.sleep();
    }
}

void RuntimeStatus::SpinThreadLoop()
{
    ros::spin();
}

void RuntimeStatus::PowerVoltageCallback(const wr_npu_msgs::VoltageData::ConstPtr& msg)
{
    power_vol_ = msg->power_voltage;
    battery_vol_ = msg->battery_voltage;
    return;
}

}

