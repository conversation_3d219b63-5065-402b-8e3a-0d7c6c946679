#include <Ice/Ice.h>
#include <ros/ros.h>

#include "npu_icei.h"
#include "npu_http.h"
#include "npu_comi.h"
#include "npu_csgi.h"

#define NSN_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("NODE", "NpuServerNode::%s() " fmt, __FUNCTION__, ##arg)
#define NSN_INFO(fmt, arg...)  ROS_INFO_NAMED ("NODE", "NpuServerNode::%s() " fmt, __FUNCTION__, ##arg)
#define NSN_WARN(fmt, arg...)  ROS_WARN_NAMED ("NODE", "NpuServerNode::%s() " fmt, __FUNCTION__, ##arg)
#define NSN_ERROR(fmt, arg...) ROS_ERROR_NAMED("NODE", "NpuServerNode::%s() " fmt, __FUNCTION__, ##arg)

using namespace wizrobo_npu;

int RunNpuIceServer(int argc, char* argv[], NpuServer* ptr)
{
    Ice::CommunicatorPtr ic;
    try
    {
        Ice::PropertiesPtr props = Ice::createProperties(argc, argv);
        props->setProperty("Ice.MessageSizeMax",  "51200");
        Ice::InitializationData initicedata;

        initicedata.properties = props;
        ic = Ice::initialize(argc, argv, initicedata);

        Ice::ObjectAdapterPtr adapter = ic->createObjectAdapterWithEndpoints("NpuAdapter", "default -p 10190 -t 1000"); //todo
        Ice::ObjectPtr object = new NpuIceI(argc, argv, ptr);
        adapter->add(object, ic->stringToIdentity("Npu"));
        adapter->activate();

        NSN_INFO("Npu server(ICE) ready!");

        ros::Rate loop_rater(1);
        while(ros::ok() && !ic->isShutdown())
        {
            loop_rater.sleep();
        }
        ic->shutdown();
    }
    catch (const Ice::Exception& e)
    {
        stringstream ss;
        ss << "Exception raised, e: " << e;
        NSN_ERROR("%s", ss.str().c_str());
    }
    if (!ic)
    {
        return -1;
    }
    try
    {
        ic->destroy();
    }
    catch (const Ice::Exception& e)
    {
        NSN_ERROR("Exception raised, e: %s", e.what());
        return -2;
    }
    return 0;
}

int RunNpuHttpServer(int argc, char* argv[], NpuServer* ptr)
{
    NpuHttpI npu_http_i;
    npu_http_i.SetNpuServer(ptr);
    NSN_INFO("Npu server(HTTP) ready!");
    npu_http_i.Run();
    return 0;
}

int RunNpuComServer(int argc, char* argv[], NpuServer* ptr,
                    string port, int baudrate, int databit,
                    string parity, icom_driver::ComDriverType com_type)
{
    wizrobo::NpuComI npu_com_i(port, baudrate, databit, parity, com_type);
    npu_com_i.SetNpuServer(ptr);
    NSN_INFO("Npu server(COM) ready!");
    npu_com_i.Run();
    return 0;
}

int main(int argc, char* argv[])
{
    ros::init(argc, argv, "npu_server_node");
    ros::NodeHandle nh;
    ros::NodeHandle private_nh("~");

    string logger_level;
    private_nh.param<string>("logger_level", logger_level, "debug");
    if (logger_level.compare("debug") == 0)
    {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Debug);
    }
    else if (logger_level.compare("info") == 0)
    {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Info);
    }
    else if (logger_level.compare("warn") == 0)
    {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Warn);
    }

    bool wait_for_param;
    private_nh.param<bool>("wait_for_param", wait_for_param, true);

    int loop_freq_hz = 30, timeout_s = 5;
    int timeout_cnt = loop_freq_hz * timeout_s;
    ros::Rate r(loop_freq_hz);
    bool is_param_loaded = false;
    while (wait_for_param==true && ros::ok() && is_param_loaded==false && timeout_cnt>0)
    {
        nh.param<bool>(STD_RUNTIME_PARAM_NS + "/is_loaded", is_param_loaded, false);
        timeout_cnt--;
        r.sleep();
    }
    if (timeout_cnt <= 0)
    {
        NSN_ERROR("TIMEOUT. Waiting param_server failed, pls check param_server. <#_#> ");
        return -1;
    }

    string server_type;
    string serial_port;
    string parity;
    int baud_rate;
    int data_bit;
    icom_driver::ComDriverType com_type;
    private_nh.param<string>("server_type", server_type, "ice");
    private_nh.param<string>("serial_port", serial_port, "/dev/comi");
    private_nh.param<int>("baud_rate", baud_rate, B19200);
    private_nh.param<int>("data_bit", data_bit, 8);
    private_nh.param<string>("parity", parity, "ODD");
    GetEnumParam<icom_driver::ComDriverType>(private_nh, "com_type", com_type,
                                            icom_driver::ComDriverType::SERIAL_LM);

    NpuServer* ptr = new NpuServer(argc, argv);
    NpuCsgI csgi;
    csgi.SetNpuServer(ptr);

    if (server_type == "http")
    {
        return RunNpuHttpServer(argc, argv, ptr);
    }
    else if(server_type == "com")
    {
        boost::thread* p_http_thread = new boost::thread(boost::bind(
                &RunNpuComServer, argc, argv, ptr,
                serial_port, baud_rate, data_bit, parity, com_type));
        return RunNpuIceServer(argc, argv, ptr);
    }
    else
    {
        boost::thread* p_http_thread = new boost::thread(boost::bind(
                &RunNpuHttpServer, argc, argv, ptr));
        return RunNpuIceServer(argc, argv, ptr);
    }
}
