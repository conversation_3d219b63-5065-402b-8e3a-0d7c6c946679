#include "npu_lzo.h"
#include <iostream>

NpuLzo::NpuLzo(){
	rgbdata = new RgbStruct[RESOLUTION*3]();
	depthdata = new DepthStruct[RESOLUTION*2]();
}

NpuLzo::~NpuLzo(){
	delete rgbdata;
	delete depthdata;
}
int NpuLzo::LzoInit(){
	if (lzo_init() != LZO_E_OK)
	{
        cout<<"lzo init fail"<<endl;
		return 3;
	}
	cout<<"lzo init success!"<<endl;
	return 0;
}
int NpuLzo::LzoCompress( lzo_bytep in,lzo_uint in_len
						,lzo_bytep out,lzo_uintp out_len){
	int r;
	static HEAP_ALLOC(wrkmem, LZO1X_1_MEM_COMPRESS);
	r = lzo1x_1_compress(in, in_len, out, out_len, wrkmem);
	if (r == LZO_E_OK){
		cout<<"Compressed "<<in_len<<" bytes into "<<*out_len<<" bytes"<<endl;
	}
	else{
		cout<<"internal error - compression failed:"<<r<<endl;
		return 2;
	}
	// check for an incompressible block
	if (*out_len >= in_len){
		cout<<"This block contains incompressible data"<<endl;
		return 0;
	}
	return 0;
}

int NpuLzo::LzoDecompress(lzo_bytep in ,lzo_uint in_len
						  	   ,lzo_bytep out,lzo_uintp out_len){
    int ret;
	ret = lzo1x_decompress(in,in_len,out,out_len,NULL);
	if (ret == LZO_E_OK)
		cout<<"Depth Decompressed "<<in_len<<" bytes into "<<*out_len<<" bytes"<<endl;
	else{
		cout<<"internal error - decompression failed:"<<ret<<endl;
		return 1;
	}
	return 0;
}
