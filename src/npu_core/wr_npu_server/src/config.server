#
# The server creates one single object adapter with the name
# "Hello". The following line sets the endpoints for this
# adapter.
#
Hello.Endpoints=tcp -h 192.168.1.180 -p 12345

#
# Warn about connection exceptions
#
Ice.Warn.Connections=1

#
# Network Tracing
#
# 0 = no network tracing
# 1 = trace connection establishment and closure
# 2 = like 1, but more detailed
# 3 = like 2, but also trace data transfer
#
#Ice.Trace.Network=1

#
# Protocol Tracing
#
# 0 = no protocol tracing
# 1 = trace protocol messages
#
#Ice.Trace.Protocol=1

#
# Security Tracing
#
# 0 = no security tracing
# 1 = trace messages
#
#IceSSL.Trace.Security=1

#
# SSL Configuration
#
Ice.Plugin.IceSSL=IceSSL:createIceSSL
IceSSL.DefaultDir=../../../certs
IceSSL.CertAuthFile=cacert.pem
IceSSL.CertFile=s_rsa1024_pub.pem
IceSSL.KeyFile=s_rsa1024_priv.pem

#
# Uncomment the properties below if you want run the demo with the
# Windows Store App hello client.
#
#Hello.Endpoints=tcp -p 10000:udp -p 10000:ssl -p 10001
#IceSSL.VerifyPeer=0
#IceSSL.DefaultDir=../../../certs/winrt

#
# IceMX configuration.
#
#Ice.Admin.Endpoints=tcp -h localhost -p 10002
Ice.Admin.InstanceName=server
IceMX.Metrics.Debug.GroupBy=id
IceMX.Metrics.ByParent.GroupBy=parent
