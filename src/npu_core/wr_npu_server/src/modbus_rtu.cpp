#include "com/serial_modbus_rtu.h"

#define SERIAL_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("SERIAL::", "ModBusRtu::%s() " fmt, __FUNCTION__,##arg)
#define SERIAL_INFO(fmt, arg...)  ROS_INFO_NAMED("SERIAL::",  "ModBusRtu::%s() " fmt, __FUNCTION__,##arg)
#define SERIAL_WARN(fmt, arg...)  ROS_WARN_NAMED("SERIAL::",  "ModBusRtu::%s() " fmt, __FUNCTION__,##arg)
#define SERIAL_ERROR(fmt, arg...) ROS_ERROR_NAMED("SERIAL::", "ModBusRtu::%s() " fmt, __FUNCTION__,##arg)
#define SERIAL_FATAL(fmt, arg...) ROS_FATAL_NAMED("SERIAL::", "ModBusRtu::%s() " fmt, __FUNCTION__,##arg)

namespace wizrobo { namespace icom_driver { namespace modbusrtu {

ModBusRtu::ModBusRtu()
    : p_func_map_(NULL)
{
}

ModBusRtu::~ModBusRtu()
{
    if(p_func_map_){
        delete p_func_map_;
        p_func_map_ = NULL;
    }
    port_.Close();
}

int ModBusRtu::SetPortParam(std::string port, int baudrate, int databit, std::string parity)
{
    if(!port_.Open(port, baudrate, databit, parity)){
        SERIAL_ERROR("failed to open port(%s)",port.c_str());
    }
    if(port_.IsOpen()){
        std::cout<<"is open"<<std::endl;
    }
    port_.ClearReadBuffer();
}

void ModBusRtu::SetFuncPtr(funcmap::FuncMap *p_func_map)
{
    p_func_map_ = p_func_map;
}

void ModBusRtu::Run()
{
    short return_data;
    char *p_return_data = new char[2];
    std::vector<unsigned char> return_frame;
    std::vector<icom_driver::funcdata> cmd_data;

    if(GetCmd(cmd_data) == 1){
        for(int i = 0;i < static_cast<int>(cmd_data.size());i++){
            switch (cmd_data[i].cmd) {
                case icom_driver::READ:
                {
                    if(p_func_map_->read_func_map_.find(cmd_data[i].func)
                            != p_func_map_->read_func_map_.end()){
                        return_data = p_func_map_->read_func_map_[cmd_data[i].func]();
                    } else{
                        return_data = 65535;
                        SERIAL_DEBUG("read no macthing function call");
                    }
                    break;
                }
                case icom_driver::WRITE:
                {
                    if(p_func_map_->write_func_map_.find(cmd_data[i].func)
                            != p_func_map_->write_func_map_.end()){
                        return_data = p_func_map_->write_func_map_[cmd_data[i].func](cmd_data[i].parameter);
                    } else{
                        return_data = 65535;
                        SERIAL_DEBUG("write no macthing function call");
                    }
                    break;
                }
                default:
                    break;
            }
            p_return_data = Short2Char(return_data);
            return_frame.push_back(p_return_data[0]);
            return_frame.push_back(p_return_data[1]);
            SERIAL_DEBUG("read no.%d return char[0] is : %02X, char[1] is : %02X", i, p_return_data[0], p_return_data[1]);
        }
        p_func_map_->SetManualSpd();
        MakeFrame(return_frame,cmd_data[0].cmd);
    } else{
        SERIAL_DEBUG("falied get cmd data");
    }

    if(p_return_data){
        delete p_return_data;
        p_return_data = NULL;
    }
}

char* ModBusRtu::Short2Char(short data)
{
    SERIAL_DEBUG("data is %d",data);
    static char buf[2];
    buf[0] = data >> 8;
    buf[1] = data;
    SERIAL_DEBUG("return buf[0] is : %02X, buf[0] is : %02X", buf[0], buf[1]);
    return buf;
}

int ModBusRtu::ReadCmd()
{
    SERIAL_DEBUG("ReadCmd()");
    cmd_.clear();
    std::vector<unsigned char> _cmd(0);
    cmd_.swap(_cmd);
    cmd_.resize(1);
    while (cmd_[0] != HEAD) {
        cmd_.clear();
        cmd_.resize(0);
        ReadBuf2Vector(1);
    }
    ReadBuf2Vector(1);
    if(cmd_[1] == READCMD || cmd_[1] == WRITECMD){
        switch (cmd_[1]) {
        case READCMD:{
            if(GetReadCmd() == serial_port::TRUE){
                PrintXCmd("get readcmd is",cmd_);
                return serial_port::TRUE;
                break;
            } else{
                PrintXCmd("get readcmd is",cmd_);
                SERIAL_WARN("crc erro, get readcmd erro");
                return serial_port::FALSE;
                break;
            }
        }
        case WRITECMD:{
            if(GetWriteCmd() == serial_port::TRUE){
                PrintXCmd("get writecmd is",cmd_);
                return serial_port::TRUE;
                break;
            } else{
                PrintXCmd("get writecmd is",cmd_);
                SERIAL_WARN("crc erro, get readcmd erro");
                return serial_port::FALSE;
                break;
            }
        }
        default:{
            SERIAL_WARN("cmd not found");
            return serial_port::FALSE;
        }
        }
    } else{
        SERIAL_DEBUG("read cmd failed");
        return serial_port::FALSE;
    }

    return serial_port::FALSE;
}

int ModBusRtu::GetReadCmd()
{
    SERIAL_DEBUG("GetReadCmd()");

    ReadBuf2Vector(6);
    if(CheckCmd(cmd_) == serial_port::TRUE){
        return serial_port::TRUE;
    } else{
        return serial_port::FALSE;
    }
}

int ModBusRtu::GetWriteCmd()
{
    SERIAL_DEBUG("GetWriteCmd()");
    ReadBuf2Vector(5);
    int len = static_cast<int>(Byte2Short(0x00,cmd_[static_cast<int>(cmd_.size()-1)]));
    ReadBuf2Vector(len+2);
    if(CheckCmd(cmd_) == serial_port::TRUE){
        return serial_port::TRUE;
    } else{
        return serial_port::FALSE;
    }
}

int ModBusRtu::CheckCmd(std::vector<unsigned char> cmd)
{
    unsigned char data[cmd_.size() - 2];
    for(int i = 0;i < (static_cast<int>(cmd_.size()) - 2);i++){
        data[i] = cmd[i];
    }
    unsigned long len = static_cast<unsigned long>(cmd_.size() - 2);
    unsigned short crc = CalcCRCbyAlgorithm(data, len);
    unsigned short cmd_crc = Byte2UShort(cmd[cmd_.size() - 1],cmd[cmd_.size() - 2]);
    if(crc == cmd_crc){
        return serial_port::TRUE;
    }
    return serial_port::FALSE;
}

int ModBusRtu::ParseCmd(std::vector<funcdata> &v_func_data)
{
    SERIAL_DEBUG("ParseCmd()");
    if(ReadCmd() == serial_port::FALSE){
        SERIAL_DEBUG("ModBusRtu::ParseCmd() false done");
        return serial_port::FALSE;
    }
    PrintXCmd("parse cmd is :",cmd_);

    funcdata func_data;
    unsigned char _f_data[2];
    v_func_data.clear();

    int count = 0;
    int num_of_read = 0;
    int num_of_write = 0;
    short start_add = 0;
    switch (cmd_[1]) {
    case 0x03:{
        start_add = Byte2Short(cmd_[2],cmd_[3]);
        num_of_read = Byte2Short(cmd_[4],cmd_[5]);
        for(int i = start_add;i < (start_add + num_of_read);i++){
            func_data.cmd = icom_driver::READ;
            func_data.func = i;
            v_func_data.push_back(func_data);
        }
        PrintVCmd("read func data is :",v_func_data);
        break;
    }
    case 0x10:{
        start_add = Byte2Short(cmd_[2],cmd_[3]);
        num_of_write = Byte2Short(cmd_[4],cmd_[5]);
        for(int i = start_add;i < (start_add + num_of_write);i++){
            func_data.cmd = icom_driver::READ;
            func_data.func = i;
#if 0
            printf("no.%d, cmd[%d] is %02X, cmd[%d] is %02X.\n",
                   count+7,count+7,cmd_[count+7],count+8,cmd_[count+8]);
#endif
            _f_data[0] = cmd_[7 + count];
            _f_data[1] = cmd_[8 + count];
            func_data.parameter = _f_data;
            v_func_data.push_back(func_data);
            count+=2;
        }
        PrintVCmd("write func data is :",v_func_data);
        break;
    }
    default:
        return serial_port::FALSE;
    }
    SERIAL_DEBUG("ParseCmd() done");
    return serial_port::TRUE;
}

int ModBusRtu::GetCmd(std::vector<funcdata>& data)
{
    ROS_DEBUG_NAMED("COMI::modbus_rtu","GetCmd()");
    if(ParseCmd(data) == serial_port::FALSE){
        SERIAL_DEBUG("false done");
        return serial_port::FALSE;
    } else{
        SERIAL_DEBUG("get cmd done");
        return serial_port::TRUE;
    }
}

char* ModBusRtu::MakeFrame(std::vector<unsigned char> data, char cmd)
{
    SERIAL_DEBUG("MakeFrame()");
    SERIAL_DEBUG("frame size is %d\n",static_cast<int>(data.size()));
    for(int i = 0;i < static_cast<int>(data.size());i++){
        SERIAL_DEBUG(" %02X(%d)",data[i],i);
    }
    int frame_len = static_cast<int>(data.size() + 5);
    unsigned char write_frame[frame_len];
    write_frame[0] = 0x01;
    write_frame[1] = cmd;
    write_frame[2] = static_cast<int>(data.size());
    for(int i = 0;i < static_cast<int>(data.size());i++){
        write_frame[i+3] = data[i];
    }
    unsigned short crc = CalcCRCbyAlgorithm(write_frame,static_cast<unsigned long>(frame_len - 2));
    char crc_l = crc;
    char crc_h = crc >> 8;

    write_frame[frame_len - 2] = crc_l;
    write_frame[frame_len - 1] = crc_h;

    SERIAL_DEBUG("\nwrite data to port is :");
    int n = 0;
    while (n < frame_len) {
        if(port_.WriteByte(write_frame[n])){
            SERIAL_DEBUG(" %02X(%d)",write_frame[n],n);
            n++;
        } else{
            continue;
        }
    }
    char frame[frame_len];
    for(int j = 0;j < frame_len;j++){
        frame[j] = static_cast<char>(write_frame[j]);
    }
    PrintXCmd("return frame is :",frame,frame_len);

    return NULL;
}

short ModBusRtu::Byte2Short(unsigned char h, unsigned char l)
{
    short n = 0;
    short temp = 0;
    temp = temp | h;
    temp = temp << 8;
    n = n | temp;
    temp = 0;
    temp = temp | l;
    n = n | temp;
    return n;
}

unsigned short ModBusRtu::Byte2UShort(unsigned char h, unsigned char l)
{
    unsigned short n = 0;
    unsigned short temp = 0;
    temp = temp | h;
    temp = temp << 8;
    n = n | temp;
    temp = 0;
    temp = temp | l;
    n = n | temp;
    return n;
}

void ModBusRtu::PrintXCmd(std::string name, std::vector<unsigned char> cmd)
{
#if 0
            std::cout<<name<<std::endl;
            for(int i = 0;i < static_cast<int>(cmd.size());i++){
                printf(" %02X",cmd[i]);
            }
            printf("\n");
#endif
}

void ModBusRtu::PrintXCmd(std::string name, char* cmd, int len)
{
#if 0
            std::cout<<name<<std::endl;
            for(int i = 0;i < len;i++){
                printf(" %02X",static_cast<unsigned char>(cmd[i]));
            }
            printf("\n");
#endif
}

void ModBusRtu::PrintVCmd(std::string name, std::vector<funcdata> cmd)
{
#if 0
            std::cout<<name<<std::endl;
            for(int i = 0;i < static_cast<int>(cmd.size());i++){
                printf(" cmd    %02X\n", cmd[i].cmd);
                printf(" func   %02d\n", cmd[i].func);
                printf(" param  %02X %02X\n", cmd[i].parameter[0], cmd[i].parameter[1]);
            }
            printf("\n");
#endif
}

unsigned short ModBusRtu::CalcCRCbyAlgorithm(unsigned char* pDataBuffer,
                                         unsigned long usDataLen)
{
    /* Use the Modbus algorithm as detailed in the Watlow comms guide */

    const unsigned short POLYNOMIAL = 0xA001;
    unsigned short wCrc;
    int iByte, iBit;

    /* Initialize CRC */
    wCrc = 0xFFFF;

    for (iByte = 0; iByte < usDataLen; iByte++)
    {
        /* Exclusive-OR the byte with the CRC */
        wCrc ^= *(pDataBuffer + iByte);

        /* Loop through all 8 data bits */

        for (iBit = 0; iBit <= 7; iBit++)
        {
            /* If the LSB is 1, shift the CRC and XOR the polynomial mask with the CRC */

            /* Note - the bit test is performed before the rotation, so can't move the << here */
            if (wCrc & 0x0001)
            {
                wCrc >>= 1;
                wCrc ^= POLYNOMIAL;
            }
            else
            {
                /* Just rotate it */
                wCrc >>= 1;
            }
        }
    }

    return wCrc;
}

void ModBusRtu::ReadBuf2Vector(int len)
{
    char ch;
    int n = 0;
    while (n < len) {
        if(port_.ReadByte(ch) !=0){
            cmd_.push_back(ch);
        } else{
            continue;
        }
        n++;
    }
}
}//namespace modbusrtu
}//namespace icom_driver
}//namespace wizrobol
