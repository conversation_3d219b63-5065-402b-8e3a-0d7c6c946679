/****************************************
*Maintainer status: developed
*Maintainer: tommy, strong, lawrence
*UpDate: 2017-06-12
*Copyright(c) 2016-2017 LinkMiao CO., Ltd.
*All rights reserved
****************************************/
#include <iostream>
#include <fstream>
#include <cmath>
#include <algorithm>

#include "npu_server.h"

#include "wr_npu_msgs/TrioooFeedPolyline.h"

#define _TEST_CONNECTTED(...) \
    do { \
        if (server_state_ != CONNECTED) { \
            ROS_ERROR("Not connect yet."); \
            throw NpuException("","", ERR_NOT_CONNECTED, "Not connect yet!!"); \
            return (__VA_ARGS__); \
        } \
    } while(0)

#define API_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("API", "NpuServer{API}::" fmt, ##arg)
#define APIG_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("API_GET", "NpuServer{API_GET}::" fmt, ##arg)
#define APIS_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("API_SET", "NpuServer{API_SET}::" fmt, ##arg)
#define CBK_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("CBK", "NpuServer{CBK}::" fmt, ##arg)
#define NSR_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("NSR", "NpuServer{NSR}::" fmt, ##arg)

#define TWO_DIM 1

namespace wizrobo_npu {
//// template
typedef wr_npu_msgs::WrMapServer SrvMapServe;
typedef wr_npu_msgs::WrMapServer::Request SrvRqst;
typedef wr_npu_msgs::WrMapServer::Response SrvRspns;
typedef SrvRqst::_arguments_type SrvArgument;
typedef SrvRspns::_return_data_type SrvReturn;

typedef wr_dyparam::FeedBoolParam SrvFedBoolParam;
typedef wr_dyparam::FeedFloatParam SrvFedFloatParam;
typedef wr_dyparam::FeedIntParam SrvFedIntParam;
typedef wr_dyparam::FeedStrParam SrvFedStrParam;
typedef wr_dyparam::GetBoolParam SrvGetBoolParam;
typedef wr_dyparam::GetFloatParam SrvGetFloatParam;
typedef wr_dyparam::GetIntParam SrvGetIntParam;
typedef wr_dyparam::GetStrParam SrvGetStrParam;
typedef wr_dyparam::DeleteConfig SrvDelConfig;

static inline int _FindMapInfoById(MapInfoList list_, string id)
{
    for (int i=0;i<list_.size();i++) {
        if (list_[i].id == id) {
            return i;
        }
    }
    return -1;
}

template<typename T>
void _GetStructOfMapServer_(string &func_name, string& arguments, NpuServer *th, T& rtn_struct)
{
    ros::NodeHandle nh;
    SrvMapServe srv;
    srv.request.function = func_name;
    if (arguments == "") {
        srv.request.arguments.resize(0);
    } else {
        srv.request.arguments.resize(arguments.size()+sizeof(int));
        SrvArgument::iterator ap = srv.request.arguments.begin();
        th->GetMapHandlder()->PackVarLength<string>(arguments, ap);
    }
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::WrMapServer>("/map_server_node/map_server");
    if (client.isValid() != true) {
        return;
    }
    bool rtn = client.call(srv);
    if ( rtn != true ) {
        return;
    }

    if (srv.response.return_data.size() != 0) {
        vector<unsigned char>::iterator rp = srv.response.return_data.begin();
        th->GetMapHandlder()->Unpack(rtn_struct, rp);
    }
    return ;
}

template<typename T>
void _SetStructOfMapServer_(string& func_name, string& arguments, NpuServer *th, T& arg_struct)
{
    ros::NodeHandle nh;
    SrvMapServe srv;
    srv.request.function = func_name;
    int length = 0;
    if (arguments != "") {
        length += arguments.size() + sizeof(int);
    }
    length += th->GetMapHandlder()->Sizeof(arg_struct);
    srv.request.arguments.resize(length);
    SrvArgument::iterator ap = srv.request.arguments.begin();

    if (arguments != "") {
        th->GetMapHandlder()->PackVarLength<string>(arguments, ap);
    }
    th->GetMapHandlder()->Pack(arg_struct, ap);
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::WrMapServer>("/map_server_node/map_server");
    bool rtn = client.call(srv);
    if ( rtn != true ) {
        return;
    }
    return;
}

template<typename T>
void _GetStructOfMapServer_(const char *func_name, string& arguments, NpuServer *th, T& rtn_struct)
{
    string func_name_str = string(func_name);
    _GetStructOfMapServer_<T>(func_name_str, arguments, th, rtn_struct);
}

template<typename T>
void _SetStructOfMapServer_(const char *func_name, string& arguments, NpuServer *th, T& arg_struct)
{
    string func_name_str = string(func_name);
    _SetStructOfMapServer_<T>(func_name_str, arguments, th, arg_struct);
}

template<typename T>
void _GetStructOfMapServer_(const char *func_name, NpuServer *th, T& rtn_struct)
{
    string func_name_str = string(func_name);
    string arguments = "";
    _GetStructOfMapServer_<T>(func_name_str, arguments, th, rtn_struct);
}

template<typename T>
void _SetStructOfMapServer_(const char *func_name, NpuServer *th, T& arg_struct)
{
    string func_name_str = string(func_name);
    string arguments = "";
    _SetStructOfMapServer_<T>(func_name_str, arguments, th, arg_struct);
}

template<typename T>
bool _SetDyParam(string service_name, string key, typename T::RequestType::_value_type value)
{
    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<T>(service_name);
    T srv;
    srv.request.key = key;
    srv.request.value = value;
    bool rtn;
    if (client.isValid() == true)
    {
        rtn = client.call(srv);
    }
    else
    {
        rtn = false;
    }
    return rtn;
}

template<typename T>
bool _SetDyBoolParam(T key, bool value)
{
    string _key = string(key);
    return _SetDyParam<SrvFedBoolParam>("FeedBoolDyParam", _key, value);
}

template<typename T>
bool _SetDyFloatParam(T key, float value)
{
    string _key = T(key);
    return _SetDyParam<SrvFedFloatParam>("FeedFloatDyParam", _key, value);
}

template<typename T>
bool _SetDyIntParam(T key, int value)
{
    string _key = string(key);
    return _SetDyParam<SrvFedIntParam>("FeedIntDyParam", _key, value);
}

template<typename T>
bool _SetDyStrParam(T key, string value)
{
    string _key = string(key);
    return _SetDyParam<SrvFedStrParam>("FeedStrDyParam", _key, value);
}

template<typename T>
typename T::ResponseType::_value_type _GetDyParam(string service_name, string key)
{
    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<T>(service_name);
    T srv;
    srv.request.key = key;

    if (client.isValid() == true && client.call(srv) == true)
    {
        return srv.response.value;
    }
    //return T::ResponseType::_value_type();
}

template<typename T>
bool _GetDyBoolParam(T key)
{
    string _key = string(key);
    return _GetDyParam<SrvGetBoolParam>("GetBoolDyParam", _key);
}

template<typename T>
float _GetDyFloatParam(T key)
{
    string _key = string(key);
    return _GetDyParam<SrvGetFloatParam>("GetFloatDyParam", _key);
}

template<typename T>
int _GetDyIntParam(T key)
{
    string _key = string(key);
    return _GetDyParam<SrvGetIntParam>("GetIntDyParam", _key);
}

template<typename T>
string _GetDyStrParam(T key)
{
    string _key = string(key);
    return _GetDyParam<SrvGetStrParam>("GetStrDyParam", _key);
}

bool _DeleteConfig(string config_id)
{
    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<SrvDelConfig>("DeleteConfig");
    if (client.isValid() != true)
    {
        return false;
    }
    SrvDelConfig srv;
    srv.request.id = config_id;
    if (client.call(srv) != true)
    {
        return false;
    }
    bool res = srv.response.result;
    return res;
}

wr_npu_msgs::StationArray NpuServer::GetCsgStationList()
{
    wr_npu_msgs::StationArray list_;
    if (pthread_spin_trylock(&csg_station_list_spinlock_) != 0) {
        return list_;
    }
    list_ = csg_station_list_;
    pthread_spin_unlock(&csg_station_list_spinlock_);
    return list_;
}

wr_npu_msgs::Station NpuServer::GetCsgCurrentStation()
{
    wr_npu_msgs::Station station_;
    station_.d2s = -1.0;
    if (pthread_spin_trylock(&csg_current_station_spinlock_) != 0) {
        return station_;
    }
    station_ = csg_current_station_;
    pthread_spin_unlock(&csg_current_station_spinlock_);
    return station_;
}

//// npu api
/// connection
string NpuServer::GetServerVersion()
{
    stringstream ss;
    ss << "npu_api_version:" << NPU_API_VERSION << " ";
    ss << "npu_ice_version:" << NPU_ICE_VERSION;
    return ss.str();
}
string NpuServer::GetCurrentFormatTimeString()
{
    struct timeval tv;
    char timeArray[40];
    stringstream ss;
    gettimeofday(&tv, NULL);
    memset(timeArray, 0, sizeof(timeArray));
    strftime(timeArray, sizeof(timeArray) - 1, "%F-%T", localtime(&tv.tv_sec));
    for(int l=0;l < string(timeArray).size();l++)
    {
        if (timeArray[l] == ':')
        {
            timeArray[l] = '-';
        }
    }
    ss << string(timeArray); //<< "." << tv.tv_usec; //us
    return ss.str();
}

void NpuServer::Connect(string version)
{
    //    if(client_is_alive)
    //      {
    //          ROS_ERROR("Another client is connected!");
    //          throw NpuException("","", ERR_UNKNOWN, "Another client is connected!");
    //          return;
    //      }
    ros::NodeHandle nh;
    ros::NodeHandle prv_nh("~");
    client_is_alive = true;

    if(npu_state_ != IDLE_STATE && npu_state_ != SLAM_STATE && npu_state_ != NAVI_STATE && npu_state_ != TELEOP_STATE && npu_state_ != SWITCH_STATE)
    {
        ROS_ERROR("Npu can't start!");
        throw NpuException("", "", ERR_UNKNOWN, "Npu can't start!");
        return;
    }

    API_DEBUG("Connect(%s)", version.c_str());
#if WR_LOCK == true
    if (!wizrobo::UsbLocker::Unlock())
    {
        ROS_FATAL("Failed to unlock! Pls heck you usblock.");
        throw NpuException("", "", ERR_NO_KEY, "Failed to unlock! Pls heck you usblock.");
        return;
    }
#endif
    static int connection_cnt = 0;
    if (this->server_state_ == CONNECTED)
    {
        API_DEBUG("Already connected.");
        return;
    }
    this->server_state_ = DISCONNECTED;
    string local_version = this->GetServerVersion();
    if (version.compare(local_version) != 0)
    {
        API_DEBUG("API or ICE verison unmatched.");
    }

    this->server_state_ = CONNECTED;
    /// update param
    core_param_ = GetCoreParam();
    // base
    base_param_ = GetBaseParam();
    //sensor
    sensor_param_ = GetSensorParam();
    // teleop
    teleop_param_ = GetTeleopParam();
    // slam
    slam_param_ = GetSlamParam();
    // navi
    navi_param_ = GetNaviParam();

    prv_nh.param<string>("server_type", server_type_, "ice");
    /// init runtime data
    // cmd vel
    if (pthread_spin_trylock(&cmd_vel_spinlock_) != 0
            || pthread_spin_trylock(&cmd_motor_spd_spinlock_) != 0
            || pthread_spin_trylock(&act_vel_spinlock_) != 0
            || pthread_spin_trylock(&act_motor_spd_spinlock_) != 0
            || pthread_spin_trylock(&act_pose_spinlock_) != 0)
    {
        pthread_spin_unlock(&act_pose_spinlock_);
        pthread_spin_unlock(&act_motor_spd_spinlock_);
        pthread_spin_unlock(&act_vel_spinlock_);
        pthread_spin_unlock(&cmd_motor_spd_spinlock_);
        pthread_spin_unlock(&cmd_vel_spinlock_);
        ROS_ERROR("Connect() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    cmd_vel_.v_x = 0;
    cmd_vel_.v_y = 0;
    cmd_vel_.v_z = 0;
    cmd_vel_.v_roll = 0;
    cmd_vel_.v_pitch = 0;
    cmd_vel_.v_yaw = 0;
    // cmd motor spd
    cmd_motor_spd_.motor_num = base_param_.motor_param.motor_num;
    cmd_motor_spd_.rpms.resize(cmd_motor_spd_.motor_num);
    for (int i = 0; i < cmd_motor_spd_.rpms.size(); i++)
    {
        cmd_motor_spd_.rpms[i] = 0;
    }
    // act vel
    act_vel_.v_x = 0;
    act_vel_.v_y = 0;
    act_vel_.v_z = 0;
    act_vel_.v_roll = 0;
    act_vel_.v_pitch = 0;
    act_vel_.v_yaw = 0;
    // act motor spd
    act_motor_spd_.motor_num = base_param_.motor_param.motor_num;
    act_motor_spd_.rpms.resize(act_motor_spd_.motor_num);
    for (int i = 0; i < act_motor_spd_.rpms.size(); i++)
    {
        act_motor_spd_.rpms[i] = 0;
    }
    // path
    ClearActPath_();
    pthread_spin_unlock(&act_pose_spinlock_);
    pthread_spin_unlock(&act_motor_spd_spinlock_);
    pthread_spin_unlock(&act_vel_spinlock_);
    pthread_spin_unlock(&cmd_motor_spd_spinlock_);
    pthread_spin_unlock(&cmd_vel_spinlock_);
    static bool done = false;
    if (!done)
    {
        done = true;
        matching_score_sub_ = nh.subscribe<std_msgs::Float32>("/matching_score", 1, &NpuServer::MatchingScoreCallBack_, this);
        // motor.master
        motor_enc_sub_ = nh.subscribe<wr_npu_msgs::MotorEnc>("/motor_enc", 1, &NpuServer::MotorEncCallback_, this);
        act_motor_spd_sub_ = nh.subscribe<wr_npu_msgs::MotorSpd>("/act_motor_spd", 1, &NpuServer::ActMotorSpdCallback_, this);
        // motor.slave
        motor_enc_pub_ = nh.advertise<wr_npu_msgs::MotorEnc>("/motor_enc", 1);
        act_motor_spd_pub_ = nh.advertise<wr_npu_msgs::MotorSpd>("/act_motor_spd", 1);
        // motor.common
        cmd_motor_spd_sub_ = nh.subscribe<wr_npu_msgs::MotorSpd>("/cmd_motor_spd", 1, &NpuServer::CmdMotorSpdCallback_, this);
        // vel
        cmd_vel_sub_ = nh.subscribe<geometry_msgs::Twist>("/cmd_vel", 1, &NpuServer::CmdVelCallback_, this);
        act_vel_sub_ = nh.subscribe<geometry_msgs::Twist>("/act_vel", 1, &NpuServer::ActVelCallback_, this);

        // navi data
        act_pose_sub_ = nh.subscribe<geometry_msgs::PoseStamped>("/wizrobo/pose", 1, &NpuServer::ActPoseCallback_, this);//TODO: "/act_pose"
        navi_status_sub_ = nh.subscribe<actionlib_msgs::GoalStatusArray>("/navi_core/status", 1, &NpuServer::TaskStatusCallback_, this);// TODO: "/navi/status"
        navi_status_pf_sub_ = nh.subscribe<actionlib_msgs::GoalStatusArray>("/navi_core/status_pf", 1, &NpuServer::TaskStatusPfCallback_, this);// TODO: "/navi/status"
        global_path_sub_ = nh.subscribe<nav_msgs::Path>("/navi_core/PathFollow/global_path", 1, &NpuServer::GlobalPathCallback_, this);// TODO: /global_path
        local_path_sub_ = nh.subscribe<geometry_msgs::PoseArray>("/local_path", 1, &NpuServer::LocalPathCallback_, this);// TODO: /local_path

        //gpscallback
        gps_raw_sub_ = nh.subscribe<sensor_msgs::NavSatFix>("/gps/gps_raw",1,&NpuServer::GpsRawCallBack_,this);
        gps_direction_sub_ = nh.subscribe<geometry_msgs::Vector3Stamped>("/imu/rpy_raw",1,&NpuServer::GpsDirectionCallBack_,this);
        //sensor status
        sensor_status_.resize(0);
        sensor_status_sub_ = nh.subscribe<wr_npu_msgs::VsensorStatus>("/sensorstatus", 1, &NpuServer::SensorStatusCallBack_, this);
        imu_data_sub_ = nh.subscribe<geometry_msgs::Vector3Stamped>("/imu/rpy_raw", 1, &NpuServer::ImuDataCallBack_, this);
        runtime_status_msgs_.runtime_status_0 = 0;
        runtime_status_msgs_.runtime_status_1 = 0;
        runtime_status_msgs_.runtime_status_2 = 0;
        runtime_status_sub_ = nh.subscribe<wr_npu_msgs::RuntimeStatus>("/runtime_status", 1, &NpuServer::RuntimeStatusCallBack_, this);
        if (enb_direct_sensor_trans_)
        {
            //trans_lidar_scan_pc_sub_ = nh.subscribe<sensor_msgs::PointCloud>("/abs_scan", 1, &NpuServer::TfTransLidarScanCallback_, this);// TODO: "/trans_lidar_scan"
            lidar_scan_sub_ = nh.subscribe<sensor_msgs::LaserScan>(STD_SCAN_TOPIC_NAME, 1, &NpuServer::LidarScanCallBack_, this);
            //lidar_scan_mf_sub_.subscribe(nh, STD_SCAN_TOPIC_NAME, 10);// TODO: "/lidar_scan"
            //p_lidar_scan_tf_filter_ = new tf::MessageFilter<sensor_msgs::LaserScan>(lidar_scan_mf_sub_, tf_listener_, STD_MAP_FRAME_ID, 10);
            //p_lidar_scan_tf_filter_->registerCallback(boost::bind(&NpuServer::LidarScanCallBack_, this, _1));
        }
        else
        {
            trans_lidar_scan_pc_sub_ = nh.subscribe<sensor_msgs::PointCloud>("/abs_scan", 1, &NpuServer::TfTransLidarScanCallback_, this);// TODO: "/trans_lidar_scan"
            //lidar_scan_sub_ = nh.subscribe<sensor_msgs::LaserScan>(STD_SCAN_TOPIC_NAME, 1, &NpuServer::LidarScanCallBack_, this);
        }
        //bumper
        bumper_data_sub_ = nh.subscribe<wr_npu_msgs::BumperData>("/bumper_data", 1, &NpuServer::BumperDataCallBack_, this);
        //gps
        gps_data_sub_ = nh.subscribe("gps/gps_raw", 1, &NpuServer::GpsFixCallback_, this);
        //infrd
        infrd_data_sub_ = nh.subscribe<wr_npu_msgs::InfrdData>("/infrd_data", 1, &NpuServer::InfrdDataCallBack_, this);
        emergencystop_data_sub_ = nh.subscribe<wr_npu_msgs::EmergencyStopData>("/emergency_stop_status", 1, &NpuServer::EmergencyStopDataCallBack_, this);
        cmd_pose_pub_ = nh.advertise<geometry_msgs::PoseStamped>("/navi_core_simple/goal", 1);// TODO: /cmd_pose
        exce_info_pub_ = nh.advertise<wr_npu_msgs::ExceptionInformation>("/exception_info", 1);
        //        initial_pose_pub_ = nh.advertise<geometry_msgs::PoseStamped>(STD_INITIAL_POSE_TOPIC_NAME, 1);// TODO: /init_pose
        bool latch_initial_pose_msg;
        prv_nh.param<bool>("latch_initial_pose_msg", latch_initial_pose_msg, false);
        initial_pose_est_pub_ = nh.advertise<geometry_msgs::PoseWithCovarianceStamped>(STD_INITIAL_POSE_EST_TOPIC_NAME, 1, latch_initial_pose_msg);// TODO: /init_pose
        initial_pose_pub_ = nh.advertise<geometry_msgs::PoseStamped>(STD_INITIAL_POSE_TOPIC_NAME, 1, latch_initial_pose_msg);// TODO: /init_pose
        initial_pose_area_pub = nh.advertise<geometry_msgs::Polygon>("/initial_region",1);
        action_pub_ = nh.advertise<actionlib_msgs::GoalID>("/navi_core/cancel", 1);
        manual_cmd_pub_ = nh.advertise<geometry_msgs::Twist>("/manual_cmd_vel", 1);
        safe_cmd_pub_ = nh.advertise<geometry_msgs::Twist>("/safe_cmd_vel", 1);
        waypoints_pub_ = nh.advertise<geometry_msgs::PoseArray>("/waypoints", 1);
        ccp_region_pub_ = nh.advertise<geometry_msgs::PolygonStamped>("/ccp_region", 1);
        task_ctrl_pub_ = nh.advertise<std_msgs::String>("/task_control", 1);
        footprint_pub_ = nh.advertise<geometry_msgs::PolygonStamped>("/footprint", 1);
        act_path_pub_ = nh.advertise<nav_msgs::Path>("/act_path", 1);

        csg_station_list_sub_ = nh.subscribe<wr_npu_msgs::StationArray>("/csg_stations", 1, &NpuServer::CsgStationsCallBack_, this);
        csg_current_station_sub_ = nh.subscribe<wr_npu_msgs::Station>("/csg_current_station", 1, &NpuServer::CsgCurrentStationCallBack_, this);
    }
    UpdateSpdLimits_(base_param_.motor_param, base_param_.chassis_param);
    UpdateFootprint_(base_param_.footprint_param);
    UpdateLidarToBaseTf_(sensor_param_);

    API_DEBUG("Connected.");
}
ServerState NpuServer::GetServerState()
{
    APIG_DEBUG("GetServerState()");
    _TEST_CONNECTTED(server_state_);
    APIG_DEBUG("server_state_ = %s", EnumString<ServerState>::EnumToStr(server_state_).c_str());
    return server_state_;
}
SystemDiagInfo NpuServer::GetSystemDiagInfo()
{
    APIG_DEBUG("GetSystemDiagInfo()");
    SystemDiagInfo system_diag_info;
    _TEST_CONNECTTED(system_diag_info);
    return system_diag_info;
}
NpuState NpuServer::GetNpuState()
{
    APIG_DEBUG("GetNpuState()");
    _TEST_CONNECTTED(npu_state_);
    client_is_alive=true;
    return npu_state_;
}

/// power
void NpuServer::Shutdown()
{
    API_DEBUG("Shutdown()");
    _TEST_CONNECTTED(void());
    stringstream ss;
    ss << "shutdown -P now &\n";
    system(ss.str().c_str());
    return;
}
void NpuServer::Reboot()
{
    API_DEBUG("Reboot()");
    _TEST_CONNECTTED(void());
    stringstream ss;
    ss << "reboot &\n";
    system(ss.str().c_str());
    return;
}

/// config
StringArray NpuServer::GetConfigIdList()
{
    APIG_DEBUG("GetConfigIdList()");
    StringArray return_value;
    _TEST_CONNECTTED(return_value);

    string str;
    ros::NodeHandle nh;
    nh.param<string>(STD_PARAM_ROOT_NS + "/CONFIG_ID_LIST", str, "");
    ROS_WARN("%s/CONFIG_ID_LIST = %s", STD_PARAM_ROOT_NS.c_str(), str.c_str());
    StringArray id_array = SplitString(str, ", ");
    return id_array;
}
void NpuServer::SelectConfig(string id)
{
    API_DEBUG("SelectConfig(\"%s\")", id.c_str());
    _TEST_CONNECTTED(void());
    if (npu_state_ != IDLE_STATE)
    {
        API_DEBUG("Only available in IDLE_STATUE");
        return;
    }
    string key = STD_CORE_PARAM_NS + "/CONFIG_ID";
    string value = id;
    if (_SetDyStrParam(key, value) != true)
    {
        API_DEBUG("SelectConfig Fail!");
        return;
    }
    API_DEBUG("SelectConfig Success!");
    return;
}
void NpuServer::DeleteConfig(string id)
{
    API_DEBUG("DeleteConfig(\"%s\")", id.c_str());
    _TEST_CONNECTTED(void());
    if (npu_state_ != IDLE_STATE)
    {
        API_DEBUG("Only available in IDLE_STATUE");
        return;
    }
    _DeleteConfig(id);
    return;
}
void NpuServer::AddConfig(string id)
{
    API_DEBUG("AddConfig(\"%s\")", id.c_str());
    _TEST_CONNECTTED(void());

    if (npu_state_ != IDLE_STATE)
    {
        API_DEBUG("Only available in IDLE_STATUE");
        return;
    }

    SelectConfig(id);

    return;
}
// npu
CoreParam NpuServer::GetCoreParam()
{
    APIG_DEBUG("GetCoreParam()");
    CoreParam param;
#if USE_NPU_API_V_0_9_12
#else
    param.npu_mode = MASTER;// TODEL
#endif
    _TEST_CONNECTTED(param);

    string key = STD_CORE_PARAM_NS + "/CONFIG_ID";
    string value = _GetDyStrParam(key);
    param.config_id = value;

    key = STD_CORE_PARAM_NS + "/MAP_ID";
    value = _GetDyStrParam(key);
    param.map_id = value;

    key = STD_CORE_PARAM_NS + "/BAG_ID";
    value = _GetDyStrParam(key);
#if USE_NPU_API_V_0_9_12
    param.bag_id = value;
#else
    param.record_id = value;
#endif

    return param;
}
void NpuServer::SetCoreParam(CoreParam param)
{
    API_DEBUG("SetCoreParam()");
    _TEST_CONNECTTED(void());

    string key = STD_CORE_PARAM_NS + "/CONFIG_ID";
    string value = param.config_id;
    _SetDyStrParam(key, value);

    key = STD_CORE_PARAM_NS + "/MAP_ID";
    value = param.map_id;
    _SetDyStrParam(key, value);

    key = STD_CORE_PARAM_NS + "/BAG_ID";
#if USE_NPU_API_V_0_9_12
    value = param.bag_id;
#else
    value = param.record_id;
#endif
    _SetDyStrParam(key, value);

    core_param_ = param;
}

// motor
MotorParam NpuServer::GetMotorParam()
{
    APIG_DEBUG("GetMotorParam()");
    MotorParam param;
    _TEST_CONNECTTED(param);

    param.motor_num = _GetDyIntParam(STD_MOTOR_PARAM_NS + "/motor_num");
    param.motor_dir_str = _GetDyStrParam(STD_MOTOR_PARAM_NS + "/motor_dir_str");
    param.motor_max_spd_rpm = _GetDyIntParam(STD_MOTOR_PARAM_NS + "/motor_max_spd_rpm");
    param.motor_rdc_ratio = _GetDyFloatParam(STD_MOTOR_PARAM_NS + "/motor_rdc_ratio");
    param.motor_enc_res_ppr = _GetDyIntParam(STD_MOTOR_PARAM_NS + "/motor_enc_res_ppr");
    param.motor_pwm_frq_hz = _GetDyIntParam(STD_MOTOR_PARAM_NS + "/motor_pwm_frq_hz");
    param.motor_brk_type = EnumString<BrakeType>::StrToEnum(_GetDyStrParam(STD_MOTOR_PARAM_NS + "/motor_brk_type"));
    param.brk_vol = EnumString<ValidOutputLevel>::StrToEnum(_GetDyStrParam(STD_MOTOR_PARAM_NS + "/brk_vol"));
    param.enb_vol = EnumString<ValidOutputLevel>::StrToEnum(_GetDyStrParam(STD_MOTOR_PARAM_NS + "/enb_vol"));
    param.dir_vol = EnumString<ValidOutputLevel>::StrToEnum(_GetDyStrParam(STD_MOTOR_PARAM_NS + "/dir_vol"));
    param.pwm_vol = EnumString<ValidOutputLevel>::StrToEnum(_GetDyStrParam(STD_MOTOR_PARAM_NS + "/pwm_vol"));
    param.enb_auxdir_mode = _GetDyBoolParam(STD_MOTOR_PARAM_NS + "/enb_auxdir_mode");
    param.enb_throttle_mode = _GetDyBoolParam(STD_MOTOR_PARAM_NS + "/enb_throttle_mode");
    param.throttle_zero_pos_fac = _GetDyFloatParam(STD_MOTOR_PARAM_NS + "/throttle_zero_pos_fac");
    param.throttle_zero_neg_fac = _GetDyFloatParam(STD_MOTOR_PARAM_NS + "/throttle_zero_neg_fac");
    param.end_left_right_switch = _GetDyBoolParam(STD_MOTOR_PARAM_NS + "/end_left_right_switch");

    return param;
}
void NpuServer::SetMotorParam(MotorParam param)
{
    API_DEBUG("SetMotorParam()");
    _TEST_CONNECTTED(void());

    _SetDyIntParam(STD_MOTOR_PARAM_NS + "/motor_num", param.motor_num);
    _SetDyStrParam(STD_MOTOR_PARAM_NS + "/motor_dir_str", param.motor_dir_str);
    _SetDyIntParam(STD_MOTOR_PARAM_NS + "/motor_max_spd_rpm", param.motor_max_spd_rpm);
    _SetDyFloatParam(STD_MOTOR_PARAM_NS + "/motor_rdc_ratio", param.motor_rdc_ratio);
    _SetDyIntParam(STD_MOTOR_PARAM_NS + "/motor_enc_res_ppr", param.motor_enc_res_ppr);
    _SetDyIntParam(STD_MOTOR_PARAM_NS + "/motor_pwm_frq_hz", param.motor_pwm_frq_hz);
    _SetDyStrParam(STD_MOTOR_PARAM_NS + "/motor_brk_type", EnumString<BrakeType>::EnumToStr(param.motor_brk_type));
    _SetDyStrParam(STD_MOTOR_PARAM_NS + "/brk_vol", EnumString<ValidOutputLevel>::EnumToStr(param.brk_vol));
    _SetDyStrParam(STD_MOTOR_PARAM_NS + "/enb_vol", EnumString<ValidOutputLevel>::EnumToStr(param.enb_vol));
    _SetDyStrParam(STD_MOTOR_PARAM_NS + "/dir_vol", EnumString<ValidOutputLevel>::EnumToStr(param.dir_vol));
    _SetDyStrParam(STD_MOTOR_PARAM_NS + "/pwm_vol", EnumString<ValidOutputLevel>::EnumToStr(param.pwm_vol));
    _SetDyBoolParam(STD_MOTOR_PARAM_NS + "/enb_auxdir_mode", param.enb_auxdir_mode);
    _SetDyBoolParam(STD_MOTOR_PARAM_NS + "/enb_throttle_mode", param.enb_throttle_mode);
    _SetDyFloatParam(STD_MOTOR_PARAM_NS + "/throttle_zero_pos_fac", param.throttle_zero_pos_fac);
    _SetDyFloatParam(STD_MOTOR_PARAM_NS + "/throttle_zero_neg_fac", param.throttle_zero_neg_fac);
    _SetDyBoolParam(STD_MOTOR_PARAM_NS + "/end_left_right_switch", param.end_left_right_switch);

    base_param_.motor_param = param;
    UpdateSpdLimits_(base_param_.motor_param, base_param_.chassis_param);
    TriggerMotorParamUpdating_();
}
// pid
PidParam NpuServer::GetPidParam()
{
    APIG_DEBUG("GetPidParam()");
    PidParam param;
    _TEST_CONNECTTED(param);

    param.enb_pid = _GetDyBoolParam(STD_PID_PARAM_NS + "/enb_pid");
    param.kp_acc = _GetDyFloatParam(STD_PID_PARAM_NS + "/kp_acc");
    param.kp_dec = _GetDyFloatParam(STD_PID_PARAM_NS + "/kp_dec");
    param.ki_acc = _GetDyFloatParam(STD_PID_PARAM_NS + "/ki_acc");
    param.ki_dec = _GetDyFloatParam(STD_PID_PARAM_NS + "/ki_dec");
    param.kd_acc = _GetDyFloatParam(STD_PID_PARAM_NS + "/kd_acc");
    param.kd_dec = _GetDyFloatParam(STD_PID_PARAM_NS + "/kd_dec");

    return param;
}
void NpuServer::SetPidParam(PidParam param)
{
    API_DEBUG("SetPidParam()");
    _TEST_CONNECTTED(void());

    _SetDyBoolParam(STD_PID_PARAM_NS + "/enb_pid", param.enb_pid);
    _SetDyFloatParam(STD_PID_PARAM_NS + "/kp_acc", param.kp_acc);
    _SetDyFloatParam(STD_PID_PARAM_NS + "/kp_dec", param.kp_dec);
    _SetDyFloatParam(STD_PID_PARAM_NS + "/ki_acc", param.ki_acc);
    _SetDyFloatParam(STD_PID_PARAM_NS + "/ki_dec", param.ki_dec);
    _SetDyFloatParam(STD_PID_PARAM_NS + "/kd_acc", param.kd_acc);
    _SetDyFloatParam(STD_PID_PARAM_NS + "/kd_dec", param.kd_dec);

    base_param_.pid_param = param;
    TriggerPidParamUpdating_();
}
// chassis
ChassisParam NpuServer::GetChassisParam()
{
    APIG_DEBUG("GetChassisParam()");
    ChassisParam param;
    _TEST_CONNECTTED(param);

    param.model_type = EnumString<ChassisModelType>::StrToEnum(_GetDyStrParam(STD_CHASSIS_PARAM_NS + "/model_type"));
    param.wheel_rdc_ratio = _GetDyFloatParam(STD_CHASSIS_PARAM_NS + "/wheel_rdc_ratio");
    param.wheel_radius_m = _GetDyFloatParam(STD_CHASSIS_PARAM_NS + "/wheel_radius_m");
    param.wheel_span_m = _GetDyFloatParam(STD_CHASSIS_PARAM_NS + "/wheel_span_m");
    param.max_lin_acc_time_s = _GetDyFloatParam(STD_CHASSIS_PARAM_NS + "/max_lin_acc_time_s");
    param.max_ang_acc_time_s = _GetDyFloatParam(STD_CHASSIS_PARAM_NS + "/max_ang_acc_time_s");
    param.autoset_max_lin_spd_mps = _GetDyFloatParam(STD_CHASSIS_PARAM_NS + "/autoset_max_lin_spd_mps");
    param.autoset_max_ang_spd_rps = _GetDyFloatParam(STD_CHASSIS_PARAM_NS + "/autoset_max_ang_spd_rps");
    //param.footprint_param = GetFootprintParam();
    param.carlike_steer_enc_location = EnumString<SteerEncLocation>::StrToEnum(_GetDyStrParam(STD_CHASSIS_PARAM_NS + "/carlike_steer_enc_location"));
    param.carlike_axle_dist_m = _GetDyFloatParam(STD_CHASSIS_PARAM_NS + "/carlike_axle_dist_m");
    return param;
}
void NpuServer::SetChassisParam(ChassisParam param)
{
    API_DEBUG("SetChassisParam()");
    _TEST_CONNECTTED(void());

    _SetDyStrParam(STD_CHASSIS_PARAM_NS + "/model_type", EnumString<ChassisModelType>::EnumToStr(param.model_type));
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/wheel_rdc_ratio", param.wheel_rdc_ratio);
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/wheel_radius_m", param.wheel_radius_m);
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/wheel_span_m", param.wheel_span_m);
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/max_lin_acc_time_s", param.max_lin_acc_time_s);
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/max_ang_acc_time_s", param.max_ang_acc_time_s);
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/autoset_max_lin_spd_mps", param.autoset_max_lin_spd_mps);
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/autoset_max_ang_spd_rps", param.autoset_max_ang_spd_rps);
    //SetFootprintParam(param.footprint_param);
    _SetDyStrParam(STD_CHASSIS_PARAM_NS + "/carlike_steer_enc_location", wizrobo::enum_ext::EnumString<SteerEncLocation>::EnumToStr(param.carlike_steer_enc_location));
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/carlike_axle_dist_m", param.carlike_axle_dist_m);

    base_param_.chassis_param = param;
    UpdateSpdLimits_(base_param_.motor_param, base_param_.chassis_param);
    TriggerChassisParamUpdating_();
}
// chassis
FootprintParam NpuServer::GetFootprintParam()
{
    APIG_DEBUG("NpuServer::GetFootprintParam()");
    FootprintParam param;
    _TEST_CONNECTTED(param);

    param.shape_type = EnumString<ShapeType>::StrToEnum(_GetDyStrParam(STD_FOOTPRINT_PARAM_NS + "/shape_type"));
    param.rot_center_offset_m = _GetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/rot_center_offset_m");
#if USE_NPU_API_V_0_9_12
    param.height_m = _GetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/height_m");
#else
    footprint_height_m_ = _GetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/height_m");
#endif
    param.round_radius_m = _GetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/round_radius_m");
    param.rectangle_width_m = _GetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/rectangle_width_m");
    param.rectangle_length_m = _GetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/rectangle_length_m");
    param.polygon_vertex_num = _GetDyIntParam(STD_FOOTPRINT_PARAM_NS + "/polygon_vertex_num");
    param.polygon_vertices.resize(param.polygon_vertex_num);
    for (int i=0;i<param.polygon_vertex_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix << STD_FOOTPRINT_PARAM_NS + "/polygon_vertices/vertex_" << i << "/";
        param.polygon_vertices[i].x = _GetDyFloatParam(ss_prefix.str()+"x");
        param.polygon_vertices[i].y = _GetDyFloatParam(ss_prefix.str()+"y");
        param.polygon_vertices[i].z = _GetDyFloatParam(ss_prefix.str()+"z");
    }
    return param;
}
void NpuServer::SetFootprintParam(FootprintParam param)
{
    API_DEBUG("NpuServer::SetFootprintParam()");
    _TEST_CONNECTTED(void());

    _SetDyStrParam(STD_FOOTPRINT_PARAM_NS + "/shape_type", EnumString<ShapeType>::EnumToStr(param.shape_type));
    _SetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/rot_center_offset_m", param.rot_center_offset_m);
#if USE_NPU_API_V_0_9_12
    _SetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/height_m", param.height_m);
#else
    _SetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/height_m", footprint_height_m_);
#endif

    if (param.shape_type == ROUND)
    {
        _SetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/round_radius_m", param.round_radius_m);
        _SetDyFloatParam(STD_LIDAR_FILTERS_PARAM_NS + "/inscribed_radius", param.round_radius_m);
        TriggerLidarFilterParamUpdating_();
    }
    else if (param.shape_type == RECTANGLE)
    {
        _SetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/rectangle_width_m", param.rectangle_width_m);
        _SetDyFloatParam(STD_FOOTPRINT_PARAM_NS + "/rectangle_length_m", param.rectangle_length_m);
        _SetDyFloatParam(STD_LIDAR_FILTERS_PARAM_NS + "/inscribed_radius", param.rectangle_width_m);
        TriggerLidarFilterParamUpdating_();
    }
    else if (param.shape_type == POLYGON)
    {
        if (param.polygon_vertices.size() < 3)
        {
            ROS_ERROR("param.polygon_vertices.size() must be greater than 3!!");
            throw NpuException("","", ERR_ILLEGAL_PARA, "param.polygon_vertices.size() must be greater than 3!!");
        }
        _SetDyIntParam(STD_FOOTPRINT_PARAM_NS + "/polygon_vertex_num", param.polygon_vertices.size());

        for (int i = 0; i < param.polygon_vertices.size();i++)
        {
            stringstream ss_prefix;
            ss_prefix.str(""); ss_prefix << STD_FOOTPRINT_PARAM_NS + "/polygon_vertices/vertex_" << i << "/";
            _SetDyFloatParam(ss_prefix.str()+"x", param.polygon_vertices[i].x);
            _SetDyFloatParam(ss_prefix.str()+"y", param.polygon_vertices[i].y);
            _SetDyFloatParam(ss_prefix.str()+"z", param.polygon_vertices[i].z);
        }
    }
    base_param_.footprint_param = param;
    UpdateFootprint_(base_param_.footprint_param);
}
// base
BaseParam NpuServer::GetBaseParam()
{
    APIG_DEBUG("GetBaseParam()");
    BaseParam param;
    _TEST_CONNECTTED(param);

    param.enb_slave_mode = _GetDyBoolParam(STD_BASE_PARAM_NS + "/enb_slave_mode");
    param.base_id = _GetDyStrParam(STD_BASE_PARAM_NS + "/base_id");
    param.motor_param = GetMotorParam();
    param.pid_param = GetPidParam();
    param.chassis_param = GetChassisParam();
    param.footprint_param = GetFootprintParam();
}

void NpuServer::SetBaseParam(BaseParam param)
{
    API_DEBUG("SetBaseParam()");
    _TEST_CONNECTTED(void());

    _SetDyBoolParam(STD_BASE_PARAM_NS + "/enb_slave_mode", param.enb_slave_mode);
    _SetDyStrParam(STD_BASE_PARAM_NS + "/base_id", param.base_id);
    SetMotorParam(param.motor_param);
    SetPidParam(param.pid_param);
    SetChassisParam(param.chassis_param);
    SetFootprintParam(param.footprint_param);

    base_param_ = param;
}
// sensor
SensorParam NpuServer::GetSensorParam()
{
    APIG_DEBUG("GetSensorParam()");
    SensorParam param;
    _TEST_CONNECTTED(param);

    GetLidarParam_(param);
    GetSonarParam_(param);
    GetInfrdParam_(param);
    GetBumperParam_(param);
    GetImuParam_(param);
    GetGpsParam_(param);
    GetCameraParam_(param);

    return param;
}
void NpuServer::SetSensorParam(SensorParam param)
{
    API_DEBUG("SetSensorParam()");
    _TEST_CONNECTTED(void());

    SetLidarParam_(param);
    SetSonarParam_(param);
    SetInfrdParam_(param);
    SetBumperParam_(param);
    SetImuParam_(param);
    SetGpsParam_(param);

    sensor_param_ = param;
    UpdateTf_();
}

StringArray NpuServer::GetSupportedLidarTypes()
{
    APIG_DEBUG("GetSupportedLidarTypes()");
    StringArray types;
    _TEST_CONNECTTED(types);
    // TODO
    return types;
}

StringArray NpuServer::GetSupportedImuTypes()
{
    APIG_DEBUG("GetSupportedImuTypes()");
    StringArray types;
    _TEST_CONNECTTED(types);
    // TODO
    return types;
}

StringArray NpuServer::GetSupportedCameraTypes()
{
    APIG_DEBUG("GetSupportedCameraTypes()");
    StringArray types;
    _TEST_CONNECTTED(types);
    // TODO
    return types;
}

StringArray NpuServer::GetSupportedGpsTypes()
{
    APIG_DEBUG("GetSupportedGpsTypes()");
    StringArray types;
    _TEST_CONNECTTED(types);
    // TODO
    return types;
}

// teleop
TeleopParam NpuServer::GetTeleopParam()
{
    APIG_DEBUG("GetTeleopParam()");
    TeleopParam param;
    _TEST_CONNECTTED(param);

    param.lin_spd_lmt_ratio =
            _GetDyFloatParam(STD_TELEOP_PARAM_NS + "/lin_spd_lmt_ratio");
    param.ang_spd_lmt_ratio =
            _GetDyFloatParam(STD_TELEOP_PARAM_NS + "/ang_spd_lmt_ratio");

    return param;
}
void NpuServer::SetTeleopParam(TeleopParam param)
{
    API_DEBUG("SetTeleopParam()");
    _TEST_CONNECTTED(void());

    _SetDyFloatParam(
                STD_TELEOP_PARAM_NS + "/lin_spd_lmt_ratio", param.lin_spd_lmt_ratio);
    _SetDyFloatParam(
                STD_TELEOP_PARAM_NS + "/ang_spd_lmt_ratio", param.ang_spd_lmt_ratio);
    teleop_param_ = param;
}

const string PARAM_TOKEN = "/csg";

void NpuServer::GetParamByPtree(ptree &pt_root)
{
    ros::NodeHandle nh;

    vector<string> keys;
    nh.getParamNames(keys);
    string prefix = STD_TELEOP_PARAM_NS + PARAM_TOKEN + "/";
    const int prefix_len = prefix.length();
    string pattern_str = prefix + ".*";
    boost::regex pattern(pattern_str);
    for (auto key : keys) {
        if(!boost::regex_match(key, pattern) ){
            continue;
        }
        string path = key.substr(prefix_len);
        double value = 0.0;
        try {
            value = nh.param<double>(key, 0.0);
            LM_DEBUG("path(%s) key(%s) -> value(%.2f)", path.c_str(), key.c_str(), value);

        } catch (exception& e) {
            LM_ERROR("exception: %s", e.what());
        }
        pt_root.add(path, value);
    }

    map<string, string> _map;
    nh.getParam(STD_TELEOP_PARAM_NS, _map);
    for (auto e : _map) {
    }
}

void NpuServer::SetParamByPtree(const ptree &pt_root)
{
    try {
        auto iter = pt_root.begin();
        while (iter != pt_root.end()) {
            string path = iter->first;
            string key = STD_TELEOP_PARAM_NS + PARAM_TOKEN + "/" + path;
            double value = iter->second.get_value<double>();
            LM_DEBUG("key(%s) -> value(%.2f)", key.c_str(), value);
            _SetDyFloatParam(key, value);
            iter++;
        }
    } catch (exception& e) {
        LM_ERROR("%s", e.what());
    }
}

// navi
NaviParam NpuServer::GetNaviParam()
{
    APIG_DEBUG("GetNaviParam()");
    NaviParam param;
    _TEST_CONNECTTED(param);
    //    ros::NodeHandle navi_nh(STD_NAVI_CORE_PARAM_NS);
    //    ros::NodeHandle blp_nh(STD_LOCAL_PLANNER_PARAM_NS);

    if (pthread_spin_trylock(&get_naviparam_spinlock_) != 0)
    {
        ROS_ERROR("GetNaviParam() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return param;
    }

    APIG_DEBUG("StartGetNaviParam");

    /// common
    param.lin_spd_lmt_ratio =
            _GetDyFloatParam(STD_NAVI_PARAM_NS + "/lin_spd_lmt_ratio");
    param.ang_spd_lmt_ratio =
            _GetDyFloatParam(STD_NAVI_PARAM_NS + "/ang_spd_lmt_ratio");
    param.x_err_tolr_m = _GetDyFloatParam(STD_NAVI_PARAM_NS + "/x_err_tolr_m");
    param.y_err_tolr_m = _GetDyFloatParam(STD_NAVI_PARAM_NS + "/y_err_tolr_m");
    param.yaw_err_tolr_rad =
            _GetDyFloatParam(STD_NAVI_PARAM_NS + "/yaw_err_tolr_rad");
    /// planners
    // p2p(9)
    /*DWA
    param.p2p_planner_param.acc_lim_x_mps2 =
            _GetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_1");
    param.p2p_planner_param.acc_lim_theta_rps2 =
            _GetDyFloatParam( STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_3");
    param.p2p_planner_param.max_vel_x_mps =
            _GetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_6");
    param.p2p_planner_param.min_vel_x_mps =
            _GetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_7");
    param.p2p_planner_param.max_rot_vel_rps =
            _GetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_10");
    param.p2p_planner_param.min_rot_vel_rps =
            _GetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_11");
    param.p2p_planner_param.plan_strictness =
            _GetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_21");
    */
    param.p2p_planner_param.max_vel_x_mps =
            _GetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/max_trans_vel");
    param.p2p_planner_param.max_rot_vel_rps =
            _GetDyFloatParam( STD_LOCAL_PLANNER_PARAM_NS + "/max_rot_vel");
    param.p2p_planner_param.path_approach_avenue_factor =
            _GetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                             + "/global_costmap/inflation_layer/path_approach_avenue_factor");
    param.p2p_planner_param.safety_inflation_factor =
            _GetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                             + "/local_costmap/inflation_layer/safety_inflation_factor");

    //    path_approach_avenue_factor_ =
    //            param.p2p_planner_param.path_approach_avenue_factor;
    //    safety_inflation_factor_ =
    //            param.p2p_planner_param.safety_inflation_factor;

    // pf(7)
    param.pf_planner_param.waypoint_mode =
            _GetDyBoolParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/waypoint_mode");
    //    param.pf_planner_param.enable_acute_turn =
    //    _GetDyBoolParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/enable_acute_turn");
    param.pf_planner_param.look_ahead_dist_m =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS
                             + "/look_ahead_dist_m");
    param.pf_planner_param.position_control_tolerance_m =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS
                             + "/position_control_tolerance_m");
    param.pf_planner_param.heading_control_tolerance_rad =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS
                             + "/heading_control_tolerance_rad");
    param.pf_planner_param.max_lin_vel_mps =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/max_lin_vel_mps");
    param.pf_planner_param.max_ang_vel_rps =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/max_ang_vel_rps");
    param.pf_planner_param.heading_kp =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/heading_kp");

    param.pf_planner_param.forward_max_lin_vel =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_max_lin_vel_mps");
    param.pf_planner_param.forward_min_lin_vel =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_min_lin_vel_mps");
    param.pf_planner_param.forward_max_ang_vel =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_max_ang_vel_mps");
    param.pf_planner_param.spin_max_ang_vel =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/spin_max_ang_vel_rps");
    param.pf_planner_param.spin_min_ang_vel =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/spin_min_ang_vel_rps");
    param.pf_planner_param.spin_deflection_kp =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/spin_deflection_kp");
    param.pf_planner_param.parking_distance_k =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/parking_distence_k");
    param.pf_planner_param.parking_offset =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/parking_offset");
    param.pf_planner_param.decelerate_distence_k =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/decelerate_distence_k");
    param.pf_planner_param.decelerate_offset =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/decelerate_offset");
    param.pf_planner_param.ang_limit_kp =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/ang_limit_kp");
    param.pf_planner_param.forward_deflection_kp =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_deflection_kp");
    param.pf_planner_param.forward_deflection_ki =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_deflection_ki");
    param.pf_planner_param.forward_deflection_kd =
            _GetDyFloatParam(STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_deflection_kd");

    // ccp
    param.ccp_planner_param.coverage_spacing_m =
            _GetDyFloatParam(STD_NAVI_CCP_PLANNER_PARAM_NS
                             + "/coverage_spacing_m");

    en_get_param_ = true;
    navi_param_ = param;

    APIG_DEBUG("GetNaviParamDone");

    pthread_spin_unlock(&get_naviparam_spinlock_);

    return param;
}
void NpuServer::SetNaviParam(NaviParam param)
{
    API_DEBUG("SetNaviParam()");
    _TEST_CONNECTTED(void());

    if (pthread_spin_trylock(&set_naviparam_spinlock_) != 0)
    {
        ROS_ERROR("SetNaviParam() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    API_DEBUG("StartSetNaviParam");
    ros::NodeHandle nh;
    /// common
    _SetDyFloatParam(
                STD_NAVI_PARAM_NS + "/lin_spd_lmt_ratio", param.lin_spd_lmt_ratio);
    _SetDyFloatParam(
                STD_NAVI_PARAM_NS + "/ang_spd_lmt_ratio", param.ang_spd_lmt_ratio);
    _SetDyFloatParam(
                STD_NAVI_PARAM_NS + "/x_err_tolr_m", param.x_err_tolr_m);
    _SetDyFloatParam(
                STD_NAVI_PARAM_NS + "/y_err_tolr_m", param.y_err_tolr_m);
    _SetDyFloatParam(
                STD_NAVI_PARAM_NS + "/yaw_err_tolr_rad", param.yaw_err_tolr_rad);
    /// planners
    // p2p
    /* DWA
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_1",
                     param.p2p_planner_param.acc_lim_x_mps2);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_3",
                     param.p2p_planner_param.acc_lim_theta_rps2);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_4",
                     param.p2p_planner_param.max_vel_x_mps);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_6",
                     param.p2p_planner_param.max_vel_x_mps);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_5",
                     fabs(param.p2p_planner_param.min_vel_x_mps));
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_7",
                     param.p2p_planner_param.min_vel_x_mps);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_10",
                     param.p2p_planner_param.max_rot_vel_rps);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_11",
                     param.p2p_planner_param.min_rot_vel_rps);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_21",
                     param.p2p_planner_param.plan_strictness);
    //    ROS_INFO("p2p_planner_param.plan_strictness is: %f.", param.p2p_planner_param.plan_strictness);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_13",
                     hypot( param.x_err_tolr_m, param.y_err_tolr_m ) * 2.0 );
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/wrlp_12",
                     param.yaw_err_tolr_rad * 2.0)
    */

    param.p2p_planner_param.max_vel_x_mps = min(float(0.8), param.p2p_planner_param.max_vel_x_mps);
    param.p2p_planner_param.max_rot_vel_rps = min(float(1.2), param.p2p_planner_param.max_rot_vel_rps);
    param.x_err_tolr_m = max(float(0.12), param.x_err_tolr_m);
    param.yaw_err_tolr_rad = max(float(0.12), param.yaw_err_tolr_rad);

    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/max_trans_vel",
                     param.p2p_planner_param.max_vel_x_mps);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/max_rot_vel",
                     param.p2p_planner_param.max_rot_vel_rps);
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/xy_goal_tolerance",
                     hypot( param.x_err_tolr_m, param.y_err_tolr_m ) * 2.0 );
    _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS + "/yaw_goal_tolerance",
                     param.yaw_err_tolr_rad * 2.0);

    _SetDyFloatParam(
                STD_NAVI_CORE_PARAM_NS
                + "/global_costmap/inflation_layer/path_approach_avenue_factor",
                param.p2p_planner_param.path_approach_avenue_factor);
    _SetDyFloatParam(
                STD_NAVI_CORE_PARAM_NS
                + "/local_costmap/inflation_layer/safety_inflation_factor",
                param.p2p_planner_param.safety_inflation_factor);
    //pf
    _SetDyBoolParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/waypoint_mode",
                param.pf_planner_param.waypoint_mode);
    //    _SetDyBoolParam(
    //                STD_NAVI_PF_PLANNER_PARAM_NS + "/enable_acute_turn",
    //                param.pf_planner_param.enable_acute_turn);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/look_ahead_dist_m",
                param.pf_planner_param.look_ahead_dist_m);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/position_control_tolerance_m",
                param.pf_planner_param.position_control_tolerance_m);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/heading_control_tolerance_rad",
                param.pf_planner_param.heading_control_tolerance_rad);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/max_lin_vel_mps",
                param.pf_planner_param.max_lin_vel_mps);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/max_ang_vel_rps",
                param.pf_planner_param.max_ang_vel_rps);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/heading_kp",
                param.pf_planner_param.heading_kp);

    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_max_lin_vel_mps",
                param.pf_planner_param.forward_max_lin_vel);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_min_lin_vel_mps",
                param.pf_planner_param.forward_min_lin_vel);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_max_ang_vel_mps",
                param.pf_planner_param.forward_max_ang_vel);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/spin_max_ang_vel_rps",
                param.pf_planner_param.spin_max_ang_vel);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/spin_min_ang_vel_rps",
                param.pf_planner_param.spin_min_ang_vel);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/spin_deflection_kp",
                param.pf_planner_param.spin_deflection_kp);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/parking_distence_k",
                param.pf_planner_param.parking_distance_k);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/parking_offset",
                param.pf_planner_param.parking_offset);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/decelerate_distence_k",
                param.pf_planner_param.decelerate_distence_k);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/decelerate_offset",
                param.pf_planner_param.decelerate_offset);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/ang_limit_kp",
                param.pf_planner_param.ang_limit_kp);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_deflection_kp",
                param.pf_planner_param.forward_deflection_kp);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_deflection_ki",
                param.pf_planner_param.forward_deflection_ki);
    _SetDyFloatParam(
                STD_NAVI_PF_PLANNER_PARAM_NS + "/forward_deflection_kd",
                param.pf_planner_param.forward_deflection_kd);

    // ccp to do!
    //    _SetDyFloatParam(
    //                STD_NAVI_CCP_PLANNER_PARAM_NS + "/coverage_spacing_m",
    //                param.ccp_planner_param.coverage_spacing_m);

    /// ccp planner, todo!
    //    UpdateCcpPlannerParam_();
    /// path follower
    UpdatePathFollowerParam_();
    /// p2p planner
    UpdateP2pPlannerParam_();
    /// footprint
    UpdateFootprint_(base_param_.footprint_param);

    navi_param_ = param;
    API_DEBUG("SetNaviParamDone");
    pthread_spin_unlock(&set_naviparam_spinlock_);
    API_DEBUG("DONE");
}
// slam
SlamParam NpuServer::GetSlamParam()
{
    APIG_DEBUG("GetSlamParam()");
    SlamParam param;
    _TEST_CONNECTTED(param);

    /// common
    param.map_res_mpp = _GetDyFloatParam(STD_SLAM_PARAM_NS + "/map_res_mpp");
    //    param.map_size_m = _GetDyIntParam(STD_SLAM_PARAM_NS + "/max_map_size_m");
    param.update_dist_thrs_m =
            _GetDyFloatParam(STD_SLAM_PARAM_NS + "/update_dist_thrs_m");
    param.update_ori_thrs_rad =
            _GetDyFloatParam(STD_SLAM_PARAM_NS + "/update_ori_thrs_rad");
    param.max_lin_spd_mps =
            _GetDyFloatParam(STD_SLAM_PARAM_NS + "/max_lin_spd_mps");
    param.max_ang_spd_rps =
            _GetDyFloatParam(STD_SLAM_PARAM_NS + "/max_ang_spd_rps");
    /// optimizer
#if USE_NPU_API_V_0_9_12
    param.optimizer.enb_map_opt =
            _GetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/enbaled");
    param.optimizer.enb_trace_clearing =
            _GetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/enb_trace_clearing");
    param.optimizer.enb_filtering =
            _GetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/enb_filtering");
    param.optimizer.update_dist_thrs_m =
            _GetDyFloatParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/update_dist_thrs_m");
    param.optimizer.update_ori_thrs_rad =
            _GetDyFloatParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/update_ori_thrs_rad");
    param.optimizer.filter_size =
            _GetDyIntParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/filter_size");
#else
    param.optimizer_param.enb_map_opt =
            _GetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/enabled");
    param.optimizer_param.enb_trace_clearing =
            _GetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/enb_trace_clearing");
    param.optimizer_param.enb_filtering =
            _GetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/enb_filtering");
    param.optimizer_param.update_dist_thrs_m =
            _GetDyFloatParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/update_dist_thrs_m");
    param.optimizer_param.update_ori_thrs_rad =
            _GetDyFloatParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/update_ori_thrs_rad");
    param.optimizer_param.filter_size =
            _GetDyIntParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/filter_size");
#endif
    return param;
}
void NpuServer::SetSlamParam(SlamParam param)
{
    APIG_DEBUG("SetSlamParam()");
    _TEST_CONNECTTED(void());

    /// common
    _SetDyFloatParam(STD_SLAM_PARAM_NS + "/map_res_mpp", param.map_res_mpp);
    _SetDyFloatParam(STD_SLAM_PARAM_NS
                     + "/update_dist_thrs_m", param.update_dist_thrs_m);
    _SetDyFloatParam(STD_SLAM_PARAM_NS
                     + "/update_ori_thrs_rad", param.update_ori_thrs_rad);
    _SetDyFloatParam(STD_SLAM_PARAM_NS
                     + "/max_lin_spd_mps", param.max_lin_spd_mps);
    _SetDyFloatParam(STD_SLAM_PARAM_NS
                     + "/max_ang_spd_rps", param.max_ang_spd_rps);
    /// optimizer
#if USE_NPU_API_V_0_9_12
    _SetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS
                    + "/enb_map_opt", param.optimizer.enb_map_opt);
    _SetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS
                    + "/enb_trace_clearing", param.optimizer.enb_trace_clearing);
    _SetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS
                    + "/enb_filtering", param.optimizer.enb_filtering);
    _SetDyFloatParam(STD_SLAM_OPTIMIZER_PARAM_NS
                     + "/update_dist_thrs_m", param.optimizer.update_dist_thrs_m);
    _SetDyFloatParam(STD_SLAM_OPTIMIZER_PARAM_NS
                     + "/update_ori_thrs_rad", param.optimizer.update_ori_thrs_rad);
    _SetDyIntParam(STD_SLAM_OPTIMIZER_PARAM_NS
                   + "/filter_size", param.optimizer.filter_size);
#else
    _SetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/enb_map_opt",
                    param.optimizer_param.enb_map_opt);
    _SetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/enb_trace_clearing",
                    param.optimizer_param.enb_trace_clearing);
    _SetDyBoolParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/enb_filtering",
                    param.optimizer_param.enb_filtering);
    _SetDyFloatParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/update_dist_thrs_m",
                     param.optimizer_param.update_dist_thrs_m);
    _SetDyFloatParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/update_ori_thrs_rad",
                     param.optimizer_param.update_ori_thrs_rad);
    _SetDyIntParam(STD_SLAM_OPTIMIZER_PARAM_NS + "/filter_size",
                   param.optimizer_param.filter_size);
#endif
    slam_param_ = param;
}
#if USE_NPU_API_V_0_9_12
// record & play
RecordParam NpuServer::GetRecordParam()
{
    APIG_DEBUG("GetRecordParam()");
    RecordParam param;
    _TEST_CONNECTTED(param);

    param.enb_record = _GetDyBoolParam(STD_BAG_PARAM_NS + "/enb_record");
    param.record_tf_static = _GetDyBoolParam(STD_BAG_PARAM_NS + "/record_tf_static");
}

void NpuServer::SetRecordParam(const RecordParam &param)
{
    API_DEBUG("SetRecordParam()");
    _TEST_CONNECTTED(void());

    _SetDyBoolParam(STD_BAG_PARAM_NS + "/enb_record", param.enb_record);
    _SetDyBoolParam(STD_BAG_PARAM_NS + "/record_tf_static", param.record_tf_static);
}

PlayParam NpuServer::GetPlayParam()
{
    APIG_DEBUG("GetPlayParam()");
    PlayParam param;
    _TEST_CONNECTTED(param);

    param.enb_play = _GetDyBoolParam(STD_BAG_PARAM_NS + "/enb_play");
    param.play_tf_static = _GetDyBoolParam(STD_BAG_PARAM_NS + "/play_tf_static");
    param.play_rate = _GetDyFloatParam(STD_BAG_PARAM_NS + "/play_rate");
}

void NpuServer::SetPlayParam(const PlayParam &param)
{
    API_DEBUG("SetBagParam()");
    _TEST_CONNECTTED(void());

    _SetDyBoolParam(STD_BAG_PARAM_NS + "/enb_play", param.enb_play);
    _SetDyBoolParam(STD_BAG_PARAM_NS + "/play_tf_static", param.play_tf_static);
    _SetDyFloatParam(STD_BAG_PARAM_NS + "/play_rate", param.play_rate);
}
#endif

//// slave mode
void NpuServer::EnableSlaveMode(bool enb)
{
    API_DEBUG("EnableSlaveMode()");
    _TEST_CONNECTTED(void());

    string key = STD_BASE_PARAM_NS + "/enb_slave_mode";
    bool value = enb;
    if (_SetDyBoolParam(key, value) != true)
    {
        API_DEBUG("EnableSlaveMode() Fail!");
        return;
    }
    base_param_.enb_slave_mode = enb;
    API_DEBUG("EnableSlaveMode() Success!");
    return;
}
void NpuServer::FeedMotorEnc(MotorEnc enc)
{
    API_DEBUG("FeedMotorEnc()");
    if (server_state_ != CONNECTED)
    {
        ROS_ERROR("Not connected, pls connect or reconnect.");
        //throw new Ice::Exception("Not connected, pls connect or reconnect.", -1);
        return;
    }
    if (!base_param_.enb_slave_mode)
    {
        ROS_WARN("Not in slave mode, skipped.");
        return;
    }
    static int cnt = 0;
    if (cnt++ == 0)
    {
        enc_offset_ = enc;
    }

    std::vector<int> ticks;
    ticks.resize(enc.ticks.size());
    for (int i = 0; i < ticks.size(); i++)
    {
        ticks[i] = enc.ticks[i] - enc_offset_.ticks[i];
    }

    wr_npu_msgs::MotorEnc motor_enc;
    motor_enc.header.stamp = ros::Time::now();
    motor_enc.header.frame_id = STD_ODOM_FRAME_ID;
    motor_enc.child_frame_id = STD_BASE_FTP_FRAME_ID;
    motor_enc.model_type =
            EnumString<ChassisModelType>::EnumToStr(
                base_param_.chassis_param.model_type);
    motor_enc.motor_num = enc.motor_num;
    motor_enc.ticks = ticks;
    motor_enc.degs.resize(enc.ticks.size());
    motor_enc.rdc_degs.resize(enc.ticks.size());
    for (int i = 0; i < enc.ticks.size(); i++)
    {
        motor_enc.degs[i] =
                360.0 * ticks[i] / base_param_.motor_param.motor_enc_res_ppr / 4;// (AB_CHNL)
        motor_enc.rdc_degs[i] =
                motor_enc.degs[i] / base_param_.motor_param.motor_rdc_ratio;
    }
    motor_enc_pub_.publish(motor_enc);

    last_enc_ = enc;
    _TEST_CONNECTTED(void());
    return;
}
void NpuServer::FeedActMotorSpd(MotorSpd spd)
{
    API_DEBUG("FeedActMotorSpd()");
    if (server_state_ != CONNECTED)
    {
        ROS_ERROR("Not connected, pls connect or reconnect.");
        //throw new Ice::Exception("Not connected, pls connect or reconnect.", -1);
        return;
    }
    if (!base_param_.enb_slave_mode)
    {
        ROS_WARN("Not in slave mode, skipped.");
        return;
    }
    wr_npu_msgs::MotorSpd act_motor_spd;
    act_motor_spd.header.stamp = ros::Time::now();
    act_motor_spd.header.frame_id = STD_BASE_FTP_FRAME_ID;
    act_motor_spd.model_type =
            EnumString<ChassisModelType>::EnumToStr(
                base_param_.chassis_param.model_type);
    act_motor_spd.motor_num = spd.motor_num;
    act_motor_spd.rpms = spd.rpms;
    act_motor_spd.rpss.resize(spd.rpms.size());
    act_motor_spd.rdc_rpss.resize(spd.rpms.size());
    for (int i = 0; i < spd.rpms.size(); i++)
    {
        act_motor_spd.rpss[i] = spd.rpms[i] / 60.0;
        act_motor_spd.rdc_rpss[i] =
                act_motor_spd.rpss[i] / base_param_.motor_param.motor_rdc_ratio;
    }
    act_motor_spd_pub_.publish(act_motor_spd);
    _TEST_CONNECTTED(void());
    return;
}

//// motor data
// enc
MotorEnc NpuServer::GetMotorEnc()
{
    APIG_DEBUG("GetMotorEnc()");
    MotorEnc motor_enc;
    motor_enc.motor_num = 0;
    _TEST_CONNECTTED(motor_enc);
    if (pthread_spin_trylock(&motor_enc_spinlock_) != 0)
    {
        ROS_ERROR("GetMotorEnc() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return motor_enc;
    }
    motor_enc = current_enc_;
    pthread_spin_unlock(&motor_enc_spinlock_);

    if (motor_enc.ticks.size() < 2)
    {
        APIG_DEBUG("GetMotorEnc() motor_enc: (%d)", motor_enc.motor_num);
    }
    else
    {
        APIG_DEBUG("GetMotorEnc() motor_enc: (%d, [%d, %d])", motor_enc.motor_num, motor_enc.ticks[0], motor_enc.ticks[1]);
    }
    return motor_enc;
}
void NpuServer::ClearMotorEnc()
{
    API_DEBUG("ClearMotorEnc()");
    _TEST_CONNECTTED(void());
    if (base_param_.enb_slave_mode)
    {
        enc_offset_ = last_enc_;
    }
    ROS_INFO("NpuServer::ClearMotorEnc()");
    ros::NodeHandle nh;
    wr_npu_msgs::ClearMotorEnc srv;
    std::string srv_name = "/chassis_server_node/clear_motor_enc";
    ros::ServiceClient client =
            nh.serviceClient<wr_npu_msgs::ClearMotorEnc>(srv_name);
    if (client.isValid() != true)
    {
        return;
    }
    if (client.isValid() != true)
    {
        ROS_ERROR("NpuServer::ClearMotorEnc(): service \"%s\" is not vaild",
                  srv_name.c_str());
        return;
    }
    bool rlt = client.call(srv);
    if (!rlt)
    {
        ROS_ERROR("NpuServer::ClearMotorEnc(): Call service \"%s\", rlt = %s",
                  srv_name.c_str(), BoolToStr(rlt).c_str());
        return;
    }
    ROS_INFO("NpuServer::ClearMotorEnc(): Call service \"%s\", rlt = %s",
             srv_name.c_str(), BoolToStr(rlt).c_str());
}
// spd
MotorSpd NpuServer::GetActMotorSpd()
{
    APIG_DEBUG("GetActMotorSpd()");
    MotorSpd motor_spd;
    motor_spd.motor_num = 0;
    _TEST_CONNECTTED(motor_spd);
    if (pthread_spin_trylock(&act_motor_spd_spinlock_) != 0)
    {
        ROS_ERROR("GetActMotorSpd() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return motor_spd;
    }
    motor_spd = act_motor_spd_;
    pthread_spin_unlock(&act_motor_spd_spinlock_);
    APIG_DEBUG("GetActMotorSpd() motor_spd: (%d, [%.2f, %.2f])",
               motor_spd.motor_num, motor_spd.rpms[0], motor_spd.rpms[1]);
    return motor_spd;
}
MotorSpd NpuServer::GetCmdMotorSpd()
{
    APIG_DEBUG("GetCmdMotorSpd()");
    MotorSpd motor_spd;
    motor_spd.motor_num = 0;
    _TEST_CONNECTTED(motor_spd);
    if (pthread_spin_trylock(&cmd_motor_spd_spinlock_) != 0)
    {
        ROS_ERROR("GetCmdMotorSpd() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return motor_spd;
    }
    motor_spd = cmd_motor_spd_;
    pthread_spin_unlock(&cmd_motor_spd_spinlock_);
    APIG_DEBUG("GetCmdMotorSpd() motor_spd: (%d, [%.2f, %.2f])",
               motor_spd.motor_num, motor_spd.rpms[0], motor_spd.rpms[1]);
    return motor_spd;
}

//// sensor data
// lidar
LidarScan NpuServer::GetLidarScan()
{
    APIG_DEBUG("GetLidarScan()");
    LidarScan lidar_scan;
    lidar_scan.points.resize(0);
    lidar_scan.intensities.resize(0);
    _TEST_CONNECTTED(lidar_scan);

    if (enb_direct_sensor_trans_)
    {
        APIG_DEBUG("GetLidarScan() enb_direct_sensor_trans_: true");
        laser_geometry::LaserProjection prj;
        sensor_msgs::PointCloud scan_pc;
        if (pthread_spin_trylock(&lidar_scan_spinlock_) != 0)
        {
            ROS_ERROR("GetLidarScan() Get thread lock failed 02");
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
            return lidar_scan;
        }
        prj.projectLaser(lidar_scan_msg_, scan_pc);
        pthread_spin_unlock(&lidar_scan_spinlock_);
        direct_trans_lidar_scan_.points.resize(scan_pc.points.size());

        for (int i = 0; i < scan_pc.points.size(); i++)
        {
            const geometry_msgs::Point32& scan_pnt = scan_pc.points[i];
            tf::Vector3 scan_pnt_base(
                        base_to_lidar_tf_ *
                        tf::Vector3(scan_pnt.x, scan_pnt.y, scan_pnt.z));

            float x_w = cos(act_pose_.yaw) * scan_pnt_base.x()
                    - sin(act_pose_.yaw) * scan_pnt_base.y()
                    + act_pose_.x;
            float y_w = sin(act_pose_.yaw) * scan_pnt_base.x()
                    + cos(act_pose_.yaw) * scan_pnt_base.y()
                    + act_pose_.y;
            float z_w = act_pose_.z + scan_pnt_base.z();
            direct_trans_lidar_scan_.points[i].x = x_w;
            direct_trans_lidar_scan_.points[i].y = y_w;
            direct_trans_lidar_scan_.points[i].z = z_w;
        }
        lidar_scan = direct_trans_lidar_scan_;
    }
    else
    {
        APIG_DEBUG("GetLidarScan() enb_direct_sensor_trans_: false");
        if (pthread_spin_trylock(&tf_lidar_scan_spinlock_) != 0)
        {
            ROS_ERROR("GetLidarScan() Get thread lock failed 01");
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
            return lidar_scan;
        }
        lidar_scan = tf_trans_lidar_scan_pc_;
        pthread_spin_unlock(&tf_lidar_scan_spinlock_);
    }

    APIG_DEBUG("GetLidarScan() lidar_scan.point.size: %d", static_cast<int>(lidar_scan.points.size()));
    if(lidar_scan.points.size() > 0)
    {
        APIG_DEBUG("GetLidarScan() lidar_scan.point[0]: (%.2f, %.2f, %.2f, )",
                   lidar_scan.points[0].x, lidar_scan.points[0].y,
                lidar_scan.points[0].z);
    }
    return lidar_scan;
}
ImgLidarScan NpuServer::GetImgLidarScan()
{
    APIG_DEBUG("GetImgLidarScan()");
    ImgLidarScan img_lidar_scan_;
    img_lidar_scan_.points.resize(0);
    img_lidar_scan_.intensities.resize(0);
    _TEST_CONNECTTED(img_lidar_scan_);

    LidarScan lidar_scan = GetLidarScan();
    APIG_DEBUG("GetImgLidarScan() Point Size: %d",
               static_cast<int>(lidar_scan.points.size()));
    if (lidar_scan.points.size() <= 0) {
        APIG_DEBUG("GetImgLidarScan() Invaild lidar scan data.");
        return img_lidar_scan_;
    }
    TransformPointList_(lidar_scan.points, img_lidar_scan_.points);
    API_DEBUG("GetImgLidarScan() Done");
    return img_lidar_scan_;
}
// imu
ImuData NpuServer::GetImuData()
{
    APIG_DEBUG("GetImuData()");
    ImuData imu_data_zero;
    imu_data_zero.roll_deg = 0.0;
    imu_data_zero.pitch_deg = 0.0;
    imu_data_zero.yaw_deg = 0.0;
    _TEST_CONNECTTED(imu_data_zero);
    return imu_data_;
}
// sonar
SonarScan NpuServer::GetSonarScan()
{
    APIG_DEBUG("GetSonarScan()");
    SonarScan return_value;
    _TEST_CONNECTTED(return_value);
    return return_value;
}
ImgSonarScan NpuServer::GetImgSonarScan()
{
    APIG_DEBUG("GetImgSonarScan()");
    ImgSonarScan return_value;
    _TEST_CONNECTTED(return_value);
    return return_value;
}
// infrd
InfrdScan NpuServer::GetInfrdScan()
{
    APIG_DEBUG("GetInfrdScan()");
    InfrdScan return_value;
    _TEST_CONNECTTED(return_value);
    return return_value;
}
ImgInfrdScan NpuServer::GetImgInfrdScan()
{
    APIG_DEBUG("GetImgInfrdScan()");
    ImgInfrdScan return_value;
    _TEST_CONNECTTED(return_value);
    return return_value;
}
// bumper
BumperArray NpuServer::GetBumperArray()
{
    APIG_DEBUG("GetBumperArray()");
    BumperArray return_value;
    _TEST_CONNECTTED(return_value);
    return return_value;
}
// battery
BatteryStatus NpuServer::GetBatteryStatus()
{
    APIG_DEBUG("GetBatteryStatus()");
    BatteryStatus return_value;
    _TEST_CONNECTTED(return_value);
    return return_value;
}

//// manual control
void NpuServer::SetManualCmd(ManualCmdType cmd)
{
    API_DEBUG("SetManualCmd(): %s", EnumString<ManualCmdType>::EnumToStr(cmd).c_str());
    API_DEBUG("manual_cmd_count = %d, auto_tick_cnt_ = %d", manual_ctrl_cnt_, auto_tick_cnt_);
    if (manual_ctrl_cnt_ == 0)
    {
        manual_ctrl_cnt_ = auto_tick_cnt_ = 1;
    }
    else
    {
        manual_ctrl_cnt_ =  auto_tick_cnt_;
    }
    float lin_vel_mps = 0.0;
    float ang_vel_rps = 0.0;
    switch(cmd)
    {
    case MOVE_FWD:
    {
        lin_vel_mps = MIN(1.0 * auto_tick_cnt_ / base_param_.chassis_param.max_lin_acc_time_s, teleop_param_.lin_spd_lmt_ratio)
                * base_param_.chassis_param.autoset_max_lin_spd_mps;
        ang_vel_rps = 0.0;
        break;
    }
    case MOVE_BCK:
    {
        lin_vel_mps = -MIN(1.0 * auto_tick_cnt_ / base_param_.chassis_param.max_lin_acc_time_s, teleop_param_.lin_spd_lmt_ratio)
                * base_param_.chassis_param.autoset_max_lin_spd_mps;
        ang_vel_rps = 0.0;
        break;
    }
    case TURN_LFT:
    {
        lin_vel_mps = 0.0;
//        ang_vel_rps = MIN(1.0 * auto_tick_cnt_ / base_param_.chassis_param.max_ang_acc_time_s, teleop_param_.ang_spd_lmt_ratio)
//                * base_param_.chassis_param.autoset_max_ang_spd_rps;
        ang_vel_rps = 0.4;
        break;
    }
    case TURN_RGT:
    {
        lin_vel_mps = 0.0;
//        ang_vel_rps = -MIN(1.0 * auto_tick_cnt_ / base_param_.chassis_param.max_ang_acc_time_s, teleop_param_.ang_spd_lmt_ratio)
//                * base_param_.chassis_param.autoset_max_ang_spd_rps;
        ang_vel_rps = -0.4;
        break;
    }
    case MOVE_FWD_LFT:
    {
        lin_vel_mps = MIN(1.0 * auto_tick_cnt_ / base_param_.chassis_param.max_lin_acc_time_s, teleop_param_.lin_spd_lmt_ratio)
                * base_param_.chassis_param.autoset_max_lin_spd_mps;
        ang_vel_rps = MIN(1.0 * auto_tick_cnt_ / base_param_.chassis_param.max_ang_acc_time_s, teleop_param_.ang_spd_lmt_ratio)
                * base_param_.chassis_param.autoset_max_ang_spd_rps;
        break;
    }
    case MOVE_FWD_RGT:
    {
        lin_vel_mps = MIN(1.0 * auto_tick_cnt_ / base_param_.chassis_param.max_lin_acc_time_s, teleop_param_.lin_spd_lmt_ratio)
                * base_param_.chassis_param.autoset_max_lin_spd_mps;
        ang_vel_rps = -MIN(1.0 * auto_tick_cnt_ / base_param_.chassis_param.max_ang_acc_time_s, teleop_param_.ang_spd_lmt_ratio)
                * base_param_.chassis_param.autoset_max_ang_spd_rps;
        break;
    }
    case STOP_MOVE:
    default:
    {
        lin_vel_mps = 0.0;
        ang_vel_rps = 0.0;
        manual_ctrl_cnt_ = auto_tick_cnt_ = 0;
        break;
    }
    }
    if (npu_state_ == SLAM_STATE)
    {
        lin_vel_mps = RANGE(lin_vel_mps, -slam_param_.max_lin_spd_mps, slam_param_.max_lin_spd_mps);
        ang_vel_rps = RANGE(ang_vel_rps, -slam_param_.max_ang_spd_rps, slam_param_.max_ang_spd_rps);
        API_DEBUG("NpuServer::SetManualCmd(): slam_param_.max_lin_spd_mps = %.2f, slam_param_.max_ang_spd_rps = %.2f"
                  , slam_param_.max_lin_spd_mps, slam_param_.max_ang_spd_rps);
    }
    API_DEBUG("SetManualCmd(): lin_vel_mps = %.2f, ang_vel_rps = %.2f", lin_vel_mps, ang_vel_rps);
    PublishManualVel_(lin_vel_mps, ang_vel_rps);
}
void NpuServer::SetManualVel(float lin_scale, float ang_scale)
{
    API_DEBUG("SetManualVel(): lin_scale = %.2f, ang_scale = %.2f", lin_scale, ang_scale);
//    ang_scale = 0.06;
    //API_DEBUG("SetManualVel(): manual_ctrl_cnt_ = %d, auto_tick_cnt_ = %d", manual_ctrl_cnt_, auto_tick_cnt_);
    float radius = sqrt(SQU(lin_scale) + SQU(ang_scale));
    if (radius > 1.0)
    {
        lin_scale /= radius;
        ang_scale /= radius;
    }
    //    lin_scale = RANGE(lin_scale, -1.0, 1.0);
    //    ang_scale = RANGE(ang_scale, -1.0, 1.0);
    if (fabs(lin_scale) < 0.01)
    {
        lin_scale = 0;
    }
    /// square-scaling
    lin_scale = SIGN(lin_scale) * SQU(lin_scale);
    ang_scale = SIGN(ang_scale) * SQU(ang_scale);

    float lin_vel_mps = lin_scale * teleop_param_.lin_spd_lmt_ratio * base_param_.chassis_param.autoset_max_lin_spd_mps;
    float ang_vel_rps = ang_scale * teleop_param_.ang_spd_lmt_ratio * base_param_.chassis_param.autoset_max_ang_spd_rps;
    //API_DEBUG("SetManualVel(): teleop_param_.lin_spd_lmt_ratio = %.2f, teleop_param_.ang_spd_lmt_ratio = %.2f"
    //, teleop_param_.lin_spd_lmt_ratio, teleop_param_.ang_spd_lmt_ratio);
    //API_DEBUG("SetManualVel(): chassis_param_.max_lin_spd_mps = %.2f, chassis_param_.max_ang_spd_rps = %.2f"
    //, base_param_.chassis_param.autoset_max_lin_spd_mps, base_param_.chassis_param.autoset_max_ang_spd_rps);
    if (npu_state_ == SLAM_STATE)
    {
        lin_vel_mps = RANGE(lin_vel_mps, -slam_param_.max_lin_spd_mps, slam_param_.max_lin_spd_mps);
        ang_vel_rps = RANGE(ang_vel_rps, -slam_param_.max_ang_spd_rps, slam_param_.max_ang_spd_rps);
        //API_DEBUG("SetManualVel(): slam_param_.max_lin_spd_mps = %.2f, slam_param_.max_ang_spd_rps = %.2f"
        //, slam_param_.max_lin_spd_mps, slam_param_.max_ang_spd_rps);

    }
    //API_DEBUG("SetManualVel(): lin_vel_mps = %.2f, ang_vel_rps = %.2f"
    //, lin_vel_mps, ang_vel_rps);
    /// filter out repeating zero
    if (!(fabs(lin_vel_mps) < 0.01 && fabs(manual_cmd_lin_vel_mps_) < 0.01
          && fabs(ang_vel_rps) < 0.01 && fabs(manual_cmd_ang_vel_rps_) < 0.01))
    {
        manual_cmd_lin_vel_mps_ = lin_vel_mps;
        manual_cmd_ang_vel_rps_ = ang_vel_rps * (lin_scale < -0.1 ? (-1) : 1);// reverse mode
        auto_tick_cnt_ = ++manual_ctrl_cnt_;
        //API_DEBUG("SetManualVel(): manual_cmd_lin_vel_mps_ = %.2f, manual_cmd_ang_vel_rps_ = %.2f"
        //, manual_cmd_lin_vel_mps_, manual_cmd_ang_vel_rps_);
    }
}

//// map management
// map
MapInfoList NpuServer::GetMapInfos()
{
    APIG_DEBUG("GetMapInfos()");
    MapInfoList map_info_list;
    map_info_list.resize(0);
    _TEST_CONNECTTED(map_info_list);
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("GetMapInfos() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return map_info_list;
    }
    _GetStructOfMapServer_("GetMapInfos", this, map_info_list);
    pthread_spin_unlock(&map_server_spinlock_);
    APIG_DEBUG("GetMapInfos() map cnt: %d", static_cast<int>(map_info_list.size()));
    //    MapInfoList::iterator map_info = map_info_list.begin();
    //    while (map_info != map_info_list.end())
    //    {
    //        APIG_DEBUG("GetMapInfos() map name: %s(thumbnail %d %d %.2f %d)",
    //                  map_info->id.c_str(),
    //                  map_info->thumbnail.width,
    //                  map_info->thumbnail.height,
    //                  map_info->thumbnail.ratio,
    //                  static_cast<int>(map_info->thumbnail.data.size()));
    //        map_info++;
    //    }
    APIG_DEBUG("GetMapInfos() Done");
    return map_info_list;
}
void NpuServer::SetMapInfos(MapInfoList list)
{
    API_DEBUG("SetMapInfos()");
    _TEST_CONNECTTED(void());
    if (list.size() <= 0)
    {
        ROS_WARN("Invalid map info list.");
        return;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("SetMapInfos() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return ;
    }
    _SetStructOfMapServer_("SetMapInfos", this, list);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}
void NpuServer::SelectMap(string id)
{
    API_DEBUG("SelectMap(\"%s\")", id.c_str());
    _TEST_CONNECTTED(void());
    if (id == "")
    {
        ROS_WARN("Invalid map id.");
        throw NpuException("", "", ERR_ILLEGAL_PARA, "Invalid map id.");
        return;
    }

    if (npu_state_ != IDLE_STATE)
    {
        ROS_ERROR("Npu state is not IDLE_STATE.");
        //throw NpuException("", "", ERR_UNKNOWN, "Npu state is not IDLE_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not IDLE_STATE.");
        return;
    }

    ros::NodeHandle nh;
    SrvMapServe srv;
    srv.request.function = "LoadMap";
    srv.request.arguments.resize(id.size()+sizeof(int));

    vector<unsigned char>::iterator p = srv.request.arguments.begin();
    p_map_handler_->PackVarLength<string>(id, p);

    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::WrMapServer>("/map_server_node/map_server");
    if (client.isValid() != true)
    {
        ROS_ERROR("SelectMap() map_server is invalid!");
        throw NpuException("", "", ERR_UNKNOWN, "map_server is invalid!");
        return;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("SelectMap() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return ;
    }
    bool rtn = client.call(srv);
    pthread_spin_unlock(&map_server_spinlock_);
    if ( rtn != true )
    {
        ROS_WARN("Load map(\"%s\") fail!", id.c_str());
        throw NpuException("", "", ERR_UNKNOWN, "Load map failed");
        return;
    }

    if (_SetDyStrParam(STD_CORE_PARAM_NS + "/MAP_ID", id) != true)
    {
        ROS_WARN("Save map id(\"%s\") fail!", id.c_str());
        throw NpuException("", "", ERR_UNKNOWN, "Save map id failed");
        return;
    }
    API_DEBUG("Success.");
    return;
}

// station
StationList NpuServer::GetStations(string map_id)
{
    APIG_DEBUG("GetStations()");
    StationList station_list;
    station_list.resize(0);
    _TEST_CONNECTTED(station_list);
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetStations() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return station_list;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return station_list;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("GetStations() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return station_list;
    }
    _GetStructOfMapServer_("GetStations", map_id, this, station_list);
    pthread_spin_unlock(&map_server_spinlock_);
    return station_list;
}

void NpuServer::SetStations(string map_id, StationList list)
{
    API_DEBUG("SetStations(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetStations() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return;
    }
    for (int i=0;i<list.size();i++) {
        API_DEBUG("station: %s(%.3f, %.3f, %.3f)", list[i].info.id.c_str(), list[i].pose.x, list[i].pose.y, list[i].pose.yaw);
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetStations() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetStations", map_id, this, list);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}

ImgStationList NpuServer::GetImgStations(string map_id)
{
    APIG_DEBUG("GetImgStations()");
    ImgStationList station_list;
    station_list.resize(0);
    _TEST_CONNECTTED(station_list);
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetImgStations() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return station_list;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return station_list;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("GetImgStations() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return station_list;
    }
    _GetStructOfMapServer_("GetImgStations", map_id, this, station_list);
    pthread_spin_unlock(&map_server_spinlock_);
    return station_list;
}

void NpuServer::SetImgStations(string map_id, ImgStationList list)
{
    API_DEBUG("SetImgStations(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetImgStations() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return;
    }
    for (int i=0;i<list.size();i++) {
        API_DEBUG("station: %s(%d, %d, %.3f)", list[i].info.id.c_str(), list[i].pose.u, list[i].pose.v, list[i].pose.theta);
    }

    ImgStationList station_list;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetImgStations() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return ;
    }
    _GetStructOfMapServer_("GetImgStations", map_id, this, station_list);
    pthread_spin_unlock(&map_server_spinlock_);
    IllegalCharacterDetection(list,station_list);
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetImgStations() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetImgStations", map_id, this, list);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}
GeoStationList NpuServer::GetGeoStations(string map_id)
{
    APIG_DEBUG("GetGeoStations()");
    GeoStationList station_list;
    station_list.resize(0);
    _TEST_CONNECTTED(station_list);
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetGeoStations() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return station_list;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return station_list;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("GetGeoStations() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return station_list;
    }
    _GetStructOfMapServer_("GetGeoStations", map_id, this, station_list);
    pthread_spin_unlock(&map_server_spinlock_);
    return station_list;
}

void NpuServer::SetGeoStations(string map_id, GeoStationList list)
{
    API_DEBUG("SetGeoStations(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetGeoStations() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return;
    }
    for (int i=0;i<list.size();i++) {
        API_DEBUG("station: %s(%.3f, %.3f, %.3f, %.3f, %.3f, %.3f)[deg,deg,m,rad,rad,rad", list[i].info.id.c_str()
                  , list[i].pose.latitude, list[i].pose.longitude, list[i].pose.altitude
                  , list[i].pose.roll, list[i].pose.pitch, list[i].pose.yaw);
    }

    GeoStationList station_list;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetGeoStations() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return ;
    }
    _GetStructOfMapServer_("GetGeoStations", map_id, this, station_list);
    pthread_spin_unlock(&map_server_spinlock_);
    IllegalCharacterDetection(list,station_list);
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetGeoStations() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetGeoStations", map_id, this, list);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}
void NpuServer::AddStation(string map_id, wizrobo_npu::StationInfo info)
{
    API_DEBUG("AddStation(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("AddStation() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    if (map_id == "")
    {
        ROS_WARN("Invalid map id.");
        return;
    }
    Station station_;
    ImgStation imgstation_;
    station_.info = info;
    station_.pose = GetActPose();
    imgstation_.info = info;
    imgstation_.pose = GetActImgPose();

    StationList addStation_list;
    ImgStationList addImgStation_list;
    addStation_list.push_back(station_);
    addImgStation_list.push_back(imgstation_);
    //    for (int i=0;i<list.size();i++)
    //    {
    //        API_DEBUG("station: %s(%d, %d, %.3f)", list[i].info.id.c_str(), list[i].pose.u, list[i].pose.v, list[i].pose.theta);
    //    }

    StationList station_list;
    ImgStationList imgStation_list;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("AddStation() Get thread lock failed");
        if(server_type_ == "ice")
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return ;
    }
    _GetStructOfMapServer_("GetStations", map_id, this, station_list);
    _GetStructOfMapServer_("GetImgStations", map_id, this, imgStation_list);
    pthread_spin_unlock(&map_server_spinlock_);
    IllegalCharacterDetection(addImgStation_list,imgStation_list);

    station_list.push_back(station_);
    imgStation_list.push_back(imgstation_);

    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("AddStation() Get thread lock failed");
        if(server_type_ == "ice")
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetStations", map_id, this, station_list);
    _SetStructOfMapServer_("SetImgStations", map_id, this, imgStation_list);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}

bool NpuServer::DeleteStation(string map_id, string station_id)
{
    API_DEBUG("DeleteStation(\"%s\")", map_id.c_str());
    //_TEST_CONNECTTED(void());
    if (server_state_ != CONNECTED)
    {
        ROS_ERROR("Not connect yet.");
        //throw NpuException("","", ERR_NOT_CONNECTED, "Not connect yet!!");
        return false;
    }

    if (map_id == "")
    {
        ROS_WARN("Invalid map id.");
        return false;
    }


    StationList station_list;
    ImgStationList imgStation_list;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("DeleteStation() Get thread lock failed");
        if(server_type_ == "ice")
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return false;
    }
    _GetStructOfMapServer_("GetStations", map_id, this, station_list);
    _GetStructOfMapServer_("GetImgStations", map_id, this, imgStation_list);
    pthread_spin_unlock(&map_server_spinlock_);

    auto sta = station_list.begin();
    for(int i=0;sta != station_list.end();i++)
    {
        if(station_list.at(i).info.id == station_id)
        {
            station_list.erase(sta);
            break;
        }
        else
            ++sta;
    }

    if(sta == station_list.end())
        if(station_list.begin() != station_list.end())
            return false;

    auto imgSta = imgStation_list.begin();
    for(int i=0;imgSta != imgStation_list.end();i++)
    {
        if(imgStation_list.at(i).info.id == station_id)
        {
            imgStation_list.erase(imgSta);
            break;
        }
        else
            ++imgSta;
    }
    if(imgSta == imgStation_list.end())
        if(imgStation_list.begin() != imgStation_list.end())
            return false;

    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("AddStation() Get thread lock failed");
        if(server_type_ == "ice")
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return false;
    }
    _SetStructOfMapServer_("SetStations", map_id, this, station_list);
    _SetStructOfMapServer_("SetImgStations", map_id, this, imgStation_list);
    pthread_spin_unlock(&map_server_spinlock_);
    return true;
}

bool NpuServer::RenameStation(string map_id, string origin_id, string new_id)
{
    API_DEBUG("RenameStation(\"%s\")", map_id.c_str());
    //_TEST_CONNECTTED(void());
    if (server_state_ != CONNECTED)
    {
        ROS_ERROR("Not connect yet.");
        //throw NpuException("","", ERR_NOT_CONNECTED, "Not connect yet!!");
        return false;
    }

    if (map_id == "")
    {
        ROS_WARN("Invalid map id.");
        return false;
    }


    StationList station_list;
    ImgStationList imgStation_list;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("RenameStation() Get thread lock failed");
        if(server_type_ == "ice")
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return false;
    }
    _GetStructOfMapServer_("GetStations", map_id, this, station_list);
    _GetStructOfMapServer_("GetImgStations", map_id, this, imgStation_list);
    pthread_spin_unlock(&map_server_spinlock_);

    auto sta = station_list.begin();
    for(int i=0;sta != station_list.end();i++)
    {
        if(station_list.at(i).info.id == origin_id)
        {
            station_list.at(i).info.id = new_id;
            break;
        }
        else
            ++sta;
    }

    if(sta == station_list.end())
        if(station_list.begin() != station_list.end())
            return false;

    auto imgSta = imgStation_list.begin();
    for(int i=0;imgSta != imgStation_list.end();i++)
    {
        if(imgStation_list.at(i).info.id == origin_id)
        {
            imgStation_list.at(i).info.id = new_id;
            break;
        }
        else
            ++imgSta;
    }
    if(imgSta == imgStation_list.end())
        if(imgStation_list.begin() != imgStation_list.end())
            return false;

    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("AddStation() Get thread lock failed");
        if(server_type_ == "ice")
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return false;
    }
    _SetStructOfMapServer_("SetStations", map_id, this, station_list);
    _SetStructOfMapServer_("SetImgStations", map_id, this, imgStation_list);
    pthread_spin_unlock(&map_server_spinlock_);
    return true;
}

// task
TaskList NpuServer::GetTaskList(string map_id)
{
    TaskList tasks;
    tasks.resize(0);
    if (npu_state_ != NAVI_STATE)
    {
        APIG_DEBUG("GetTaskList() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return tasks;
    }
    _TEST_CONNECTTED(tasks);

    if (map_id == "")
    {
        ROS_WARN("Invalid map id.");
        return tasks;
    }

    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        APIG_DEBUG("GetTaskList() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return tasks;
    }
    _GetStructOfMapServer_("GetTaskList", map_id, this, tasks);
    pthread_spin_unlock(&map_server_spinlock_);
    //    for (int i=0;i<tasks.size();i++)
    //    {
    //        APIG_DEBUG("Task: %02d", i);
    //        for (int j=0;j<tasks[i].info.action_list.size();j++)
    //        {
    //            APIG_DEBUG("Action-3d: %02d[%.s, %.d]", j, tasks[i].info.action_list[j].action_args, tasks[i].info.action_list[j].duration);
    //        }
    //    }
    return tasks;
}
void NpuServer::SetTasks(string map_id, TaskList list)
{
    _TEST_CONNECTTED(void());
    if (npu_state_ != NAVI_STATE)
    {
        API_DEBUG("SetTasks() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    if (map_id == "")
    {
        ROS_WARN("Invalid map id.");
        return;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        API_DEBUG("SetTasks() Get thread lock failed 2");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    /******/
    int n = list.size();
    ROS_INFO("list.size is: %d",n);
    for(int i=0; i<n; i++)
    {
        int m = list[i].task_loop_times;
        ROS_INFO("task_loop is: %d",list[i].enb_taskloop);
        ROS_INFO("times is: %d", m);
        ROS_INFO("task_action_list is: %ld",list[i].info.action_list.size());
    }
    /******/
    _SetStructOfMapServer_("SetTasks", map_id, this, list);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}

void NpuServer::ExecuteTask(TaskList list)
{
    if (npu_state_ != NAVI_STATE)
    {
        API_DEBUG("ExecuteTask() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    ROS_INFO("*********************ExecuteTask::TaskList***********************");
    ROS_INFO("list size is: %ld",list.size());
    for(int i=0; i<list.size(); i++)
    {
        int m = list[i].task_loop_times;
        ROS_INFO("task_loop is: %d",list[i].enb_taskloop);
        ROS_INFO("times is: %d", m);
        ROS_INFO("task_action_list is: %ld",list[i].info.action_list.size());
    }
    string status;
    ros::NodeHandle nh;
    nh.param<string>("/wr_task_system/system_status", status, "");
    if(status == "doing")
    {
        ROS_INFO("Task system have task doing!");
        throw NpuException("","", ERR_UNKNOWN, "Task system have task doing!");
    }
    list_ = list;
    launch_file_execute_thread_("execute");
    return;
}

// path
bool NpuServer::GetPathsFile(Path &path)
{

    ROS_DEBUG("GetPathsFile");
    ros::NodeHandle private_nh("~");
    private_nh.param("unfinishpath",unfinishpath,std::string("/npu.x86_64/unfinishpath.yaml"));

    std::ifstream pathsfile(unfinishpath);

    ROS_DEBUG("GetPathsFile path is : %s",unfinishpath.c_str());
    YAML::Node yaml_paths = YAML::Load(pathsfile);
    // PathList file_paths;
    path.poses.resize(0);
    Pose3D pose;

    if(pathsfile) {
        for(int i = 0; i< yaml_paths["unfinishpaths"][0]["poses"].size();i++) {
            pose.roll = yaml_paths["unfinishpaths"][0]["poses"][i]["roll"].as<float>();
            pose.pitch = yaml_paths["unfinishpaths"][0]["poses"][i]["pitch"].as<float>();
            pose.yaw = yaml_paths["unfinishpaths"][0]["poses"][i]["yaw"].as<float>();
            pose.x = yaml_paths["unfinishpaths"][0]["poses"][i]["x"].as<float>();
            pose.y = yaml_paths["unfinishpaths"][0]["poses"][i]["y"].as<float>();
            pose.z = yaml_paths["unfinishpaths"][0]["poses"][i]["z"].as<float>();
            ROS_DEBUG("roll: %f, pitch: %f, yaw: %f, x: %f, y: %f, z: %f",pose.roll,pose.pitch,pose.yaw,pose.x,pose.y,pose.z);
            if((pose.roll + pose.pitch + pose.yaw + pose.x + pose.y + pose.z) == 0){
                ROS_DEBUG("GetPathsFile : num is o return false");
                return false;
            } else {
                path.poses.push_back(pose);
            }
        }

        //    file_paths.push_back(path);
        ROS_DEBUG("yaw: %f",yaml_paths["unfinishpaths"][0]["poses"][0]["yaw"].as<float>());
        path.info.id = "unfinished_path";
        path.info.map_id = yaml_paths["unfinishpaths"][1]["info"][0]["map_id"].as<std::string>();
        pathsfile.close();
    } else {
        ROS_WARN("GetPathsFile: Not have pathsfile.");
        return false;
    }
    ROS_INFO("GetPathsFile is over");
    return true;
}

PathList NpuServer::GetPaths(string map_id)
{
    APIG_DEBUG("GetPaths() Map id: %s", map_id.c_str());
    ros::NodeHandle nn("~");
    nn.setParam("map_id", map_id);
    PathList paths;
    paths.resize(0);
    _TEST_CONNECTTED(paths);
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetPaths() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return paths;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return paths;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("GetPaths() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return paths;
    }
    _GetStructOfMapServer_("GetPaths", map_id, this, paths);
    pthread_spin_unlock(&map_server_spinlock_);
    for (int i=0;i<paths.size();i++) {
        APIG_DEBUG("Path: %02d", i);
        for (int j=0;j<paths[i].poses.size();j++) {
            APIG_DEBUG("Pose-3d: %02d[ %.2f, %.2f, %.2f]",
                       j, paths[i].poses[j].x, paths[i].poses[j].y,
                       paths[i].poses[j].yaw);
        }
    }
    for(PathList::iterator it = paths.begin();it!=paths.end();) {
        if ((*it).info.id == "unfinished_path") {
            it = paths.erase(it);
        } else {
            ++it;
        }
    }
    Path path;
    path.poses.resize(0);
    if (GetPathsFile(path)) {
        ROS_DEBUG("getpathsfile is true");
        if(path.info.map_id == map_id) {
            paths.push_back(path);
            ROS_DEBUG("return getpathsfile()");
        }
    }
    SetPaths(map_id, paths);
    return paths;
}
void NpuServer::SetPaths(string map_id, PathList list)
{
    API_DEBUG("SetPaths(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetPaths() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return;
    }
    for (int i=0;i<list.size();i++) {
        API_DEBUG("Path: %02d", i);
        for (int j=0;j<list[i].poses.size();j++) {
            API_DEBUG("Pose-3d: %02d[ %.2f, %.2f, %.2f]", j, list[i].poses[j].x, list[i].poses[j].y, list[i].poses[j].yaw);
        }
    }
    PathList paths;
    _GetStructOfMapServer_("GetPaths", map_id, this, paths);
    IllegalCharacterDetection(list,paths);
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetPaths() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetPaths", map_id, this, list);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}

ImgPathList NpuServer::GetImgPaths(string map_id)
{
    GetPaths(map_id);
    APIG_DEBUG("GetImgPaths(\"%s\")", map_id.c_str());
    ImgPathList paths;
    paths.resize(0);
    _TEST_CONNECTTED(paths);
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetImgPaths() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return paths;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return paths;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("GetImgPaths() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return paths;
    }
    _GetStructOfMapServer_("GetImgPaths", map_id, this, paths);
    pthread_spin_unlock(&map_server_spinlock_);
    for (int i=0;i<paths.size();i++) {
        APIG_DEBUG("Path: %02d", i);
        for (int j=0;j<paths[i].poses.size();j++) {
            APIG_DEBUG("Pose-im: %02d[ %02d, %02d, %.2f]",
                       j, paths[i].poses[j].u, paths[i].poses[j].v,
                       paths[i].poses[j].theta);
        }
    }
    return paths;
}
void NpuServer::SetImgPaths(string map_id, ImgPathList list)
{
    API_DEBUG("SetImgPaths(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetImgPaths() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return;
    }
    for (int i=0;i<list.size();i++) {
        API_DEBUG("Path: %02d", i);
        for (int j=0;j<list[i].poses.size();j++) {
            API_DEBUG("Pose-im: %02d[ %02d, %02d, %.2f]",
                      j, list[i].poses[j].u, list[i].poses[j].v,
                      list[i].poses[j].theta);
        }
    }
    ImgPathList paths;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetImgPaths() Get thread lock failed 1");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _GetStructOfMapServer_("GetImgPaths", map_id, this, paths);
    pthread_spin_unlock(&map_server_spinlock_);
    if(paths.size() >= 1 && paths.size()<list.size()) {
        for(int i=0;i<paths.size();i++) {
            if(list[list.size()-1].info.id == paths[i].info.id) {
                ROS_ERROR("MapServer::SetImgPaths():Path id is already exist.");
                throw NpuException("","", ERR_ILLEGAL_PARA, "MapServer::SetImgPaths():Path id is already exist.");
            }
        }
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetImgPaths() Get thread lock failed 2");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetImgPaths", map_id, this, list);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}

GeoPathList NpuServer::GetGeoPaths(string map_id)
{
    GetPaths(map_id);
    APIG_DEBUG("GetGeoPaths(\"%s\")", map_id.c_str());
    GeoPathList paths;
    paths.resize(0);
    _TEST_CONNECTTED(paths);
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetGeoPaths() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return paths;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return paths;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("GetGeoPaths() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return paths;
    }
    _GetStructOfMapServer_("GetGeoPaths", map_id, this, paths);
    pthread_spin_unlock(&map_server_spinlock_);
    for (int i=0; i<paths.size(); i++) {
        APIG_DEBUG("Path: %02d", i);
        for (int j=0; j<paths[i].poses.size(); j++) {
            APIG_DEBUG("Pose-im: %02d (%.3f, %.3f, %.3f)[deg,deg,m] (%.3f, %.3f, %.3f)[rad,rad,rad]",
                       j, paths[i].poses[j].latitude, paths[i].poses[j].longitude, paths[i].poses[j].altitude,
                       paths[i].poses[j].roll, paths[i].poses[j].pitch, paths[i].poses[j].yaw);
        }
    }
    return paths;
}

GeoPoseList NpuServer::GetGeoPath()
{
    APIG_DEBUG("GetGeoPaths()");
    GeoPoseList paths;
    paths.resize(0);
    _TEST_CONNECTTED(paths);
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetGeoPaths() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return paths;
    }

    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0) {
        ROS_ERROR("GetGeoPaths() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return paths;
    }

    pthread_spin_unlock(&cmd_path_spinlock_);
    TransformPoseList_(global_path_.poses, paths);
    return paths;
}

void NpuServer::SetGeoPaths(string map_id, GeoPathList list)
{
    API_DEBUG("SetGeoPaths(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetGeoPaths() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return;
    }
    if (list.size() > 1) {
        LOG_ERROR << "One map can onle exist one gps path!!!!!";
        return;
    }

    geographic_msgs::GeoPath gps_path;
    gps_path.header.stamp = ros::Time::now();
    gps_path.header.frame_id = "utm_frame";
    for (int i=0; i<list.size(); i++) {
        API_DEBUG("Path id : %02d", i);
        for (int j=0; j<list[i].poses.size(); j++) {
            API_DEBUG("Pose-im: %02d[ %.3f, %.3f, %.3f, %.3f, %.3f, %.3f][deg,deg,m,rad,rad,rad]",
                      j, list[i].poses[j].latitude, list[i].poses[j].longitude, list[i].poses[j].altitude,
                      list[i].poses[j].roll,list[i].poses[j].pitch,list[i].poses[j].yaw);
            gps_path.poses[j].pose.position.latitude = list[i].poses[j].latitude;
            gps_path.poses[j].pose.position.longitude = list[i].poses[j].longitude;
            gps_path.poses[j].pose.position.altitude = list[i].poses[j].altitude;
            gps_path.poses[j].pose.orientation =
                    tf::createQuaternionMsgFromRollPitchYaw(
                        list[i].poses[j].roll,
                        list[i].poses[j].pitch,
                        list[i].poses[j].yaw);
//                    tf::createQuaternionFromYaw(list[i].poses[j].yaw);
        }
    }

    //trans path from wps-84 to map frame
    ros::NodeHandle nh;
    wr_npu_msgs::TransGpsPath srv;
    srv.request.gps_path = gps_path;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::TransGpsPath>("/trans_gps_path");
    if (client.isValid() != true)
    {
        return;
    }
    client.call(srv);

    GeoPathList paths;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetGeoPaths() Get thread lock failed 1");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _GetStructOfMapServer_("GetGeoPaths", map_id, this, paths);
    pthread_spin_unlock(&map_server_spinlock_);
    if(paths.size() >= 1 && paths.size()<list.size()) {
        for(int i=0;i<paths.size();i++) {
            if(list[list.size()-1].info.id == paths[i].info.id) {
                ROS_ERROR("MapServer::SetGeoPaths():Path id is already exist.");
                throw NpuException("","", ERR_ILLEGAL_PARA, "MapServer::SetGeoPaths():Path id is already exist.");
            }
        }
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetGeoPaths() Get thread lock failed 2");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetGeoPaths", map_id, this, list);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}

void NpuServer::SetGeoPath(GeoPath geo_path)
{
    API_DEBUG("SetGeoPath()");
    _TEST_CONNECTTED(void());

    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetGeoPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    Pose3DList map_path;
    if (pthread_spin_trylock(&geo_trans_server_spinlock_) != 0) {
        ROS_ERROR("SetGeoPath() Get thread lock failed 1");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    TransformPoseList_(geo_path.poses, map_path);
    pthread_spin_unlock(&geo_trans_server_spinlock_);
    APIG_DEBUG("SetGeoPath() Done");
    return;
}

//// virtual wall
VirtualWallList NpuServer::GetVirtualWalls(string map_id)
{
    APIG_DEBUG("GetVirtualWalls() Map id: %s", map_id.c_str());
    VirtualWallList VirtualWalls;
    VirtualWalls.resize(0);
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetVirtualWalls() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return VirtualWalls;
    }
#endif
    _TEST_CONNECTTED(VirtualWalls);
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return VirtualWalls;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("GetVirtualWalls() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return VirtualWalls;
    }
    _GetStructOfMapServer_("GetVirtualWalls", map_id, this, VirtualWalls);
    pthread_spin_unlock(&map_server_spinlock_);
    for (int i=0;i<VirtualWalls.size();i++) {
        APIG_DEBUG("VirtualWall: %02d", i);
        for (int j=0;j<VirtualWalls[i].points.size();j++) {
            APIG_DEBUG("Pose-3d: %02d[ %.2f, %.2f]",
                       j, VirtualWalls[i].points[j].x, VirtualWalls[i].points[j].y);
        }
    }
    return VirtualWalls;
}

void NpuServer::SetVirtualWalls(string map_id, VirtualWallList virtual_walls)
{
    API_DEBUG("SetVirtualWalls(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetVirtualWalls() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return;
    }
    for (int i=0;i<virtual_walls.size();i++) {
        API_DEBUG("VirtualWalls: %02d", i);
        for (int j=0;j<virtual_walls[i].points.size();j++) {
            API_DEBUG("Pose-3d: %02d[ %.2f, %.2f]", j, virtual_walls[i].points[j].x, virtual_walls[i].points[j].y);
        }
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetVirtualWalls() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetVirtualWalls", map_id, this, virtual_walls);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}

ImgVirtualWallList NpuServer::GetImgVirtualWalls(string map_id)
{
    APIG_DEBUG("GetImgVirtualWalls(\"%s\")", map_id.c_str());
    ImgVirtualWallList VirtualWalls;
    VirtualWalls.resize(0);
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetImgVirtualWalls() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return VirtualWalls;
    }
#endif
    _TEST_CONNECTTED(VirtualWalls);
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return VirtualWalls;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        APIG_DEBUG("GetImgVirtualWalls() Get thread lock failed 2");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return VirtualWalls;
    }
    _GetStructOfMapServer_("GetImgVirtualWalls", map_id, this, VirtualWalls);
    pthread_spin_unlock(&map_server_spinlock_);
    for (int i=0;i<VirtualWalls.size();i++) {
        APIG_DEBUG("VirtualWall: %02d", i);
        for (int j=0;j<VirtualWalls[i].points.size();j++) {
            APIG_DEBUG("Pose-im: %02d[ %02d, %02d]",
                       j, VirtualWalls[i].points[j].u, VirtualWalls[i].points[j].v);
        }
    }
    return VirtualWalls;
}

void NpuServer::SetImgVirtualWalls(string map_id, ImgVirtualWallList virtual_walls)
{
    API_DEBUG("SetImgVirtualWalls(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetImgVirtualWalls() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return;
    }
    for (int i=0;i<virtual_walls.size();i++) {
        API_DEBUG("virtual_walls: %02d", i);
        for (int j=0;j<virtual_walls[i].points.size();j++) {
            API_DEBUG("Pose-im: %02d[ %02d, %02d]", j, virtual_walls[i].points[j].u, virtual_walls[i].points[j].v);
        }
    }
    ImgVirtualWallList VirtualWalls;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetImgVirtualWalls() Get thread lock failed 1");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _GetStructOfMapServer_("GetImgVirtualWalls", map_id, this, VirtualWalls);
    pthread_spin_unlock(&map_server_spinlock_);
    IllegalCharacterDetection(virtual_walls, VirtualWalls);
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetImgVirtualWalls() Get thread lock failed 2");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetImgVirtualWalls", map_id, this, virtual_walls);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}

GeoVirtualWallList NpuServer::GetGeoVirtualWalls(string map_id)
{
    APIG_DEBUG("GetGeoVirtualWalls(\"%s\")", map_id.c_str());
    GeoVirtualWallList VirtualWalls;
    VirtualWalls.resize(0);
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetGeoVirtualWalls() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return VirtualWalls;
    }
#endif
    _TEST_CONNECTTED(VirtualWalls);
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return VirtualWalls;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        APIG_DEBUG("GetGeoVirtualWalls() Get thread lock failed 2");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return VirtualWalls;
    }
    _GetStructOfMapServer_("GetGeoVirtualWalls", map_id, this, VirtualWalls);
    pthread_spin_unlock(&map_server_spinlock_);
    for (int i=0;i<VirtualWalls.size();i++) {
        APIG_DEBUG("VirtualWall: %02d", i);
        for (int j=0;j<VirtualWalls[i].points.size();j++) {
            API_DEBUG("Pose-im: %02d[ %.3f, %.3f, %.3f][deg,deg,m]",
                       j, VirtualWalls[i].points[j].latitude, VirtualWalls[i].points[j].longitude, VirtualWalls[i].points[j].altitude);
        }
    }
    return VirtualWalls;
}

void NpuServer::SetGeoVirtualWalls(string map_id, GeoVirtualWallList virtual_walls)
{
    API_DEBUG("SetGeoVirtualWalls(\"%s\")", map_id.c_str());
    _TEST_CONNECTTED(void());
#if 0
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("SetGeoVirtualWalls() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
#endif
    if (map_id == "") {
        ROS_WARN("Invalid map id.");
        return;
    }
    for (int i=0;i<virtual_walls.size();i++) {
        API_DEBUG("virtual_walls: %02d", i);
        for (int j=0;j<virtual_walls[i].points.size();j++) {
            API_DEBUG("Pose-im: %02d[ %.3f, %.3f, %.3f][deg,deg,m]", j,
                      virtual_walls[i].points[j].latitude, virtual_walls[i].points[j].longitude, virtual_walls[i].points[j].altitude);
        }
    }
    GeoVirtualWallList VirtualWalls;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetGeoVirtualWalls() Get thread lock failed 1");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _GetStructOfMapServer_("GetGeoVirtualWalls", map_id, this, VirtualWalls);
    pthread_spin_unlock(&map_server_spinlock_);
    IllegalCharacterDetection(virtual_walls, VirtualWalls);
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SetGeoVirtualWalls() Get thread lock failed 2");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _SetStructOfMapServer_("SetGeoVirtualWalls", map_id, this, virtual_walls);
    pthread_spin_unlock(&map_server_spinlock_);
    return;
}

void NpuServer::DoBagOpt(const string &opt, const string &args, string &res)
{
    LM_DEBUG("opt: %s, args: %s", opt.c_str(), args.c_str());
    stringstream ss_res("");
    if (opt == "help") {
        ss_res << "Operation: [start_recording|stop_recording|get_recording_state|get_bag_data_size|delete_bag_data|get_download_url|help]";
    } else if (opt == "start_recording") {
#if 1
        int r = system("ls /npu.x86_64/bag/*.bag 1>/dev/null 2>&1");
#else
        int r = system("ls /npu.x86_64/bag/*.bag");
#endif
        if (r == 0) {
            ss_res << "There is a bag data, delete it first!";
        } else {
            stringstream ss_cmd("");
            if (args != "") {
                ss_cmd << "cd /npu.x86_64/bag/ && rosbag record " << args << " 1>/dev/null 2>&1 &";
            } else {
                ss_cmd << "cd /npu.x86_64/bag/ && rosbag record -j -o lmbag /scan /imu/data_raw /odom 1>/dev/null 2>&1 &";
            }
            system(ss_cmd.str().c_str());
            ss_res << "OK";
        }
    } else if (opt == "stop_recording") {
        system("killall -s SIGINT /opt/ros/indigo/lib/rosbag/record 1>/dev/null 2>&1");
        ss_res << "OK";
    } else if (opt == "get_recording_state") {
        int r = system("ps -ef | grep -v grep | grep -e /opt/ros/indigo/lib/rosbag/record 1>/dev/null 2>&1");
        ss_res << ((r==0)?"RECORDING":"IDLE");
    } else if (opt == "get_bag_data_size") {
        int r = system("ls /npu.x86_64/bag/*.bag* 1>/dev/null 2>&1");
        if (r == 0) {
            system("ls -lh /npu.x86_64/bag/*.bag* | head -n1 | awk -F ' ' '{print $5}' 1>/tmp/npu-bag-data-size.tmp 2>&1");
            ifstream ifile;
            ifile.open("/tmp/npu-bag-data-size.tmp");
            ss_res << ifile.rdbuf();
            ifile.close();
        } else {
            ss_res << "Invalid bag data.";
        }
    } else if (opt == "delete_bag_data") {
        system("rm -rf /npu.x86_64/bag/*");
        ss_res << "OK";
    } else if (opt == "get_download_url") {
        int r;
        r = system("ps -ef | grep -v grep | grep -e /opt/ros/indigo/lib/rosbag/record 1>/dev/null 2>&1");
        if (r == 0) {
            ss_res << "It is recording!";
        } else {
            r = system("ls /npu.x86_64/bag/*.bag 1>/dev/null 2>&1");
            if (r == 0) {
                system("cd /npu.x86_64/bag/ && ls -1 *.bag | head -n1 1>/tmp/npu-bag-data-filename.tmp 2>&1");
                ifstream ifile;
                ifile.open("/tmp/npu-bag-data-filename.tmp");
                ss_res << ifile.rdbuf();
                ifile.close();
            } else {
                ss_res << "Invalid bag data.";
            }
        }
    } else {
        ss_res << "Invalid operation!";
    }
    res = ss_res.str();
    LM_DEBUG("res: %s", res.c_str());
    return;
}

//// common runtime data
// vel
Vel3D NpuServer::GetCmdVel()
{
    APIG_DEBUG("GetCmdVel()");
    Vel3D vel;
    vel.v_x = 0.0;
    vel.v_y = 0.0;
    vel.v_z = 0.0;
    vel.v_roll = 0.0;
    vel.v_pitch = 0.0;
    vel.v_yaw = 0.0;
    _TEST_CONNECTTED(vel);
    if (pthread_spin_trylock(&cmd_vel_spinlock_) != 0)
    {
        ROS_ERROR("GetCmdVel() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return vel;
    }
    vel = cmd_vel_;
    pthread_spin_unlock(&cmd_vel_spinlock_);

    APIG_DEBUG("GetCmdVel() vel: (%.2f, %.2f, %.2f, %.2f, %.2f, %.2f)",
               vel.v_x, vel.v_y, vel.v_z, vel.v_roll, vel.v_pitch, vel.v_yaw);
    return vel;
}
Vel3D NpuServer::GetActVel()
{
    APIG_DEBUG("GetActVel()");
    Vel3D vel;
    vel.v_x = 0.0;
    vel.v_y = 0.0;
    vel.v_z = 0.0;
    vel.v_roll = 0.0;
    vel.v_pitch = 0.0;
    vel.v_yaw = 0.0;
    _TEST_CONNECTTED(vel);
    if (pthread_spin_trylock(&act_vel_spinlock_) != 0)
    {
        ROS_ERROR("GetActVel() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return vel;
    }
    vel = act_vel_;
    pthread_spin_unlock(&act_vel_spinlock_);
    APIG_DEBUG("GetActVel() vel: (%.2f, %.2f, %.2f, %.2f, %.2f, %.2f)",
               vel.v_x, vel.v_y, vel.v_z, vel.v_roll, vel.v_pitch, vel.v_yaw);
    return vel;
}
// acc
Acc3D NpuServer::GetAcc()
{
    APIG_DEBUG("GetAcc(): TODO");
    Acc3D return_value;
    _TEST_CONNECTTED(return_value);
    return return_value;
}
// pose
Pose3D NpuServer::GetCmdPose()
{
    APIG_DEBUG("GetCmdPose(): (%.2f, %.2f, %.2f)[m,m,deg]",
               cmd_pose_.x, cmd_pose_.y, RAD2DEG(cmd_pose_.yaw));
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GetCmdPose() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return cmd_pose_;
    }
    _TEST_CONNECTTED(cmd_pose_);
    return cmd_pose_;
}
Pose3D NpuServer::GetActPose()
{
    APIG_DEBUG("GetActPose()");
    Pose3D pose;
    pose.x = 0.0;
    pose.y = 0.0;
    pose.z = 0.0;
    pose.roll = 0.0;
    pose.pitch = 0.0;
    pose.yaw = 0.0;
    _TEST_CONNECTTED(pose);
    if (npu_state_!=NAVI_STATE && npu_state_!=SLAM_STATE)
    {
        APIG_DEBUG("GetActPose() Npu state is not NAVI_STATE or SLAM_STATE.");
        //throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return pose;
    }
    if (pthread_spin_trylock(&act_pose_spinlock_) != 0)
    {
        ROS_ERROR("GetActPose() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return pose;
    }
    pose = act_pose_;
    APIG_DEBUG("GetActPose(): (%.2f, %.2f, %.2f)[m,m,deg]",
               pose.x, pose.y, RAD2DEG(pose.yaw));
    pthread_spin_unlock(&act_pose_spinlock_);
    client_is_alive=true;
    return pose;
}
ImgPose NpuServer::GetCmdImgPose()
{
    APIG_DEBUG("GetCmdImgPose()");
    ImgPose img_pose;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GetCmdImgPose() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return img_pose;
    }
    _TEST_CONNECTTED(img_pose);
    TransformPose_(cmd_pose_, img_pose);
    APIG_DEBUG("GetCmdImgPose() Done");
    return img_pose;
}
ImgPose NpuServer::GetActImgPose()
{
    APIG_DEBUG("GetActImgPose()");
    ImgPose img_pose;
    img_pose.u = 0.0;
    img_pose.v = 0.0;
    img_pose.theta = 0.0;
    _TEST_CONNECTTED(img_pose);
    if (npu_state_!=NAVI_STATE && npu_state_!=SLAM_STATE)
    {
        APIG_DEBUG("GetActImgPose() Npu state is not NAVI_STATE or SLAM_STATE.");
        //throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE or SLAM_STATE.");
        return img_pose;
    }
    TransformPose_(act_pose_, img_pose);
    client_is_alive=true;
    APIG_DEBUG("GetActImgPose() pose: (%d, %d, %.2f)[m,m,deg]",
               img_pose.u, img_pose.v, img_pose.theta);
    APIG_DEBUG("GetActImgPose() Done");
    return img_pose;
}
GeoPose NpuServer::GetCmdGeoPose()
{
    APIG_DEBUG("GetCmdGeoPose()");
    GeoPose geo_pose;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GetCmdGeoPose() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return geo_pose;
    }
    _TEST_CONNECTTED(geo_pose);
    TransformPose_(cmd_pose_, geo_pose);
    APIG_DEBUG("GetCmdGeoPose() Done");
    return geo_pose;
}
GeoPose NpuServer::GetActGeoPose()
{
    APIG_DEBUG("GetActGeoPose()");
    GeoPose geo_pose;
    GeoPose temp_pose;
    geo_pose.latitude = 123.0;
    geo_pose.longitude = 1000.0;
    geo_pose.altitude = 14.0;
    geo_pose.roll = 0.0;
    geo_pose.pitch = 0.0;
    geo_pose.yaw = 0.0;
    temp_pose = geo_pose;
    _TEST_CONNECTTED(geo_pose);
    if (npu_state_!=NAVI_STATE && npu_state_!=SLAM_STATE)
    {
        APIG_DEBUG("GetActGeoPose() Npu state is not NAVI_STATE or SLAM_STATE.");
        //throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE or SLAM_STATE.");
        return geo_pose;
    }
//    TransformPose_(act_pose_, temp_pose);
    client_is_alive=true;
    geo_pose.latitude = gps_data_msg_.latitude;
    geo_pose.longitude = gps_data_msg_.longitude;
    geo_pose.altitude = gps_data_msg_.altitude;
    geo_pose.roll = act_pose_.roll;
    geo_pose.pitch = act_pose_.pitch;
    geo_pose.yaw = act_pose_.yaw;
    APIG_DEBUG("GetActGeoPose() pose: (%.3f, %.3f %.3f, %.3f, %.3f, %.3f, )[deg,deg,m,rad,rad,rad]",
               geo_pose.latitude, geo_pose.longitude, geo_pose.altitude,
               geo_pose.roll, geo_pose.pitch, geo_pose.yaw);
    APIG_DEBUG("GetActGeoPose() Done");
    return geo_pose;
}

void NpuServer::SaveActPose()
{
    ros::NodeHandle n("~");
    if (save_act_pose_switch) {
        std::ofstream posefile(init_act_pose_);
        if (posefile) {
            posefile << "init_act_pose_x: " << act_pose_.x << endl;
            posefile << "init_act_pose_y: " << act_pose_.y << endl;
            posefile << "init_act_pose_yaw: " << act_pose_.yaw << endl;
            posefile.close();
            n.setParam("init_act_pose_x",act_pose_.x);
            n.setParam("init_act_pose_y",act_pose_.y);
            n.setParam("init_act_pose_yaw",act_pose_.yaw);
        } else {
            ROS_ERROR("SaveActPose(): Open \"%s\" faild.", init_act_pose_.c_str());
        }
    } else {
        ROS_WARN("save_act_pose_switch is false");
    }
}

Pose3D NpuServer::GetActPosetoFile()
{
    ros::NodeHandle private_nh("~");
    Pose3D init_act_pose;
    private_nh.param("init_act_pose_x",init_act_pose_x_,0.0);
    private_nh.param("init_act_pose_y",init_act_pose_y_,0.0);
    private_nh.param("init_act_pose_yaw",init_act_pose_yaw_,0.0);
    init_act_pose.x = init_act_pose_x_;
    init_act_pose.y = init_act_pose_y_;
    init_act_pose.yaw = init_act_pose_yaw_;
    ROS_WARN("x= %lf ,y= %lf ,yaw= %lf",init_act_pose_x_,init_act_pose_y_,init_act_pose_yaw_);
    return init_act_pose;
}

// path
//Path NpuServer::GetCmdPath()
//{
//    API_DEBUG("GetCmdPath()");
//    Path path;
//    path.info.id = "";
//    path.poses.resize(0);
//    _TEST_CONNECTTED(path);
//    if (npu_state_ != NAVI_STATE)
//    {
//        ROS_ERROR("GetCmdPath() Npu state is not NAVI_STATE.");
//        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
//        return path;
//    }
//    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0)
//    {
//        ROS_ERROR("GetCmdPath() Get thread lock failed");
//        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
//        return path;
//    }
//    path = cmd_path_;
//    pthread_spin_unlock(&cmd_path_spinlock_);
//    return path;
//}

Path NpuServer::GetGlobalPath()
{
    APIG_DEBUG("GetGlobalPath()");
    Path path;
    path.info.id = "";
    path.poses.resize(0);
    _TEST_CONNECTTED(path);
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetGlobalPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return path;
    }
    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0) {
        ROS_ERROR("GetGlobalPath() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return path;
    }
    path = global_path_;
    pthread_spin_unlock(&cmd_path_spinlock_);
    return path;
}

Path NpuServer::GetLocalPath()
{
    APIG_DEBUG("GetLocalPath()");
    Path path;
    path.info.id = "";
    path.poses.resize(0);
    _TEST_CONNECTTED(path);
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetLocalPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return path;
    }
    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0) {
        ROS_ERROR("GetLocalPath() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return path;
    }
    path = local_path_;
    pthread_spin_unlock(&cmd_path_spinlock_);
    return path;
}

//ImgPath NpuServer::GetCmdImgPath()
//{
//    API_DEBUG("GetCmdImgPath()");
//    ImgPath img_path;
//    img_path.info.id = "";
//    img_path.poses.resize(0);
//    _TEST_CONNECTTED(img_path);
//    if (npu_state_ != NAVI_STATE)
//    {
//        ROS_ERROR("GetCmdImgPath() Npu state is not NAVI_STATE.");
//        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
//        return img_path;
//    }

//    Path path;
//    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0)
//    {
//        ROS_ERROR("GetCmdImgPath() Get thread lock failed");
//        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
//        return img_path;
//    }
//    path = cmd_path_;
//    pthread_spin_unlock(&cmd_path_spinlock_);
//    TransformPoseList_(path.poses, img_path.poses);
//    API_DEBUG("GetCmdImgPath() Done");
//    return img_path;
//}
Path NpuServer::GetActPath()
{
    APIG_DEBUG("GetActPath()");
    Path path;
    path.info.id = "";
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GetActPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return act_path_;
    }
    _TEST_CONNECTTED(path);
    return act_path_;
}

ImgPath NpuServer::GetGlobalImgPath()
{
    APIG_DEBUG("GetGlobalImgPath()");
    ImgPath img_path;
    img_path.info.id = "";
    img_path.poses.resize(0);
    _TEST_CONNECTTED(img_path);
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetGlobalImgPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return img_path;
    }

    Path path;
    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0) {
        ROS_ERROR("GetGlobalImgPath() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return img_path;
    }
    path = global_path_;
    pthread_spin_unlock(&cmd_path_spinlock_);
    TransformPoseList_(path.poses, img_path.poses);
    API_DEBUG("GetGlobalImgPath() Done");
    return img_path;
}
ImgPath NpuServer::GetLocalImgPath()
{
    APIG_DEBUG("GetLocalImgPath()");
    ImgPath img_path;
    img_path.info.id = "";
    img_path.poses.resize(0);
    _TEST_CONNECTTED(img_path);
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GetLocalImgPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return img_path;
    }

    Path path;
    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0)
    {
        ROS_ERROR("GetLocalImgPath() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return img_path;
    }
    path = local_path_;
    pthread_spin_unlock(&cmd_path_spinlock_);
    TransformPoseList_(path.poses, img_path.poses);
    APIG_DEBUG("GetGlobalImgPath() Done");
    return img_path;
}
ImgPath NpuServer::GetActImgPath()
{
    APIG_DEBUG("GetActImgPath()");
    ImgPath img_path;
    img_path.info.id = "";
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GetActImgPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return img_path;
    }
    _TEST_CONNECTTED(img_path);
    TransformPoseList_(act_path_.poses, img_path.poses);
    APIG_DEBUG("GetActImgPath() Done");
    return img_path;
}

GeoPath NpuServer::GetGlobalGeoPath()
{
    APIG_DEBUG("GetGlobalGeoPath()");
    GeoPath geo_path;
    geo_path.info.id = "";
    geo_path.poses.resize(0);
    _TEST_CONNECTTED(geo_path);
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("GetGlobalGeoPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return geo_path;
    }

    Path path;
    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0) {
        ROS_ERROR("GetGlobalGeoPath() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return geo_path;
    }
    path = global_path_;
    pthread_spin_unlock(&cmd_path_spinlock_);
    TransformPoseList_(path.poses, geo_path.poses);
    API_DEBUG("GetGlobalGeoPath() Done");
    return geo_path;
}
GeoPath NpuServer::GetLocalGeoPath()
{
    APIG_DEBUG("GetLocalGeoPath()");
    GeoPath geo_path;
    geo_path.info.id = "";
    geo_path.poses.resize(0);
    _TEST_CONNECTTED(geo_path);
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GetLocalGeoPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return geo_path;
    }

    Path path;
    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0)
    {
        ROS_ERROR("GetLocalGeoPath() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return geo_path;
    }
    path = local_path_;
    pthread_spin_unlock(&cmd_path_spinlock_);
    TransformPoseList_(path.poses, geo_path.poses);
    APIG_DEBUG("GetGlobalGeoPath() Done");
    return geo_path;
}
GeoPath NpuServer::GetActGeoPath()
{
    APIG_DEBUG("GetActGeoPath()");
    GeoPath geo_path;
    geo_path.info.id = "";
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GetActGeoPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return geo_path;
    }
    _TEST_CONNECTTED(geo_path);
    TransformPoseList_(act_path_.poses, geo_path.poses);
    APIG_DEBUG("GetActGeoPath() Done");
    return geo_path;
}

// map
MapInfo NpuServer::GetCurrentMapInfo()
{}

Map2D NpuServer::GetMap()
{
    APIG_DEBUG("GetMap()");
    Map2D map2d;
    map2d.info.id = "";
    _TEST_CONNECTTED(map2d);
    /*
    if (npu_state_ != NAVI_STATE && npu_state_ != SLAM_STATE) {
        return map2d;
    }
    */
    ros::NodeHandle nh;
    SrvMapServe srv;
    bool rtn;
    srv.request.function = "GetCurrentMap";
    srv.request.arguments.resize(0);
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::WrMapServer>("/map_server_node/map_server");
    if (client.isValid() != true) {
        ROS_WARN("GetMap() service: \"/map_server_node/map_server\" is NOT avaliable.");
        return map2d;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_WARN("GetMap() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return map2d;
    }

    if(client.isValid()==true) {
        rtn = client.call(srv);
    } else {
        APIG_DEBUG("/map_server_node/map_server is not vaild!");
        rtn = false;
    }
    pthread_spin_unlock(&map_server_spinlock_);
    if ( rtn != true ) {
        ROS_WARN("GetMap() service: \"/map_server_node/map_server\" return FAIL.");
        return map2d;
    }
    SrvReturn::iterator p = srv.response.return_data.begin();
    //  APIG_DEBUG("Return data length: %d", static_cast<int>(srv.response.return_data.size()));
    p_map_handler_->Unpack(map2d.info, p);
    p_map_handler_->Unpack(map2d.mat, p);
    p_map_handler_->Unpack(map2d.stations, p);
    p_map_handler_->Unpack(map2d.paths, p);
    APIG_DEBUG("width: %d, height: %d", map2d.mat.width, map2d.mat.height);
    APIG_DEBUG("Success.");
    return map2d;
}

ImgMap NpuServer::GetImgMap()
{
    APIG_DEBUG("GetImgMap() call in.");
    ImgMap img_map;
    img_map.info.id = "";
    img_map.mat.height = 0;
    img_map.mat.width = 0;
    img_map.mat.data.resize(0);
    _TEST_CONNECTTED(img_map);

    if (npu_state_ == SLAM_STATE) {
        if (pthread_spin_trylock(&map_cache_spinlock_) != 0) {
            ROS_ERROR("GetImgMap() Get thread lock failed");
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
            return img_map;
        }
        if (imgmap_cache_[map_cache_index_].info.id != "") {
            img_map = imgmap_cache_[map_cache_index_];
        }
        pthread_spin_unlock(&map_cache_spinlock_);
    } else {
        get_imgmap_from_map_server_(img_map);
    }
    current_imgmap_head_info_.info.id = img_map.info.id;
    current_imgmap_head_info_.info.resolution = img_map.info.resolution;
    current_imgmap_head_info_.info.offset = img_map.info.offset;
    current_imgmap_head_info_.mat.ratio = img_map.mat.ratio;
    current_imgmap_head_info_.mat.width = img_map.mat.width;
    current_imgmap_head_info_.mat.height = img_map.mat.height;

    APIG_DEBUG("img_map.info.id: %s.", img_map.info.id.c_str());
    APIG_DEBUG("img_map.info.resolution: %.2f.", img_map.info.resolution);
    APIG_DEBUG("img_map.mat.width: %d.", img_map.mat.width);
    APIG_DEBUG("img_map.mat.height: %d.", img_map.mat.height);
    APIG_DEBUG("GetImgMap() return.");
    return img_map;
}
GeoMap NpuServer::GetGeoMap()
{
    APIG_DEBUG("GetGeoMap() call in.");
    GeoMap geo_map;
    geo_map.info.id = "";
    geo_map.mat.height = 0;
    geo_map.mat.width = 0;
    geo_map.mat.data.resize(0);
    _TEST_CONNECTTED(geo_map);

    if (npu_state_ == SLAM_STATE) {
        if (pthread_spin_trylock(&map_cache_spinlock_) != 0) {
            ROS_ERROR("GetGeoMap() Get thread lock failed");
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
            return geo_map;
        }
        if (geomap_cache_[map_cache_index_].info.id != "") {
            geo_map = geomap_cache_[map_cache_index_];
        }
        pthread_spin_unlock(&map_cache_spinlock_);
    } else {
        get_geomap_from_map_server_(geo_map);
    }
    current_geomap_head_info_.info.id = geo_map.info.id;
    current_geomap_head_info_.info.resolution = geo_map.info.resolution;
    current_geomap_head_info_.info.offset = geo_map.info.offset;
    current_geomap_head_info_.mat.ratio = geo_map.mat.ratio;
    current_geomap_head_info_.mat.width = geo_map.mat.width;
    current_geomap_head_info_.mat.height = geo_map.mat.height;

    APIG_DEBUG("geo_map.info.id: %s.", geo_map.info.id.c_str());
    APIG_DEBUG("geo_map.info.resolution: %.2f.", geo_map.info.resolution);
    APIG_DEBUG("geo_map.mat.width: %d.", geo_map.mat.width);
    APIG_DEBUG("geo_map.mat.height: %d.", geo_map.mat.height);
    APIG_DEBUG("GetGeoMap() return.");
    return geo_map;
}
// footprint
Point3DList NpuServer::GetFootprintVertices()
{
    APIG_DEBUG("GetFootprintVertices()");
    Point3DList points;
    points.resize(0);
    _TEST_CONNECTTED(points);
    if (npu_state_!=NAVI_STATE && npu_state_!=SLAM_STATE)
    {
        ROS_ERROR("GetFootprintVertices() Npu state is not NAVI_STATE or SLAM_STATE");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE or SLAM_STATE.");
        return points;
    }
    if (pthread_spin_trylock(&direct_trans_footprint_vertices_spinlock_) != 0)
    {
        ROS_ERROR("GetFootprintVertices() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return points;
    }
    points = direct_trans_footprint_vertices_;
    pthread_spin_unlock(&direct_trans_footprint_vertices_spinlock_);
    return points;
}
ImgPointList NpuServer::GetFootprintImgVertices()
{
    APIG_DEBUG("GetFootprintImgVertices()");
    ImgPointList img_points;
    img_points.resize(0);
    _TEST_CONNECTTED(img_points);
    if (npu_state_!=NAVI_STATE && npu_state_!=SLAM_STATE)
    {
        APIG_DEBUG("GetFootprintImgVertices() Npu state is not NAVI_STATE or SLAM_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE or SLAM_STATE.");
        return img_points;
    }

    Point3DList points;
    if (pthread_spin_trylock(&direct_trans_footprint_vertices_spinlock_) != 0)
    {
        ROS_ERROR("GetFootprintImgVertices() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return img_points;
    }
    points = direct_trans_footprint_vertices_;
    pthread_spin_unlock(&direct_trans_footprint_vertices_spinlock_);

    TransformPointList_(points, img_points);
    APIG_DEBUG("GetFootprintImgVertices() img_points.size: %d", static_cast<int>(img_points.size()));
    for(int i=0;i<img_points.size();i++)
    {
        APIG_DEBUG("GetFootprintImgVertices() img_points[%d]: (%d, %d)",
                   i, img_points[i].u, img_points[i].v);
    }
    return img_points;
}
GeoPointList NpuServer::GetFootprintGeoVertices()
{
    APIG_DEBUG("GetFootprintGeoVertices()");
    GeoPointList geo_points;
    geo_points.resize(0);
    _TEST_CONNECTTED(geo_points);
    if (npu_state_!=NAVI_STATE && npu_state_!=SLAM_STATE)
    {
        APIG_DEBUG("GetFootprintGeoVertices() Npu state is not NAVI_STATE or SLAM_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE or SLAM_STATE.");
        return geo_points;
    }

    Point3DList points;
    if (pthread_spin_trylock(&direct_trans_footprint_vertices_spinlock_) != 0)
    {
        ROS_ERROR("GetFootprintGeoVertices() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return geo_points;
    }
    points = direct_trans_footprint_vertices_;
    pthread_spin_unlock(&direct_trans_footprint_vertices_spinlock_);

    TransformPointList_(points, geo_points);
    APIG_DEBUG("GetFootprintGeoVertices() geo_points.size: %d", static_cast<int>(geo_points.size()));
    for(int i=0;i<geo_points.size();i++)
    {
        APIG_DEBUG("GetFootprintGeoVertices() geo_points[%d]: (%.3f, %.3f,%.3f)[deg,deg,m]",
                   i, geo_points[i].latitude, geo_points[i].longitude, geo_points[i].altitude);
    }
    return geo_points;
}

void NpuServer::StartTelop()
{
    API_DEBUG("StartTelop()");
    return;
}

void NpuServer::StopTelop()
{
    API_DEBUG("StopTelop()");
    return;
}

//// navi
/// navi.common
// initial pose
void NpuServer::SetInitPose(Pose3D pose)
{
    ROS_INFO("SetInitPose(): x=%lf ,y=%lf ,yaw=%lf",pose.x,pose.y,pose.yaw);
    API_DEBUG("SetInitPose()");
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("SetInitPose() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());
    if (pose.x == INVALID_POSE3D_VALUE)
    {
        ROS_WARN("Invalid pose value.");
        throw NpuException("", "", ERR_ILLEGAL_PARA, "Invalid pose value.");
        return;
    }
    std::string fixed_frame = STD_MAP_FRAME_ID;
    geometry_msgs::PoseWithCovarianceStamped initial_pose_est;
    initial_pose_est.header.frame_id = fixed_frame;
    initial_pose_est.header.stamp = ros::Time::now();
    initial_pose_est.pose.pose.position.x = pose.x;
    initial_pose_est.pose.pose.position.y = pose.y;
    tf::Quaternion quat;
    quat.setRPY(0.0, 0.0, pose.yaw);
    tf::quaternionTFToMsg(quat, initial_pose_est.pose.pose.orientation);
    initial_pose_est.pose.covariance[6*0+0] = 0.5 * 0.5;
    initial_pose_est.pose.covariance[6*1+1] = 0.5 * 0.5;
    initial_pose_est.pose.covariance[6*5+5] = M_PI/12.0 * M_PI/12.0;
    this->initial_pose_est_pub_.publish(initial_pose_est);

    geometry_msgs::PoseStamped initial_pose;
    initial_pose.header = initial_pose_est.header;
    initial_pose.pose = initial_pose_est.pose.pose;
    this->initial_pose_pub_.publish(initial_pose);
}
void NpuServer::SetInitPoseArea(InitPoseArea pose)
{
    geometry_msgs::Polygon initpose;

    initpose.points.resize(2);
    geometry_msgs::Point32 point32;
    point32.x = pose.pose.x - pose.width/2;
    point32.y = pose.pose.y - pose.height/2;
    point32.z = pose.pose.z;
    initpose.points[0] = point32;
    point32.x = pose.pose.x + pose.width/2;
    point32.y = pose.pose.y + pose.height/2;
    point32.z = pose.pose.z;
    initpose.points[1] = point32;
    ROS_INFO("POSE: x= %f ,y= %f ",point32.x,point32.y);
    this->initial_pose_area_pub.publish(initpose);
}

void NpuServer::SetInitImgPose(ImgPose pose)
{
    API_DEBUG("SetInitImgPose()");
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("SetInitImgPose() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());

    Pose3D _3d_pose;
    TransformPose_(pose, _3d_pose);
    SetInitPose(_3d_pose);
    return;
}
void NpuServer::SetInitImgPoseArea(InitImgPoseArea pose)
{

    Pose3D _3d_pose;
    TransformPose_(pose.pose, _3d_pose);
    InitPoseArea initpose;
    initpose.pose = _3d_pose;
    ROS_INFO("TransformPoseList_: map_id(\"%s\")", current_imgmap_head_info_.info.id.c_str());

    initpose.height = pose.height*current_imgmap_head_info_.info.resolution;
    initpose.width = pose.width*current_imgmap_head_info_.info.resolution;
    SetInitPoseArea(initpose);
    return;
}

void NpuServer::SetInitGeoPose(GeoPose pose)
{
    API_DEBUG("SetInitGeoPose()");
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("SetInitGeoPose() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());

    Pose3D _3d_pose;
    TransformPose_(pose, _3d_pose);
    SetInitPose(_3d_pose);
    return;
}
void NpuServer::SetInitGeoPoseArea(InitGeoPoseArea pose)
{

    Pose3D _3d_pose;
    TransformPose_(pose.pose, _3d_pose);
    InitPoseArea initpose;
    initpose.pose = _3d_pose;
    ROS_INFO("TransformPoseList_: map_id(\"%s\")", current_geomap_head_info_.info.id.c_str());

    initpose.height = pose.height*current_geomap_head_info_.info.resolution;
    initpose.width = pose.width*current_geomap_head_info_.info.resolution;
    SetInitPoseArea(initpose);
    return;
}
// start & stop
void NpuServer::StartNavi(NaviMode navi_mode)
{
    API_DEBUG("StartNavi(%s)", EnumString<NaviMode>::EnumToStr(navi_mode).c_str());
    continue_switch_ = false;
    if (npu_state_ == SLAM_STATE) {
        ROS_ERROR("Stop SLAM first.");
        throw NpuException("", "", ERR_UNKNOWN, "Stop SLAM first.");
        return;
    } else if (npu_state_ == NAVI_STATE) {
        if (navi_mode_ == navi_mode) {
            ROS_ERROR("NAVI is already started.");
            throw NpuException("", "", ERR_UNKNOWN, "NAVI is already started.");
            return;
        } else {
            ROS_ERROR("Stop old NAVI first.");
            throw NpuException("", "", ERR_UNKNOWN, "Stop old NAVI first.");
            return;
        }
    } else if (npu_state_ == TELEOP_STATE) {
        ROS_ERROR("Stop TELOP first.");
        throw NpuException("", "", ERR_UNKNOWN, "Stop TELOP first.");
        return;
    }

    SetMapServerMode_("NAVI_MODE");
    ros::NodeHandle nh;
    ros::NodeHandle private_nh("~");
    std::string logger_level_str;
    nh.param<string>(STD_CORE_PARAM_NS + "/LOGGER_LEVEL", logger_level_str, "info");

    bool enb_handheld_mode;
    nh.param<bool>(STD_BASE_PARAM_NS + "/enb_handheld_mode", enb_handheld_mode, false);

    std::string local_mode;
    nh.param<string>(STD_NAVI_PARAM_NS + "/locl_mode", local_mode, "wo_amcl");

    string host_platform;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    stringstream ss;
    ss << "bash /npu."<< host_platform << "/script/npu_run.sh --navi \""
       << "logger_level:=" << logger_level_str << " "
       << "navi_mode:=" << EnumString<NaviMode>::EnumToLowerStr(navi_mode) << " "
       << "enb_handheld_mode:=" << BoolToStr(enb_handheld_mode) << " "
       << "locl_mode:=" << local_mode << " "
       << "\"&\n";
    API_DEBUG("%s", ss.str().c_str());
    system(ss.str().c_str());

    private_nh.param("init_act_pose",init_act_pose_,std::string("/npu.x86_64/share/wr_npu_server/param/init_act_pose.yaml"));
    private_nh.param("init_pose_area_width",init_pose__area_width_,0.0);
    private_nh.param("init_pose_area_height",init_pose__area_height_,0.0);
    npu_state_ = NAVI_STATE;
    nh.setParam(STD_RUNTIME_PARAM_NS + "/npu_state", EnumString<NpuState>::EnumToStr(npu_state_).c_str());

    navi_mode_ = navi_mode;
    nh.setParam(STD_RUNTIME_PARAM_NS + "/navi_mode", EnumString<NaviMode>::EnumToStr(navi_mode).c_str());

    UpdateFootprint_(base_param_.footprint_param);// TODO: set footprint only
    ClearActPath_();
    bool bag_enb_play;
    nh.param<bool>(STD_BAG_PARAM_NS + "/enb_play", bag_enb_play, false);// TODO: STD_RUNTIME_NS
    //    if (!bag_enb_play)
    //    {
    //        SetInitPose(act_pose_);
    //    }
    ros::Rate loop(10);
    int times = 0;
    //Pose3D initpose = GetActPosetoFile();
    InitPoseArea pose_area;
    pose_area.pose = GetActPosetoFile();
    pose_area.height = init_pose__area_height_;
    pose_area.width = init_pose__area_width_;
    while(times < 21)
    {
        //SetInitPose(initpose); //set init pose to file
        //  SetInitPoseArea(pose_area);
        loop.sleep();
        times++;
    }
    SetInitPoseArea(pose_area);
    ROS_WARN("Set init pose is finish.");
    save_act_pose_switch = true;
    matching_identifiation = true;
    ROS_WARN("matching_score_data: %f", GetMatchingScore());

    return;
}
void NpuServer::StopNavi()
{
    API_DEBUG("NpuServer::StopNavi()");
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("StopNavi() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    ros::NodeHandle nh;
    string host_platform;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    stringstream ts;
    ts << "bash /npu."<< host_platform << "/script/task_stop.sh &\n";
    system(ts.str().c_str());
    API_DEBUG("%s",ts.str().c_str());
    stringstream ss;
    ss << "bash /npu."<< host_platform << "/script/npu_stop.sh &\n";
    system(ss.str().c_str());
    API_DEBUG("%s", ss.str().c_str());

    PublishManualVel_(0, 0);
    if (base_param_.enb_slave_mode) {
        if (pthread_spin_trylock(&cmd_motor_spd_spinlock_) != 0) {
            ROS_ERROR("StopNavi() Get thread lock failed");
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
            return ;
        }
        for (int i = 0; i < cmd_motor_spd_.rpms.size(); i++) {
            cmd_motor_spd_.rpms[i] = 0;
        }
        pthread_spin_unlock(&cmd_motor_spd_spinlock_);
    }
    npu_state_ = IDLE_STATE;
    nh.setParam(STD_RUNTIME_PARAM_NS + "/npu_state", EnumString<NpuState>::EnumToStr(npu_state_).c_str());
    ClearActPath_();
    save_act_pose_switch = false;
    return;
}
// task control
void NpuServer::PauseTask()
{
    API_DEBUG("PauseTask()");
    if (!continue_switch_)
    {
        ROS_ERROR("CancelTask() Task is stop.");
        return;
    }
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("PauseTask() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    //ros::NodeHandle nh;
    //action_pub_ = nh.advertise<actionlib_msgs::GoalID>("navi_core/cancel", 1000,true);

    //if (navi_state_ == ACTIVE || navi_state_ == PENDING)
    {
        // navi_core
        actionlib_msgs::GoalID pause_pose;
        action_pub_.publish(pause_pose);
        // path_follower
        std_msgs::String str;
        str.data = "pause";
        task_ctrl_pub_.publish(str);
    }
    return;
}
void NpuServer::ContinueTask()
{
    API_DEBUG("ContinueTask()");
    if (!continue_switch_)
    {
        ROS_ERROR("CancelTask() Task is stop.");
        return;
    }
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("ContinueTask() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    ros::NodeHandle nh;
    bool check;
    bool check_;
    nh.param<bool>("/wr_task_system/navi_status", check, false);
    nh.param<bool>("/wr_task_system/navi_pf_status", check_, false);
    if( check != true && check_ != true) { // navi_core && path_follower
        GotoPose(cmd_pose_);
        std_msgs::String str;
        str.data = "continue";
        task_ctrl_pub_.publish(str);
    } else if(check_ == true) { // task_path_follower
        std_msgs::String str;
        str.data = "continue";
        task_ctrl_pub_.publish(str);
    } else if(check == true) { //task_navi
        std_msgs::String str;
        str.data = "task_continue";
        task_ctrl_pub_.publish(str);
    }
    return;
}

void NpuServer::CancelTask()
{
    API_DEBUG("CancelTask()");
    continue_switch_ = false;
    if (npu_state_ != NAVI_STATE) {
        ROS_ERROR("CancelTask() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    ros::NodeHandle nh;
    nh.setParam("/wr_task_system/system_status", "idle");
    string host_platform;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    stringstream ss;
    ss << "bash /npu."<< host_platform << "/script/task_stop.sh &\n";
    API_DEBUG("%s", ss.str().c_str());
    system(ss.str().c_str());
    // navi_core
    actionlib_msgs::GoalID pause_pose;
    action_pub_.publish(pause_pose);
    // path_follower
    std_msgs::String str;
    str.data = "cancel";
    task_ctrl_pub_.publish(str);
    return;
}

float NpuServer::GetTaskProgress()
{
    APIG_DEBUG("GetTaskProgress()");
    float return_value;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GetTaskProgress() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return return_value;
    }
    _TEST_CONNECTTED(return_value);
    return return_value;
}
// state
NaviState NpuServer::GetNaviState()
{
    APIG_DEBUG("GetNaviState()");
    if (npu_state_ != NAVI_STATE)
    {
        APIG_DEBUG("GetNaviState() Npu state is not NAVI_STATE.");
        //throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return IDLE;
    }
    ros::NodeHandle n;
    int navi_mode;
    if(n.getParam("/navi_mode",navi_mode))
    {
        if(navi_mode == 1)
        {
            ROS_DEBUG("GetNaviState() navi_state_pf: %d", (int)navi_state_pf_);
            return navi_state_pf_;
        }
        else
        {
            ROS_DEBUG("GetNaviState() navi_state_: %d", (int)navi_state_);
            return navi_state_;
        }

    }

    if(navi_mode_ == P2P_NAVI)
    {
        APIG_DEBUG("GetNaviState() navi_state_: %d", (int)navi_state_);
        return navi_state_;
    }
    else if(navi_mode_ == PF_NAVI)
    {
        APIG_DEBUG("GetNaviState() navi_state_: %d", (int)navi_state_pf_);
        return navi_state_pf_;
    }

}

/// navi.p2p
void NpuServer::GotoPose(Pose3D pose)
{
    API_DEBUG("GotoPose()");
    continue_switch_ = true;
    if (GetMatchingScore() <= 0.6)
    {
//        ROS_ERROR("Matching_Score_Data: %f.", GetMatchingScore());
        //throw NpuException("", "", ERR_ILLEGAL_PARA, "StopNavi,init pose is inaccurate");
        //return;
    }
    cmd_pose_ = pose;
    std::string fixed_frame = STD_MAP_FRAME_ID;
    geometry_msgs::PoseStamped cmd_pose_;
    cmd_pose_.header.frame_id = fixed_frame;
    cmd_pose_.header.stamp = ros::Time::now();
    cmd_pose_.pose.position.x = pose.x;
    cmd_pose_.pose.position.y = pose.y;
    tf::Quaternion quat;
    quat.setRPY(0.0, 0.0, pose.yaw);
    tf::quaternionTFToMsg(quat, cmd_pose_.pose.orientation);
    this->cmd_pose_pub_.publish(cmd_pose_);
    ClearActPath_();
}
void NpuServer::GotoImgPose(ImgPose pose)
{
    API_DEBUG("GotoImgPose()");
    continue_switch_ = true;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GotoImgPose() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());
    Pose3D _3d_pose;
    TransformPose_(pose, _3d_pose);
    GotoPose(_3d_pose);
    return;
}
void NpuServer::GotoGeoPose(GeoPose pose)
{
    API_DEBUG("GotoGeoPose()");
    continue_switch_ = true;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GotoGeoPose() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());
    Pose3D _3d_pose;
    TransformPose_(pose, _3d_pose);
    GotoPose(_3d_pose);
    return;
}
void NpuServer::GotoStation(string map_id, string station_id)
{
    API_DEBUG("GotoStation(\"%s\", \"%s\"", map_id.c_str(), station_id.c_str());
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("GotoStation() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());
    if (map_id == "")
    {
        ROS_WARN("Invalid map id.");
        return;
    }
    if (station_id == "")
    {
        ROS_WARN("Invalid station id.");
        return;
    }

    Station station;
    station.pose.x = INVALID_POSE3D_VALUE;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("GotoStation() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _GetStructOfMapServer_("GetStation", station_id, this, station);
    pthread_spin_unlock(&map_server_spinlock_);
    if (station.pose.x <= INVALID_POSE3D_VALUE)
    {
        ROS_WARN("Ivalid map id/station id.");
        return;
    }
    //API_DEBUG("pose: %.2f, %.2f, %.2f", station.pose.x, station.pose.y, station.pose.yaw);
    GotoPose(station.pose);
    ClearActPath_();
    API_DEBUG("Success.");
    return;
}
/// navi.pf
void NpuServer::FollowTempPath(Pose3DList poses)
{
    API_DEBUG("FollowTempPath()");
    continue_switch_ = true;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("FollowTempPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());

    if (poses.size() <= 0)
    {
        ROS_WARN("Invalid pose list.");
        return;
    }
    Path path;
    path.info.id = "FreePath";
    path.info.map_id = "FreeMap";
    path.info.length = 0;
    path.info.pose_num = poses.size();
    path.poses = poses;

    PublishWaypoints_(path);
    ClearActPath_();
    API_DEBUG("Success.");
    return;
}
void NpuServer::FollowTempImgPath(ImgPoseList img_poses)
{
    API_DEBUG("FollowTempImgPath()");
    continue_switch_ = true;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("FollowTempImgPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());

    Path path;
    path.info.id = "FreePath";
    path.info.map_id = "FreeMap";
    path.info.length = 0;
    path.info.pose_num = img_poses.size();

    TransformPoseList_(img_poses, path.poses);

    for (int i = 0; i < img_poses.size(); i++)
    {
        API_DEBUG("ImgPose=(%d, %d, %.2f)[u,v,theta] => Pose=(%.2f, %.2f, %.2f)[x,y,yaw]"
                  , img_poses[i].u, img_poses[i].v, img_poses[i].theta
                  , path.poses[i].x, path.poses[i].y, path.poses[i].yaw);
    }

    PublishWaypoints_(path);
    API_DEBUG("Success.");
    return;
}
void NpuServer::FollowTempGeoPath(GeoPoseList geo_poses)
{
    API_DEBUG("FollowTempGeoPath()");
    continue_switch_ = true;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("FollowTempGeoPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());

    Path path;
    path.info.id = "FreePath";
    path.info.map_id = "FreeMap";
    path.info.length = 0;
    path.info.pose_num = geo_poses.size();

    TransformPoseList_(geo_poses, path.poses);

    for (int i = 0; i < geo_poses.size(); i++)
    {
        API_DEBUG("GeoPose=(%.3f, %.3f, %.3f)[latitude,longitude,yaw] => Pose=(%.2f, %.2f, %.2f)[x,y,yaw]"
                  , geo_poses[i].latitude, geo_poses[i].longitude, geo_poses[i].yaw
                  , path.poses[i].x, path.poses[i].y, path.poses[i].yaw);
    }

    PublishWaypoints_(path);
    API_DEBUG("Success.");
    return;
}
void NpuServer::FollowPath(string map_id, string path_id)
{
    API_DEBUG("FollowPath(\"%s\", \"%s\"", map_id.c_str(), path_id.c_str());
    continue_switch_ = true;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("FollowPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());
    if (map_id == "")
    {
        ROS_WARN("Invalid map id.");
        return;
    }
    if (path_id == "")
    {
        ROS_WARN("Invalid path id.");
        return;
    }
    Path path;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("FollowPath() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    _GetStructOfMapServer_("GetPath", path_id, this, path);
    pthread_spin_unlock(&map_server_spinlock_);

    if (path.info.id == "")
    {
        ROS_WARN("Invalid map id / path id.");
    }

    PublishWaypoints_(path);
    ClearActPath_();
    ROS_WARN("follow path id is : %s",path.info.id.c_str());
    if (path.info.id == "unfinished_path")
    {
        ROS_WARN("~~~~~~~~~~~~~");
        ros::NodeHandle private_nh("~");
        private_nh.param("unfinishpath",unfinishpath,std::string("/npu.x86_64/unfinishpath.yaml"));
        std::ofstream paths(unfinishpath);
        paths<<"unfinishpaths: "<<std::endl;
        paths<<"  "<<"- "<<"poses: "<<std::endl;
        paths<<"      - "<<"roll: "<<"0"<<std::endl;
        paths<<"        "<<"pitch: "<<"0"<<std::endl;
        paths<<"        "<<"yaw: "<<"0"<<std::endl;
        paths<<"        "<<"x: "<<"0"<<std::endl;
        paths<<"        "<<"y: "<<"0"<<std::endl;
        paths<<"        "<<"z: "<<"0"<<std::endl;
        paths<<"  "<<"- "<<"info: "<<std::endl;
        paths<<"      - "<<"map_id: "<<"0"<<std::endl;
        paths.close();
        ROS_WARN("get path switch is true ,reset path yaml");
    }
    API_DEBUG("Success.");
    return;
}
/// navi.ccp
Pose3DList NpuServer::PlanCoveragePath(Point3DList vertices)
{
    API_DEBUG("PlanCoveragePath()");
    Pose3DList return_value;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("PlanCoveragePath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return return_value;
    }
    _TEST_CONNECTTED(return_value);

    if (vertices.size() < 3)
    {
        ROS_WARN("It needs at lease 3 vertices to form a polygon.");
        Pose3DList numb_list;
        return numb_list;
    }
    else
    {
        geometry_msgs::PolygonStamped region_msg;
        region_msg.polygon.points.resize(vertices.size());
        for (int i = 0; i < vertices.size(); i++)
        {
            geometry_msgs::Point32 pnt32;
            pnt32.x = vertices[i].x;
            pnt32.y = vertices[i].y;
            pnt32.z = vertices[i].z;
            region_msg.polygon.points[i] = pnt32;
        }
        region_msg.polygon.points.push_back(region_msg.polygon.points.front());
        region_msg.header.stamp = ros::Time::now();
        region_msg.header.frame_id = STD_MAP_FRAME_ID;
        // TODO: transform cpp_rgion into path through service or through python
        ccp_region_pub_.publish(region_msg);
        ClearActPath_();
    }
    return return_value;
}

ImgPoseList NpuServer::PlanCoverageImgPath(ImgPointList vertices)
{
    API_DEBUG("PlanCoverageImgPath()");
    ImgPoseList img_path;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("PlanCoverageImgPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return img_path;
    }
    _TEST_CONNECTTED(img_path);
    Point3DList _3d_vertices;
    TransformPointList_(vertices, _3d_vertices);
    Pose3DList _3d_path = this->PlanCoveragePath(_3d_vertices);
    TransformPoseList_(_3d_path, img_path);
    API_DEBUG("PlanCoverageImgPath() Done");
    return img_path;
}
GeoPoseList NpuServer::PlanCoverageGeoPath(GeoPointList vertices)
{
    API_DEBUG("PlanCoverageGeoPath()");
    GeoPoseList geo_path;
    if (npu_state_ != NAVI_STATE)
    {
        ROS_ERROR("PlanCoverageGeoPath() Npu state is not NAVI_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not NAVI_STATE.");
        return geo_path;
    }
    _TEST_CONNECTTED(geo_path);
    Point3DList _3d_vertices;
    TransformPointList_(vertices, _3d_vertices);
    Pose3DList _3d_path = this->PlanCoveragePath(_3d_vertices);
    TransformPoseList_(_3d_path, geo_path);
    API_DEBUG("PlanCoverageGeoPath() Done");
    return geo_path;
}

bool NpuServer::IllegalCharacterDetection(ImgStationList list,ImgStationList station_list)
{
    if (list.size() != 0) {
        for(int length=0;length!=list[list.size()-1].info.id.size();++length) {
            if ((65 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 90)
                    || (97 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 122)
                    || (48 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 57)
                    || (list[list.size()-1].info.id.at(length) == 95)
                    || (list[list.size()-1].info.id.at(length) == 45)
                    ) {
            } else {
                ROS_ERROR("NpuServer::SetImgStations():Station id is only input 'Letter , Number , _'.");
                if(server_type_ == "ice") {
                    throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetImgStations():Station id is only input 'Letter , Number , _'.");
                }
                return false;
            }
        }
    }
    if(station_list.size() >= 1 && station_list.size()<list.size()) {
        for(int i=0;i<station_list.size();i++) {
            if(list[list.size()-1].info.id == station_list[i].info.id) {
                ROS_ERROR("NpuServer::SetImgStations():Station id is already exist.");
                if(server_type_ == "ice") {
                    throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetImgStations():Station id is already exist.");
                }
                return false;
            }
        }
    }
    return true;
}
bool NpuServer::IllegalCharacterDetection(const GeoStationList& list, const GeoStationList& station_list)
{
    if (list.size() != 0) {
        for(int length=0;length!=list[list.size()-1].info.id.size();++length) {
            if ((65 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 90)
                    || (97 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 122)
                    || (48 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 57)
                    || (list[list.size()-1].info.id.at(length) == 95)
                    || (list[list.size()-1].info.id.at(length) == 45)
                    ) {
            } else {
                ROS_ERROR("NpuServer::SetImgStations():Station id is only input 'Letter , Number , _'.");
                if(server_type_ == "ice") {
                    throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetImgStations():Station id is only input 'Letter , Number , _'.");
                }
                return false;
            }
        }
    }
    if(station_list.size() >= 1 && station_list.size()<list.size()) {
        for(int i=0;i<station_list.size();i++) {
            if(list[list.size()-1].info.id == station_list[i].info.id) {
                ROS_ERROR("NpuServer::SetImgStations():Station id is already exist.");
                if(server_type_ == "ice") {
                    throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetImgStations():Station id is already exist.");
                }
                return false;
            }
        }
    }
    return true;
}

bool NpuServer::IllegalCharacterDetection(PathList list,PathList paths)
{
    if (list.size() != 0) {
        for(int length=0;length!=list[list.size()-1].info.id.size();++length) {
            if ((65 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 90)
                    || (97 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 122)
                    || (48 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 57)
                    || (list[list.size()-1].info.id.at(length) == 95)
                    || (list[list.size()-1].info.id.at(length) == 45)
                    ) {
            } else {
                throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetImgPaths():Path id is only input 'Letter , Number , _'.");
                return false;
            }
        }
    }
    if(paths.size() >= 1 && paths.size()<list.size()) {
        for(int i=0;i<paths.size();i++) {
            if(list[list.size()-1].info.id == paths[i].info.id) {
                throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetImgPaths():Path id is already exist.");
                return false;
            }
        }
    }
    return true;
}
bool NpuServer::IllegalCharacterDetection(ImgVirtualWallList virtual_walls,ImgVirtualWallList VirtualWallList)
{
    if (virtual_walls.size() != 0) {
        for(int length=0;length!=virtual_walls[virtual_walls.size()-1].info.id.size();++length) {
            if ((65 <= virtual_walls[virtual_walls.size()-1].info.id.at(length) && virtual_walls[virtual_walls.size()-1].info.id.at(length) <= 90)
                    || (97 <= virtual_walls[virtual_walls.size()-1].info.id.at(length) && virtual_walls[virtual_walls.size()-1].info.id.at(length) <= 122)
                    || (48 <= virtual_walls[virtual_walls.size()-1].info.id.at(length) && virtual_walls[virtual_walls.size()-1].info.id.at(length) <= 57)
                    || (virtual_walls[virtual_walls.size()-1].info.id.at(length) == 95)
                    || (virtual_walls[virtual_walls.size()-1].info.id.at(length) == 45)
                    ) {
            } else {
                ROS_ERROR("NpuServer::SetImgVirtualWalls():VirtualWall id is only input 'Letter , Number , _'.");
                throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetImgVirtualWalls():VirtualWall id is only input 'Letter , Number , _'.");
                return false;
            }
        }
    }
    if(VirtualWallList.size() >= 1 && VirtualWallList.size()<virtual_walls.size()) {
        for(int i=0;i<VirtualWallList.size();i++) {
            if(virtual_walls[virtual_walls.size()-1].info.id == VirtualWallList[i].info.id) {
                ROS_ERROR("NpuServer::SetImgVirtualWalls():VirtualWall id is already exist.");
                throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetImgVirtualWalls():VirtualWall id is already exist.");
                return false;
            }
        }
    }
    return true;
}
bool NpuServer::IllegalCharacterDetection(const GeoVirtualWallList& list, const GeoVirtualWallList& list_tmp)
{
    if (list.size() != 0) {
        for(int length=0;length!=list[list.size()-1].info.id.size();++length) {
            if ((65 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 90)
                    || (97 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 122)
                    || (48 <= list[list.size()-1].info.id.at(length) && list[list.size()-1].info.id.at(length) <= 57)
                    || (list[list.size()-1].info.id.at(length) == 95)
                    || (list[list.size()-1].info.id.at(length) == 45)
                    ) {
            } else {
                ROS_ERROR("NpuServer::SetGeoVirtualWalls():VirtualWall id is only input 'Letter , Number , _'.");
                throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetGeoVirtualWalls():VirtualWall id is only input 'Letter , Number , _'.");
                return false;
            }
        }
    }
    if(list_tmp.size() >= 1 && list_tmp.size()<list.size()) {
        for(int i=0;i<list_tmp.size();i++) {
            if(list[list.size()-1].info.id == list_tmp[i].info.id) {
                ROS_ERROR("NpuServer::SetGeoVirtualWalls():VirtualWall id is already exist.");
                throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SetGeoVirtualWalls():VirtualWall id is already exist.");
                return false;
            }
        }
    }
    return true;
}

bool NpuServer::IllegalCharacterDetection(string map_id,MapInfoList map_info_list)
{
    for(int map_l=0;map_l != map_id.size();++map_l) {
        if ((65 <= map_id.at(map_l) && map_id.at(map_l) <= 90)
                || (97 <= map_id.at(map_l) && map_id.at(map_l) <= 122)
                || (48 <= map_id.at(map_l) && map_id.at(map_l) <= 57)
                || (map_id.at(map_l) == 95)
                || (map_id.at(map_l) == 45)
                ) {
        } else {
            ROS_ERROR("NpuServer::SaveMap_():Map id is only input 'Letter , Number , _'.");
            throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SaveMap_():Map id is only input 'Letter , Number , _'.");
            return false;
        }
    }
    if(map_info_list.size() >= 1) {
        for(int i=0;i<map_info_list.size();i++) {
            if(map_id == map_info_list[i].id) {
                ROS_ERROR("NpuServer::SaveMap_():Map id is already exist.");
                throw NpuException("","", ERR_ILLEGAL_PARA, "NpuServer::SaveMap_():Map id is already exist.");
                return false;
            }
        }
    }
    return true;
}

string NpuServer::GetBaseLaunchArgs_(ros::NodeHandle &nh)
{
    bool enb_play;
    nh.param<bool>(STD_BAG_PARAM_NS + "/enb_play", enb_play, false);

    bool enb_handheld_mode;
    nh.param<bool>(STD_BASE_PARAM_NS + "/enb_handheld_mode", enb_handheld_mode, false);

    std::string base_id;
    nh.param<string>(STD_BASE_PARAM_NS + "/base_id", base_id, "bcu");

    //to adapt kobuki
    bool enb_kobuki = ( base_id == "kobuki" );
    //    bool enb_odom_tf_pub = ( EnumString<SlamMode>::EnumToStr(slam_mode_) == "PF_SLAM" );
    //    if( enb_kobuki && enb_odom_tf_pub)
    //    {
    //        enb_handheld_mode = true;
    //    }

    std::stringstream ss;
    ss << "base_id:=" << ( ( (enb_handheld_mode || enb_play) && !enb_kobuki ) ? "sim_bcu" : base_id ) << " "
       << "enb_slave_mode:=" << wizrobo::str_ext::BoolToStr(base_param_.enb_slave_mode) << " "
       << "enb_kobuki:=" << BoolToStr(enb_kobuki) << " "
          //       << "enb_odom_tf_pub:=" << BoolToStr(enb_odom_tf_pub) << " "
       << "enb_handheld_mode:=" << BoolToStr(enb_handheld_mode);
    return ss.str();
}
string NpuServer::GetLidarLaunchArgs_(ros::NodeHandle& nh)
{
    bool enb_play;
    nh.param<bool>(STD_BAG_PARAM_NS + "/enb_play", enb_play, false);

    string param_ns = STD_SENSOR_PARAM_NS + "/lidar_params/lidar_0";
#if USE_NPU_API_V_0_9_12
    LidarType lidar_type;
    if (enb_play)
    {
        lidar_type = SIM_LIDAR;
    }
    else
    {
        GetEnumParam<LidarType>(nh, param_ns + "/type", lidar_type, SIM_LIDAR);
    }
#else
    wizrobo::LidarType lidar_type;
    if (enb_play)
    {
        lidar_type = wizrobo::SIM_LIDAR;
    }
    else
    {
        GetEnumParam<wizrobo::LidarType>(nh, param_ns + "/type", lidar_type, wizrobo::SIM_LIDAR);
    }
#endif
    InterfaceType lidar_itf_type;
    GetEnumParam<InterfaceType>(nh, param_ns + "/itf_type", lidar_itf_type, SERIAL);
    string lidar_itf_id;
    nh.param<string>(param_ns + "/itf_id", lidar_itf_id, "/dev/rplidar");

    bool enb_itl_flt, enb_beam_skip, enb_fov_mask;
    nh.param<bool>(param_ns + "/lidar_enb_filter", enb_itl_flt, false);
    /* to do
    nh.param<bool>(param_ns + "/filter/enb_beam_skip", enb_beam_skip, false);
    nh.param<bool>(param_ns + "/filter/enb_fov_mask", enb_fov_mask, false);
    */
    bool lidar_enb_filter = enb_itl_flt || enb_beam_skip || enb_fov_mask;

    std::stringstream ss;
#if USE_NPU_API_V_0_9_12
    ss << "lidar_type:=" << EnumString<LidarType>::EnumToLowerStr(lidar_type) << " "
      #else
    ss << "lidar_type:=" << EnumString<wizrobo::LidarType>::EnumToLowerStr(lidar_type) << " "
      #endif
       << "lidar_itf_type:=" << EnumString<InterfaceType>::EnumToLowerStr(lidar_itf_type) << " "
       << "lidar_itf_id:=" << lidar_itf_id << " "
       << "lidar_enb_filter:=" << BoolToStr(lidar_enb_filter);
    return ss.str();
}
string NpuServer::GetImuLaunchArgs_(ros::NodeHandle &nh)
{
    bool enb_play;
    nh.param<bool>(STD_BAG_PARAM_NS + "/enb_play", enb_play, false);

    int imu_num;
    nh.param<int>(STD_SENSOR_PARAM_NS + "/imu_num", imu_num, 0);
    bool enb_imu = (imu_num > 0);

    string param_ns = STD_SENSOR_PARAM_NS + "/imu_params/imu_0";
    ImuType imu_type;
    if (enb_play)
    {
        imu_type = SIM_IMU;
    }
    else
    {
        GetEnumParam<ImuType>(nh, param_ns + "/type", imu_type, SIM_IMU);
    }

    InterfaceType imu_itf_type;
    GetEnumParam<InterfaceType>(nh, param_ns + "/itf_type", imu_itf_type, SERIAL);
    string imu_itf_id;
    nh.param<string>(param_ns + "/itf_id", imu_itf_id, "/dev/uranus_imu");

    std::stringstream ss;
    ss << "enb_imu:=" << BoolToStr(enb_imu) << " "
       << "imu_type:=" << EnumString<ImuType>::EnumToLowerStr(imu_type) << " "
       << "imu_itf_type:=" << EnumString<InterfaceType>::EnumToLowerStr(imu_itf_type) << " "
       << "imu_itf_id:=" << imu_itf_id;
    return ss.str();
}
string NpuServer::GetCameraLaunchArgs_(ros::NodeHandle &nh)
{
    bool enb_play;
    nh.param<bool>(STD_BAG_PARAM_NS + "/enb_play", enb_play, false);

    int camera_num;
    nh.param<int>(STD_SENSOR_PARAM_NS + "/camera_num", camera_num, 0);
    bool enb_camera = (camera_num > 0);

    string param_ns = STD_SENSOR_PARAM_NS + "/camera_params/camera_0";
    CameraType camera_type;
    if (enb_play)
    {
        camera_type = SIM_CAMERA;
    }
    else
    {
        GetEnumParam<CameraType>(nh, param_ns + "/type", camera_type, SIM_CAMERA);
    }

    InterfaceType camera_itf_type;
#if USE_NPU_API_V_0_9_12
    GetEnumParam<InterfaceType>(nh, param_ns + "/itf_type", camera_itf_type, USB);
#else
    GetEnumParam<InterfaceType>(nh, param_ns + "/itf_type", camera_itf_type, SERIAL);
#endif
    string camera_itf_id;
    nh.param<string>(param_ns + "/itf_id", camera_itf_id, "/dev/ps3eye");

    int camera_width_pix;
    nh.param<int>(param_ns + "/width_pix", camera_width_pix, 640);
    int camera_height_pix;
    nh.param<int>(param_ns + "/height_pix", camera_height_pix, 480);
    int camera_fps_hz;
    nh.param<int>(param_ns + "/fps_hz", camera_fps_hz, 30);

    std::stringstream ss;
    ss << "enb_camera:=" << BoolToStr(enb_camera) << " "
       << "camera_type:=" << EnumString<CameraType>::EnumToLowerStr(camera_type) << " "
       << "camera_itf_type:=" << EnumString<InterfaceType>::EnumToLowerStr(camera_itf_type) << " "
       << "camera_itf_id:=" << camera_itf_id << " "
       << "camera_width_pix:=" << camera_width_pix << " "
       << "camera_height_pix:=" << camera_height_pix << " "
       << "camera_fps_hz:=" << camera_fps_hz;
    return ss.str();
}
string NpuServer::GetGpsLaunchArgs_(ros::NodeHandle &nh)
{
    bool enb_play;
    nh.param<bool>(STD_BAG_PARAM_NS + "/enb_play", enb_play, false);

    int gps_num;
    nh.param<int>(STD_SENSOR_PARAM_NS + "/gps_num", gps_num, 0);
    bool enb_gps = (gps_num > 0);

    string param_ns = STD_SENSOR_PARAM_NS + "/gps_params/gps_0";
    GpsType gps_type;
    if (enb_play)
    {
        gps_type = SIM_GPS;
    }
    else
    {
        GetEnumParam<GpsType>(nh, param_ns + "/type", gps_type, SIM_GPS);
    }

    InterfaceType gps_itf_type;
    GetEnumParam<InterfaceType>(nh, param_ns + "/itf_type", gps_itf_type, SERIAL);
    string gps_itf_id;
    nh.param<string>(param_ns + "/itf_id", gps_itf_id, "/dev/gps");

    std::stringstream ss;
    ss << "enb_gps:=" << BoolToStr(enb_gps) << " "
       << "gps_type:=" << EnumString<GpsType>::EnumToLowerStr(gps_type) << " "
       << "gps_itf_type:=" << EnumString<InterfaceType>::EnumToLowerStr(gps_itf_type) << " "
       << "gps_itf_id:=" << gps_itf_id;
    return ss.str();
}

string NpuServer::GetBagLaunchArgs_(ros::NodeHandle &nh)
{
    string bag_id;
    nh.param<string>(STD_CORE_PARAM_NS + "/BAG_ID", bag_id, "");

    string param_ns = STD_BAG_PARAM_NS;
    bool enb_record;
    nh.param<bool>(param_ns + "/enb_record", enb_record, false);// TODO: STD_RUNTIME_NS
    bool record_tf_static;
    nh.param<bool>(param_ns + "/record_tf_static", record_tf_static, true);

    bool enb_play;
    nh.param<bool>(param_ns + "/enb_play", enb_play, false);// TODO: STD_RUNTIME_NS
    bool play_tf_static;
    nh.param<bool>(param_ns + "/play_tf_static", play_tf_static, false);
    double play_rate;
    nh.param<double>(param_ns + "/play_rate", play_rate, 1.0);

    int lidar_num;
    nh.param<int>(STD_SENSOR_PARAM_NS + "/lidar_num", lidar_num, 0);
    int imu_num;
    nh.param<int>(STD_SENSOR_PARAM_NS + "/imu_num", imu_num, 0);
    int camera_num;
    nh.param<int>(STD_SENSOR_PARAM_NS + "/camera_num", camera_num, 0);
    int gps_num;
    nh.param<int>(STD_SENSOR_PARAM_NS + "/gps_num", gps_num, 0);
    stringstream bag_topics_ss;
    bag_topics_ss << "\'" << "/odom "
                  << (lidar_num > 0 ? (STD_SCAN_TOPIC_NAME + " ") : "")
                  << (imu_num > 0 ? (STD_IMU_TOPIC_NAME + " ") : "")
                  << (camera_num > 0 ? (STD_ARTAG_TOPIC_NAME + " ") : "")
                  << (gps_num > 0 ? (STD_GPS_TOPIC_NAME + " ") : "")
                  << (record_tf_static || play_tf_static ? (STD_TF_STATIC_TOPIC_NAME + " ") : "") << "\'";
    ROS_WARN("bag_topics = %s", bag_topics_ss.str().c_str());

    std::stringstream ss;
    if ((enb_record || enb_play ) && !bag_id.empty())
    {
        ss << "bag_id:=" << bag_id << " "
           << "bag_topics:=" << bag_topics_ss.str() << " ";
        ss << "enb_record:=" << BoolToStr(enb_record) << " ";
        if (enb_record)
        {
            ss << "record_args:=" << " ";
        }
        ss << "enb_play:=" << BoolToStr(enb_play) << " ";
        if (enb_play)
        {
            ss << "play_args:=" << "\'-r " << play_rate << "\'";
        }
    }
    return ss.str();
}

void NpuServer::StartRecord()
{

}

void NpuServer::StopRecord(string bag_id)
{

}

void NpuServer::StartPlay()
{
    //TODO: set /use_sim_time to true

}

void NpuServer::StopPlay()
{

}

//// slam
void NpuServer::StartSlam(SlamMode slam_mode)
{
    API_DEBUG("StartSlam(%s)", EnumString<SlamMode>::EnumToStr(slam_mode).c_str());
    if (npu_state_ == SLAM_STATE) {
        if (slam_mode_ == slam_mode) {
            ROS_ERROR("SLAM is already started.");
            throw NpuException("", "", ERR_UNKNOWN, "SLAM is already started.");
            return;
        } else {
            ROS_ERROR("Stop old SLAM first.");
            throw NpuException("", "", ERR_UNKNOWN, "Stop old SLAM first.");
            StopSlam("");
        }
    } else if (npu_state_ == NAVI_STATE) {
        ROS_ERROR("Stop NAVI first.");
        throw NpuException("", "", ERR_UNKNOWN, "Stop NAVI first.");
        StopNavi();
    } else if (npu_state_ == TELEOP_STATE) {
        ROS_ERROR("Stop TELOP first.");
        throw NpuException("", "", ERR_UNKNOWN, "Stop TELOP first.");
        StopTelop();
    }
    SetMapServerMode_("SLAM_MODE");
    ros::NodeHandle nh;
    ros::NodeHandle private_nh("~");
    private_nh.param("init_act_pose",init_act_pose_,std::string("/npu.x86_64/share/wr_npu_server/param/init_act_pose.yaml"));
    std::string logger_level_str;
    nh.param<string>(STD_CORE_PARAM_NS + "/LOGGER_LEVEL", logger_level_str, "info");

    npu_state_ = SLAM_STATE;//->SWITCHING_ACTION
    nh.setParam(STD_RUNTIME_PARAM_NS + "/npu_state", EnumString<NpuState>::EnumToStr(npu_state_).c_str());

    slam_mode_ = slam_mode;
    nh.setParam(STD_RUNTIME_PARAM_NS + "/slam_mode", EnumString<SlamMode>::EnumToStr(slam_mode).c_str());

    bool enb_odom_tf_pub = ( EnumString<SlamMode>::EnumToStr(slam_mode_) == "PF_SLAM" );

    std::string host_platform;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");

//    std::string record_id = std::to_string(ros::Time::now().toSec());
    std::string record_id = GetCurrentFormatTimeString();
    ROS_INFO("Recore id is : %s.", record_id.c_str());

    stringstream ss;
    ss << "bash /npu."<< host_platform << "/script/npu_run.sh --slam \""
       << "logger_level:=" << logger_level_str << " "
       << "slam_mode:=" << EnumString<SlamMode>::EnumToLowerStr(slam_mode) << " "
       << "enb_odom_tf_pub:=" << BoolToStr(enb_odom_tf_pub) << " "
       << "bag_id:=" << record_id << ""
       << "\" &\n";
    launch_map_receiving_thread_();
    system(ss.str().c_str());
    API_DEBUG("%s", ss.str().c_str());

    UpdateFootprint_(base_param_.footprint_param);// TODO: set footprint only
    ClearActPath_();
    save_act_pose_switch = true;
}

void NpuServer::StopSlam(string map_id)
{
    /// parse flc_opt
    if (npu_state_ != SLAM_STATE) {
        ROS_ERROR("Npu state is not SLAM_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not SLAM_STATE.");
        return;
    }
    int idx = map_id.find("flc_opt");
    API_DEBUG("NpuServer::StopSlam(): idx = %d", idx);
    if (idx >= 0) {
        ForceLoopClosure_();
        return;
    }
    API_DEBUG("NpuServer::StopSlam(\"%s\")", map_id.c_str());
    ros::NodeHandle nh;
    string host_platform;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    stringstream ss;
    ss << "bash /npu."<< host_platform << "/script/npu_stop.sh &\n";
    stop_map_receiving_thread_();
    SaveMap_(map_id);
    SetMapServerMode_("NAVI_MODE");//TODO
    system(ss.str().c_str());
    API_DEBUG("%s", ss.str().c_str());

    PublishManualVel_(0, 0);
    if (base_param_.enb_slave_mode) {
        if (pthread_spin_trylock(&cmd_motor_spd_spinlock_) != 0) {
            ROS_ERROR("StopSlam() Get thread lock failed");
            throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
            return ;
        }
        for (int i = 0; i < cmd_motor_spd_.rpms.size(); i++) {
            cmd_motor_spd_.rpms[i] = 0;
        }
        pthread_spin_unlock(&cmd_motor_spd_spinlock_);
    }
    npu_state_ = IDLE_STATE;
    nh.setParam(STD_RUNTIME_PARAM_NS + "/npu_state", EnumString<NpuState>::EnumToStr(npu_state_).c_str());
    ClearActPath_();
    save_act_pose_switch = false;
    return;
}

static bool IsFileNameIlleagel(const string& file_name)
{
    for (auto _char : file_name) {
        if ((_char>='a' && _char<='z')
                || (_char>='A' && _char<='Z')
                || (_char>='0' && _char<='9')
                || _char == '-' || _char == '_' || _char == '.') {
            continue;
        } else {
            return false;
        }
    }
    return true;
}

//// file
ZipFile NpuServer::ExportConfigFile(string file_name)
{
    ROS_INFO("ExportConfigFile()");
    ZipFile return_value;
    if (npu_state_ != IDLE_STATE)
    {
        ROS_ERROR("Npu state is not IDLE_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not IDLE_STATE.");
        return return_value;
    }
    _TEST_CONNECTTED(return_value);
    return return_value;
}
void NpuServer::ImportConfigFile(ZipFile file, string file_name)
{
    ROS_INFO("ImportConfigFile()");
    if (npu_state_ != IDLE_STATE)
    {
        ROS_ERROR("Npu state is not IDLE_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not IDLE_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());
    return;
}

void NpuServer::PackMapFile(const string& file_name)
{
    LM_DEBUG("");
    string host_platform;
    ros::NodeHandle nh;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    string dirname =  "/npu." + host_platform + "/map";
    string path = dirname + "/" + file_name;
    LM_DEBUG("path: %s", path.c_str());

    stringstream ss;
    ss << "rm -rf " << dirname << "/*.zip ";
    LM_WARN("%s", ss.str().c_str());
    system(ss.str().c_str());

    ss.str("");
    ss << "cd " << dirname
       << " && zip " << file_name << ".zip "
       << file_name << ".pgm "
       << file_name << ".yaml";
    LM_WARN("%s", ss.str().c_str());
    system(ss.str().c_str());
    LM_DEBUG("<");
}

void NpuServer::UnPackMapFile(const string &file_name)
{
    LM_DEBUG("");
    string host_platform;
    ros::NodeHandle nh;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    string dirname =  "/npu." + host_platform + "/map";
    LM_DEBUG("dirname: %s, file_name: %s", dirname.c_str(), file_name.c_str());

    stringstream ss;
    ss << "cd " << dirname
       << " && unzip " << file_name << ".zip ";
    LM_WARN("%s", ss.str().c_str());
    system(ss.str().c_str());
    LM_DEBUG("<");
}

ZipFile NpuServer::ExportMapFile(string file_name)
{
    ZipFile _file;
    _file.resize(0);
    API_DEBUG("ExportMapFile()");
    if (npu_state_ != IDLE_STATE)
    {
        ROS_ERROR("Npu state is not IDLE_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not IDLE_STATE.");
        return _file;
    }
    _TEST_CONNECTTED(_file);

    if (!IsFileNameIlleagel(file_name)) {
        API_DEBUG("Illeagel file name");
        return _file;
    }
    if (file_name.substr(file_name.size()-4, 4) != ".pgm"
            && file_name.substr(file_name.size()-4, 4) != ".PGM"
            && file_name.substr(file_name.size()-5, 5) != ".yaml"
            && file_name.substr(file_name.size()-5, 5) != ".YAML") {
        API_DEBUG("Only file end with \"pgm\" of \"yaml\" is allowed");
        return _file;
    }

    string host_platform;
    ros::NodeHandle nh;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    string path = "/npu." + host_platform + "/map/" + file_name;

    API_DEBUG("ExportMapFile() path: %s", path.c_str());
    ifstream input_file(path.c_str(), ios::in|ios::binary);
    input_file.seekg(0, ios::end);
    int length = input_file.tellg();
    API_DEBUG("ExportMapFile() file size: %d", length);
    if (length > (1024 * 1024 * 50))
    {
        API_DEBUG("file \"%s\" is bigger than 50M", path.c_str());
        return _file;
    }
    input_file.seekg(0, ios::beg);
    _file.resize(length);
    char *buffer = new char[length];
    input_file.read(buffer, length);
    input_file.close();
    _file.data();
    for (int i=0;i<length;i++) {
        _file[i] = buffer[i];
    }
    delete [] buffer;
    return _file;
}

void NpuServer::ImportMapFile(ZipFile file, string file_name)
{
    API_DEBUG("ImportMapFile()");
    if (npu_state_ != IDLE_STATE)
    {
        ROS_ERROR("Npu state is not IDLE_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not IDLE_STATE.");
        return;
    }
    _TEST_CONNECTTED(void());

    if (!IsFileNameIlleagel(file_name)) {
        API_DEBUG("Illeagel file name");
        return;
    }
    if (file_name.substr(file_name.size()-4, 4) != ".pgm"
            && file_name.substr(file_name.size()-4, 4) != ".PGM"
            && file_name.substr(file_name.size()-5, 5) != ".yaml"
            && file_name.substr(file_name.size()-5, 5) != ".YAML") {
        API_DEBUG("Only file end with \"pgm\" of \"yaml\" is allowed");
        return;
    }

    string host_platform;
    ros::NodeHandle nh;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    string path = "/npu." + host_platform + "/map/" + file_name;

    char *buffer = new char [file.size()];
    for (int i=0;i<file.size();i++) {
        buffer[i] = file[i];
    }
    ofstream output_file(path, ios::out|ios::binary);
    output_file.write(buffer, file.size());
    delete [] buffer;
    output_file.close();
    return;
}

void NpuServer::GetExportFileInfo_async(const ::wizrobo_npu::AMD_NpuIce_GetExportFileInfoPtr& ptr,string fileName)
{
    API_DEBUG("GetExportFileInfo_async()");
    if (npu_state_ != IDLE_STATE)
    {
        ROS_ERROR("Npu state is not IDLE_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not IDLE_STATE.");
        return;
    }
    p_get_files_info_->getExportFileInfoPtr = ptr;
    p_get_files_info_->fileName = fileName;
    launch_file_manage_thread_("compress");
}

FileData NpuServer::GetExportFiledata(string fileName,int chunk_index)
{
    API_DEBUG("GettingFiledata:%d",chunk_index);
    FileData file_data;
    file_data.data.resize(chunk_size_);
    if (npu_state_ != IDLE_STATE)
    {
        ROS_ERROR("Npu state is not IDLE_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not IDLE_STATE.");
        return file_data;
    }

    /// 数组大小1M
    char buffer[chunk_size_];
    int offset = 0;

    const char *name=fileName.c_str();
    fstream file;
    FILE *fp=fopen(name,"r");
    file.open(name, ios::binary|ios::in);

    if ( !file.is_open() )
    {
        cout << "fail" << endl;
    }

    fseek(fp,0L,SEEK_END);
    int filesize=ftell(fp);

    /// 最后一个数据块长度
    int last_chunk_length=0;
    /// 一个数据块为1M,计算数据块的数量
    int chunk_num=filesize/chunk_size_;

    if(filesize%chunk_size_==0)
    {
        chunk_num=chunk_num;
        last_chunk_length=chunk_size_;
    }

    else
    {
        chunk_num+=1;
        last_chunk_length=filesize%chunk_size_;
    }

    int chunk_length=0;

    file.seekg(chunk_index*chunk_size_,ios::cur);
    file.read(buffer, chunk_size_);

    if(chunk_index==chunk_num-1)
    {
        file_data.chunk_length=last_chunk_length;
        chunk_length=last_chunk_length;
    }
    else
    {
        file_data.chunk_length = chunk_size_;
        chunk_length = chunk_size_;
    }

    offset = chunk_size_*chunk_index ;
    file_data.file_name=fileName;
    file_data.chunk_num=chunk_num;
    memcpy((unsigned char*)(&file_data.data[0]), buffer, chunk_size_);
    file_data.offset=offset;
    file_data.chunk_index=chunk_index;

    if(chunk_num-1==chunk_index)
    {
        launch_file_manage_thread_("delete");
    }

    return file_data;
}

void NpuServer::SendImportFileData(FileData data)
{
    API_DEBUG("SendImportFileData:%d",data.chunk_index);
    if (npu_state_ != IDLE_STATE)
    {
        ROS_ERROR("Npu state is not IDLE_STATE.");
        throw NpuException("", "", ERR_UNKNOWN, "Npu state is not IDLE_STATE.");
        return;
    }
    string path2 = data.file_name;
    const char *name=path2.c_str();

    int index=0;
    string temp=".tar.gz";
    index=path2.find(temp);
    string file_dir=name;
    file_dir=file_dir.substr(0,index);

    const char *newname=file_dir.c_str();
    fstream file;
    file.open(newname, ios::binary|ios::in);

    if(!file)
    {
        ROS_ERROR("No such file was found!!!");
        throw NpuException("","", ERR_UNKNOWN, "No such file was found!");
        return ;
    }

    ofstream myfile (name, ios::out | ios::app | ios::binary);
    myfile.seekp(data.offset, ios::beg);
    myfile.write((char*)&data.data[0], data.chunk_length);
    myfile.close();
    if(data.chunk_index==data.chunk_num-1)
    {
        API_DEBUG("SendImportFileData Succeed!");
        //memcpy(&file_data.data[0], buffer,chunk_size_);
        const char *a=data.file_name.c_str();
        strcpy(uncompress_file_name,a);
        launch_file_manage_thread_("uncompress");
    }
}

//// implementation
/// img-real transformation
bool NpuServer::TransformPose_(const Pose3D& input_data, ImgPose& output_data)
{
    Pose3DList _3d_poses;
    ImgPoseList img_poses;
    _3d_poses.push_back(input_data);
    if (TransformPoseList_(_3d_poses, img_poses) == false)
    {
        return false;
    }
    output_data = img_poses[0];
    return true;
}
bool NpuServer::TransformPose_(const ImgPose& input_data, Pose3D& output_data)
{
    ImgPoseList img_poses;
    Pose3DList _3d_poses;
    img_poses.push_back(input_data);
    if (TransformPoseList_(img_poses, _3d_poses) == false)
    {
        return false;
    }
    output_data = _3d_poses[0];
    return true;
}
bool NpuServer::TransformPoseList_(const Pose3DList& input_data, ImgPoseList& output_data)
{
    NSR_DEBUG("TransformPoseList_: map_id(\"%s\")", current_imgmap_head_info_.info.id.c_str());
    if (current_imgmap_head_info_.info.id == "") {
        NSR_DEBUG("return false");
        return false;
    }
    double offset_x = current_imgmap_head_info_.info.offset.x;
    double offset_y = current_imgmap_head_info_.info.offset.y;
    double resolution = current_imgmap_head_info_.info.resolution;
    double ratio = current_imgmap_head_info_.mat.ratio;
    double height = current_imgmap_head_info_.mat.height;

    int size = input_data.size();
    output_data.resize(size);

    for (int i=0;i<size;i++) {
        output_data[i].u = static_cast<int>(
                    ((input_data[i].x - offset_x) / resolution) * ratio);
        output_data[i].v = static_cast<int>(
                    height - ((input_data[i].y - offset_y) / resolution) *  ratio);
        output_data[i].theta = input_data[i].yaw;
    }
    NSR_DEBUG("return true");
    return true;
}
bool NpuServer::TransformPoseList_(const ImgPoseList& input_data, Pose3DList& output_data)
{
    NSR_DEBUG("TransformPoseList_: map_id(\"%s\")", current_imgmap_head_info_.info.id.c_str());
    if (current_imgmap_head_info_.info.id == "") {
        NSR_DEBUG("return false");
        return false;
    }
    double offset_x = current_imgmap_head_info_.info.offset.x;
    double offset_y = current_imgmap_head_info_.info.offset.y;
    double resolution = current_imgmap_head_info_.info.resolution;
    double ratio = current_imgmap_head_info_.mat.ratio;
    double height = current_imgmap_head_info_.mat.height;

    int size = input_data.size();
    output_data.resize(size);

    for (int i=0;i<size;i++) {
        output_data[i].x = \
                input_data[i].u * 1.0  / ratio * resolution + offset_x;
        output_data[i].y = \
                (height - input_data[i].v) * 1.0 / ratio * resolution + offset_y;
        output_data[i].z = 0.0;
        output_data[i].roll = 0.0;
        output_data[i].pitch = 0.0;
        output_data[i].yaw = input_data[i].theta;
    }
    NSR_DEBUG("return true");
    return true;
}
bool NpuServer::TransformPointList_(const Point3DList& input_data, ImgPointList& output_data)
{
    Pose3DList _3d_poses;
    ImgPoseList img_poses;

    _3d_poses.resize(input_data.size());
    for (int i=0;i<input_data.size();i++) {
        _3d_poses[i].x = input_data[i].x;
        _3d_poses[i].y = input_data[i].y;
        _3d_poses[i].z = input_data[i].z;
        _3d_poses[i].roll = 0.0;
        _3d_poses[i].pitch = 0.0;
        _3d_poses[i].yaw = 0.0;
    }
    if (TransformPoseList_(_3d_poses, img_poses) == false) {
        return false;
    }
    output_data.resize(img_poses.size());
    for (int i=0;i<img_poses.size();i++) {
        output_data[i].u = img_poses[i].u;
        output_data[i].v = img_poses[i].v;
    }
    return true;
}
bool NpuServer::TransformPointList_(const ImgPointList& input_data, Point3DList& output_data)
{
    ImgPoseList img_poses;
    Pose3DList _3d_poses;

    img_poses.resize(input_data.size());
    for (int i = 0; i < input_data.size(); i++)
    {
        img_poses[i].u = input_data[i].u;
        img_poses[i].v = input_data[i].v;
        img_poses[i].theta = 0;
    }
    if (TransformPoseList_(img_poses, _3d_poses) == false)
    {
        return false;
    }
    output_data.resize(_3d_poses.size());
    for (int i = 0; i < _3d_poses.size(); i++)
    {
        output_data[i].x = _3d_poses[i].x;
        output_data[i].y = _3d_poses[i].y;
        output_data[i].z = _3d_poses[i].z;
    }
    return true;
}

bool NpuServer::TransformPose_(const GeoPose &input_data, Pose3D &output_data)
{
    NSR_DEBUG("TransformPose_: geo_pose to pose_3d : input_data (%f, %f, %f)",
              input_data.latitude,
              input_data.longitude,
              input_data.altitude);

    //trans path from wps-84 to map frame
    geographic_msgs::GeoPoseStamped gps_goal;
    gps_goal.header.stamp = ros::Time::now();
    gps_goal.header.frame_id = "utm_frame";
    gps_goal.pose.position.latitude = input_data.latitude;
    gps_goal.pose.position.longitude = input_data.longitude;
    gps_goal.pose.position.altitude = input_data.altitude;
    gps_goal.pose.orientation = tf::createQuaternionMsgFromRollPitchYaw(
                input_data.roll, input_data.pitch, input_data.yaw);
    ros::NodeHandle nh;
    wr_npu_msgs::TransGpsGoal srv;
    srv.request.gps_goal = gps_goal;
    ros::ServiceClient client =
            nh.serviceClient<wr_npu_msgs::TransGpsGoal>("/trans_gps_goal");
    if (client.isValid() != true)
    {
        LOG_ERROR << "Trans GPS goal service is not valid!";
        return false;
    }
    client.call(srv);
    output_data.x = srv.response.map_goal.pose.position.x;
    output_data.y = srv.response.map_goal.pose.position.y;
    output_data.z = srv.response.map_goal.pose.position.z;
    output_data.roll = 0.0;
    output_data.pitch = 0.0;
    output_data.yaw = tf::getYaw(srv.response.map_goal.pose.orientation);

    NSR_DEBUG("TransformPose_: geo_pose to pose_3d : output_data "
              "(x: %f, y: %f, z :%f) , (roll: %f, pitch: %f, yaw:%f)",
              output_data.x, output_data.y, output_data.z,
              output_data.roll, output_data.pitch, output_data.yaw);
    return true;
}
bool NpuServer::TransformPose_(const Pose3D &input_data, GeoPose &output_data)
{
    NSR_DEBUG("TransformPoseList_: pose_3d to geo_pose.");
    geometry_msgs::PoseStamped map_pose;
    map_pose.header.stamp = ros::Time::now();
    map_pose.header.frame_id = "map_frame";
    map_pose.pose.position.x = input_data.x;
    map_pose.pose.position.y = input_data.y;
    map_pose.pose.position.z = input_data.z;
    map_pose.pose.orientation = tf::createQuaternionMsgFromRollPitchYaw(
                input_data.roll,
                input_data.pitch,
                input_data.yaw);

    //trans path from wps-84 to map frame
    ros::NodeHandle nh;
    wr_npu_msgs::ReTransGpsGoal srv;
    srv.request.map_pose = map_pose;
    ros::ServiceClient client =
            nh.serviceClient<wr_npu_msgs::ReTransGpsGoal>("/retrans_gps_goal");
    if (client.isValid() != true)
    {
        LOG_ERROR << "Trans map pose service is not valid!";
        return false;
    }
    client.call(srv);

    output_data.latitude = srv.response.gps_pose.pose.position.latitude;
    output_data.longitude = srv.response.gps_pose.pose.position.longitude;
    output_data.altitude = srv.response.gps_pose.pose.position.altitude;

    return true;
}
bool NpuServer::TransformPoseList_(const Pose3DList& input_data, GeoPoseList& output_data)
{
    NSR_DEBUG("TransformPoseList_: pose_3d to geo_pose.");
    nav_msgs::Path map_path;
    map_path.header.stamp = ros::Time::now();
    map_path.header.frame_id = "map_frame";
    for (int i=0; i < input_data.size(); i++) {
        API_DEBUG("Pose-im: [ %.3f, %.3f, %.3f, %.3f, %.3f, %.3f][deg,deg,m,rad,rad,rad]",
                  input_data[i].x, input_data[i].y, input_data[i].z,
                  input_data[i].roll, input_data[i].pitch, input_data[i].yaw);
        map_path.poses[i].pose.position.x = input_data[i].x;
        map_path.poses[i].pose.position.y = input_data[i].y;
        map_path.poses[i].pose.position.z = input_data[i].z;
        map_path.poses[i].pose.orientation =
                tf::createQuaternionMsgFromRollPitchYaw(
                    input_data[i].roll,
                    input_data[i].pitch,
                    input_data[i].yaw);
    }

    //trans path from wps-84 to map frame
    ros::NodeHandle nh;
    wr_npu_msgs::ReTransGpsPath srv;
    srv.request.map_path = map_path;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::ReTransGpsPath>("/retrans_gps_path");
    if (client.isValid() != true)
    {
        LOG_ERROR << "Trans map path service is not valid!";
        return false;
    }
    client.call(srv);

    for (int i = 0; i < srv.response.gps_path.poses.size(); i ++) {
         output_data[i].latitude =
                 srv.response.gps_path.poses[i].pose.position.latitude;
         output_data[i].longitude =
                 srv.response.gps_path.poses[i].pose.position.longitude;
         output_data[i].altitude =
                 srv.response.gps_path.poses[i].pose.position.altitude;
         output_data[i].roll = 0.0;
         output_data[i].pitch = 0.0;
         output_data[i].yaw =
                 tf::getYaw(srv.response.gps_path.poses[i].pose.orientation);
    }

    return true;
}
bool NpuServer::TransformPoseList_(const GeoPoseList& input_data, Pose3DList& output_data)
{
    NSR_DEBUG("TransformPoseList_: geo_pose to pose_3d.");
    geographic_msgs::GeoPath gps_path;
    gps_path.header.stamp = ros::Time::now();
    gps_path.header.frame_id = "utm_frame";
    gps_path.poses.resize(input_data.size());
    for (int i=0; i<input_data.size(); i++) {
        API_DEBUG("Geo path : %02d[ %.3f, %.3f, %.3f, %.3f, %.3f, %.3f][deg,deg,m,rad,rad,rad]",
                  i, input_data[i].latitude, input_data[i].longitude,
                  input_data[i].altitude, input_data[i].roll,
                  input_data[i].pitch, input_data[i].yaw);
        gps_path.poses[i].header.stamp = ros::Time::now();
        gps_path.poses[i].header.frame_id = "utm_frame";
        gps_path.poses[i].pose.position.latitude = input_data[i].latitude;
        gps_path.poses[i].pose.position.longitude = input_data[i].longitude;
        gps_path.poses[i].pose.position.altitude = input_data[i].altitude;
        gps_path.poses[i].pose.orientation =
                tf::createQuaternionMsgFromRollPitchYaw(
                    input_data[i].roll,
                    input_data[i].pitch,
                    input_data[i].yaw);

    }
    //trans path from wps-84 to map frame
    ros::NodeHandle nh;
    wr_npu_msgs::TransGpsPath srv;
    srv.request.gps_path = gps_path;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::TransGpsPath>("/trans_gps_path");
    if (client.isValid() != true)
    {
        return false;
    }
    client.call(srv);

    output_data.resize(srv.response.map_path.poses.size());
    for (int i=0; i<srv.response.map_path.poses.size(); i++) {
        output_data[i].x = srv.response.map_path.poses[i].pose.position.x;
        output_data[i].y = srv.response.map_path.poses[i].pose.position.y;
        output_data[i].z = srv.response.map_path.poses[i].pose.position.z;
        output_data[i].roll = 0.0;
        output_data[i].pitch = 0.0;
        output_data[i].yaw = tf::getYaw(srv.response.map_path.poses[i].pose.orientation);
    }
    return true;
}
bool NpuServer::TransformPointList_(const Point3DList& input_data, GeoPointList& output_data)
{
    ROS_ERROR("TODO: NpuServer::TransformPointList_(const Point3DList& input_data, GeoPointList& output_data)");
    return false;
}
bool NpuServer::TransformPointList_(const GeoPointList& input_data, Point3DList& output_data)
{
    NSR_DEBUG("TransformPoseList_: geo_pint to point_3d.");
    geographic_msgs::GeoPath gps_path;
    gps_path.header.stamp = ros::Time::now();
    gps_path.header.frame_id = "utm_frame";
    gps_path.poses.resize(input_data.size());
    for (int i=0; i<input_data.size(); i++) {
        API_DEBUG("Geo path : %02d[ %.3f, %.3f, %.3f][deg,deg,m]",
                  i, input_data[i].latitude, input_data[i].longitude,
                  input_data[i].altitude);
        gps_path.poses[i].header.stamp = ros::Time::now();
        gps_path.poses[i].header.frame_id = "utm_frame";
        gps_path.poses[i].pose.position.latitude = input_data[i].latitude;
        gps_path.poses[i].pose.position.longitude = input_data[i].longitude;
        gps_path.poses[i].pose.position.altitude = input_data[i].altitude;
        gps_path.poses[i].pose.orientation.x = 0.0;
        gps_path.poses[i].pose.orientation.y = 0.0;
        gps_path.poses[i].pose.orientation.z = 0.0;
        gps_path.poses[i].pose.orientation.w = 1.0;

    }
    //trans path from wps-84 to map frame
    ros::NodeHandle nh;
    wr_npu_msgs::TransGpsPath srv;
    srv.request.gps_path = gps_path;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::TransGpsPath>("/trans_gps_path");
    if (client.isValid() != true)
    {
        return false;
    }
    client.call(srv);

    output_data.resize(srv.response.map_path.poses.size());
    for (int i=0; i<srv.response.map_path.poses.size(); i++) {
        output_data[i].x = srv.response.map_path.poses[i].pose.position.x;
        output_data[i].y = srv.response.map_path.poses[i].pose.position.y;
        output_data[i].z = srv.response.map_path.poses[i].pose.position.z;
    }
    return true;
}

/// callback
void NpuServer::ImuDataCallBack_(const geometry_msgs::Vector3Stamped::ConstPtr& msg)
{
    CBK_DEBUG("ImuDataCallBack_()");
    imu_data_.roll_deg = msg->vector.x;
    imu_data_.pitch_deg = msg->vector.y;
    imu_data_.yaw_deg = msg->vector.z;
    return;
}

//sensorstatus
void NpuServer::SensorStatusCallBack_(const wr_npu_msgs::VsensorStatus::ConstPtr& msg)
{
    CBK_DEBUG("SensorStatusCallBack_()");
    SensorState sensor_state;
    if (pthread_spin_trylock(&sensor_status_spinlock_) != 0)
    {
        CBK_DEBUG("SensorStatusCallBack_() Get thread lock failed");
        return;
    }
    sensor_status_.clear();
    for(int i = 0;i<msg->vsensor_status.size();i++)
    {
        sensor_state.sensor_id = msg->vsensor_status[i].frame_id;
        if(msg->vsensor_status[i].hardware_status == 0)
        {
            sensor_state.hardware_status = DISCONNECT;
        }
        else if(msg->vsensor_status[i].hardware_status == 1)
        {
            sensor_state.hardware_status = CONNECT;
        }
        else
        {
            sensor_state.hardware_status = IGNORE;
        }

        if(msg->vsensor_status[i].topic_status == 0)
        {
            sensor_state.topic_status = DATA_ERROR;
        }
        else if(msg->vsensor_status[i].topic_status == 1)
        {
            sensor_state.topic_status = DATA_RECEIVED;
        }
        else
        {
            sensor_state.topic_status = IGNORE;
        }

        if(msg->vsensor_status[i].node_status == 0)
        {
            sensor_state.node_status = PROGRAM_ERROR;
        }
        else if(msg->vsensor_status[i].node_status == 1)
        {
            sensor_state.node_status = PROGRAM_NORMAL;
        }
        else
        {
            sensor_state.node_status = IGNORE;
        }
        sensor_status_.push_back(sensor_state);
        // cout<<sensor_status_[i].sensor_id<<"++++++"<<sensor_status_[i].hardware_status<<endl;
    }

    pthread_spin_unlock(&sensor_status_spinlock_);

    //    cout<<npu_state_<<endl;
    //    cout<<"sensorstatuscb()   "<<safe_thread_num_<<endl;
    if(npu_state_ == IDLE_STATE)
    {
        ROS_INFO("stop check");
        if(safe_thread_num_ == 1)
        {
            p_check_sensor_status_->checkSensorStatusPtr_->ice_response(sensor_status_);
            safe_thread_num_ = 0;
            string host_platform;
            ros::NodeHandle nh;
            nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
            stringstream ss;
            ss << "bash /npu."<< host_platform << "/script/check_stop.sh";
            system(ss.str().c_str());
        }
        ROS_INFO("stop ok");
    }

}

void NpuServer::RuntimeStatusCallBack_(const wr_npu_msgs::RuntimeStatus::ConstPtr& msg)
{
    CBK_DEBUG("RuntimeStatusCallBack_()");
    runtime_status_msgs_.runtime_status_0 = msg->runtime_status_0;
    runtime_status_msgs_.runtime_status_1 = msg->runtime_status_1;
    runtime_status_msgs_.runtime_status_2 = msg->runtime_status_2;
}

void NpuServer::CsgStationsCallBack_(const wr_npu_msgs::StationArray::ConstPtr& msg)
{
    csg_station_list_ = (*msg);
}

void NpuServer::CsgCurrentStationCallBack_(const wr_npu_msgs::Station::ConstPtr& msg)
{
    csg_current_station_ = (*msg);
}

void NpuServer::CheckSensorStatus_async(const ::wizrobo_npu::AMD_NpuIce_CheckSensorStatusPtr& ptr,CheckMode mode)
{

    ROS_INFO("CheckSensorStatus_async()");
    p_check_sensor_status_->checkSensorStatusPtr_ = ptr;
    p_check_sensor_status_->mode_ = mode;
    ROS_INFO("launch_sensor_status_thread_()");
    launch_sensor_status_thread_(mode);
}

SensorStatus NpuServer::GetSensorStatus()
{
    APIG_DEBUG("GetSensorStatus()");
    SensorStatus sensor_state;
    sensor_state.resize(0);
    if (pthread_spin_trylock(&sensor_status_spinlock_) != 0)
    {
        ROS_ERROR("GetSensorStatus() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return sensor_state;
    }
    if(sensor_status_.size() != 0)
    {
        for(int i = 0;i<sensor_status_.size();i++)
        {
            sensor_state.push_back(sensor_status_[i]);
        }
    }
    pthread_spin_unlock(&sensor_status_spinlock_);
    return sensor_state;
}

RuntimeStatusList NpuServer::GetRuntimeStatus()
{

    APIG_DEBUG("GetRuntimeStatus()");
    RuntimeStatusList runtime_status;
    runtime_status.resize(3);
    runtime_status[0] = runtime_status_msgs_.runtime_status_0;
    runtime_status[1] = runtime_status_msgs_.runtime_status_1;
    runtime_status[2] = runtime_status_msgs_.runtime_status_2;
    return runtime_status;
}

//gpsdata
GpsData1 NpuServer::GetGpsData()
{
    GpsData1 data;
    data.altitude_m = gps_raw_.altitude;
    data.latitude_deg = gps_raw_.latitude;
    data.longitude_deg = gps_raw_.longitude;
    data.direction_deg = gps_direction_;

    return data;
}
/// callback
//gps
void NpuServer::GpsRawCallBack_(const sensor_msgs::NavSatFixConstPtr &msg)
{
    gps_raw_.altitude = msg->altitude;
    gps_raw_.latitude = msg->latitude;
    gps_raw_.longitude = msg->longitude;

    gps_raw_ = *msg;
    return;
}

void NpuServer::GpsDirectionCallBack_(const geometry_msgs::Vector3StampedConstPtr &msg)
{
    gps_direction_ = msg->vector.z;
    return;
}

//matching_score
void NpuServer::MatchingScoreCallBack_(const std_msgs::Float32::ConstPtr &msg)
{
    CBK_DEBUG("MatchingScoreCallback_()");
    matching_score_data = msg->data;
    return;
}

float NpuServer::GetMatchingScore()
{
    return matching_score_data;
}

// motor
void NpuServer::MotorEncCallback_(const wr_npu_msgs::MotorEnc::ConstPtr& msg)
{
    CBK_DEBUG("MotorEncCallback_()");
    if (pthread_spin_trylock(&motor_enc_spinlock_) != 0)
    {
        CBK_DEBUG("MotorEncCallback_() Get thread lock failed");
        return;
    }
    current_enc_.motor_num = msg->motor_num;
    current_enc_.ticks.resize(msg->motor_num);
    for (int i = 0; i < msg->motor_num; i++)
    {
        current_enc_.ticks[i] = msg->ticks[i];
    }
    pthread_spin_unlock(&motor_enc_spinlock_);
    CBK_DEBUG("current_motor_enc = (%d, %d)[ticks]", current_enc_.ticks[0], current_enc_.ticks[1]);
    CBK_DEBUG("MotorEncCallback_() Done");
    return;
}
void NpuServer::ActMotorSpdCallback_(const wr_npu_msgs::MotorSpd::ConstPtr& msg)
{
    CBK_DEBUG("ActMotorSpdCallback_()");
    if (pthread_spin_trylock(&act_motor_spd_spinlock_) != 0)
    {
        CBK_DEBUG("ActMotorSpdCallback_() Get thread lock failed");
        return;
    }
    act_motor_spd_.motor_num = msg->motor_num;
    act_motor_spd_.rpms.resize(msg->motor_num);
    for (int i = 0; i < msg->motor_num; i++)
    {
        act_motor_spd_.rpms[i] = msg->rpms[i];
    }
    pthread_spin_unlock(&act_motor_spd_spinlock_);
    CBK_DEBUG("cmd_motor_spd = (%.2f, %.2f)[rpm]", act_motor_spd_.rpms[0], act_motor_spd_.rpms[1]);
    CBK_DEBUG("ActMotorSpdCallback_() Done");
}
void NpuServer::CmdMotorSpdCallback_(const wr_npu_msgs::MotorSpd::ConstPtr& msg)
{
    CBK_DEBUG("CmdMotorSpdCallback_()");
    if (pthread_spin_trylock(&cmd_motor_spd_spinlock_) != 0)
    {
        CBK_DEBUG("CmdMotorSpdCallback_() Get thread lock failed");
        return;
    }
    cmd_motor_spd_.motor_num = msg->motor_num;
    cmd_motor_spd_.rpms.resize(msg->motor_num);
    for (int i = 0; i < msg->motor_num; i++)
    {
        cmd_motor_spd_.rpms[i] = msg->rpms[i];
    }
    cmd_motor_spd_.steer_angle_deg = msg->carlike_steer_angle_deg;
    pthread_spin_unlock(&cmd_motor_spd_spinlock_);
    CBK_DEBUG("cmd_motor_spd = (%.2f, %.2f)[rpm]", cmd_motor_spd_.rpms[0], cmd_motor_spd_.rpms[1]);
    CBK_DEBUG("CmdMotorSpdCallback_() Done");
}
// vel
void NpuServer::ActVelCallback_(const geometry_msgs::Twist::ConstPtr &msg)
{
    CBK_DEBUG("ActVelCallback_()");
    if (pthread_spin_trylock(&act_vel_spinlock_) != 0)
    {
        CBK_DEBUG("ActVelCallback_() Get thread lock failed");
        return;
    }
    act_vel_.v_x = msg->linear.x;
    act_vel_.v_y = msg->linear.y;
    act_vel_.v_z = msg->linear.z;
    act_vel_.v_roll = msg->angular.x;
    act_vel_.v_pitch = msg->angular.y;
    act_vel_.v_yaw = msg->angular.z;
    pthread_spin_unlock(&act_vel_spinlock_);
    CBK_DEBUG("ActVelCallback_() Done");
}
void NpuServer::CmdVelCallback_(const geometry_msgs::Twist::ConstPtr &msg)
{
    CBK_DEBUG("CmdvelCallback_()");
    if (pthread_spin_trylock(&cmd_vel_spinlock_) != 0)
    {
        CBK_DEBUG("CmdVelCallback_() Get thread lock failed");
        return;
    }
    cmd_vel_.v_x = msg->linear.x;
    cmd_vel_.v_y = msg->linear.y;
    cmd_vel_.v_z = msg->linear.z;
    cmd_vel_.v_roll = msg->angular.x;
    cmd_vel_.v_pitch = msg->angular.y;
    cmd_vel_.v_yaw = msg->angular.z;
    pthread_spin_unlock(&cmd_vel_spinlock_);
    CBK_DEBUG("CmdvelCallback_() Done");
}
// pose
void NpuServer::ActPoseCallback_(const geometry_msgs::PoseStamped::ConstPtr& message)
{
    CBK_DEBUG("ActPoseCallBack_()");
    act_pose_msg_ = *message;
    geometry_msgs::PoseStamped currPose = *message;
    tf::Quaternion quat;
    quat.setX(currPose.pose.orientation.x);
    quat.setY(currPose.pose.orientation.y);
    quat.setZ(currPose.pose.orientation.z);
    quat.setW(currPose.pose.orientation.w);
    if (pthread_spin_trylock(&act_pose_spinlock_) != 0
            || pthread_spin_trylock(&direct_trans_footprint_vertices_spinlock_) != 0) {
        pthread_spin_unlock(&act_pose_spinlock_);
        pthread_spin_unlock(&direct_trans_footprint_vertices_spinlock_);
        CBK_DEBUG("ActPoseCallback_() Get thread lock failed");
        return;
    }
    act_pose_.x = currPose.pose.position.x;
    act_pose_.y = currPose.pose.position.y;
    //    curr_pose_.yaw = quat.getAngle();
    if (currPose.pose.orientation.z>0) {
        /*
        * for robot pose direction presetation on picturebox in upper computer
        */
        act_pose_.yaw = quat.getAngle(); // the default angle is positive
    } else {
        act_pose_.yaw = -quat.getAngle();
    }
    pthread_spin_unlock(&act_pose_spinlock_);
    //    if(map_chop_param_.Rotate == 90)
    //        act_pose_.yaw = actPose.Yaw + 1.57;
    //CBK_DEBUG("ActPoseCallBack():: x:%f, y:%f,yaw:%f",curr_pose_.x,curr_pose_.y,curr_pose_.yaw);

    // transofrm trans_footprint
    direct_trans_footprint_vertices_.resize(footprint_msg_.polygon.points.size());
    for (int i = 0; i < direct_trans_footprint_vertices_.size(); i++) {
        float x_w = cos(act_pose_.yaw) * footprint_msg_.polygon.points[i].x
                - sin(act_pose_.yaw) * footprint_msg_.polygon.points[i].y
                + act_pose_.x;
        float y_w = sin(act_pose_.yaw) * footprint_msg_.polygon.points[i].x
                + cos(act_pose_.yaw) * footprint_msg_.polygon.points[i].y
                + act_pose_.y;
        float z_w = act_pose_.z + footprint_msg_.polygon.points[i].z;
        direct_trans_footprint_vertices_[i].x = x_w;
        direct_trans_footprint_vertices_[i].y = y_w;
        direct_trans_footprint_vertices_[i].z = z_w;
    }
    pthread_spin_unlock(&direct_trans_footprint_vertices_spinlock_);
    CBK_DEBUG("ActPoseCallBack_() all lock release");
    ros::NodeHandle nh;
    nh.setParam(STD_RUNTIME_PARAM_NS + "/current_pose/x", act_pose_.x);
    nh.setParam(STD_RUNTIME_PARAM_NS + "/current_pose/y", act_pose_.y);
    nh.setParam(STD_RUNTIME_PARAM_NS + "/current_pose/yaw", act_pose_.yaw);
    UpdateActPath_(message->header.stamp);
    footprint_msg_.header.stamp = message->header.stamp;
    footprint_msg_.header.frame_id = STD_BASE_FTP_FRAME_ID;
    footprint_pub_.publish(footprint_msg_);
    CBK_DEBUG("ActPoseCallBack_() Done");
    SaveActPose();
}

// path
void NpuServer::GlobalPathCallback_(const nav_msgs::Path::ConstPtr& msg)
{
    CBK_DEBUG("GlobalPathCallback_()");
    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0) {
        CBK_DEBUG("GlobalPathCallback_() Get thread lock failed");
        return;
    }
    nav_msgs::Path path = *msg;
    global_path_.poses.clear();
    global_path_.poses.resize(path.poses.size());
    for(int i = 0; i < path.poses.size(); i++) {
        global_path_.poses[i].x = path.poses[i].pose.position.x;
        global_path_.poses[i].y = path.poses[i].pose.position.y;
    }
//    if (path.poses.size() > 10) {
//        for(int i = 0; i < path.poses.size() - 10; ++i) {
//            global_path_.poses[i].x = path.poses[i + 8].pose.position.x;
//            global_path_.poses[i].y = path.poses[i + 8].pose.position.y;
//            CBK_DEBUG("path_plan():: x:%f, y:%f", path.poses[i + 8].pose.position.x, path.poses[i + 8].pose.position.y);
//        }
//    }
    pthread_spin_unlock(&cmd_path_spinlock_);
    CBK_DEBUG("GlobalPathCallback_() Done");
}

void NpuServer::LocalPathCallback_(const geometry_msgs::PoseArray::ConstPtr& msg)
{
    CBK_DEBUG("LocalPathCallback_()");
    if (pthread_spin_trylock(&cmd_path_spinlock_) != 0)
    {
        CBK_DEBUG("LocalPathCallback_() Get thread lock failed");
        return;
    }
    geometry_msgs::PoseArray path = *msg;
    local_path_.poses.clear();
    local_path_.poses.resize(path.poses.size());
    if (path.poses.size() > 10) {
        for(int i = 0; i < path.poses.size() - 10; ++i)
        {
            local_path_.poses[i].x = path.poses[i + 8].position.x;
            local_path_.poses[i].y = path.poses[i + 8].position.y;
            CBK_DEBUG("path_plan():: x:%f, y:%f", path.poses[i + 8].position.x, path.poses[i + 8].position.y);
        }
    }
    pthread_spin_unlock(&cmd_path_spinlock_);
    CBK_DEBUG("LocalPathCallback_() Done");
}

// task
void NpuServer::TaskStatusCallback_(const actionlib_msgs::GoalStatusArray::ConstPtr& status)
{
    CBK_DEBUG("TaskStatusCallback_()");
    if (pthread_spin_trylock(&task_status_spinlock_) != 0)
    {
        CBK_DEBUG("TaskStatusCallback_() Get thread lock failed");
        return;
    }
    if(!status->status_list.empty()) {//in case status_list is empty which will return error.
        actionlib_msgs::GoalStatus robotstatus = status->status_list[0];
        switch(robotstatus.status) {//change actionlib_msgs to int number for ice transfer.
        case  actionlib_msgs::GoalStatus::PENDING:
            navi_state_ = PENDING;
            break;
        case actionlib_msgs::GoalStatus::ACTIVE:
            navi_state_ = ACTIVE;
            break;
        case actionlib_msgs::GoalStatus::PREEMPTED:
            navi_state_ = PREEMPTED;
            break;
        case actionlib_msgs::GoalStatus::SUCCEEDED:
            navi_state_ = SUCCEEDED;
            break;
        case actionlib_msgs::GoalStatus::ABORTED:
            navi_state_ = ABORTED;
            break;
        case actionlib_msgs::GoalStatus::REJECTED:
            navi_state_ = REJECTED;
            break;
        case actionlib_msgs::GoalStatus::PREEMPTING:
            navi_state_ = PREEMPTED;
            break;
        case actionlib_msgs::GoalStatus::RECALLING:
            navi_state_ = RECALLING;
            break;
        case actionlib_msgs::GoalStatus::RECALLED:
            navi_state_ = RECALLED;
            break;
        case actionlib_msgs::GoalStatus::LOST:
            navi_state_ = LOST;
            break;
        default:
            break;
        }
    } else {
        navi_state_ = IDLE;
        //navi_state = 10; // default state while waiting.
    }
    pthread_spin_unlock(&task_status_spinlock_);
    CBK_DEBUG("TaskStatusCallback_() Done");
}

void NpuServer::TaskStatusPfCallback_(const actionlib_msgs::GoalStatusArray::ConstPtr& status)
{
    //static int num = 1;
    //ROS_WARN("pf state cb (%d),state is : %d",num++,status->status_list[0].status);
    CBK_DEBUG("TaskStatusPfCallback_()");
    if (pthread_spin_trylock(&task_status_pf_spinlock_) != 0)
    {
        CBK_DEBUG("TaskStatusPfCallback_() Get thread lock failed");
        return;
    }
    if(!status->status_list.empty()) {//in case status_list is empty which will return error.
        actionlib_msgs::GoalStatus robotstatus = status->status_list[0];
        //ROS_WARN("pf callback robot.status:%d",robotstatus.status);
        switch(robotstatus.status) {//change actionlib_msgs to int number for ice transfer.
        case  actionlib_msgs::GoalStatus::PENDING:
            navi_state_pf_ = PENDING;
            break;
        case actionlib_msgs::GoalStatus::ACTIVE:
            navi_state_pf_ = ACTIVE;
            break;
        case actionlib_msgs::GoalStatus::PREEMPTED:
            navi_state_pf_ = PREEMPTED;
            break;
        case actionlib_msgs::GoalStatus::SUCCEEDED:
            navi_state_pf_ = SUCCEEDED;
            break;
        case actionlib_msgs::GoalStatus::ABORTED:
            navi_state_pf_ = ABORTED;
            break;
        case actionlib_msgs::GoalStatus::REJECTED:
            navi_state_pf_ = REJECTED;
            break;
        case actionlib_msgs::GoalStatus::PREEMPTING:
            navi_state_pf_ = PREEMPTED;
            break;
        case actionlib_msgs::GoalStatus::RECALLING:
            navi_state_pf_ = RECALLING;
            break;
        case actionlib_msgs::GoalStatus::RECALLED:
            navi_state_pf_ = RECALLED;
            break;
        case actionlib_msgs::GoalStatus::LOST:
            navi_state_pf_ = LOST;
            break;
        default:
            break;
        }
    } else {
        navi_state_pf_ = IDLE;
        //navi_state = 10; // default state while waiting.
    }
    //ROS_WARN("pf callback state:%d",(int)navi_state_pf_);
    pthread_spin_unlock(&task_status_pf_spinlock_);
    CBK_DEBUG("TaskStatusPfCallback_() Done");
}
// lidar
void NpuServer::TfTransLidarScanCallback_(const sensor_msgs::PointCloud::ConstPtr& message)
{
    CBK_DEBUG("TfTransLidarScanCallBack_()");
    if (pthread_spin_trylock(&tf_lidar_scan_spinlock_) != 0)
    {
        ROS_ERROR("TfTransLidarScanCallBack_() Get thread lock failed");
        return;
    }
    tf_trans_lidar_scan_pc_.points.clear();
    tf_trans_lidar_scan_pc_.points.resize(message->points.size());
    for(int i = 0; i < message->points.size(); i++)
    {
        tf_trans_lidar_scan_pc_.points[i].x = message->points[i].x;
        tf_trans_lidar_scan_pc_.points[i].y = message->points[i].y;
        //CBK_DEBUG("LidarScanCloud():: x:%f, y:%f", tf_trans_lidar_scan_pc_.points[i].x,
        //            tf_trans_lidar_scan_pc_.points[i].y);
    }
    pthread_spin_unlock(&tf_lidar_scan_spinlock_);
    CBK_DEBUG("TfTransLidarScanCallBack_() Done");
}
//bumper
void NpuServer::BumperDataCallBack_(const wr_npu_msgs::BumperData::ConstPtr &msg)
{
    CBK_DEBUG("BumperDataCallBack_()");
    if (pthread_spin_trylock(&bumper_data_spinlock_) !=0)
    {
        CBK_DEBUG("BumperDataCallBack_() Get thread lock failed");
        return;
    }
    bumper_data_msgs_ = *msg;
    wr_npu_msgs::ExceptionInformation sensor_;
    float spd_left = act_motor_spd_.rpms[0];
    float spd_right = act_motor_spd_.rpms[1];
    float spd_diff = abs(abs(spd_left) - abs(spd_right));
    if(spd_left>0 && spd_right>0 && spd_diff < abs(spd_left)*0.1 && spd_diff < abs(spd_right)*0.1)
    {
        sensor_.state_index = 5;
        exce_info_pub_.publish(sensor_);
    }
    else if(spd_left<0 && spd_right<0 && spd_diff < abs(spd_left)*0.1 && spd_diff < abs(spd_right)*0.1)
    {
        sensor_.state_index = 6;
        exce_info_pub_.publish(sensor_);
    }
    else if(spd_diff > abs(spd_left)*0.1)
    {
        sensor_.state_index = 7;
        exce_info_pub_.publish(sensor_);
    }
    else if(spd_diff > abs(spd_right)*0.1)
    {
        sensor_.state_index = 8;
        exce_info_pub_.publish(sensor_);
    }
    pthread_spin_unlock(&bumper_data_spinlock_);
}
//infrd
void NpuServer::InfrdDataCallBack_(const wr_npu_msgs::InfrdData::ConstPtr &msg)
{
    CBK_DEBUG("InfrdDataCallBaack_()");
    if(pthread_spin_trylock(&infrd_data_spinlock_) !=0)
    {
        CBK_DEBUG("InfrdDataCallBack_() Get thread lock failed");
        return;
    }
    infrd_data_msgs_ = *msg;
    pthread_spin_unlock(&infrd_data_spinlock_);
}
//emergencystop
void NpuServer::EmergencyStopDataCallBack_(const wr_npu_msgs::EmergencyStopData::ConstPtr &msg)
{
    CBK_DEBUG("EmergencyStopDataCallBack_()");
    if(pthread_spin_trylock(&emergencystop_data_spinlock_) !=0)
    {
        CBK_DEBUG("EmergencyStopDataCallBack_() Get thread lock failed");
        return;
    }
    emergencystop_data_msgs_ = *msg;
    pthread_spin_unlock(&emergencystop_data_spinlock_);
}

void NpuServer::LidarScanCallBack_(const sensor_msgs::LaserScan::ConstPtr& msg)
{
    CBK_DEBUG("LidarScanCallBack_()");
    if (pthread_spin_trylock(&lidar_scan_spinlock_) != 0)
    {
        CBK_DEBUG("LidarScanCallBack_() Get thread lock failed");
        return;
    }
    lidar_scan_msg_ = *msg;
    pthread_spin_unlock(&lidar_scan_spinlock_);
    /*
    if (enb_direct_sensor_trans_)
    {
        pthread_spin_lock(&lidar_scan_spinlock_);
        lidar_scan_msg_ = *msg;
        pthread_spin_unlock(&lidar_scan_spinlock_);
    }
    */
    if (lidar_range_max_ != lidar_scan_msg_.range_max)
    {
        lidar_range_max_ = lidar_scan_msg_.range_max;
        UpdateFootprint_(base_param_.footprint_param);
        ROS_WARN("LidarScanCallBack_(): lidar_range_max_ = %.2f", lidar_range_max_);
        ros::NodeHandle nh;
        nh.setParam(STD_RUNTIME_PARAM_NS + "/lidar/range_max", lidar_scan_msg_.range_max);
        nh.setParam(STD_RUNTIME_PARAM_NS + "/lidar/range_min", lidar_scan_msg_.range_min);
        nh.setParam(STD_RUNTIME_PARAM_NS + "/lidar/scan_time", lidar_scan_msg_.scan_time);
        nh.setParam(STD_RUNTIME_PARAM_NS + "/lidar/angle_max", lidar_scan_msg_.angle_max);
        nh.setParam(STD_RUNTIME_PARAM_NS + "/lidar/angle_min", lidar_scan_msg_.angle_min);
        nh.setParam(STD_RUNTIME_PARAM_NS + "/lidar/angle_increment", lidar_scan_msg_.angle_increment);
        nh.setParam(STD_RUNTIME_PARAM_NS + "/lidar/beam_num", static_cast<int>(lidar_scan_msg_.intensities.size()));
    }
    CBK_DEBUG("LidarScanCallBack_() Done");
}
//GPS
void NpuServer::GpsFixCallback_(const sensor_msgs::NavSatFixConstPtr& msg)
{
    CBK_DEBUG("GpsFixCallback_()");
//    LOG_INFO << "GPS data is : ( latitude : "
//             << msg->latitude << ", longitude : "
//             << msg->longitude << " )";
    if(pthread_spin_trylock(&gps_data_spinlock_) !=0)
    {
        CBK_DEBUG("GpsFixCallback_() Get thread lock failed");
        return;
    }
    gps_data_msg_ = *msg;
    pthread_spin_unlock(&gps_data_spinlock_);
}

/// multi-thread
void NpuServer::SafeStopThread_()
{
    NSR_DEBUG("SafeStopThread_()");
    int loop_frq_hz = 10;
    int timeout_sec = 2;
    int cnt = 0;
    ros::Rate loop_rater(loop_frq_hz);
    while(ros::ok())
    {
        if (cnt % 100 == 0) {
            NSR_DEBUG("npu_server_loop: %06d", cnt/100);
        }
        cnt += 1;
        if (manual_ctrl_cnt_ > 0)// got manual cmd
        {
            auto_tick_cnt_++;
            NSR_DEBUG("SafeStopThread_(): manual_ctrl_cnt = %d, auto_tick_cnt = %d", manual_ctrl_cnt_, auto_tick_cnt_);
            if (abs(auto_tick_cnt_ - manual_ctrl_cnt_) >= timeout_sec * loop_frq_hz)
            {
                ROS_WARN("SafeStopThread_(): Lost manual vel cmd for more than %.1f seconds, safely stop!", 1.0 * (auto_tick_cnt_ - manual_ctrl_cnt_) / loop_frq_hz);
                manual_cmd_lin_vel_mps_ = manual_cmd_ang_vel_rps_ = 0;
                manual_ctrl_cnt_ = auto_tick_cnt_ = 0;
            }
            PublishManualVel_(manual_cmd_lin_vel_mps_, manual_cmd_ang_vel_rps_);
        }
        ros::spinOnce();
        loop_rater.sleep();
    }
}

void NpuServer::UpdateSpdLimits_(const MotorParam &motor_param, ChassisParam &chassis_param)
{
    ROS_DEBUG("NpuServer::UpdateSpdLimits");
    wizrobo::chassis_model::DiffdrvChassisModel::UpdateSpdLimits(motor_param, chassis_param);
    ROS_WARN("chassis_param.autoset_max_lin_spd_mps = %.2f, chassis_param.autoset_max_ang_spd_rps = %.2f"
             , chassis_param.autoset_max_lin_spd_mps, chassis_param.autoset_max_ang_spd_rps);
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/autoset_max_lin_spd_mps", chassis_param.autoset_max_lin_spd_mps);
    _SetDyFloatParam(STD_CHASSIS_PARAM_NS + "/autoset_max_ang_spd_rps", chassis_param.autoset_max_ang_spd_rps);
}

/// vel
void NpuServer::PublishManualVel_(float lin_vel_mps, float ang_vel_rps)
{
    NSR_DEBUG("PublishManulVel_()");
    manual_cmd_lin_vel_mps_ = lin_vel_mps;
    manual_cmd_ang_vel_rps_ = ang_vel_rps;
    geometry_msgs::Twist vel;
    vel.linear.x = lin_vel_mps;
    vel.angular.z = ang_vel_rps;
    manual_cmd_pub_.publish(vel);
}

/// path
void NpuServer::PublishWaypoints_(Path path)
{
    NSR_DEBUG("PublishWaypoints_()");
    geometry_msgs::PoseArray msg;
    msg.header.frame_id = STD_MAP_FRAME_ID;
    msg.header.stamp = ros::Time::now();

    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::TrioooFeedPolyline>("/feed_polyline_triooo");
    wr_npu_msgs::TrioooFeedPolyline srv;

    int cnt = path.poses.size();
    if (cnt <= 0) {
        ROS_WARN("There is no pose in path.");
        return;
    }
    msg.poses.resize(cnt);
    srv.request.polyline.poses.resize(cnt);
    for(int i=0;i<cnt;i++) {
        msg.poses[i].position.x = path.poses[i].x;
        msg.poses[i].position.y = path.poses[i].y;
        msg.poses[i].position.z = path.poses[i].z;
        srv.request.polyline.poses[i].position.x = path.poses[i].x;
        srv.request.polyline.poses[i].position.y = path.poses[i].y;
        srv.request.polyline.poses[i].position.z = path.poses[i].z;

        tf::Quaternion quat;
        quat.setRPY(path.poses[i].roll, path.poses[i].pitch, path.poses[i].yaw);
        tf::quaternionTFToMsg(quat, msg.poses[i].orientation);
    }
    waypoints_pub_.publish(msg);
    if (client.isValid() == true) {
        client.call(srv);
        geometry_msgs::PoseStamped _pose;
        _pose.header.frame_id = STD_MAP_FRAME_ID;
        _pose.header.stamp = ros::Time::now();
        cmd_pose_pub_.publish(_pose);
    }
}
void NpuServer::ClearActPath_()
{
    API_DEBUG("ClearActPath_()");
    act_path_.info.id = "ACT_PATH";
    act_path_.info.map_id = "MAP";
    act_path_.info.length = 0;
    act_path_.info.pose_num = 0;
    act_path_.poses.clear();

    act_path_msg_.header.stamp = ros::Time::now();
    act_path_msg_.header.frame_id = STD_MAP_FRAME_ID;
    act_path_msg_.poses.clear();
}
void NpuServer::UpdateActPath_(const ros::Time& time)
{
    NSR_DEBUG("UpdateActPath_()");
    if (act_path_.poses.size() == 0)
    {
        act_path_.poses.push_back(act_pose_);
    }
    else
    {
        float x_diff = fabs(act_path_.poses.back().x - act_pose_.x);
        float y_diff = fabs(act_path_.poses.back().y - act_pose_.y);
        float dist = sqrt(SQU(x_diff) + SQU(y_diff));
        float yaw_diff = fabs(act_path_.poses.back().yaw - act_pose_.yaw);
        if (x_diff >= navi_param_.x_err_tolr_m
                || y_diff >= navi_param_.y_err_tolr_m
                || yaw_diff >= navi_param_.yaw_err_tolr_rad)
        {
            act_path_.info.length += dist;
            act_path_.info.pose_num++;
            act_path_.poses.push_back(act_pose_);

            act_path_msg_.poses.push_back(act_pose_msg_);
        }
    }
    act_path_msg_.header.stamp = time;
    act_path_pub_.publish(act_path_msg_);
}

/// map
bool NpuServer::SaveMap_(string map_id)
{
    MapInfoList map_info_list;
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SaveMap_() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return false;
    }

    _GetStructOfMapServer_("GetMapInfos",this, map_info_list);

    pthread_spin_unlock(&map_server_spinlock_);
    IllegalCharacterDetection(map_id,map_info_list);
    API_DEBUG("SaveMap(\"%s\")", map_id.c_str());
    /*
    if (map_id == "")
    {
        ROS_WARN("Invalid map id.");
        return false;
    }
    */
    ros::NodeHandle nh;
    SrvMapServe srv;
    srv.request.function = "SaveMap";

    int length = map_id.size() + sizeof(int);
    srv.request.arguments.resize(length);

    SrvArgument::iterator ap = srv.request.arguments.begin();
    p_map_handler_->PackVarLength<string>(map_id, ap);

    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::WrMapServer>("/map_server_node/map_server");
    if (client.isValid() != true) {
        return false;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("SaveMap_() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return false;
    }
    bool rtn = client.call(srv);
    pthread_spin_unlock(&map_server_spinlock_);
    if ( rtn != true ) {
        API_DEBUG("SaveMap Fail!");
        return false;
    }
    API_DEBUG("Success.");
    return true;
}

void NpuServer::SaveMapImg(string map_id)
{
    int idx = map_id.find("flc_opt");
    API_DEBUG("NpuServer::StopSlam(): idx = %d", idx);
    if (idx >= 0)
    {
        ForceLoopClosure_();
        return;
    }
    API_DEBUG("NpuServer::StopSlam(\"%s\")", map_id.c_str());
    SaveMap_(map_id);
    //SetMapServerMode_("NAVI_MODE");
    ROS_INFO("SaveMapImg() is finish.");
}

geometry_msgs::Polygon NpuServer::Footprint2Polygon(const FootprintParam &param)
{
    geometry_msgs::Polygon polygon;
    if (param.shape_type == ROUND)
    {
        int round_vertex_num = 32;
        polygon.points.resize(round_vertex_num);
        for (int i = 0; i < round_vertex_num; i++)
        {
            float theta_rad = i * 2 * M_PI / round_vertex_num;
            geometry_msgs::Point32 pnt32;
            pnt32.x = param.round_radius_m * cos(theta_rad) + param.rot_center_offset_m;
            pnt32.y = param.round_radius_m * sin(theta_rad);
            pnt32.z = 0;
            polygon.points[i] = pnt32;
        }
    }
    else if (param.shape_type == RECTANGLE)
    {
        int rectangle_vertex_num = 4;
        polygon.points.resize(rectangle_vertex_num);
        for (int i = 0; i < rectangle_vertex_num; i++)
        {
            geometry_msgs::Point32 pnt32;
            if (i == 0 || i == 1)
            {
                pnt32.x = param.rectangle_length_m / 2  + param.rot_center_offset_m;
            }
            else
            {
                pnt32.x = -param.rectangle_length_m / 2  + param.rot_center_offset_m;
            }
            if (i == 0 || i == 3)
            {
                pnt32.y = param.rectangle_width_m / 2;
            }
            else
            {
                pnt32.y = -param.rectangle_width_m / 2;
            }
            pnt32.z = 0;
            polygon.points[i] = pnt32;
        }
    }
    else if (param.shape_type == POLYGON)
    {
        int polygon_vertex_num = param.polygon_vertices.size();
        if (polygon_vertex_num < 3)
        {
            ROS_ERROR("NpuServer::Footprint2Polygon(): polygon_vertex_num must be at least 3!!!");
            return polygon;
        }
        polygon.points.resize(polygon_vertex_num);
        for (int i = 0; i < polygon_vertex_num;i++)
        {
            geometry_msgs::Point32 pnt32;
            pnt32.x = param.polygon_vertices[i].x  + param.rot_center_offset_m;
            pnt32.y = param.polygon_vertices[i].y;
            pnt32.z = 0;
            polygon.points[i] = pnt32;
        }
    }
    polygon.points.push_back(polygon.points.front());
    return polygon;
}
bool NpuServer::SetMapServerMode_(string mode)
{
    NSR_DEBUG("SetMapServerMode: %s", mode.c_str());
    if (mode == "")
    {
        ROS_WARN("Invalid mode value.");
        return false;
    }
    ros::NodeHandle nh;
    SrvMapServe srv;
    srv.request.function = "SetServerMode";

    int length = mode.size() + sizeof(int);
    srv.request.arguments.resize(length);

    SrvArgument::iterator ap = srv.request.arguments.begin();
    p_map_handler_->PackVarLength<string>(mode, ap);

    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::WrMapServer>("/map_server_node/map_server");
    if (client.isValid() != true)
    {
        return false;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0)
    {
        ROS_ERROR("SetMapServerMode_() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return false;
    }
    bool rtn = client.call(srv);
    pthread_spin_unlock(&map_server_spinlock_);
    if ( rtn != true )
    {
        NSR_DEBUG("SetMapServerMode Fail!");
        return false;
    }
    NSR_DEBUG("Success.");
    return true;
}

/// footprint
void NpuServer::UpdateFootprint_(const FootprintParam& param)
{
    NSR_DEBUG("UpdateFootprint_()");
    /// set navi_core param
    ros::NodeHandle nh;
    geometry_msgs::Polygon polygon = Footprint2Polygon(param);

    /// update navi_core param
    double circumscribed_radius = 0;
    double circumscribed_width = 0;
    double circumscribed_length = 0;
    double inscribed_radius = 9999;
    stringstream ss;
    ss << "[";
    for (int i = 0; i < polygon.points.size();i++)
    {
        ss << "[" << polygon.points[i].x << ", " << polygon.points[i].y << "]";
        if (i < polygon.points.size() - 1)
        {
            ss << ", ";
        }
        else
        {
            ss << "]";
        }
        double dist = sqrt(SQU(polygon.points[i].x) + SQU(polygon.points[i].y));
        circumscribed_radius = MAX(circumscribed_radius, dist);
        circumscribed_width = MAX(circumscribed_width, 2 * fabs(polygon.points[i].x));
        circumscribed_length = MAX(circumscribed_length, 2 * fabs(polygon.points[i].y));
        inscribed_radius = MIN(inscribed_radius, dist);
    }
    string footprint_str = ss.str();
    _SetDyStrParam(STD_RUNTIME_PARAM_NS + "/footprint", footprint_str);

    if (npu_state_ == NAVI_STATE)
    {
        double robot_radius = 0;
        // footprint
        nh.setParam(STD_NAVI_CORE_PARAM_NS
                    + "/global_costmap/circumscribed_radius",
                    circumscribed_radius);
        nh.setParam(STD_NAVI_CORE_PARAM_NS
                    + "/local_costmap/circumscribed_radius",
                    circumscribed_radius);
        nh.setParam(STD_NAVI_CORE_PARAM_NS + "/global_costmap/inscribed_radius",
                    inscribed_radius);
        nh.setParam(STD_NAVI_CORE_PARAM_NS + "/local_costmap/inscribed_radius",
                    inscribed_radius);
        nh.setParam(STD_NAVI_CORE_PARAM_NS + "/global_costmap/robot_radius",
                    robot_radius);
        nh.setParam(STD_NAVI_CORE_PARAM_NS + "/local_costmap/robot_radius",
                    robot_radius);
        _SetDyStrParam(STD_NAVI_CORE_PARAM_NS + "/global_costmap/footprint",
                       footprint_str);
        _SetDyStrParam(STD_NAVI_CORE_PARAM_NS + "/local_costmap/footprint",
                       footprint_str);
        _SetDyStrParam(STD_NAVI_CORE_PARAM_NS + "/collision_costmap/footprint",
                       footprint_str);
        // inflation
        double global_inflation_scalar =
                _GetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                                 + "/global_costmap/inflation_layer/path_approach_avenue_factor");
        double local_inflation_scalar =
                _GetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                                 + "/local_costmap/inflation_layer/safety_inflation_factor");

        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/global_costmap/inflation_layer/inflation_radius",
                         circumscribed_radius * global_inflation_scalar);
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/local_costmap/inflation_layer/inflation_radius",
                         circumscribed_radius * local_inflation_scalar);
        // obstacle.range
        double obs_avoid_time_s;
        obs_avoid_time_s = _GetDyFloatParam(STD_NAVI_PARAM_NS
                                            + "/obs_avoid_time_s");
        //        _SetDyFloatParam(STD_NAVI_PARAM_NS + "/obs_avoid_time_s", obs_avoid_time_s);
        double obs_avoid_dist_m = circumscribed_radius
                +  base_param_.chassis_param.autoset_max_lin_spd_mps * obs_avoid_time_s;
        if (lidar_range_max_ > 0)
        {
            obs_avoid_dist_m = MIN(obs_avoid_dist_m, lidar_range_max_);
        }
        ROS_WARN("NpuServer::UpdateFootprint_(): obs_avoid_time_s = %f, obs_avoid_dist = %f[m]", obs_avoid_time_s, obs_avoid_dist_m);
        nh.setParam(STD_NAVI_PARAM_NS + "/runtime/obs_avoid_dist_m", obs_avoid_dist_m);
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/local_costmap/width", max(6.0, ceil(obs_avoid_dist_m * 2)));
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/local_costmap/height", max(6.0, ceil(obs_avoid_dist_m * 2)));
//        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
//                         + "/global_costmap/obstacle_layer/obstacle_range",
//                         max(2.0, obs_avoid_dist_m - 0.5));
//        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
//                         + "/global_costmap/obstacle_layer/raytrace_range",
//                         lidar_range_max_ + 0.5);
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/global_costmap/obstacle_layer/scan/obstacle_range",
                         max(2.0, ceil(obs_avoid_dist_m) - 0.5));
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/global_costmap/obstacle_layer/scan/raytrace_range",
                         lidar_range_max_ + 0.5);
//        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
//                         + "/local_costmap/obstacle_layer/obstacle_range",
//                         max(2.0, obs_avoid_dist_m - 0.5));
//        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
//                         + "/local_costmap/obstacle_layer/raytrace_range",
//                         lidar_range_max_ + 0.5);
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/local_costmap/obstacle_layer/scan/obstacle_range",
                         max(2.0, obs_avoid_dist_m - 0.5));
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/local_costmap/obstacle_layer/scan/raytrace_range",
                         lidar_range_max_ + 0.5);
        // obstacle.height
#if USE_NPU_API_V_0_9_12
        nh.setParam(STD_NAVI_CORE_PARAM_NS
                    + "/global_costmap/obstacle_layer/max_obstacle_height",
                    param.height_m);
        nh.setParam(STD_NAVI_CORE_PARAM_NS
                    + "/global_costmap/obstacle_layer/min_obstacle_height",
                    -param.height_m);
        nh.setParam(STD_NAVI_CORE_PARAM_NS
                    + "/local_costmap/max_obstacle_height", param.height_m);
        nh.setParam(STD_NAVI_CORE_PARAM_NS
                    + "/local_costmap/min_obstacle_height", -param.height_m);
        nh.setParam(STD_NAVI_CORE_PARAM_NS
                    + "/local_costmap/obstacle_layer/max_obstacle_height",
                    param.height_m);
        nh.setParam(STD_NAVI_CORE_PARAM_NS
                    + "/local_costmap/obstacle_layer/min_obstacle_height",
                    -param.height_m);
#else
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/global_costmap/obstacle_layer/max_obstacle_height",
                         footprint_height_m_);
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/global_costmap/obstacle_layer/min_obstacle_height",
                         -footprint_height_m_);
        //        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
        //                         + "/local_costmap/max_obstacle_height", footprint_height_m_);
        //        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
        //                         + "/local_costmap/min_obstacle_height", -footprint_height_m_);
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/local_costmap/obstacle_layer/max_obstacle_height",
                         footprint_height_m_);
        _SetDyFloatParam(STD_NAVI_CORE_PARAM_NS
                         + "/local_costmap/obstacle_layer/min_obstacle_height",
                         -footprint_height_m_);
#endif
        // planner just for dwa planner
//        _SetDyFloatParam(STD_LOCAL_PLANNER_PARAM_NS +"/wrlp_24", circumscribed_radius * 1.1);

        TriggerCostMapParamUpdating_();
    }

    /// footprint msg
    footprint_msg_.header.stamp = ros::Time::now();
    footprint_msg_.header.frame_id = STD_BASE_FTP_FRAME_ID;
    footprint_msg_.polygon = polygon;
    footprint_pub_.publish(footprint_msg_);
}

/// img-real transformation

/// motor param
void NpuServer::TriggerMotorParamUpdating_()
{
    NSR_DEBUG("TriggerMotorParamUpdating_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::DynParam>("/dyn_param/motor/bcu_server");
    if (client.isValid() != true)
    {
        return;
    }
    client.call(srv);
}
void NpuServer::TriggerPidParamUpdating_()
{
    NSR_DEBUG("TriggerPidParamUpdating_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/pid/bcu_server");
    if (client.isValid() != true)
    {
        return;
    }
    client.call(srv);
}

/// chassis param
void NpuServer::TriggerChassisParamUpdating_()
{
    NSR_DEBUG("TriggerChassisParamUpdating_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/chassis/chassis_server");
    if (client.isValid() != true)
    {
        return;
    }
    client.call(srv);
}

/// lidar filter param
// lidar angular param
void NpuServer::TriggerLidarFilterParamUpdating_()
{
    NSR_DEBUG("TriggerLidarFilterParamUpdating_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient lidar_angular_client =
            nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/lidar_angular_filter_server");
    ros::ServiceClient lidar_footprint_client =
            nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/lidar_footprint_filter_server");
    if ( (lidar_angular_client.isValid() != true)
         && (lidar_footprint_client.isValid() != true) )
    {
        return;
    }
    lidar_angular_client.call(srv);
    lidar_footprint_client.call(srv);

}
//lidar footprint param
//void NpuServer::TriggerLidarFootprintFilterParamUpdating_()
//{
//    NSR_DEBUG("TriggerLidarFootprintFilterParamUpdating_()");
//    ros::NodeHandle nh;
//    wr_npu_msgs::DynParam srv;
//    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::DynParam>("/dyn_param/lidar_footprint_filter_server");
//    if (client.isValid() != true)
//    {
//      return;
//    }
//    client.call(srv);
//}

/// costmap param
void NpuServer::TriggerCostMapParamUpdating_()
{
    NSR_DEBUG("TriggerCostMapParamUpdating_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient go_client =
            nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/navi_core/global_costmap/obstacle_layer_server");
    ros::ServiceClient lo_client =
            nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/navi_core/local_costmap/obstacle_layer_server");
    ros::ServiceClient co_client =
            nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/navi_core/collision_costmap/obstacle_layer_server");
    ros::ServiceClient gi_client =
            nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/navi_core/global_costmap/inflation_layer_server");
    ros::ServiceClient li_client =
            nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/navi_core/local_costmap/inflation_layer_server");
    ros::ServiceClient ci_client =
            nh.serviceClient<wr_npu_msgs::DynParam>(
                "/dyn_param/navi_core/collision_costmap/inflation_layer_server");
    if ( (go_client.isValid() != true) && (lo_client.isValid() != true) )
    {
        return;
    }
    go_client.call(srv);
    lo_client.call(srv);
    co_client.call(srv);
    gi_client.call(srv);
    li_client.call(srv);
    ci_client.call(srv);
}

/// sensor param
void NpuServer::GetLidarParam_(SensorParam &param)
{
    NSR_DEBUG("GetLidarParam_()");
    param.lidar_num = _GetDyIntParam(STD_SENSOR_PARAM_NS + "/lidar_num");
    param.lidar_params.resize(param.lidar_num);
    for(int i=0;i<param.lidar_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS
                                       + "/lidar_params/lidar_" << i << "/";
        param.lidar_params[i].type = static_cast<wizrobo_npu::LidarType>(
                    EnumString<wizrobo::LidarType>::StrToEnum(
                        _GetDyStrParam(ss_prefix.str()+"type")));
        param.lidar_params[i].itf_type = EnumString<InterfaceType>::StrToEnum(
                    _GetDyStrParam(ss_prefix.str()+"itf_type"));
        param.lidar_params[i].serial_port_id = _GetDyStrParam(
                    ss_prefix.str()+"itf_id");
        param.lidar_params[i].ethernet_ip = _GetDyStrParam(
                    ss_prefix.str()+"itf_id");
        // TODO: param.lidar_params[i].filter
        param.lidar_params[i].enb_itl_filter = _GetDyBoolParam(
                    ss_prefix.str()+"enb_filter");
        param.lidar_params[i].install_pose.x = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/x");
        param.lidar_params[i].install_pose.y = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/y");
        param.lidar_params[i].install_pose.z = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/z");
        param.lidar_params[i].install_pose.yaw = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/yaw");
        param.lidar_params[i].install_pose.pitch = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/pitch");
        param.lidar_params[i].install_pose.roll = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/roll");
        param.lidar_params[i].min_angle = _GetDyFloatParam(
                    STD_LIDAR_FILTERS_PARAM_NS+"/lower_angle");
        param.lidar_params[i].max_angle = _GetDyFloatParam(
                    STD_LIDAR_FILTERS_PARAM_NS+"/upper_angle");
    }
}
void NpuServer::GetSonarParam_(SensorParam& param)
{
    NSR_DEBUG("GetSonarParam_()");
    param.sonar_num = _GetDyIntParam(STD_SENSOR_PARAM_NS + "/sonar_num");
    param.sonar_params.resize(param.sonar_num);
    for(int i=0;i<param.sonar_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS
                                       + "/sonar_params/sonar_" << i << "/";
        param.sonar_params[i].min_range_m = _GetDyFloatParam(
                    ss_prefix.str()+"min_range_m");
        param.sonar_params[i].max_range_m = _GetDyFloatParam(
                    ss_prefix.str()+"max_range_m");
        param.sonar_params[i].scan_freq_hz = _GetDyFloatParam(
                    ss_prefix.str()+"scan_freq_hz");
        param.sonar_params[i].fov_deg = _GetDyFloatParam(
                    ss_prefix.str()+"fov_deg");
        param.sonar_params[i].install_pose.x = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/x");
        param.sonar_params[i].install_pose.y = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/y");
        param.sonar_params[i].install_pose.z = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/z");
        param.sonar_params[i].install_pose.yaw = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/yaw");
        param.sonar_params[i].install_pose.pitch = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/pitch");
        param.sonar_params[i].install_pose.roll = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/roll");
    }
}
void NpuServer::GetInfrdParam_(SensorParam& param)
{
    NSR_DEBUG("GetInfrdParam_()");
    if (pthread_spin_trylock(&get_infparam_spinlock_) != 0)
    {
        ROS_ERROR("GetInfrdParam_() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    param.infrd_num = _GetDyIntParam(STD_SENSOR_PARAM_NS + "/infrd_num");
    param.infrd_params.resize(param.infrd_num);
    for(int i=0;i<param.infrd_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS
                                       + "/infrd_params/infrd_" << i << "/";
        param.infrd_params[i].min_range_m = _GetDyFloatParam(
                    ss_prefix.str()+"min_range_m");
        param.infrd_params[i].max_range_m = _GetDyFloatParam(
                    ss_prefix.str()+"max_range_m");
        param.infrd_params[i].fov_deg = _GetDyFloatParam(
                    ss_prefix.str()+"fov_deg");
        param.infrd_params[i].install_pose.x = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/x");
        param.infrd_params[i].install_pose.y = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/y");
        param.infrd_params[i].install_pose.z = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/z");
        param.infrd_params[i].install_pose.yaw = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/yaw");
        param.infrd_params[i].install_pose.pitch = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/pitch");
        param.infrd_params[i].install_pose.roll = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/roll");
    }
    pthread_spin_unlock(&get_infparam_spinlock_);

}
void NpuServer::GetBumperParam_(SensorParam& param)
{
    NSR_DEBUG("GetBumperParam_()");
    param.bumper_num = _GetDyIntParam(STD_SENSOR_PARAM_NS + "/bumper_num");
    param.bumper_params.resize(param.bumper_num);
    for(int i=0;i<param.bumper_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS
                                       + "/bumper_params/bumper_" << i << "/";
        param.bumper_params[i].location =
                wizrobo::enum_ext::EnumString<BumperLocation>::StrToEnum(
                    _GetDyStrParam(ss_prefix.str()+"location"));
    }
}
void NpuServer::GetImuParam_(SensorParam& param)
{
    NSR_DEBUG("GetImuParam_()");
    param.imu_num = _GetDyIntParam(STD_SENSOR_PARAM_NS + "/imu_num");
    param.imu_params.resize(param.imu_num);
    for(int i=0;i<param.imu_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS
                                       + "/imu_params/imu_" << i << "/";
        param.imu_params[i].install_pose.x = _GetDyFloatParam(
                    ss_prefix.str() + "install_pose/x");
        param.imu_params[i].install_pose.y = _GetDyFloatParam(
                    ss_prefix.str() + "install_pose/y");
        param.imu_params[i].install_pose.z = _GetDyFloatParam(
                    ss_prefix.str() + "install_pose/z");
        param.imu_params[i].install_pose.yaw = _GetDyFloatParam(
                    ss_prefix.str() + "install_pose/yaw");
        param.imu_params[i].install_pose.pitch = _GetDyFloatParam(
                    ss_prefix.str() + "install_pose/pitch");
        param.imu_params[i].install_pose.roll = _GetDyFloatParam(
                    ss_prefix.str() + "install_pose/roll");
    }
}
void NpuServer::GetGpsParam_(SensorParam& param)
{
    NSR_DEBUG("GetGpsParam_()");
    param.gps_num = _GetDyIntParam(STD_SENSOR_PARAM_NS + "/gps_num");
    param.gps_params.resize(param.gps_num);
    for(int i=0;i<param.gps_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS
                                       + "/gps_params/gps_" << i << "/";
        param.gps_params[i].install_pose.x = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/x");
        param.gps_params[i].install_pose.y = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/y");
        param.gps_params[i].install_pose.z = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/z");
        param.gps_params[i].install_pose.yaw = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/yaw");
        param.gps_params[i].install_pose.pitch = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/pitch");
        param.gps_params[i].install_pose.roll = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/roll");
    }
}
void NpuServer::GetCameraParam_(SensorParam& param)
{
    NSR_DEBUG("GetCameraParam_()");
    param.camera_num = _GetDyIntParam(STD_SENSOR_PARAM_NS + "/camera_num");
    param.camera_params.resize(param.camera_num);
    for(int i=0;i<param.camera_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS
                                       + "/camera_params/camera_" << i << "/";
        param.camera_params[i].install_pose.x = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/x");
        param.camera_params[i].install_pose.y = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/y");
        param.camera_params[i].install_pose.z = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/z");
        param.camera_params[i].install_pose.yaw = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/yaw");
        param.camera_params[i].install_pose.pitch = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/pitch");
        param.camera_params[i].install_pose.roll = _GetDyFloatParam(
                    ss_prefix.str()+"install_pose/roll");
    }
}

void NpuServer::UpdateLidarToBaseTf_(const SensorParam& param)
{
    wizrobo_npu::Pose3D lidar_wr_pose = param.lidar_params.front().install_pose;
    geometry_msgs::Pose lidar_geo_pose;
    lidar_geo_pose.position.x = lidar_wr_pose.x;
    lidar_geo_pose.position.y = lidar_wr_pose.y;
    lidar_geo_pose.position.z = lidar_wr_pose.z;
    lidar_geo_pose.orientation = ros_ext::RPY2GeoQuat(lidar_wr_pose.roll,
                                                      lidar_wr_pose.pitch,
                                                      lidar_wr_pose.yaw);
    tf::poseMsgToTF(lidar_geo_pose, base_to_lidar_tf_);
}

void NpuServer::SetLidarParam_(SensorParam& param)
{
    NSR_DEBUG("SetLidarParam_()");
    _SetDyIntParam(STD_SENSOR_PARAM_NS + "/lidar_num", param.lidar_num);
    for(int i=0;i<param.lidar_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS + "/lidar_params/lidar_" << i << "/";
#if USE_NPU_API_V_0_9_12
        _SetDyStrParam(ss_prefix.str()+"type", param.lidar_params[i].type);
        _SetDyStrParam(ss_prefix.str()+"itf_type", EnumString<InterfaceType>::EnumToStr(param.lidar_params[i].itf_type));
        _SetDyStrParam(ss_prefix.str()+"itf_id", param.lidar_params[i].itf_id);
#else
        _SetDyStrParam(ss_prefix.str()+"type", EnumString<wizrobo::LidarType>::EnumToStr(static_cast<wizrobo::LidarType>(param.lidar_params[i].type)));
        _SetDyStrParam(ss_prefix.str()+"itf_type", EnumString<InterfaceType>::EnumToStr(param.lidar_params[i].itf_type));
        string itf_id = param.lidar_params[i].serial_port_id;
        if (itf_id.empty())
        {
            itf_id = param.lidar_params[i].ethernet_ip;
        }
        _SetDyStrParam(ss_prefix.str()+"itf_id", itf_id);
#endif

        _SetDyBoolParam(ss_prefix.str() + "enb_filter", param.lidar_params[i].enb_itl_filter);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/x", param.lidar_params[i].install_pose.x);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/y", param.lidar_params[i].install_pose.y);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/z", param.lidar_params[i].install_pose.z);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/yaw", param.lidar_params[i].install_pose.yaw);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/pitch", param.lidar_params[i].install_pose.pitch);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/roll", param.lidar_params[i].install_pose.roll);
        _SetDyFloatParam(STD_LIDAR_FILTERS_PARAM_NS + "/lower_angle", param.lidar_params[i].min_angle);
        _SetDyFloatParam(STD_LIDAR_FILTERS_PARAM_NS + "/upper_angle", param.lidar_params[i].max_angle);
    }
    UpdateLidarToBaseTf_(param);
    TriggerLidarFilterParamUpdating_();
}
void NpuServer::SetSonarParam_(SensorParam& param)
{
    NSR_DEBUG("SetSonarParam_()");
    _SetDyIntParam(STD_SENSOR_PARAM_NS + "/sonar_num", param.sonar_num);
    for(int i=0;i<param.sonar_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str("");
        ss_prefix <<STD_SENSOR_PARAM_NS + "/sonar_params/sonar_" << i << "/";
        _SetDyFloatParam(ss_prefix.str()+"min_range_m", param.sonar_params[i].min_range_m);
        _SetDyFloatParam(ss_prefix.str()+"max_range_m", param.sonar_params[i].max_range_m);
        _SetDyFloatParam(ss_prefix.str()+"scan_freq_hz", param.sonar_params[i].scan_freq_hz);
        _SetDyFloatParam(ss_prefix.str()+"fov_deg", param.sonar_params[i].fov_deg);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/x", param.sonar_params[i].install_pose.x);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/y", param.sonar_params[i].install_pose.y);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/z", param.sonar_params[i].install_pose.z);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/yaw", param.sonar_params[i].install_pose.yaw);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/pitch", param.sonar_params[i].install_pose.pitch);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/roll", param.sonar_params[i].install_pose.roll);
    }
}
void NpuServer::SetInfrdParam_(SensorParam& param)
{
    NSR_DEBUG("SetInfrdParam_()");
    _SetDyIntParam(STD_SENSOR_PARAM_NS + "/infrd_num", param.infrd_num);
    for(int i=0;i<param.infrd_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS + "/infrd_params/infrd_" << i << "/";
        _SetDyFloatParam(ss_prefix.str()+"min_range_m", param.infrd_params[i].min_range_m);
        _SetDyFloatParam(ss_prefix.str()+"max_range_m", param.infrd_params[i].max_range_m);
        _SetDyFloatParam(ss_prefix.str()+"fov_deg", param.infrd_params[i].fov_deg);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/x", param.infrd_params[i].install_pose.x);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/y", param.infrd_params[i].install_pose.y);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/z", param.infrd_params[i].install_pose.z);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/yaw", param.infrd_params[i].install_pose.yaw);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/pitch", param.infrd_params[i].install_pose.pitch);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/roll", param.infrd_params[i].install_pose.roll);
    }
}
void NpuServer::SetBumperParam_(SensorParam& param)
{
    NSR_DEBUG("SetBumperParam_()");
    _SetDyIntParam(STD_SENSOR_PARAM_NS + "/bumper_num", param.bumper_num);
    for(int i=0;i<param.bumper_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS + "/bumper_params/bumper_" << i << "/";
        _SetDyStrParam(ss_prefix.str()+"location", wizrobo::enum_ext::EnumString<BumperLocation>::EnumToStr(param.bumper_params[i].location));
    }
}
void NpuServer::SetImuParam_(SensorParam& param)
{
    NSR_DEBUG("SetImuParam_()");
    _SetDyIntParam(STD_SENSOR_PARAM_NS + "/imu_num", param.imu_num);
    for(int i=0;i<param.imu_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS + "/imu_params/imu_" << i << "/";
        _SetDyFloatParam(ss_prefix.str()+"install_pose/x", param.imu_params[i].install_pose.x);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/y", param.imu_params[i].install_pose.y);
#if TWO_DIM
        _SetDyFloatParam(ss_prefix.str()+"install_pose/z", 0.0);
#else
        _SetDyFloatParam(ss_prefix.str()+"install_pose/z", param.lidar_params[i].install_pose.z);
#endif
        _SetDyFloatParam(ss_prefix.str()+"install_pose/yaw", param.imu_params[i].install_pose.yaw);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/pitch", param.imu_params[i].install_pose.pitch);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/roll", param.imu_params[i].install_pose.roll);
    }
}
void NpuServer::SetGpsParam_(SensorParam& param)
{
    NSR_DEBUG("SetGpsParam_()");
    _SetDyIntParam(STD_SENSOR_PARAM_NS + "/gps_num", param.gps_num);
    for(int i=0;i<param.gps_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS + "/gps_params/gps_" << i << "/";
        _SetDyFloatParam(ss_prefix.str()+"install_pose/x", param.gps_params[i].install_pose.x);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/y", param.gps_params[i].install_pose.y);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/z", param.gps_params[i].install_pose.z);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/yaw", param.gps_params[i].install_pose.yaw);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/pitch", param.gps_params[i].install_pose.pitch);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/roll", param.gps_params[i].install_pose.roll);
    }
}
void NpuServer::SetCameraParam_(SensorParam& param)
{
    NSR_DEBUG("SetCameraParam_()");
    _SetDyIntParam(STD_SENSOR_PARAM_NS + "/camera_num", param.camera_num);
    for(int i=0;i<param.camera_num;i++)
    {
        stringstream ss_prefix;
        ss_prefix.str(""); ss_prefix <<STD_SENSOR_PARAM_NS + "/camera_params/camera_" << i << "/";
        _SetDyFloatParam(ss_prefix.str()+"install_pose/x", param.camera_params[i].install_pose.x);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/y", param.camera_params[i].install_pose.y);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/z", param.camera_params[i].install_pose.z);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/yaw", param.camera_params[i].install_pose.yaw);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/pitch", param.camera_params[i].install_pose.pitch);
        _SetDyFloatParam(ss_prefix.str()+"install_pose/roll", param.camera_params[i].install_pose.roll);
    }
}

///check sensor
void NpuServer::CheckSensorParam_()
{
    NSR_DEBUG("CheckSensorParam_()...CheckSensorParam_()...CheckSensorParam_()...CheckSensorParam_()...CheckSensorParam_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::DynParam>("/check_port_param/rplidar");
    if (client.isValid() != true)
    {
        NSR_DEBUG("CheckSensorParam_() lidar port error!");
        return;
    }
    client.call(srv);
}/// navi param
void NpuServer::UpdatePathFollowerParam_()
{
    NSR_DEBUG("UpdatePathFollowerParam_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::DynParam>("/dyn_param/navi/path_follower");
    if (client.isValid() != true)
    {
        return;
    }
    client.call(srv);
}
void NpuServer::UpdateCcpPlannerParam_()
{
    NSR_DEBUG("UpdateCcpPlannerParam_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::DynParam>("/dyn_param/navi/ccp_planner");
    if (client.isValid() != true)
    {
        return;
    }
    client.call(srv);
}
void NpuServer::UpdateP2pPlannerParam_()
{
    NSR_DEBUG("UpdateP2pPlannerParam_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::DynParam>("/dyn_param" + STD_LOCAL_PLANNER_PARAM_NS);
    if (client.isValid() != true)
    {
        return;
    }
    client.call(srv);
}

/// tf
void NpuServer::UpdateTf_()
{
    NSR_DEBUG("UpdateTf_()");
    ros::NodeHandle nh;
    wr_npu_msgs::DynParam srv;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::DynParam>("/tf_publisher_node/update_tf");
    if (client.isValid() != true)
    {
        return;
    }
    client.call(srv);
}

bool NpuServer::ForceLoopClosure_()
{
    ROS_INFO("NpuServer::ForceLoopClosure()");
    ros::NodeHandle nh;
    wr_npu_msgs::ForceLoopClosure srv;
    std::string srv_name = "/map_server_node/force_loop_closure";
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::ForceLoopClosure>(srv_name);
    if (client.isValid() != true)
    {
        return false;
    }

    bool rlt = client.call(srv);
    if (!rlt)
    {
        ROS_ERROR("NpuServer::ForceLoopClosure(): Call service \"%s\", rlt = %s", srv_name.c_str(), BoolToStr(rlt).c_str());
        return false;
    }
    ROS_INFO("NpuServer::ForceLoopClosure(): Call service \"%s\", rlt = %s", srv_name.c_str(), BoolToStr(rlt).c_str());
    return true;
}

void NpuServer::launch_map_receiving_thread_()
{
    is_map_caching_ = true;
    p_map_receiving_thread_ = new boost::thread(boost::bind(&NpuServer::map_receiving_thread_, this));
}

void NpuServer::stop_map_receiving_thread_()
{
    if (p_map_receiving_thread_)
    {
        //p_map_receiving_thread_->interrupt();
        is_map_caching_ = false;
        p_map_receiving_thread_->join();
        delete p_map_receiving_thread_;
        p_map_receiving_thread_ = NULL;
    }
}

void NpuServer::map_receiving_thread_()
{
    int cache_index;
    NSR_DEBUG("map_receiving_thread started.");
    ros::Rate loop_rater(1);
    while (ros::ok() && is_map_caching_ == true) {
        loop_rater.sleep();
        NSR_DEBUG("map_receiving_loop_begin");
        cache_index = (map_cache_index_==0)?(1):(0);
        get_imgmap_from_map_server_(imgmap_cache_[cache_index]);
        //get_map2d_from_map_server_(map2d_cache_[cache_index]);
        if (pthread_spin_trylock(&map_cache_spinlock_) != 0) {
            ROS_ERROR("map_receiving_thread_() Get thread lock failed");
            continue;
        }
        map_cache_index_ = (map_cache_index_==0)?(1):(0);
        pthread_spin_unlock(&map_cache_spinlock_);
        NSR_DEBUG("map_receiving_loop_end");
    }
    NSR_DEBUG("map_receiving_thread stoped.");
}

void NpuServer::get_imgmap_from_map_server_(ImgMap &imgmap)
{
    ros::NodeHandle nh;
    SrvMapServe srv;
    srv.request.function = "GetCurrentImgMap";
    srv.request.arguments.resize(0);
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::WrMapServer>("/map_server_node/map_server");
    if (client.isValid() != true) {
        return;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("get_imgmap_from_map_server_() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    bool rtn = client.call(srv);
    pthread_spin_unlock(&map_server_spinlock_);
    if ( rtn != true ) {
        NSR_DEBUG("GetCurrentImgMap return fail!");
        return;
    }
    SrvReturn::iterator p = srv.response.return_data.begin();
    p_map_handler_->Unpack(imgmap.info, p);
    p_map_handler_->Unpack(imgmap.mat, p);
    p_map_handler_->Unpack(imgmap.stations, p);
    p_map_handler_->Unpack(imgmap.paths, p);
}
void NpuServer::get_geomap_from_map_server_(GeoMap &geomap)
{
    ros::NodeHandle nh;
    SrvMapServe srv;
    srv.request.function = "GetCurrentGeoMap";
    srv.request.arguments.resize(0);
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::WrMapServer>("/map_server_node/map_server");
    if (client.isValid() != true) {
        return;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("get_geomap_from_map_server_() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    bool rtn = client.call(srv);
    pthread_spin_unlock(&map_server_spinlock_);
    if ( rtn != true ) {
        NSR_DEBUG("GetCurrentGeoMap return fail!");
        return;
    }
    SrvReturn::iterator p = srv.response.return_data.begin();
    p_map_handler_->Unpack(geomap.info, p);
    p_map_handler_->Unpack(geomap.mat, p);
    p_map_handler_->Unpack(geomap.stations, p);
    p_map_handler_->Unpack(geomap.paths, p);
}
void NpuServer::get_map2d_from_map_server_(Map2D &map2d)
{
    ros::NodeHandle nh;
    SrvMapServe srv;
    srv.request.function = "GetCurrentMap";
    srv.request.arguments.resize(0);
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::WrMapServer>("/map_server_node/map_server");
    if (client.isValid() != true) {
        return;
    }
    if (pthread_spin_trylock(&map_server_spinlock_) != 0) {
        ROS_ERROR("get_map_from_map_server_() Get thread lock failed");
        throw NpuException("", "", ERR_UNKNOWN, "Get thread lock failed");
        return;
    }
    bool rtn = client.call(srv);
    pthread_spin_unlock(&map_server_spinlock_);
    if ( rtn != true ) {
        NSR_DEBUG("GetCurrentMap return fail!");
        return;
    }
    SrvReturn::iterator p = srv.response.return_data.begin();
    p_map_handler_->Unpack(map2d.info, p);
    p_map_handler_->Unpack(map2d.mat, p);
    p_map_handler_->Unpack(map2d.stations, p);
    p_map_handler_->Unpack(map2d.paths, p);
}

void NpuServer::launch_file_manage_thread_(string mode)
{
    p_file_manage_thread_ = new boost::thread(boost::bind(&NpuServer::file_manage_thread_, this,mode));
}

void NpuServer::launch_file_execute_thread_(string mode)
{
    p_execute_task_ = new boost::thread(boost::bind(&NpuServer::file_execute_thread_, this,mode));
}

void NpuServer::file_execute_thread_(string mode)
{
    if(mode=="execute")
    {
        execute_file();
    }
}

void NpuServer::file_manage_thread_(string mode)
{
    if(mode=="compress") {
        compress_file();
    }
    if(mode=="delete") {
        delete_file();
    }
    if(mode=="uncompress") {
        uncompress_file();
    }
}

void NpuServer::execute_file()
{
    ROS_INFO("Function execute_file()");
    //    string str="roslaunch wr_task_system task.launch";
    string s;
    for(int i=0; i<list_.size();i++)
    {
        s.append(" ").append("action_name:=");
        for(int u = 0;u < list_[i].info.action_list.size();u++)
        {
            actionname aa = list_[i].info.action_list[u].action_name;
            string action_ = to_string(aa);
            s.append(action_).append(",");
        }
        s.append(" ").append("action_args:=");
        for(int j=0; j<list_[i].info.action_list.size(); j++)
        {
            s.append(list_[i].info.action_list[j].action_args).append(",");
        }
        s.append(" ").append("duration:=");
        for(int k=0; k<list_[i].info.action_list.size(); k++)
        {
            int aa = list_[i].info.action_list[k].duration;
            string duration_ = to_string(aa);
            s.append(duration_).append(",");
        }
        string map_name = list_[i].info.map_id;
        int bb = list_[i].task_loop_times;
        string times_ = to_string(bb);
        bool cc = list_[i].enb_taskloop;
        string loop_ = to_string(cc);
        s.append(" ").append("times:=").append(times_).append(" ").append("taskloop:=").append(loop_).append(" ").append("map_name:=").append(map_name);
    }
    ros::NodeHandle nh;
    string host_platform;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    stringstream ss;
    //    const char *cmd=str.c_str();
    ss << "bash /npu." << host_platform << "/script/task_run.sh \"" << " "
       << s << "\"&\n";
    system(ss.str().c_str());
    ROS_INFO("%s",ss.str().c_str());
    delete p_file_manage_thread_;
    p_file_manage_thread_ = NULL;
}

void NpuServer::compress_file()
{
    ROS_INFO("Compressing file...");
    string str="tar -zcvf";
    const char *newname=p_get_files_info_->fileName.c_str();
    fstream file;
    file.open(newname, ios::binary|ios::in);
    if(!file)
    {
        p_get_files_info_->getExportFileInfoPtr->ice_exception();
        ROS_INFO("Compress file failed!!!");
        return ;
    }


    int index=0;
    int len = strlen(newname);
    string file_dir=newname;
    index=file_dir.rfind('/',len);
    file_dir=file_dir.substr(0,index+1);

    string file_dir1=newname;
    file_dir1=file_dir1.substr(index+1);

    str.append(" ").append(p_get_files_info_->fileName).append(".tar.gz").append(" -C ").append(file_dir).append(" ").append(file_dir1);
    const char *cmd=str.c_str();
    system(cmd);
    ROS_INFO("Compress file end!");

    string filename=p_get_files_info_->fileName;
    filename.append(".tar.gz");

    FileInfo info;
    const char *name=filename.c_str();
    fstream compress_file;
    FILE *fp=fopen(name,"r");

    compress_file.open(name, ios::binary|ios::in);

    if ( !compress_file.is_open() )
    {
        cout << "fail" << endl;
    }

    fseek(fp,0L,SEEK_END);
    int filesize=ftell(fp);

    //一个数据块为1M,计算数据块的数量
    int chunk_num=filesize/chunk_size_;
    if(filesize%chunk_size_==0)
    {
        chunk_num=chunk_num;
    }
    else
    {
        chunk_num+=1;
    }

    info.chunk_num=chunk_num;
    info.chunk_size_Bytes=chunk_size_;
    info.file_name=filename;
    fclose(fp);

    p_get_files_info_->getExportFileInfoPtr->ice_response(info);
    ROS_INFO("Return ExportFileInfo");

    delete p_file_manage_thread_;
    p_file_manage_thread_ = NULL;
}

void NpuServer::delete_file()
{
    ROS_INFO("Removing compressed file...");
    //remove file command
    string str="rm";
    str.append(" ").append(p_get_files_info_->fileName).append(".tar.gz");
    const char *cmd=str.c_str();
    system(cmd);
    ROS_INFO("Remove compressed file Succeed!");
    delete p_file_manage_thread_;
    p_file_manage_thread_ = NULL;
}


void NpuServer::uncompress_file()
{
    int index=0;
    int len = strlen(uncompress_file_name);
    string file_dir=uncompress_file_name;
    index=file_dir.rfind('/',len);
    file_dir=file_dir.substr(0,index+1);

    //uncompress file command
    string str="tar zxvf";
    str.append(" ").append(uncompress_file_name).append(" -C ").append(file_dir);
    const char *cmd=str.c_str();
    system(cmd);

    //remove file command
    string str_rm="rm";
    str_rm.append(" ").append(uncompress_file_name);
    const char *cmd_rm=str_rm.c_str();
    system(cmd_rm);

    ROS_INFO("uncompress&&delete file Succeed!");
    delete p_file_manage_thread_;
    p_file_manage_thread_ = NULL;
}


////sensor status
void NpuServer::launch_sensor_status_thread_(CheckMode mode)
{
    ROS_INFO("launch_sensor_status_thread_()");
    p_sensor_status_thread_ = new boost::thread(boost::bind(&NpuServer::sensor_status_thread_, this,mode));
}


void NpuServer::sensor_status_thread_(CheckMode mode)
{
    ROS_INFO("sensor_status_thread_()");
    if (npu_state_ == NAVI_STATE)
    {
        API_DEBUG("Stop NAVI first.");
        StopNavi();
    }
    else if (npu_state_ == SLAM_STATE)
    {
        API_DEBUG("Stop SLAM first.");
        StopSlam("");
    }
    else if (npu_state_ == TELEOP_STATE)
    {
        API_DEBUG("TELOP is already started.");
        StopTelop();
    }
    safe_thread_num_ = 1;
    //  cout<<"sensor_status_thread_()     "<<safe_thread_num_<<endl;
    ros::NodeHandle nh;
    string host_platform;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    stringstream ss;
    ss << "bash /npu."<< host_platform << "/script/sensor_check.sh --all";
    system(ss.str().c_str());
    delete p_sensor_status_thread_;
    p_sensor_status_thread_ = NULL;
}


void NpuServer::launch_connection_limit_thread_()
{
    p_connection_limit_thread_ = new boost::thread(boost::bind(&NpuServer::connection_limit_thread_, this));
}


void NpuServer::connection_limit_thread_()
{
    //sleep 10s
    ros::Rate count(0.1);
    while (true)
    {
        if(client_is_alive)
        {
            NSR_DEBUG("start sleep");
        }
        count.sleep();
        client_is_alive=false;
        NSR_DEBUG("sleep finished!");
    }

}

///exception handling
void NpuServer::launch_exce_info_thread_()
{
    p_exce_info_thread_ = new boost::thread(boost::bind(&NpuServer::CheckAbnormalInfo, this));
}

void NpuServer::CheckAbnormalInfo()
{
    NSR_DEBUG("exce_info_()");
    wr_npu_msgs::ExceptionInformation sensor_;
    //    float spd_left = act_motor_spd_.rpms[0];
    //    float spd_right = act_motor_spd_.rpms[1];
    //    float spd_diff = abs(abs(spd_left) - abs(spd_right));
    SensorParam param;

    ros::NodeHandle nh;
    bool aks_;
    nh.param<bool>("/check_port_param/aks", aks_, true);
    string base_id;
    nh.param<std::string>("/npu_param/base/base_id", base_id, "");
    bool base_;
    nh.param<bool>("/check_port_param/"+base_id, base_, true);
    bool imu_;
    nh.param<bool>("/check_port_param/imu", imu_, true);
    int imu_num;
    nh.param<int>("/npu_param/sensor/imu_num", imu_num, 1);
    string lidar_type;
    nh.param<std::string>("/npu_param/sensor/lidar_params/lidar_0/type", lidar_type, "");
    transform(lidar_type.begin(), lidar_type.end(), lidar_type.begin(), ::tolower);
    bool lidar_;
    nh.param<bool>("/check_port_param/"+lidar_type, lidar_, true);

    if(aks_ == true) {
        if(base_ == true) {
            if(lidar_ == true) {
                if(imu_num > 0) {
                    ROS_DEBUG("imu_num is: %d",param.imu_num);
                    if(imu_ == true) {
                        return;
                    } else {
                        // StopNavi();
                        sensor_.state_index = 4;
                        exce_info_pub_.publish(sensor_);
                        API_DEBUG("Imu connect error!!!");
                        throw NpuException("","", ERR_UNKNOWN, "Imu connect error!!!");
                    }
                } else {
                    API_DEBUG("Have no imu!!!");
                }
            } else {
                // StopNavi();
                sensor_.state_index = 4;
                exce_info_pub_.publish(sensor_);
                API_DEBUG("Lidar connect error!!!");
                throw NpuException("","", ERR_UNKNOWN, "Lidar connect error!!!");
            }
        } else {
            // StopNavi();
            sensor_.state_index = 4;
            exce_info_pub_.publish(sensor_);
            API_DEBUG("Robot connect error!!!");
            throw NpuException("","", ERR_UNKNOWN, "Robot connect error!!!");
        }
    } else {
        // topNavi();
        sensor_.state_index = 4;
        exce_info_pub_.publish(sensor_);
        API_DEBUG("AKS connect error!!!");
        throw NpuException("","", ERR_UNKNOWN, "AKS connect error!!!");
    }
    int bumper_num = bumper_data_msgs_.bumper_num;
    vector<int>bumper(bumper_num);
    for(int i=0; i<bumper_num; i++) {
        bumper[i] = bumper_data_msgs_.states[i];
        if(bumper[i] == 1) {
            StopNavi();
            sensor_.state_index = 1;
            exce_info_pub_.publish(sensor_);
            NSR_DEBUG("Bumper Triggered!");
        }
    }

    int infrd_num = infrd_data_msgs_.infrd_num;
    vector<float>infrd(infrd_num);
    for(int i=0; i<infrd_num; i++) {
        infrd[i] = infrd_data_msgs_.meters[i];
        if(infrd[i] > 0.35) {
            StopNavi();
            sensor_.state_index = 3;
            exce_info_pub_.publish(sensor_);
            NSR_DEBUG("Infrd Triggered!");
        }
    }

    int emergency_stop = emergencystop_data_msgs_.status;
    if(emergency_stop == 1) {
        StopNavi();
        sensor_.state_index = 2;
        exce_info_pub_.publish(sensor_);
        NSR_DEBUG("EmergencyStop Triggered!");
    }
    if (npu_state_ == NAVI_STATE && matching_identifiation == true) {
        if (GetMatchingScore() < 0.6) {
//            ROS_ERROR("matching_score_data: %f", GetMatchingScore());
            throw NpuException("", "", ERR_UNKNOWN, "Init pose matching failed ,please set init pose.");
            return;
        } else {
            matching_identifiation = false;
            ROS_WARN("matching_score_data: %f", GetMatchingScore());
        }
    }
    if(navi_mode_ == P2P_NAVI && npu_state_ == NAVI_STATE && navi_state_ == ABORTED)
    {
        sensor_.state_index = 21;
        exce_info_pub_.publish(sensor_);
    } else if(navi_mode_ == P2P_NAVI && npu_state_ == NAVI_STATE && navi_state_ == SUCCEEDED) {
        sensor_.state_index = 24;
        exce_info_pub_.publish(sensor_);
    } else if(navi_mode_ == PF_NAVI && npu_state_ == NAVI_STATE && navi_state_ == SUCCEEDED) {
        sensor_.state_index = 33;
        exce_info_pub_.publish(sensor_);
    }
    NSR_DEBUG("exce_info done!");
}

bool NpuServer::CsgChangeStation(int opt, int id, double x, double y, double theta, int type)
{
    NSR_DEBUG("CsgChangeStation() opt: %d, id: %d, x: %.2f, y: %.2f, theta: %.2f, type: %d",
              opt, id, x, y, theta, type);
    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::CsgStation>("/change_csg_station");
    if (client.isValid() != true) {
        NSR_DEBUG("Client is NOT valid!");
        return false;
    }
    NSR_DEBUG("Do Client.call()");
    wr_npu_msgs::CsgStation srv;
    srv.request.action_mode = opt;
    srv.request.station.id = id;
    srv.request.station.x = x;
    srv.request.station.y = y;
    srv.request.station.theta = theta;
    srv.request.station.inflection_point = ((type&0x00000001)?true:false);
    srv.request.station.csg_station = ((type&0x00000002)?true:false);
    srv.request.station.enb_navi_arrived = ((type&0x00000004)?true:false);
    srv.request.station.is_charge_station = ((type&0x00000008)?true:false);
    srv.request.station.is_charger = ((type&0x00000010)?true:false);
    if (client.call(srv) != true) {
        NSR_DEBUG("Client.call() return FALSE!");
        return false;
    }
    NSR_DEBUG("Client.call() return TRUE!");
    return true;
}

bool NpuServer::CsgChangePath(int opt, int s_id, int e_id)
{
    NSR_DEBUG("CsgChangePath() opt: %d, s_id: %d, e_id: %d", opt, s_id, e_id);
    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::CsgPath>("/change_csg_path");
    if (client.isValid() != true) {
        NSR_DEBUG("Client is NOT valid!");
        return false;
    }
    NSR_DEBUG("Do Client.call()");
    wr_npu_msgs::CsgPath srv;
    srv.request.action_mode_path = opt;
    srv.request.station_array.station_array.resize(2);
    srv.request.station_array.station_array[0].id = s_id;
    srv.request.station_array.station_array[1].id = e_id;
    if (client.call(srv) != true) {
        NSR_DEBUG("Client.call() return FALSE!");
        return false;
    }
    NSR_DEBUG("Client.call() return TRUE!");
    return true;
}

bool NpuServer::CsgEnableAutoMark(int opt)
{
    ROS_WARN("CsgEnableAutoMark() TODO opt: %d", opt);
    ros::NodeHandle nh;
    nh.setParam("/csg_enb_automark", opt);
    return true;
}

bool NpuServer::CsgEnableAutoConnect(int opt)
{
    ROS_WARN("CsgEnableAutoConnect() TODO opt: %d", opt);
    return true;
}

bool NpuServer::CsgDoConnectOnce()
{
    if (npu_state_ == NAVI_STATE) {
        StopNavi();
        p_start_csg_node_ = new boost::thread(boost::bind(&NpuServer::StartCsgNode, this));
        return true;
    } else {
        p_start_csg_node_ = new boost::thread(boost::bind(&NpuServer::StartCsgNode, this));
        return true;
    }
}

void NpuServer::StartCsgNode()
{
    is_started_csg = true;
    ROS_WARN("StartCsgNode() TODO");
    stringstream ss;
    ss << "bash /npu.x86_64/script/csg_run.sh";
    system(ss.str().c_str());
    ROS_WARN("StartCsgNode() TODO");
}
}//namespce
