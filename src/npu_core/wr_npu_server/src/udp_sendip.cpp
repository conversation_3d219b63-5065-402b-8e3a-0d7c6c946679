#include <iostream>
#include <stdio.h>
#include <sys/socket.h>
#include <unistd.h>
#include <sys/types.h>
#include <netdb.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <string.h>
#include <sys/ioctl.h>
#include <linux/if.h>

#include <Ice/Ice.h>
#include "npu_icei.h"
#include "udp_sendip.h"

using namespace std;

#ifdef LM_INFO
#  undef LM_INFO
#endif

#ifdef LM_ERROR
#  undef LM_ERROR
#endif

#define LM_INFO(fmt, arg...) printf("[UDP_SENDER] " fmt "\n", ##arg)
#define LM_ERROR(fmt, arg...) printf("[UDP_SENDER] " fmt "\n", ##arg)

UdpIpPublisher::UdpIpPublisher(const std::string &version_file)
{
    version_file_ = version_file;
    npu_sys_version_ = "NULL";
    npu_api_version_ = "NULL";
    ip_addr_ = "";
    LoadConfigFile();
    extra_msg_ = "API<" + npu_api_version_ + ">" + " SYS{" + npu_sys_version_ + "}";
#if WR_LOCK == true
    extra_msg_ = extra_msg_ + " LOCKED";
#else
    extra_msg_ = extra_msg_ + " UNLOCKED";
#endif
    return;
}

UdpIpPublisher::~UdpIpPublisher()
{
    return;
}

int UdpIpPublisher::BroadcastIpByUdp()
{
    setvbuf(stdout, NULL, _IONBF, 0);
    fflush(stdout);
    int sock = -1;
    if ((sock = socket(AF_INET, SOCK_DGRAM, 0)) == -1) {
        LM_INFO("socket error");
        return 0;
    }
    const int opt = 1;
    int nb = 0;
    nb = setsockopt(sock, SOL_SOCKET, SO_BROADCAST, (char *)&opt, sizeof(opt));
    if(nb == -1) {
        LM_INFO("set socket error...");
        return 0;
    }
    struct sockaddr_in addrto;
    bzero(&addrto, sizeof(struct sockaddr_in));
    addrto.sin_family=AF_INET;
    addrto.sin_addr.s_addr=htonl(INADDR_BROADCAST);
    addrto.sin_port=htons(5188);
    int nlen=sizeof(addrto);
    int cnt = 0;
    char msg[128];
    while(1) {
        sleep(1);
        if (ip_addr_ == "null") {
            LM_INFO("ip_addr is null, exit.");
            return 0;
        } else if (ip_addr_ == "" || ip_addr_ == "auto") {
            ipaddress.ip =  Getlocalhostip();
            unsigned char *smsg = ipaddress.ipchar;
            char str_ip[16];
            sprintf(str_ip,"%d.%d.%d.%d", smsg[0],smsg[1],smsg[2],smsg[3]);
            sprintf(msg, "NPU IP[%s]%s<SN:%06d>", str_ip, extra_msg_.c_str(), cnt);
        } else {
            sprintf(msg, "NPU IP[%s]%s<SN:%06d>", ip_addr_.c_str(), extra_msg_.c_str(), cnt);
        }
        cnt += 1;
        int ret = sendto(sock, msg, strlen(msg), 0, (sockaddr*)&addrto, nlen);
        if( ret < 0 ) {
            LM_INFO("Udp send error(%d)....", ret);
        }
    }
    LM_INFO("Udp Send thread break!");
}

void* _UdpSendIpData(void *para)
{
    UdpIpPublisher *p_udp_send_ip = (UdpIpPublisher *)para;
    p_udp_send_ip->BroadcastIpByUdp();
    pthread_detach(pthread_self());
    pthread_exit(0);
    return NULL;
}

long UdpIpPublisher::Getlocalhostip()
{
    int MAXINTERFACES=16;
    long ip;
    int fd, intrface ;
    //if.h
    struct ifreq buf[MAXINTERFACES];
    struct ifconf ifc; ///if.h
    ip = -1;
    //socket.h
    if ((fd = socket (AF_INET, SOCK_DGRAM, 0)) < 0) {
        return -1;
    }
    ifc.ifc_len = sizeof buf;
    ifc.ifc_buf = (caddr_t) buf;
    //ioctl.h
    if (ioctl (fd, SIOCGIFCONF, (char *) &ifc)) {
        close(fd);
        return -2;
    }
    intrface = ifc.ifc_len / sizeof (struct ifreq);
    while (intrface-- > 0) {
        if (!(ioctl (fd, SIOCGIFADDR, (char *) &buf[intrface]))) {
            ip = inet_addr( inet_ntoa( ((struct sockaddr_in*)(&buf[intrface].ifr_addr))->sin_addr) );//types
            break;
        }
    }
    close (fd);
    return ip;
}

int UdpIpPublisher::UdpSendIpData()
{
    if(pthread_create(&m_send_threadid, NULL, _UdpSendIpData,this) != 0) {
        LM_INFO("creat Send_Thread failed!");
        return -1;
    }
    return 0;
}

int UdpIpPublisher::LoadConfigFile()
{
    if (version_file_ == "null") {
        return -1;
    }
    ifstream infile(version_file_, ios::in);
    if(!infile) {
        LM_ERROR("open file error!");
        return -2;
    }
    LM_INFO("open file success!");
    infile >> npu_sys_version_;
    infile >> npu_api_version_;
    infile >> ip_addr_;
    LM_INFO("ip_addr_ is %s", ip_addr_.c_str());
    infile.close();
    return 0;
}

int main(int argc, char* argv[])
{
    UdpIpPublisher sendip(argv[1]);
    sendip.BroadcastIpByUdp();
}
