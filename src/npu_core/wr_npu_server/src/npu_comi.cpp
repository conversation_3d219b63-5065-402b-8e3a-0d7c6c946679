#include "npu_comi.h"

#define COMI_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("COMI::", "NpuComI::%s() " fmt, __FUNCTION__,##arg)
#define COMI_INFO(fmt, arg...)  ROS_INFO_NAMED("COMI::",  "NpuComI::%s() " fmt, __FUNCTION__,##arg)
#define COMI_WARN(fmt, arg...)  ROS_WARN_NAMED("COMI::",  "NpuComI::%s() " fmt, __FUNCTION__,##arg)
#define COMI_ERROR(fmt, arg...) ROS_ERROR_NAMED("COMI::", "NpuComI::%s() " fmt, __FUNCTION__,##arg)
#define COMI_FATAL(fmt, arg...) ROS_FATAL_NAMED("COMI::", "NpuComI::%s() " fmt, __FUNCTION__,##arg)

namespace wizrobo {

NpuComI::NpuComI(std::string port, int baudrate, int databit, std::string parity, icom_driver::ComDriverType com_type):
    p_com_(NULL)
  , p_func_map_(NULL)
  , loop_frequence_(30.0)

{
    ros::NodeHandle n;
    n.param<float>("/npu_sever/comi/loop_frequence",loop_frequence_,30.0);
    icom_driver::ComDriverCreator creator;
    com_type_ = com_type;
    p_com_ = creator.creator(com_type_);
    p_com_->SetPortParam(port, baudrate, databit, parity);
    p_func_map_ = new funcmap::FuncMap();
    p_func_map_->InitFuncMap();

    p_com_->SetFuncPtr(p_func_map_);
}

NpuComI::~NpuComI()
{
    if(p_com_ != NULL){
        delete p_com_;
    }
    if(p_func_map_ != NULL){
        delete p_func_map_;
    }
}

void NpuComI::SetNpuServer(wizrobo_npu::NpuServer* ptr)
{
    p_func_map_->SetNpuServer(ptr);
}

void NpuComI::Run()
{
    ros::Rate r(loop_frequence_);

    while(ros::ok()){
        p_com_->Run();
        ros::spinOnce();
        r.sleep();
    }

    return;
}

}//namespace wizrobo
