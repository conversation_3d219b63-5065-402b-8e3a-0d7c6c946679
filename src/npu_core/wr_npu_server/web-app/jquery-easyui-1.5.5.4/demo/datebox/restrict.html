<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Restrict Date Range in DateBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Restrict Date Range in DateBox</h2>
	<p>This example shows how to restrict the user to select only ten days from now.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input id="dd" label="Select Date:" labelPosition="top" style="width:100%;">
		</div>
	</div>
	<script>
		$(function(){
			$('#dd').datebox().datebox('calendar').calendar({
				validator: function(date){
					var now = new Date();
					var d1 = new Date(now.getFullYear(), now.getMonth(), now.getDate());
					var d2 = new Date(now.getFullYear(), now.getMonth(), now.getDate()+10);
					return d1<=date && date<=d2;
				}
			});
		});
	</script>
</body>
</html>