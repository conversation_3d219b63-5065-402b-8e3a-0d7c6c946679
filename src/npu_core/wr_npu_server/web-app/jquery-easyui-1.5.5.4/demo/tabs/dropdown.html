<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Tabs with DropDown - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Tabs with DropDown</h2>
	<p>This sample shows how to add a dropdown menu over a tab strip.</p>
	<div style="margin:20px 0;"></div>
	<div id="tt" style="width:700px;height:250px">
		<div title="About" style="padding:10px">
			<p style="font-size:14px">jQuery EasyUI framework helps you build your web pages easily.</p>
			<ul>
				<li>easyui is a collection of user-interface plugin based on jQuery.</li>
				<li>easyui provides essential functionality for building modem, interactive, javascript applications.</li>
				<li>using easyui you don't need to write many javascript code, you usually defines user-interface by writing some HTML markup.</li>
				<li>complete framework for HTML5 web page.</li>
				<li>easyui save your time and scales while developing your products.</li>
				<li>easyui is very easy but powerful.</li>
			</ul>
		</div>
		<div title="My Documents" style="padding:10px">
			<ul class="easyui-tree" data-options="url:'tree_data1.json',method:'get',animate:true"></ul>
		</div>
		<div title="Help" style="padding:10px">
			This is the help content.
		</div>
	</div>
	<div id="mm">
		<div>Welcome</div>
		<div>Help Contents</div>
		<div data-options="iconCls:'icon-search'">Search</div>
		<div>Dynamic Help</div>
	</div>
	
	<script>
		$(function(){
			var p = $('#tt').tabs().tabs('tabs')[2];
			var mb = p.panel('options').tab.find('a.tabs-inner');
			mb.menubutton({
				menu:'#mm',
				iconCls:'icon-help'
			}).click(function(){
				$('#tt').tabs('select',2);
			});
		});
	</script>
</body>
</html>