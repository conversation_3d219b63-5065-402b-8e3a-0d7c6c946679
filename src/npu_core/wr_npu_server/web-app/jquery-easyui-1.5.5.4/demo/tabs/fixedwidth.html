<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Fixed Tab Width - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Fixed Tab Width</h2>
	<p>The tab strips have fixed width and height.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-tabs" data-options="tabWidth:112" style="width:700px;height:250px">
		<div title="Home" style="padding:10px">
			<p>Home Content.</p>
		</div>
		<div title="Maps" style="padding:10px">
			<p>Maps Content.</p>
		</div>
		<div title="Journal" style="padding:10px">
			<p>Journal Content.</p>
		</div>
		<div title="History" style="padding:10px">
			<p>History Content.</p>
		</div>
		<div title="References" style="padding:10px">
			<p>References Content.</p>
		</div>
		<div title="Contact" data-options="tabWidth:110" style="padding:10px">
			<p>Contact Content.</p>
		</div>
	</div>
</body>
</html>