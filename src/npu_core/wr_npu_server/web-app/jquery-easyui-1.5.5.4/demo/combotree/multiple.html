<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Multiple ComboTree - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Multiple ComboTree</h2>
	<p>Click the right arrow button to show the tree panel and select multiple nodes.</p>
	<div style="margin:20px 0">
		<span>Cascade Check: </span>
		<input type="checkbox" checked onclick="$('#cc').combotree({cascadeCheck:$(this).is(':checked')})">
	</div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input id="cc" class="easyui-combotree" data-options="url:'tree_data1.json',method:'get',label:'Select Nodes:',labelPosition:'top',multiple:true,value:[122,124]" style="width:100%">
		</div>
	</div>

</body>
</html>