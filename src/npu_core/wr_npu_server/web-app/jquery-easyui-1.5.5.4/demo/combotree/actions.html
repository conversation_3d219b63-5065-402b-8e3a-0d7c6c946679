<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>ComboTree Actions - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>ComboTree Actions</h2>
	<p>Click the buttons below to perform actions</p>
	<div style="margin:20px 0">
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="getValue()">GetValue</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="setValue()">SetValue</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="disable()">Disable</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="enable()">Enable</a>
	</div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input id="cc" class="easyui-combotree" data-options="url:'tree_data1.json',method:'get',label:'Select Node:',labelPosition:'top'" style="width:100%">
		</div>
	</div>
	<script type="text/javascript">
		function getValue(){
			var val = $('#cc').combotree('getValue');
			alert(val);
		}
		function setValue(){
			$('#cc').combotree('setValue', '122');
		}
		function disable(){
			$('#cc').combotree('disable');
		}
		function enable(){
			$('#cc').combotree('enable');
		}
	</script>

</body>
</html>