<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>TextBox Size - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>TextBox Size</h2>
	<p>The textbox can vary in size.</p>
	<div style="margin:20px 0 40px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-textbox" data-options="iconCls:'icon-search',iconWidth:28,prompt:'Search small...'" style="width:100%;height:22px;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-textbox" data-options="iconCls:'icon-search',iconWidth:28,prompt:'Search large...'" style="width:100%;height:26px;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-textbox" data-options="iconCls:'icon-search',iconWidth:28,prompt:'Search big...'" style="width:100%;height:32px;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-textbox" data-options="iconCls:'icon-search',iconWidth:28,prompt:'Search huge...'" style="width:100%;height:40px;">
		</div>
	</div>
</body>
</html>