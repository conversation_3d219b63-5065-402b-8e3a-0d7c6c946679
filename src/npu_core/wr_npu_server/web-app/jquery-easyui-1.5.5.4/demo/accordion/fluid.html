<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Fluid Accordion - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Fluid Accordion</h2>
	<p>This example shows how to set the width of accordion to a percentage of its parent container.</p>
	<div style="margin:20px 0 10px 0;"></div>
	<div class="easyui-accordion" style="width:100%;height:180px;">
		<div title="About" data-options="iconCls:'icon-ok'" style="overflow:auto;padding:10px;">
			<p>width: 100%</p>
		</div>
		<div title="Help" data-options="iconCls:'icon-help',href:'_content.html'" style="padding:10px;">
		</div>
	</div>
		<div style="margin:20px 0 10px 0;"></div>

	<div class="easyui-accordion" style="width:50%;height:180px;">
		<div title="About" data-options="iconCls:'icon-ok'" style="overflow:auto;padding:10px;">
			<p>width: 50%</p>
		</div>
		<div title="Help" data-options="iconCls:'icon-help',href:'_content.html'" style="padding:10px;">
		</div>
	</div>
</body>
</html>