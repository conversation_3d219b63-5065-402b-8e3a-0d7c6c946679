<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Tooltip Position - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Tooltip Position</h2>
	<p>Click the drop-down list below to change where the tooltip appears.</p>
	<div style="margin:20px 0;"></div>
	<span>Select position:</span>
	<select onchange="changePosition(this.value)">
		<option value="bottom">Bottom</option>
		<option value="top">Top</option>
		<option value="left">Left</option>
		<option value="right">Right</option>
	</select>
	<div style="padding:10px 200px">
	<div id="pp" class="easyui-panel easyui-tooltip" title="This is the tooltip message." style="width:100px;padding:5px">Hover Me</div>
	</div>
	<script type="text/javascript">
		function changePosition(pos){
			$('#pp').tooltip({
				position: pos
			});
		}
	</script>
</body>
</html>