<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Window Border Style - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../../themes/color.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Window Border Style</h2>
	<p>This example shows how to set the different border style.</p>
	<div style="margin:20px 0;">
	</div>
	<div style="position:relative;overflow:auto;height:370px;border:1px solid #ddd">
		<div class="easyui-window" title="Window" data-options="width:300,height:150,left:10,top:10,inline:true">
			<p class="w-content">Window content</p>
		</div>
		<div class="easyui-window" title="Window" data-options="width:300,height:150,left:320,top:10,inline:true,cls:'c1'">
			<p class="w-content">Window content</p>
		</div>
		<div class="easyui-window" title="Window" data-options="width:300,height:150,left:630,top:10,inline:true,cls:'c2'">
			<p class="w-content">Window content</p>
		</div>
		<div class="easyui-window" title="Window" data-options="width:300,height:150,left:10,top:180,inline:true,border:'thin',cls:'c5'">
			<p class="w-content">Window content</p>
		</div>
		<div class="easyui-window" title="Window" data-options="width:300,height:150,left:320,top:180,inline:true,border:'thin',cls:'c6'">
			<p class="w-content">Window content</p>
		</div>
		<div class="easyui-window" title="Window" data-options="width:300,height:150,left:630,top:180,inline:true,border:'thin',cls:'c7'">
			<p class="w-content">Window content</p>
		</div>
	</div>

	<script type="text/javascript">
		$('.easyui-window').window({
			collapsible: false,
			minimizable: false,
			maximizable: false,
			closable: false
		});
	</script>
	<style type="text/css" scoped="scoped">
		.w-content{
			padding:5px 10px;
		}
	</style>
</body>
</html>