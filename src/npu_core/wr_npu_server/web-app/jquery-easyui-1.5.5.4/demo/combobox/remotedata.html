<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Binding to Remote Data - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Binding to Remote Data</h2>
	<p>The ComboBox is bound to a remote data.</p>
	<div style="margin:20px 0"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-combobox" name="language" style="width:100%;" data-options="
					url:'combobox_data1.json',
					method:'get',
					valueField:'id',
					textField:'text',
					panelHeight:'auto',
					label: 'Language:',
					labelPosition: 'top'
					">
		</div>
	</div>
</body>
</html>