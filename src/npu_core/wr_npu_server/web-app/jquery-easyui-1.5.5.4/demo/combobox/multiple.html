<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Multiple Select - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Load Dynamic ComboBox Data</h2>
	<p>Drop down the panel and select multiple items.</p>
	<div style="margin:20px 0"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-combobox" name="language[]" style="width:100%;" data-options="
					url:'combobox_data1.json',
					method:'get',
					valueField:'id',
					textField:'text',
					value:[1,3],
					multiple:true,
					panelHeight:'auto',
					label: 'Language:',
					labelPosition: 'top'
					">
		</div>
	</div>
</body>
</html>