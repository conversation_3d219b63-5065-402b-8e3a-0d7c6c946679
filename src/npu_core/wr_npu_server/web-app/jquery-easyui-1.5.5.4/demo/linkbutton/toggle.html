<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Toggle Button - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Toggle Button</h2>
	<p>Click the button below to switch its selected state.</p>
	<div style="margin:10px 0 40px 0;"></div>
	<div style="padding:5px 0;">
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-add',toggle:true">Add</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-remove',toggle:true">Remove</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-save',toggle:true,selected:true">Save</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-cut',toggle:true">Cut</a>
		<a href="#" class="easyui-linkbutton" data-options="toggle:true">Text Button</a>
	</div>
	
</body>
</html>