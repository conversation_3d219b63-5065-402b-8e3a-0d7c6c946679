<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Multiple Selection DataList - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Multiple Selection DataList</h2>
	<p>The multiple selection allows the user to select multiple items in a datalist.</p>
	<div style="margin:20px 0"></div>
	<div class="easyui-datalist" title="Multiple Selection DataList" style="width:400px;height:250px" data-options="
			url: 'datalist_data1.json',
			method: 'get',
			singleSelect: false
			">
	</div>
</body>
</html>