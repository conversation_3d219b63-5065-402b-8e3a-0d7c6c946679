<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Custom ValidateBox Tooltip - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Custom ValidateBox Tooltip</h2>
	<p>This sample shows how to display another tooltip message on a valid textbox.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" title="Register" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<label for="username" class="label-top">User Name:</label>
			<input id="username" class="easyui-validatebox tb" data-options="prompt:'Enter User Name.',required:true,validType:'length[3,10]'">
		</div>
		<div style="margin-bottom:20px">
			<label for="email" class="label-top">Email:</label>
			<input id="email" class="easyui-validatebox tb" data-options="prompt:'Enter a valid email.',required:true,validType:'email'">
		</div>
		<div style="margin-bottom:20px">
			<label for="url" class="label-top">Url:</label>
			<input id="url" class="easyui-validatebox tb" data-options="prompt:'Enter your URL.',required:true,validType:'url'">
		</div>
		<div style="margin-bottom:20px">
			<label for="phone" class="label-top">Phone:</label>
			<input id="phone" class="easyui-validatebox tb" data-options="prompt:'Enter your phone number.',required:true">
		</div>
	</div>
	<style scoped="scoped">
		.tb{
			width:100%;
			margin:0;
			padding:5px 4px;
			border:1px solid #ccc;
			box-sizing:border-box;
		}
	</style>
	<script>
		$(function(){
			$('input.easyui-validatebox').validatebox({
				validateOnCreate: false,
				err: function(target, message, action){
					var opts = $(target).validatebox('options');
					message = message || opts.prompt;
					$.fn.validatebox.defaults.err(target, message, action);
				}
			});
		});
	</script>
</body>
</html>