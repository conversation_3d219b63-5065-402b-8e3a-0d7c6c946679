<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>TreeGrid with Footer - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>TreeGrid with Footer</h2>
	<p>Show summary information on TreeGrid footer.</p>
	<div style="margin:20px 0;"></div>
	<table id="tg"></table>
	<script type="text/javascript">
		$(function(){
			$('#tg').treegrid({
				title:'TreeGrid with Footer',
				iconCls:'icon-ok',
				width:700,
				height:250,
				rownumbers: true,
				animate:true,
				collapsible:true,
				fitColumns:true,
				url:'treegrid_data2.json',
				method: 'get',
				idField:'id',
				treeField:'name',
				showFooter:true,
				columns:[[
	                {title:'Task Name',field:'name',width:180},
					{field:'persons',title:'Persons',width:60,align:'right'},
					{field:'begin',title:'Begin Date',width:80},
					{field:'end',title:'End Date',width:80},
					{field:'progress',title:'Progress',width:120,
					    formatter:function(value){
					    	if (value){
						    	var s = '<div style="width:100%;border:1px solid #ccc">' +
						    			'<div style="width:' + value + '%;background:#cc0000;color:#fff">' + value + '%' + '</div>'
						    			'</div>';
						    	return s;
					    	} else {
						    	return '';
					    	}
				    	}
					}
				]]
			});
		})
	</script>
</body>
</html>