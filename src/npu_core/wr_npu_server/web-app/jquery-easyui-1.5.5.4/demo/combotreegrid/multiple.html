<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Multiple ComboTreeGrid - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Multiple ComboTreeGrid</h2>
	<p>Click the right arrow button to show the TreeGrid and select items.</p>
	<div style="margin:20px 0"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-combotreegrid" data-options="
					width:'100%',
					panelWidth:500,
					label:'Select Items:',
					labelPosition:'top',
					url:'treegrid_data1.json',
					idField:'id',
					treeField:'name',
					multiple:true,
					value:['211','31'],
					columns:[[
						{field:'name',title:'Name',width:200},
						{field:'size',title:'Size',width:100},
						{field:'date',title:'Date',width:100}
					]]">
		</div>
	</div>
</body>
</html>