<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Switch Button - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/color.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/icon.css">  
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script>
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script>
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<span class="m-title">Switch Button</span>
			</div>
		</header>
		<ul class="m-list">
			<li>
				<span>Network</span>
				<div class="m-right"><input class="easyui-switchbutton" checked></div>
			</li>
			<li>
				<span>Bluetooth</span>
				<div class="m-right"><input class="easyui-switchbutton" checked></div>
			</li>
			<li>
				<span>Sent mail</span>
				<div class="m-right"><input class="easyui-switchbutton" data-options="checked:false"></div>
			</li>
			<li>
				<a href="javascript:void(0)">Storage...</a>
			</li>
			<li>
				<a href="javascript:void(0)">More...</a>
			</li>
		</ul>
	</div>
</body>	
</html>
