<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Group LinkButton - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/icon.css">  
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script> 
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script> 
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<div class="m-title">Button Group</div>
			</div>
		</header>
		<div style="text-align:center;padding:10px">
			<span class="m-buttongroup">
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="toggle:true,group:'g1',selected:true" onclick="show1('#m-modem')" style="width:80px;height:30px">Modem</a> 
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="toggle:true,group:'g1'" onclick="show1('#m-scanner')" style="width:80px;height:30px">Scanner</a> 
				<a href="javascript:void(0)" class="easyui-linkbutton" data-options="toggle:true,group:'g1'" onclick="show1('#m-tablet')" style="width:80px;height:30px">Tablet</a> 
			</span>
		</div>
		<div id="items" style="padding:10px 20px;">
			<div id="m-modem" class="m-item">
				<img src="../images/modem.png"/>
				<p>A modem (modulator-demodulator) is a device that modulates an analog carrier signal to encode digital information, and also demodulates such a carrier signal to decode the transmitted information.</p>
			</div>
			<div id="m-scanner" class="m-item" style="display:none">
				<img src="../images/scanner.png"/>
				<p>In computing, an image scanner—often abbreviated to just scanner—is a device that optically scans images, printed text, handwriting, or an object, and converts it to a digital image.</p>
			</div>
			<div id="m-tablet" class="m-item" style="display:none">
				<img src="../images/tablet.png"/>
				<p>A tablet computer, or simply tablet, is a one-piece mobile computer. Devices typically have a touchscreen, with finger or stylus gestures replacing the conventional computer mouse.</p>
			</div>
		</div>
	</div>

	<script>
		function show1(id){
			$('div.m-item').hide();
			$(id).show();
		}
	</script>
	<style scoped>
		p{
			line-height:150%;
		}
		#items img{
			margin:0 10px 10px 0;
			float:left;
		}
	</style>
</body>	
</html>
