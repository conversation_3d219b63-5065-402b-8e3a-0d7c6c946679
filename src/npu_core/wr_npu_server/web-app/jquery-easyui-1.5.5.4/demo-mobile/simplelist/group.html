<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Group List - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/icon.css">  
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script> 
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script> 
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<span class="m-title">Group List</span>
			</div>
		</header>
		<ul class="m-list">
			<li class="m-list-group">FL-DSH-01</li>
			<li><a href="javascript:void(0)" onclick="openit(this)">Large</a></li>
			<li><a href="javascript:void(0)" onclick="openit(this)">Spotted Adult Female</a></li>
			<li><a href="javascript:void(0)" onclick="openit(this)">Venomless</a></li>
			<li><a href="javascript:void(0)" onclick="openit(this)">Rattleless</a></li>
			<li><a href="javascript:void(0)" onclick="openit(this)">Green Adult</a></li>
			<li><a href="javascript:void(0)" onclick="openit(this)">Tailless</a></li>
			<li class="m-list-group">FL-DSH-02</li>
			<li><a href="javascript:void(0)" onclick="openit(this)">With tail</a></li>
			<li><a href="javascript:void(0)" onclick="openit(this)">Adult Female</a></li>
		</ul>
	</div>
	<div id="p2" class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<span id="p2-title" class="m-title">Detail</span>
                <div class="m-left">
                    <a href="javascript:void(0)" class="easyui-linkbutton m-back" plain="true" outline="true" onclick="$.mobile.back()">Back</a>
                </div>
			</div>
		</header>
        <div style="margin:50px 0 0;text-align:center">
            <a href="javascript:void(0)" class="easyui-linkbutton" style="width:100px;height:30px" onclick="$.mobile.back()">Go Back</a>
        </div>
	</div>
	<script type="text/javascript">
		function openit(target){
			var text = $(target).text();
			$('#p2-title').html(text);
			$.mobile.go('#p2');
		}
	</script>
</body>	
</html>
