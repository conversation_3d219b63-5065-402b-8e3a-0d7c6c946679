#! /bin/bash
## [2017-03-06] v1.1 by lawrence.han
echo "$(tput setaf 1)Stop All...$(tput sgr 0)"

[ -d "/tmp/npu_script" ] && [ -f "/tmp/npu_script/start_slam.pid" ] && kill -INT `cat /tmp/npu_script/start_slam.pid`
[ -d "/tmp/npu_script" ] && [ -f "/tmp/npu_script/start_navi.pid" ] && kill -INT `cat /tmp/npu_script/start_navi.pid`
[ -d "/tmp/npu_script" ] && [ -f "/tmp/npu_script/start_track.pid" ] && kill -INT `cat /tmp/npu_script/start_track.pid`
