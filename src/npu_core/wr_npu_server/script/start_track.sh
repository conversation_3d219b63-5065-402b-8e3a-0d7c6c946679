#!/bin/bash
## [2017-03-06] v1.1 by lawrence.han
echo "$(tput setaf 2)Start Track...$(tput sgr 0)"

## kill other started functions
. `rospack find wr_npu_server`/script/stop_all.sh

## load map 
str=`cat $NPU_CONFIG_FILE | grep MAP_ID` && export MAP_ID=${str#*=}

## start track and record pid
[ -d "/tmp/npu_script" ] || mkdir /tmp/npu_script
roslaunch --pid=/tmp/npu_script/start_track.pid wr_${CLIENT_ID} ${MOBILE_BASE_ID}_${SENSOR_TYPE}_track.launch
