#ifndef NPU_HTTP_H
#define NPU_HTTP_H
#include "ros/ros.h"
#include "std_msgs/String.h"
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <geometry_msgs/Twist.h>
#include <nav_msgs/OccupancyGrid.h>
#include <tf/transform_listener.h>

#include <IceUtil/IceUtil.h>
#include <Ice/Ice.h>
#include "npu_server.h"

#include "server_http.hpp"

// Added for the json-example
#define BOOST_SPIRIT_THREADSAFE
#include <boost/property_tree/json_parser.hpp>
#include <boost/property_tree/ptree.hpp>

// Added for the default_resource example
#include <algorithm>
#include <boost/filesystem.hpp>
#include <fstream>
#include <vector>
#include <memory>
#if __cplusplus < 201103L
#include <bits/shared_ptr.h>
#endif

#include "client_manager.hpp"

namespace wizrobo_npu {

using namespace std;
using namespace boost::property_tree;
using HttpServer = SimpleWeb::Server<SimpleWeb::HTTP>;

typedef std::shared_ptr<HttpServer::Response> ResponsePtr;
typedef std::shared_ptr<HttpServer::Request> RequestPtr;
typedef std::map<std::string, std::string> ParamMap;

typedef enum ResponseType
{
    response_success,
    response_fail,
    response_error,
    response_not_found
}ResponseType;

class NpuHttpI : public NpuClientManagerMixin<RequestPtr>
{
private:
    HttpServer http_server;
    NpuServer *p_npu_server;
    std::string slam_map_name;
    std::string current_map_name;
    char *p_png_data;
    int pnd_data_length;
    std::string pnd_map_name;

public:
    NpuHttpI();
    void ParseQueryString(std::string& query_string, ParamMap& param_map);
    ClientAddress GetClientAddress(RequestPtr request);
    void InitHttpServerUrl();
    void SetNpuServer(NpuServer* ptr);
    void SetHttpServerPort(int port);
    void GetTypicalResponse(ResponsePtr response, ResponseType type, const char* message);
    void GetTypicalResponse(ResponsePtr response, ResponseType type, std::string& message);
    void GetTypicalResponse(ResponsePtr response, ResponseType type, ptree& data);
    void Run();

    void PostString(ResponsePtr response, RequestPtr request);
    void GetHttpInfo(ResponsePtr response, RequestPtr request);

    void RealTimeDataLaserRaw(ResponsePtr response, RequestPtr request);
    void RealTimeDataLaserPhit(ResponsePtr response, RequestPtr request);
    void RealTimeDataUltrasonicRaw(ResponsePtr response, RequestPtr request);
    void RealTimeDataUltrasonicPhit(ResponsePtr response, RequestPtr request);
    void RealTimeDataProtector(ResponsePtr response, RequestPtr request);
    void RealTimeDataMobileData(ResponsePtr response, RequestPtr request);
    void RealTimeDataNonMapData(ResponsePtr response, RequestPtr request);
    void DataDeviceStatus(ResponsePtr response, RequestPtr request);
    void RealTimeDataFootprint(ResponsePtr response, RequestPtr request);
    void DataPositions(ResponsePtr response, RequestPtr request);
    void CmdAddPosition(ResponsePtr response, RequestPtr request);
    void CmdDeletePosition(ResponsePtr response, RequestPtr request);
    void CmdRenamePosition(ResponsePtr response, RequestPtr request);
    void CmdStartScanMap(ResponsePtr response, RequestPtr request);
    void CmdStopScanMap(ResponsePtr response, RequestPtr request);
    void CmdCancelScanMap(ResponsePtr response, RequestPtr request);
    void CmdAsyncStopScanMap(ResponsePtr response, RequestPtr request);
    void CmdIsStopScanFinished(ResponsePtr response, RequestPtr request);
    void DataMapPng(ResponsePtr response, RequestPtr request);
    void DataMaps(ResponsePtr response, RequestPtr request);
    void CmdDeleteMap(ResponsePtr response, RequestPtr request);
    void CmdRenameMap(ResponsePtr response, RequestPtr request);
    void CmdLoadMap(ResponsePtr response, RequestPtr request);
    void CmdInitializeDirectly(ResponsePtr response, RequestPtr request);
    void CmdInitialize(ResponsePtr response, RequestPtr request);
    void CmdInitializeCustomized(ResponsePtr response, RequestPtr request);
    void CmdIsInitializeFinished(ResponsePtr response, RequestPtr request);
    void RealTimeDataCurrentInitializeStatus(ResponsePtr response, RequestPtr request);
    void RealTimeDataPosition(ResponsePtr response, RequestPtr request);
    void RealTimeDataGps(ResponsePtr response, RequestPtr request);
    void CmdPositionNavigate(ResponsePtr response, RequestPtr request);
    void CmdNavigate(ResponsePtr response, RequestPtr request);
    void CmdPauseNavigate(ResponsePtr response, RequestPtr request);
    void CmdResumeNavigate(ResponsePtr response, RequestPtr request);
    void CmdCancelNavigate(ResponsePtr response, RequestPtr request);
    void RealTimeDataNavigationPath(ResponsePtr response, RequestPtr request);
    void DataNavigationPath(ResponsePtr response, RequestPtr request);
    void DataPaths(ResponsePtr response, RequestPtr request);
    void CmdDeletePath(ResponsePtr response, RequestPtr request);
    void CmdRenamePath(ResponsePtr response, RequestPtr request);
    void CmdStartRecordPath(ResponsePtr response, RequestPtr request);
    void CmdStopRecordPath(ResponsePtr response, RequestPtr request);
    void CmdCancelRecordPath(ResponsePtr response, RequestPtr request);
    void CmdStartRecordArea(ResponsePtr response, RequestPtr request);
    void CmdStopRecordArea(ResponsePtr response, RequestPtr request);
    void CmdCancelRecordArea(ResponsePtr response, RequestPtr request);
    void CmdAddPathAction(ResponsePtr response, RequestPtr request);
    void DataPathDataList(ResponsePtr response, RequestPtr request);
    void CmdGenerateGraphPath(ResponsePtr response, RequestPtr request);
    void CmdVerifyGraphLine(ResponsePtr response, RequestPtr request);
    void CmdVerifyGraphPath(ResponsePtr response, RequestPtr request);
    void DataActionList(ResponsePtr response, RequestPtr request);
    void CmdUpdateGraphPath(ResponsePtr response, RequestPtr request);
    void CmdVerifyGraph(ResponsePtr response, RequestPtr request);
    void DataGraphPaths(ResponsePtr response, RequestPtr request);
    void CmdDeleteGraphPath(ResponsePtr response, RequestPtr request);
    void CmdRenameGraphPath(ResponsePtr response, RequestPtr request);
    void RealTimeDataWorkStatus(ResponsePtr response, RequestPtr request);
    void CmdSaveTaskQueue(ResponsePtr response, RequestPtr request);
    void DataTaskQueues(ResponsePtr response, RequestPtr request);
    void CmdDeleteTaskQueue(ResponsePtr response, RequestPtr request);
    void CmdStartTaskQueue(ResponsePtr response, RequestPtr request);
    void CmdStopTaskQueue(ResponsePtr response, RequestPtr request);
    void CmdPauseTaskQueue(ResponsePtr response, RequestPtr request);
    void CmdResumeTaskQueue(ResponsePtr response, RequestPtr request);
    void CmdStopCurrentTask(ResponsePtr response, RequestPtr request);
    void CmdIsTaskQueueFinished(ResponsePtr response, RequestPtr request);
    void DataVirtual_obstacles(ResponsePtr response, RequestPtr request);
    void CmdUpdateVirtualObstacles(ResponsePtr response, RequestPtr request);
    void CmdPositionAddPosition(ResponsePtr response, RequestPtr request);
    void CmdMove(ResponsePtr response, RequestPtr request);
    void CmdRotate(ResponsePtr response, RequestPtr request);
    void CmdIsRotateFinished(ResponsePtr response, RequestPtr request);
    void CmdPing(ResponsePtr response, RequestPtr request);
    void CmdSetQHeart(ResponsePtr response, RequestPtr request);
    void CmdStartCharge(ResponsePtr response, RequestPtr request);
    void CmdStopCharge(ResponsePtr response, RequestPtr request);
    void DataChargeStatus(ResponsePtr response, RequestPtr request);
    void CmdPowerOff(ResponsePtr response, RequestPtr request);
    void CmdUpdateLaserParam(ResponsePtr response, RequestPtr request);
    void InfoVersion(ResponsePtr response, RequestPtr request);
    void CmdSetNavigationSpeedLevel(ResponsePtr response, RequestPtr request);
    void CmdSetSpeedLevel(ResponsePtr response, RequestPtr request);

    void GetMainPage(ResponsePtr response, RequestPtr request);
    void GetWebFile(ResponsePtr response, RequestPtr request);
    void GetJs(ResponsePtr response, RequestPtr request);
    void GetCss(ResponsePtr response, RequestPtr request);
    void GetHtml(ResponsePtr response, RequestPtr request);
    void GetIco(ResponsePtr response, RequestPtr request);
    void GetBagData(ResponsePtr response, RequestPtr request);

    //connect
    void GetServerVersion(ResponsePtr response, RequestPtr request);
    void Connect(ResponsePtr response, RequestPtr request);
    void GetServerState(ResponsePtr response, RequestPtr request);
    void GetSystemDiagInfo(ResponsePtr response, RequestPtr request);
    void GetActionState(ResponsePtr response, RequestPtr request);
    void GetNpuState(ResponsePtr response, RequestPtr request);

    //power
    void Shutdown(ResponsePtr response, RequestPtr request);
    void Reboot(ResponsePtr response, RequestPtr request);

    // config management
    void GetConfigIdList(ResponsePtr response, RequestPtr request);
    void SelectConfig(ResponsePtr response, RequestPtr request);
    void DeleteConfig(ResponsePtr response, RequestPtr request);
    void AddConfig(ResponsePtr response, RequestPtr request);

    // npu
    void GetCoreParam(ResponsePtr response, RequestPtr request);
    void SetCoreParam(ResponsePtr response, RequestPtr request);

    // motor
    void GetMotorParam(ResponsePtr response, RequestPtr request);
    void SetMotorParam(ResponsePtr response, RequestPtr request);

    // pid
    void GetPidParam(ResponsePtr response, RequestPtr request);
    void SetPidParam(ResponsePtr response, RequestPtr request);

    // chassis
    void GetChassisParam(ResponsePtr response, RequestPtr request);
    void SetChassisParam(ResponsePtr response, RequestPtr request);

    // footprint
    void GetFootprintParam(ResponsePtr response, RequestPtr request);
    void SetFootprintParam(ResponsePtr response, RequestPtr request);

    // base
    void GetBaseParam(ResponsePtr response, RequestPtr request);
    void SetBaseParam(ResponsePtr response, RequestPtr request);

    // sensor
    void GetSensorParam(ResponsePtr response, RequestPtr request);
    void SetSensorParam(ResponsePtr response, RequestPtr request);

    // teleop
    void GetTeleopParam(ResponsePtr response, RequestPtr request);
    void SetTeleopParam(ResponsePtr response, RequestPtr request);

    // teleop
    void GetParamByPtree(ResponsePtr response, RequestPtr request);
    void SetParamByPtree(ResponsePtr response, RequestPtr request);

    // navi
    void GetNaviParam(ResponsePtr response, RequestPtr request);
    void SetNaviParam(ResponsePtr response, RequestPtr request);

    // slam
    void GetSlamParam(ResponsePtr response, RequestPtr request);
    void SetSlamParam(ResponsePtr response, RequestPtr request);

    // slave mode
    void FeedMotorEnc(ResponsePtr response, RequestPtr request);
    void FeedActMotorSpd(ResponsePtr response, RequestPtr request);

    //// motor data

    // enc
    void GetMotorEnc(ResponsePtr response, RequestPtr request);
    void ClearMotorEnc(ResponsePtr response, RequestPtr request);

    // spd
    void GetCmdMotorSpd(ResponsePtr response, RequestPtr request);
    void GetActMotorSpd(ResponsePtr response, RequestPtr request);

    //// sensor data

    // lidar
    void GetLidarScan(ResponsePtr response, RequestPtr request);
    void GetImgLidarScan(ResponsePtr response, RequestPtr request);

    // imu
    void GetImuData(ResponsePtr response, RequestPtr request);

    // sonar
    void GetSonarScan(ResponsePtr response, RequestPtr request);
    void GetImgSonarScan(ResponsePtr response, RequestPtr request);

    // infrd
    void GetInfrdScan(ResponsePtr response, RequestPtr request);
    void GetImgInfrdScan(ResponsePtr response, RequestPtr request);

    // bumper
    void GetBumperArray(ResponsePtr response, RequestPtr request);

    // battery
    void GetBatteryStatus(ResponsePtr response, RequestPtr request);

    //// manul control
    void SetManualCmd(ResponsePtr response, RequestPtr request);
    void SetManualVel(ResponsePtr response, RequestPtr request);

    //// map management

    // map
    void GetMapInfos(ResponsePtr response, RequestPtr request);
    void SetMapInfos(ResponsePtr response, RequestPtr request);
    void SelectMap(ResponsePtr response, RequestPtr request);

    // station
    void GetStations(ResponsePtr response, RequestPtr request);
    void SetStations(ResponsePtr response, RequestPtr request);
    void GetImgStations(ResponsePtr response, RequestPtr request);
    void SetImgStations(ResponsePtr response, RequestPtr request);

    // task
    void GetTaskList(ResponsePtr response, RequestPtr request);
    void ExecuteTask(ResponsePtr response, RequestPtr request);
    void SetTasks(ResponsePtr response, RequestPtr request);

    // path
    void GetPaths(ResponsePtr response, RequestPtr request);
    void SetPaths(ResponsePtr response, RequestPtr request);
    void GetImgPaths(ResponsePtr response, RequestPtr request);
    void SetImgPaths(ResponsePtr response, RequestPtr request);

    // virtual wall
    void GetVirtualWalls(ResponsePtr response, RequestPtr request);
    void SetVirtualWalls(ResponsePtr response, RequestPtr request);
    void GetImgVirtualWalls(ResponsePtr response, RequestPtr request);
    void SetImgVirtualWalls(ResponsePtr response, RequestPtr request);

    //// common runtime data

    // vel
    void GetCmdVel(ResponsePtr response, RequestPtr request);
    void GetActVel(ResponsePtr response, RequestPtr request);

    // TODEL: be replaced by GetActVel()
    void GetCurrentVel(ResponsePtr response, RequestPtr request);

    // acc
    void GetAcc(ResponsePtr response, RequestPtr request);

    // TODEL: be replaced by GetAcc
    void GetCurrentAcc(ResponsePtr response, RequestPtr request);

    // pose
    void GetCmdPose(ResponsePtr response, RequestPtr request);
    void GetCmdImgPose(ResponsePtr response, RequestPtr request);
    void GetActPose(ResponsePtr response, RequestPtr request);
    void GetActImgPose(ResponsePtr response, RequestPtr request);

    // TODEL: be replaced by GetActPose()
    void GetCurrentPose(ResponsePtr response, RequestPtr request);

    // TODEL: be replaced by GetActImgPose()
    void GetCurrentImgPose(ResponsePtr response, RequestPtr request);

    // path
    void GetGlobalPath(ResponsePtr response, RequestPtr request);
    void GetGlobalImgPath(ResponsePtr response, RequestPtr request);
    void GetLocalPath(ResponsePtr response, RequestPtr request);
    void GetLocalImgPath(ResponsePtr response, RequestPtr request);
    void GetActPath(ResponsePtr response, RequestPtr request);
    void GetActImgPath(ResponsePtr response, RequestPtr request);

    // TODEL: replaced by GetGlobalPath()
    void GetCurrentPath(ResponsePtr response, RequestPtr request);

    // TODEL: replaced by GetGlobalImgPath()
    void GetCurrentImgPath(ResponsePtr response, RequestPtr request);

    // path
    void GetMap(ResponsePtr response, RequestPtr request);
    void GetImgMap(ResponsePtr response, RequestPtr request);

    // TODEL: be replaced by GetMap()
    void GetCurrentMap(ResponsePtr response, RequestPtr request);

    // TODEL: be replaced by GetImgMap()
    void GetCurrentImgMap(ResponsePtr response, RequestPtr request);

    // footprint
    void GetFootprintVertices(ResponsePtr response, RequestPtr request);
    void GetFootprintImgVertices(ResponsePtr response, RequestPtr request);

    //// telop
    void StartTelop(ResponsePtr response, RequestPtr request);
    void StopTelop(ResponsePtr response, RequestPtr request);

    //// navi

    /// navi.common

    // initial pose
    void SetInitPose(ResponsePtr response, RequestPtr request);
    void SetInitImgPose(ResponsePtr response, RequestPtr request);
    void SetInitPoseArea(ResponsePtr response, RequestPtr request);
    void SetInitImgPoseArea(ResponsePtr response, RequestPtr request);
    void GetMatchingScore(ResponsePtr response, RequestPtr request);

    // start & stop
    void GetNaviMode(ResponsePtr response, RequestPtr request);
    void StartNavi(ResponsePtr response, RequestPtr request);
    void StopNavi(ResponsePtr response, RequestPtr request);

    // task control
    void PauseTask(ResponsePtr response, RequestPtr request);
    void ContinueTask(ResponsePtr response, RequestPtr request);
    void CancelTask(ResponsePtr response, RequestPtr request);
    void GetTaskProgress(ResponsePtr response, RequestPtr request);

    // status
    void GetNaviState(ResponsePtr response, RequestPtr request);

    /// navi.p2p
    void GotoPose(ResponsePtr response, RequestPtr request);
    void GotoImgPose(ResponsePtr response, RequestPtr request);

    //TODEL: replaced by GotoPose(Pose3D pose);
    void GotoGoal(ResponsePtr response, RequestPtr request);

    // TODEL: replaced by GotoImgPose(Pose3D pose);
    void GotoImgGoal(ResponsePtr response, RequestPtr request);
    void GotoStation(ResponsePtr response, RequestPtr request);

    /// navi.pf
    void FollowTempPath(ResponsePtr response, RequestPtr request);
    void FollowTempImgPath(ResponsePtr response, RequestPtr request);
    void FollowPath(ResponsePtr response, RequestPtr request);

    //// navi.ccp
    void PlanCoveragePath(ResponsePtr response, RequestPtr request);
    void PlanCoverageImgPath(ResponsePtr response, RequestPtr request);

    //// slam
    void GetSlamMode(ResponsePtr response, RequestPtr request);
    void StartSlam(ResponsePtr response, RequestPtr request);
    void StopSlam(ResponsePtr response, RequestPtr request);

    /// save map
    void SaveMapImg(ResponsePtr response, RequestPtr request);

    //// file
    void ExportConfigFile(ResponsePtr response, RequestPtr request);
    void ImportConfigFile(ResponsePtr response, RequestPtr request);
    void ExportMapFile(ResponsePtr response, RequestPtr request);
    void ImportMapFile(ResponsePtr response, RequestPtr request);
    void GetExportFileInfo(ResponsePtr response, RequestPtr request);
    void GetExportFiledata(ResponsePtr response, RequestPtr request);
    void SendImportFileData(ResponsePtr response, RequestPtr request);

    ////sensorstatus
    void CheckSensorStatus(ResponsePtr response, RequestPtr request);
    void GetSensorStatus(ResponsePtr response, RequestPtr request);

    ////exce_info
    void CheckAbnormalInfo(ResponsePtr response, RequestPtr request);

    ////task_system
    void GetRuntimeStatus(ResponsePtr response, RequestPtr request);

//    void GetImgLidarScan(ResponsePtr response, RequestPtr request);
//    void GetStations(ResponsePtr response, RequestPtr request);
//    void SetStations(ResponsePtr response, RequestPtr request);
//    void GetPaths(ResponsePtr response, RequestPtr request);
//    void SetPaths(ResponsePtr response, RequestPtr request);
//    void GetVirtualWalls(ResponsePtr response, RequestPtr request);
//    void SetVirtualWalls(ResponsePtr response, RequestPtr request);

//    void GetMapInfos(ResponsePtr response, RequestPtr request);
//    void SelectMap(ResponsePtr response, RequestPtr request);
    void GetPngMap(ResponsePtr response, RequestPtr request);
    void GetCurrentMapInfo(ResponsePtr response, RequestPtr request);
    void BagOpt(ResponsePtr response, RequestPtr request);
    void GetGeneralFile(const string& path, ResponsePtr& response);
//    void GetActPose(ResponsePtr response, RequestPtr request);
//    void StartNavi(ResponsePtr response, RequestPtr request);
//    void StopNavi(ResponsePtr response, RequestPtr request);
//    void StartSlam(ResponsePtr response, RequestPtr request);
//    void StopSlam(ResponsePtr response, RequestPtr request);
//    void FollowTempPath(ResponsePtr response, RequestPtr request);
//    void GotoPose(ResponsePtr response, RequestPtr request);
//    void CancelTask(ResponsePtr response, RequestPtr request);

    void GetCsgStations(ResponsePtr response, RequestPtr request);
    void CsgStationOpt(ResponsePtr response, RequestPtr request);
    void CsgPathOpt(ResponsePtr response, RequestPtr request);
    void CsgEnableAutoMark(ResponsePtr response, RequestPtr request);
    void CsgEnableAutoConnect(ResponsePtr response, RequestPtr request);
    void CsgConnectOnce(ResponsePtr response, RequestPtr request);
};

}
#endif // NPU_HTTP_H
