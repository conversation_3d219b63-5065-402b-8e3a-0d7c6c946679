#ifndef __UDP_SENDIP_H__
#define __UDP_SENDIP_H__
#include <pthread.h>
#include <string>

union IpAddress{
    long ip;
    unsigned char ipchar[4];
};

class UdpIpPublisher{
public:
    UdpIpPublisher(const std::string &version_file);
    ~UdpIpPublisher();
    long Getlocalhostip();
    int UdpSendIpData();
    int BroadcastIpByUdp();
    int LoadConfigFile();
    void SetLockStatus();

    union IpAddress ipaddress;
private:
    pthread_t m_send_threadid;
    std::string version_file_;
    std::string extra_msg_;
    std::string npu_sys_version_;
    std::string npu_api_version_;
    std::string ip_addr_;
    std::string lock_status_;
};

#endif
