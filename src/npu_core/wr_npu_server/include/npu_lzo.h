#ifndef __NPU_LZO_H__
#define __NPU_LZO_H__
#define  WANT_XMALLOC 1

extern "C"{
#ifdef __cplusplus
#include "minilzo.h"
#endif
}

using namespace std;

#define RESOLUTION (640*480L)
#define RGB_OUT_LEN (RESOLUTION*3 + RESOLUTION*3/16 +64 +3)
#define DEPTH_OUT_LEN (RESOLUTION*2 + RESOLUTION*2/16 +64 +3)

#define HEAP_ALLOC(var,size) \
    lzo_align_t __LZO_MMODEL var [ ((size) + (sizeof(lzo_align_t) - 1)) / sizeof(lzo_align_t) ]


struct RgbStruct{
	unsigned char red;
	unsigned char green;
	unsigned char blue;
};

struct DepthStruct{
    unsigned short DepthDate;
};

class NpuLzo{
public:
	NpuLzo();
	~NpuLzo();
	int LzoInit();
	int LzoCompress(lzo_bytep in,lzo_uint in_len
				   ,lzo_bytep out,lzo_uintp out_len);
	int LzoDecompress(lzo_bytep in,lzo_uint in_len
						  ,lzo_bytep out,lzo_uintp out_len);
	DepthStruct *depthdata;
	RgbStruct *rgbdata;
};

#endif
