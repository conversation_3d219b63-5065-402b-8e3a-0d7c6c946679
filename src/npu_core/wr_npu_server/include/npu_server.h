/****************************************
*Maintainer status: developed
*Maintainer: tommy (<EMAIL>)
*Copyright(c) 2016-2017 LinkMiao CO., Ltd.
*All rights reserved
****************************************/
#ifndef WIZROBO_NPU_NPU_SERVER_H
#define WIZROBO_NPU_NPU_SERVER_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <dirent.h>
#include <math.h>
#include <time.h>

#include <iostream>
#include <fstream>
#include <sstream>
#include <map>

#define BOOST_SPIRIT_THREADSAFE
#include <boost/property_tree/json_parser.hpp>
#include <boost/property_tree/ptree.hpp>
#include <boost/regex.hpp>

#include <IceUtil/Thread.h>
#include <yaml-cpp/yaml.h>

#include <ros/ros.h>
#include <ros/console.h>
#include <actionlib/client/simple_action_client.h>
#include <std_msgs/String.h>
#include <std_msgs/Int64MultiArray.h> 
#include <std_msgs/Float32MultiArray.h> 
#include <std_msgs/Float32.h>
#include <geometry_msgs/Vector3Stamped.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <geometry_msgs/Twist.h>
#include <geometry_msgs/PoseArray.h>
#include <geographic_msgs/GeoPath.h>
#include <nav_msgs/OccupancyGrid.h>
#include <sensor_msgs/LaserScan.h>
#include <sensor_msgs/NavSatFix.h>
#include <nav_msgs/Path.h>
#include <actionlib_msgs/GoalStatusArray.h>
#include <actionlib_msgs/GoalStatus.h>
#include <tf/transform_listener.h>
//#include <kobuki_msgs/Sound.h>
#include <sensor_msgs/Image.h>
#include <sensor_msgs/CameraInfo.h>
#include <actionlib/client/simple_action_client.h>
#include <wr_navi_core_msgs/WRNaviCoreAction.h>
#include <std_srvs/Empty.h>
#include <ecl/threads.hpp>
#include <sensor_msgs/LaserScan.h>
#include <sensor_msgs/NavSatFix.h>
#include <laser_geometry/laser_geometry.h>
#include <tf/message_filter.h>
#include <tf/tf.h>
#include <message_filters/subscriber.h>
#include <geometry_msgs/PolygonStamped.h>
#include <wr_npu_msgs/VsensorStatus.h>
#include <wr_npu_msgs/SensorStatus.h>

#include <wr_npu_msgs/DynParam.h>
#include <wr_npu_msgs/WrMapServer.h>
#include <wr_npu_msgs/ForceLoopClosure.h>
#include <wr_npu_msgs/ClearMotorEnc.h>
#include <wr_dyparam/FeedBoolParam.h>
#include <wr_dyparam/FeedFloatParam.h>
#include <wr_dyparam/FeedIntParam.h>
#include <wr_dyparam/FeedStrParam.h>
#include <wr_dyparam/GetBoolParam.h>
#include <wr_dyparam/GetFloatParam.h>
#include <wr_dyparam/GetIntParam.h>
#include <wr_dyparam/GetStrParam.h>
#include <wr_dyparam/AddConfig.h>
#include <wr_dyparam/DeleteConfig.h>

#include <wr_npu_msgs/MotorEnc.h>
#include <wr_npu_msgs/MotorSpd.h>
#include <wr_npu_msgs/GetOptMap.h>
#include <wr_npu_msgs/BumperData.h>
#include <wr_npu_msgs/InfrdData.h>
#include <wr_npu_msgs/ExceptionInformation.h>
#include <wr_npu_msgs/EmergencyStopData.h>
#include <wr_npu_msgs/RuntimeStatus.h>
#include <wr_npu_msgs/Station.h>
#include <wr_npu_msgs/StationArray.h>
#include <wr_npu_msgs/CsgPath.h>
#include <wr_npu_msgs/CsgStation.h>
#include <wr_npu_msgs/TransGpsGoal.h>
#include <wr_npu_msgs/TransGpsPath.h>
#include <wr_npu_msgs/ReTransGpsGoal.h>
#include <wr_npu_msgs/ReTransGpsPath.h>
#include <map_server.h>
#include <wr_navi_core_msgs/WRNaviCoreAction.h>
#include <chassis_model/diffdrv_chassis_model.h>

#include <usblock.h>
#include <npu.h>
#include <npuice_api.h>
#include <npuice_map.h>
#include <npuice_enum_ext.h>
#include <fstream>

#include "npu_lzo.h"
#include "map_handler.h"

#define MAXLINE 1024

/// <title WR C++ API />
/// <version 0.1.0 />
/// <date 2017-04-05 />
/// <maintain <EMAIL>>
/// updated by lhan@2017-06-09-1754

namespace wizrobo_npu {
using namespace std;
using namespace boost::property_tree;
using namespace wizrobo::enum_ext;
using namespace wizrobo::str_ext;
using namespace wizrobo::ros_ext;

struct GetFilesInfoCallback{
    wizrobo_npu::AMD_NpuIce_GetExportFileInfoPtr getExportFileInfoPtr;
    string fileName;
};

struct CheckSensorStatusCB{
    wizrobo_npu::AMD_NpuIce_CheckSensorStatusPtr checkSensorStatusPtr_;
    CheckMode mode_;
};

struct GpsData1
{
    double latitude_deg;
    double longitude_deg;
    double altitude_m;
    double direction_deg;
};
class NpuServer
{
public:
    NpuServer(int argc, char **argv)
        : server_state_(UNKNOWN)
        , gps_direction_(0.0)
        , npu_state_(IDLE_STATE)
        , navi_state_(IDLE)
        , navi_state_pf_(IDLE)
        , manual_ctrl_cnt_(0)
        , auto_tick_cnt_(0)
        , p_safe_stop_thread_(NULL)
        , p_manual_cmd_thread_(NULL)
        , p_map_receiving_thread_(NULL)
        , p_exce_info_thread_(NULL)
        , p_file_manage_thread_(NULL)
        , p_connection_limit_thread_(NULL)
        , p_map_handler_(NULL)
        , p_lidar_scan_tf_filter_(NULL)
        , p_sensor_status_thread_(NULL)
        , manual_cmd_lin_vel_mps_(0)
        , manual_cmd_ang_vel_rps_(0)
//        , path_approach_avenue_factor_(1.0)
//        , safety_inflation_factor_(0.3)
//        , max_range_(4.0)
        , checkstatus_num_(0)
        , p_get_files_info_(NULL)
        , p_check_sensor_status_(NULL)
        , safe_thread_num_(0)
//        , footprint_str_("")
//        , robot_radius_(0.0)
        , en_get_param_(true)
        //, base_param_.motor_param(base_param_.motor_param)
        //, base_param_.chassis_param(base_param_.chassis_param)
        //, base_param_.footprint_param_(base_param_.chassis_param.footprint_param)
        , lidar_range_max_(-1)
        , continue_switch_(true)
    {
        ROS_INFO("NpuServer::NpuServer()");
        p_map_handler_ = new MapHandler("/map");

        /// rosparam
        ros::NodeHandle prv_nh("~");
        prv_nh.param<bool>("enb_direct_sensor_trans", enb_direct_sensor_trans_, true);
        /// sub & pub
        ros::NodeHandle nh;
        //matching_score_sub

        //gpsdata init
        gps_raw_.altitude = 0.0;
        gps_raw_.latitude = 0.0;
        gps_raw_.longitude = 0.0;

        p_get_files_info_ = new GetFilesInfoCallback;
        p_check_sensor_status_ = new CheckSensorStatusCB;
        //// multi-thread
        p_safe_stop_thread_ = new boost::thread(boost::bind(&NpuServer::SafeStopThread_, this));
        //p_safe_stop_thread_->start_thread();

        act_pose_.x = 0.0;
        act_pose_.y = 0.0;
        act_pose_.z = 0.0;
        act_pose_.roll = 0.0;
        act_pose_.pitch = 0.0;
        act_pose_.yaw = 0.0;

        slam_mode_ = ICP_SLAM;
        navi_mode_ = P2P_NAVI;

        pthread_spin_init(&motor_enc_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&act_motor_spd_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&cmd_motor_spd_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&act_vel_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&cmd_vel_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&act_pose_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&cmd_path_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&task_status_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&task_status_pf_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&tf_lidar_scan_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&lidar_scan_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&bumper_data_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&infrd_data_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&gps_data_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&map_server_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&map_cache_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&sensor_status_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&get_naviparam_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&set_naviparam_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&get_infparam_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&direct_trans_footprint_vertices_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&csg_station_list_spinlock_, PTHREAD_PROCESS_PRIVATE);
        pthread_spin_init(&csg_current_station_spinlock_, PTHREAD_PROCESS_PRIVATE);

        map_cache_index_ = 0;
        imgmap_cache_[0].info.id = "";
        imgmap_cache_[1].info.id = "";

        current_imgmap_head_info_.info.id = "";

        launch_connection_limit_thread_();
        launch_exce_info_thread_();
    }

    ~NpuServer()
    {
        if (p_safe_stop_thread_)
        {
            p_safe_stop_thread_->interrupt();
            p_safe_stop_thread_->join();
            delete p_safe_stop_thread_;
            p_safe_stop_thread_ = NULL;
        }
        if (p_manual_cmd_thread_)
        {
            p_manual_cmd_thread_->interrupt();
            p_manual_cmd_thread_->join();
            delete p_manual_cmd_thread_;
            p_manual_cmd_thread_ = NULL;
        }
        if (p_map_handler_)
        {
            delete p_map_handler_;
            p_map_handler_ = NULL;
        }
        if (p_lidar_scan_tf_filter_)
        {
            delete p_lidar_scan_tf_filter_;
            p_lidar_scan_tf_filter_ = NULL;
        }
        if (p_map_receiving_thread_)
        {
            p_map_receiving_thread_->interrupt();
            p_map_receiving_thread_->join();
            delete p_map_receiving_thread_;
            p_map_receiving_thread_ = NULL;
        }
//        if (p_exce_info_thread_)
//        {
//            p_exce_info_thread_->interrupt();
//            p_exce_info_thread_->join();
//            delete p_exce_info_thread_;
//            p_exce_info_thread_ = NULL;
//        }
    }

    inline MapHandler* GetMapHandlder() { return p_map_handler_; }

    wr_npu_msgs::StationArray GetCsgStationList();
    wr_npu_msgs::Station GetCsgCurrentStation();

    //// npu api
    //// connection
    string GetServerVersion();
    string GetCurrentFormatTimeString();
    void Connect(string version);
    ServerState GetServerState();
    SystemDiagInfo GetSystemDiagInfo();
    inline ActionState GetActionState()
    {
        return static_cast<ActionState>(static_cast<int>(npu_state_));
    }
    NpuState GetNpuState();

    ///gps
    GpsData1 GetGpsData();

    /// power
    void Shutdown();
    void Reboot();

    //// param
    // npu
    CoreParam GetCoreParam();
    void SetCoreParam(CoreParam param);
    // motor
    MotorParam GetMotorParam();
    void SetMotorParam(MotorParam param);
    // pid
    PidParam GetPidParam();
    void SetPidParam(PidParam param);
    // chassis
    ChassisParam GetChassisParam();
    void SetChassisParam(ChassisParam param);
    // footprint
    FootprintParam GetFootprintParam();
    void SetFootprintParam(FootprintParam param);
    // base
    BaseParam GetBaseParam();
    void SetBaseParam(BaseParam param);
    // sensor
    SensorParam GetSensorParam();
    void SetSensorParam(SensorParam param);
	// sensor types
	StringArray GetSupportedLidarTypes();
    StringArray GetSupportedImuTypes();
    StringArray GetSupportedCameraTypes();
    StringArray GetSupportedGpsTypes();
    // teleop
    TeleopParam GetTeleopParam();
    void SetTeleopParam(TeleopParam param);

    void GetParamByPtree(ptree& pt_root);
    void SetParamByPtree(const ptree& pt_root);

    // navi
    NaviParam GetNaviParam();
    void SetNaviParam(NaviParam param);
    // slam
    SlamParam GetSlamParam();
    void SetSlamParam(SlamParam param);
#if USE_NPU_API_V_0_9_12
    // record & play
    RecordParam GetRecordParam();
    void SetRecordParam(const RecordParam& param);
    PlayParam GetPlayParam();
    void SetPlayParam(const PlayParam& param);
#endif

    //// slave mode
    void EnableSlaveMode(bool enb);
    void FeedMotorEnc(MotorEnc enc);
    void FeedActMotorSpd(MotorSpd spd);

    //// motor data
    // enc
    MotorEnc GetMotorEnc();
    void ClearMotorEnc();
    // spd
    MotorSpd GetActMotorSpd();
    MotorSpd GetCmdMotorSpd();

    //// sensor data
    // lidar
    LidarScan GetLidarScan();
    ImgLidarScan GetImgLidarScan();
    // imu
    ImuData GetImuData();
    // sonar
    SonarScan GetSonarScan();
    ImgSonarScan GetImgSonarScan();
    // infrd
    InfrdScan GetInfrdScan();
    ImgInfrdScan GetImgInfrdScan();
    // bumper
    BumperArray GetBumperArray();
    // battery
    BatteryStatus GetBatteryStatus();

    //// manual control
    void SetManualCmd(ManualCmdType cmd);
    void SetManualVel(float lin_scale, float ang_scale);

    //// config management
    StringArray GetConfigIdList();
    void SelectConfig(string id);
    void DeleteConfig(string id);
    void AddConfig(string id);

    //// map management
    // map
    MapInfoList GetMapInfos();
    void SetMapInfos(MapInfoList list);
    void SelectMap(string id);
    // station
    StationList GetStations(string map_id);
    void SetStations(string map_id, StationList list);
    ImgStationList GetImgStations(string map_id);
    void SetImgStations(string map_id, ImgStationList list);
    GeoStationList GetGeoStations(string map_id);
    void SetGeoStations(string map_id, GeoStationList list);
    void AddStation(string map_id, wizrobo_npu::StationInfo info);
    bool DeleteStation(string map_id, std::string station_id);
    bool RenameStation(string map_id, std::string origin_id, std::string new_id);
    // task
    TaskList GetTaskList(string map_id);
    void ExecuteTask(TaskList list);
    void SetTasks(string map_id, TaskList list);
    //task system
    //    void StopTaskSystem();
    // path
    PathList GetPaths(string map_id);
    void SetPaths(string map_id, PathList list);
    ImgPathList GetImgPaths(string map_id);
    void SetImgPaths(string map_id, ImgPathList list);
    GeoPathList GetGeoPaths(string map_id);
    GeoPoseList GetGeoPath();
    void SetGeoPaths(string map_id, GeoPathList list);
    void SetGeoPath(GeoPath geo_path);

    bool GetPathsFile(Path& path);

    // virtual walls
    VirtualWallList GetVirtualWalls(string map_id);
    void SetVirtualWalls(string map_id, VirtualWallList virtual_walls);
    ImgVirtualWallList GetImgVirtualWalls(string map_id);
    void SetImgVirtualWalls(string map_id, ImgVirtualWallList virtual_walls);
    GeoVirtualWallList GetGeoVirtualWalls(string map_id);
    void SetGeoVirtualWalls(string map_id, GeoVirtualWallList virtual_walls);

    //// bag management
    void DoBagOpt(const string &opt, const string &args, string &res);

    //// common runtime data
    // vel
    Vel3D GetCmdVel();
    Vel3D GetActVel();
    inline Vel3D GetCurrentVel() { return GetActVel(); }
    // acc
    Acc3D GetAcc();
    inline Acc3D GetCurrentAcc() { return GetAcc(); }
    // pose
    Pose3D GetCmdPose();
    Pose3D GetActPose();
    ImgPose GetCmdImgPose();
    ImgPose GetActImgPose();
    GeoPose GetCmdGeoPose();
    GeoPose GetActGeoPose();
    inline Pose3D GetCurrentPose() { return GetActPose(); }
    inline ImgPose GetCurrentImgPose() { return GetActImgPose(); }
    void SaveActPose();
    Pose3D GetActPosetoFile();
    // path
    Path GetGlobalPath();
    Path GetLocalPath();
    Path GetActPath();
    ImgPath GetGlobalImgPath();
    ImgPath GetLocalImgPath();
    ImgPath GetActImgPath();
    GeoPath GetGlobalGeoPath();
    GeoPath GetLocalGeoPath();
    GeoPath GetActGeoPath();
    inline Path GetCurrentPath() { return GetGlobalPath(); }
    inline ImgPath GetCurrentImgPath() { return GetGlobalImgPath(); }
    // map
    MapInfo GetCurrentMapInfo();
    Map2D GetMap();
    ImgMap GetImgMap();
    GeoMap GetGeoMap();
    inline Map2D GetCurrentMap() { return GetMap(); }
    inline ImgMap GetCurrentImgMap() { return GetImgMap(); }
    // footprint
    Point3DList GetFootprintVertices();
    ImgPointList GetFootprintImgVertices();
    GeoPointList GetFootprintGeoVertices();

    //// Hard ware
    void StartTelop();
    void StopTelop();
    //// navi
    /// navi.common
    // initial pose
    void SetInitPose(Pose3D pose);
    void SetInitPoseArea(InitPoseArea pose);
    void SetInitImgPose(ImgPose pose);
    void SetInitImgPoseArea(InitImgPoseArea pose);
    void SetInitGeoPose(GeoPose pose);
    void SetInitGeoPoseArea(InitGeoPoseArea pose);
    // start & stop
    inline NaviMode GetNaviMode() { return navi_mode_; }
    void StartNavi(NaviMode mode);
    void StopNavi();
    // task control
    void PauseTask();
    void ContinueTask();
    void CancelTask();
    float GetTaskProgress();
    // state
    NaviState GetNaviState();

    /// navi.p2p
    void GotoPose(Pose3D pose);
    void GotoImgPose(ImgPose pose);
    void GotoGeoPose(GeoPose pose);
    inline void GotoGoal(Pose3D goal) { GotoPose(goal); }
    inline void GotoImgGoal(ImgPose goal) { GotoImgPose(goal); }
    void GotoStation(string map_id, string station_id);
    /// navi.pf
    void FollowTempPath(Pose3DList poses);
    void FollowTempImgPath(ImgPoseList poses);
    void FollowTempGeoPath(GeoPoseList poses);
    void FollowPath(string map_id, string path_id);
    /// navi.ccp
    Pose3DList PlanCoveragePath(Point3DList vertices);
    ImgPoseList PlanCoverageImgPath(ImgPointList vertices);
    GeoPoseList PlanCoverageGeoPath(GeoPointList vertices);

    //// slam
    inline SlamMode GetSlamMode() { return slam_mode_; }
    void StartSlam(SlamMode mode);
    void StopSlam(string map_id);

    //// file
    ZipFile ExportConfigFile(string file_name);
    void ImportConfigFile(ZipFile file, string file_name);
    void PackMapFile(const string& file_name);
    void UnPackMapFile(const string &file_name);
    ZipFile ExportMapFile(string file_name);
    void ImportMapFile(ZipFile file, string file_name);

    void GetExportFileInfo_async(const ::wizrobo_npu::AMD_NpuIce_GetExportFileInfoPtr& ptr,string fileName);
    FileData GetExportFiledata(string fileName,int chunk_index);
    void SendImportFileData(FileData data);

    ////sensorstatus
    void CheckSensorStatus_async(const ::wizrobo_npu::AMD_NpuIce_CheckSensorStatusPtr& ptr,CheckMode mode);
    SensorStatus GetSensorStatus();
//    SensorStatus CheckSensorStatus(CheckMode mode);
    RuntimeStatusList GetRuntimeStatus();

    string GetBaseLaunchArgs_(ros::NodeHandle& nh);
    string GetLidarLaunchArgs_(ros::NodeHandle& nh);
    string GetImuLaunchArgs_(ros::NodeHandle& nh);
    string GetCameraLaunchArgs_(ros::NodeHandle& nh);
    string GetGpsLaunchArgs_(ros::NodeHandle& nh);
    string GetBagLaunchArgs_(ros::NodeHandle& nh);

    //matching_score
    float GetMatchingScore();

    //// record & play
    void StartRecord();
    void StopRecord(string bag_id);
    void StartPlay();
    void StopPlay();

    void CheckAbnormalInfo();
    //   Illegal character detection
    bool IllegalCharacterDetection(ImgStationList list,ImgStationList station_list);
    bool IllegalCharacterDetection(const GeoStationList& list0, const GeoStationList& list1);

    bool IllegalCharacterDetection(PathList list,PathList paths);
    bool IllegalCharacterDetection(ImgVirtualWallList virtual_walls,ImgVirtualWallList VirtualWallList);
    bool IllegalCharacterDetection(const GeoVirtualWallList& list0, const GeoVirtualWallList& list1);

    bool IllegalCharacterDetection(string map_id,MapInfoList map_info_list);
    //save map
    void SaveMapImg(string map_id);
    void StartCsgNode();

    /// for csg
    bool CsgChangeStation(int opt, int id, double x, double y, double theta, int type);
    bool CsgChangePath(int opt, int s_id, int e_id);
    bool CsgEnableAutoMark(int opt);
    bool CsgEnableAutoConnect(int opt);
    bool CsgDoConnectOnce();
private:
    //// implementation
    /// img-real transformation
    bool TransformPose_(const ImgPose& input_data, Pose3D& output_data);
    bool TransformPose_(const Pose3D& input_data, ImgPose& output_data);
    bool TransformPoseList_(const Pose3DList& input_data, ImgPoseList& output_data);
    bool TransformPoseList_(const ImgPoseList& input_data, Pose3DList& output_data);
    bool TransformPointList_(const Point3DList& input_data, ImgPointList& output_data);
    bool TransformPointList_(const ImgPointList& input_data, Point3DList& output_data);

    bool TransformPose_(const GeoPose& input_data, Pose3D& output_data); //TODO
    bool TransformPose_(const Pose3D& input_data, GeoPose& output_data); //TODO
    bool TransformPoseList_(const Pose3DList& input_data, GeoPoseList& output_data); //TODO
    bool TransformPoseList_(const GeoPoseList& input_data, Pose3DList& output_data); //TODO
    bool TransformPointList_(const Point3DList& input_data, GeoPointList& output_data); //TODO
    bool TransformPointList_(const GeoPointList& input_data, Point3DList& output_data); //TODO
/*
    bool TransformImgPoseTo3DPose_(const ImgPose& img_pose, Pose3D& _3d_pose);
    bool Transform3DPoseToImgPose_(const Pose3D& _3d_pose, ImgPose& img_pose);
    bool TransformImgPosesTo3DPoses_(const ImgPoseList& img_poses, Pose3DList& _3d_poses);
    bool Transform3DPosesToImgPoses_(const Pose3DList& _3d_poses, ImgPoseList& img_poses);
    bool Transform3DPointsToImgPoints_(const Point3DList& _3d_points, ImgPointList& img_points);
    bool TransformImgPointsTo3DPoints_(const ImgPointList& img_points, Point3DList& _3d_points);
*/
    /// callback
    // motor
    void MotorEncCallback_(const wr_npu_msgs::MotorEnc::ConstPtr& msg);
    void ActMotorSpdCallback_(const wr_npu_msgs::MotorSpd::ConstPtr& msg);
    void CmdMotorSpdCallback_(const wr_npu_msgs::MotorSpd::ConstPtr& msg);
    // vel
    void ActVelCallback_(const geometry_msgs::Twist::ConstPtr& msg);
    void CmdVelCallback_(const geometry_msgs::Twist::ConstPtr& msg);
    // pose
    void ActPoseCallback_(const geometry_msgs::PoseStamped::ConstPtr& msg);
    // path
    void GlobalPathCallback_(const nav_msgs::Path::ConstPtr& msg);
    void LocalPathCallback_(const geometry_msgs::PoseArray::ConstPtr& msg);
    // task
    void TaskStatusCallback_(const actionlib_msgs::GoalStatusArray::ConstPtr& msg);
    void TaskStatusPfCallback_(const actionlib_msgs::GoalStatusArray::ConstPtr& msg);
    // lidar
    void TfTransLidarScanCallback_(const sensor_msgs::PointCloud::ConstPtr& msg);
    void LidarScanCallBack_(const sensor_msgs::LaserScan::ConstPtr& msg);// NOT IN USE
    //bumper
    void BumperDataCallBack_(const wr_npu_msgs::BumperData::ConstPtr& msg);
    //infrd
    void InfrdDataCallBack_(const wr_npu_msgs::InfrdData::ConstPtr& msg);
    //emergencystop
    void EmergencyStopDataCallBack_(const wr_npu_msgs::EmergencyStopData::ConstPtr& msg);
    //sensor status
    void ImuDataCallBack_(const geometry_msgs::Vector3Stamped::ConstPtr& msg);
    void SensorStatusCallBack_(const wr_npu_msgs::VsensorStatus::ConstPtr& msg);
    //matching_score
    void MatchingScoreCallBack_(const std_msgs::Float32::ConstPtr& msg);
    void RuntimeStatusCallBack_(const wr_npu_msgs::RuntimeStatus::ConstPtr& msg);
    void CsgStationsCallBack_(const wr_npu_msgs::StationArray::ConstPtr& msg);
    void CsgCurrentStationCallBack_(const wr_npu_msgs::Station::ConstPtr& msg);
    //GPS
    void GpsFixCallback_(const sensor_msgs::NavSatFixConstPtr& msg);
    void GpsRawCallBack_(const sensor_msgs::NavSatFixConstPtr& msg);
    ///gps direction
    void GpsDirectionCallBack_(const geometry_msgs::Vector3StampedConstPtr& msg);
    /// multi-thread
    void SafeStopThread_();

    /// vel
    void UpdateSpdLimits_(const MotorParam& motor_param, ChassisParam& chassis_param);
    void PublishManualVel_(float lin_vel_mps, float ang_vel_rps);

    /// path
    void PublishWaypoints_(const Path path);
    void ClearActPath_();
    void UpdateActPath_(const ros::Time& time);//TODO: move to pose_moniter_node

    /// map
    bool SetMapServerMode_(const string mode);
    bool SaveMap_(const string map_id);


    /// footprint
    geometry_msgs::Polygon Footprint2Polygon(const wizrobo_npu::FootprintParam& param);
    void UpdateFootprint_(const FootprintParam& param);

    /// motor param
    void TriggerMotorParamUpdating_();
    void TriggerPidParamUpdating_();

    /// chassis param
    void TriggerChassisParamUpdating_();

    /// lidar filter param
    // lidar angular param
    void TriggerLidarFilterParamUpdating_();
    // lidar footprint param
//    void TriggerLidarFootprintFilterParamUpdating_();

    /// costmap param
    void TriggerCostMapParamUpdating_();


    /// sensor param
    void GetLidarParam_(SensorParam &param);
    void GetSonarParam_(SensorParam &param);
    void GetInfrdParam_(SensorParam &param);
    void GetBumperParam_(SensorParam &param);
    void GetImuParam_(SensorParam &param);
    void GetGpsParam_(SensorParam &param);
    void GetCameraParam_(SensorParam &param);
    void SetLidarParam_(SensorParam &param);
    void SetSonarParam_(SensorParam &param);
    void SetInfrdParam_(SensorParam &param);
    void SetBumperParam_(SensorParam &param);
    void SetImuParam_(SensorParam &param);
    void SetGpsParam_(SensorParam &param);
    void SetCameraParam_(SensorParam &param);

    /// check sensor
    void CheckSensorParam_();
    /// navi param
    void UpdatePathFollowerParam_();
    void UpdateCcpPlannerParam_();
    void UpdateP2pPlannerParam_();

    /// tf
    void UpdateTf_();
    void UpdateLidarToBaseTf_(const SensorParam& param);

    /// loop closure
    bool ForceLoopClosure_();

    /// receive map
    void launch_map_receiving_thread_();
    void stop_map_receiving_thread_();
    void map_receiving_thread_();
    void get_imgmap_from_map_server_(ImgMap &imgmap);
    void get_map2d_from_map_server_(Map2D &map2d);
    void get_geomap_from_map_server_(GeoMap &geomap);

    /// exception_info
    void launch_exce_info_thread_();
    ///  file manage
    void launch_file_manage_thread_(string mode);
    void launch_file_execute_thread_(string mode);
    void file_manage_thread_(string mode);
    void file_execute_thread_(string mode);
    void execute_file();
    void compress_file();
    void uncompress_file();
    void delete_file();

    /// sensor status
    void launch_sensor_status_thread_(CheckMode mode);
    void sensor_status_thread_(CheckMode mode);

    /// connection limit
    void launch_connection_limit_thread_();
    void connection_limit_thread_();

private:
    //// functional members
    /// map
    MapHandler *p_map_handler_;
    /// multi-thread
    boost::thread* p_safe_stop_thread_;
    boost::thread* p_manual_cmd_thread_;
    boost::thread* p_map_receiving_thread_;
    boost::thread* p_file_manage_thread_;
    boost::thread* p_connection_limit_thread_;
    boost::thread* p_exce_info_thread_;
    boost::thread* p_sensor_status_thread_;
    boost::thread* p_execute_task_;
    boost::thread* p_start_csg_node_;

    /// sub
    ros::Subscriber act_vel_sub_;
    ros::Subscriber cmd_vel_sub_;
    ros::Subscriber act_pose_sub_;
    ros::Subscriber global_path_sub_;
    ros::Subscriber local_path_sub_;
    ros::Subscriber navi_status_sub_;
    ros::Subscriber navi_status_pf_sub_;
    ros::Subscriber trans_lidar_scan_pc_sub_;
    //ros::Subscriber lidar_scan_sub_;
    ros::Subscriber trans_footprint_sub_;
    tf::TransformListener tf_listener_;
    ros::Subscriber lidar_scan_sub_;
    ros::Subscriber bumper_data_sub_;
    ros::Subscriber infrd_data_sub_;
    ros::Subscriber emergencystop_data_sub_;
    ros::Subscriber gps_data_sub_;

    message_filters::Subscriber<sensor_msgs::LaserScan> lidar_scan_mf_sub_;
    tf::MessageFilter<sensor_msgs::LaserScan>* p_lidar_scan_tf_filter_;
    /// pub
    ros::Publisher cmd_pose_pub_;
    ros::Publisher exce_info_pub_;
    ros::Publisher action_pub_;
    ros::Publisher initial_pose_est_pub_;
    ros::Publisher initial_pose_pub_;
    ros::Publisher initial_pose_area_pub;
    ros::Publisher manual_cmd_pub_;
    ros::Publisher safe_cmd_pub_;
    ros::Publisher waypoints_pub_;
    ros::Publisher ccp_region_pub_;
    ros::Publisher task_ctrl_pub_;
    ros::Publisher footprint_pub_;
    ros::Publisher act_path_pub_;//TODO: move to pose_monitor_node
    // motor.master
    ros::Subscriber motor_enc_sub_;
    ros::Subscriber act_motor_spd_sub_;
    // motor.slave
    ros::Publisher motor_enc_pub_;
    ros::Publisher act_motor_spd_pub_;
    // motor.common
    ros::Subscriber cmd_motor_spd_sub_;
    //sensor_status
    ros::Subscriber sensor_status_sub_;
    //matching_score sub
    ros::Subscriber matching_score_sub_;
    ros::Subscriber imu_data_sub_;
    //gps pose
    ros::Subscriber gps_raw_sub_;
    ros::Subscriber gps_direction_sub_;

    double gps_direction_;
	 //init_act_pose
    string init_act_pose_;
    double init_act_pose_x_;
    double init_act_pose_y_;
    double init_act_pose_yaw_;
    double init_pose__area_width_;
    double init_pose__area_height_;
    bool save_act_pose_switch = false;
   	string unfinishpath;
private:
    //// variables
    /// connection
    ServerState server_state_;
    NpuState npu_state_;
    NpuState target_npu_state_;
    NaviMode navi_mode_;
    SlamMode slam_mode_;
    string server_type_;

    bool client_is_alive=false;
    char *uncompress_file_name=new char[0];


    /// param
    CoreParam core_param_;
    //ChassisParam& base_param_.chassis_param;
    //FootprintParam& base_param_.footprint_param_;
    //MotorParam& base_param_.motor_param;
    BaseParam base_param_;
#if USE_NPU_API_V_0_9_12
#else
    float footprint_height_m_;
#endif
    SensorParam sensor_param_;
//    LidarParamList lidar_param_list_;
//    SonarParamList sonar_param_list_;
//    InfrdParamList infrd_param_list_;
//    BumperParamList bumper_param_list_;
    TeleopParam teleop_param_;
    SlamParam slam_param_;
    NaviParam navi_param_;
    tf::Transform base_to_lidar_tf_;
    float lidar_range_max_;
    bool en_get_param_;

    /// navi
    NaviState navi_state_;
    NaviState navi_state_pf_;
    /// manual control
    int manual_ctrl_cnt_;
    int auto_tick_cnt_;
    float manual_cmd_lin_vel_mps_;
    float manual_cmd_ang_vel_rps_;
//    float lin_vel_mps;
//    float ang_vel_dps;

    /// enc
    MotorEnc last_enc_;
    MotorEnc enc_offset_;
    MotorEnc current_enc_;

    /// vel
    MotorSpd cmd_motor_spd_;
    MotorSpd act_motor_spd_;
    Vel3D cmd_vel_;
    Vel3D act_vel_;

    /// task
    TaskList list_;

    /// path
    Path global_path_;
    Path local_path_;
    Path act_path_;
    nav_msgs::Path act_path_msg_;

    /// pose
    Pose3D cmd_pose_;
    Pose3D act_pose_;
    geometry_msgs::PoseStamped act_pose_msg_;

    ///bumper
    wr_npu_msgs::BumperData bumper_data_msgs_;

    ///infrd
    wr_npu_msgs::InfrdData infrd_data_msgs_;

    ///emergencystop
    wr_npu_msgs::EmergencyStopData emergencystop_data_msgs_;

    /// lidar
    LidarScan tf_trans_lidar_scan_pc_;
    sensor_msgs::LaserScan lidar_scan_msg_;
    bool enb_direct_sensor_trans_;
    LidarScan direct_trans_lidar_scan_;

    ///GPS
    sensor_msgs::NavSatFix gps_data_msg_;

    /// chassis
    geometry_msgs::PolygonStamped footprint_msg_;
    Point3DList direct_trans_footprint_vertices_;

    ///sensor status
    SensorStatus sensor_status_;
    CheckSensorStatusCB *p_check_sensor_status_;

    /// spinlock
    pthread_spinlock_t motor_enc_spinlock_;
    pthread_spinlock_t act_motor_spd_spinlock_;
    pthread_spinlock_t cmd_motor_spd_spinlock_;
    pthread_spinlock_t act_vel_spinlock_;
    pthread_spinlock_t cmd_vel_spinlock_;
    pthread_spinlock_t act_pose_spinlock_;
    pthread_spinlock_t cmd_path_spinlock_;
    pthread_spinlock_t task_status_spinlock_;
    pthread_spinlock_t task_status_pf_spinlock_;
    pthread_spinlock_t tf_lidar_scan_spinlock_;
    pthread_spinlock_t lidar_scan_spinlock_;
    pthread_spinlock_t bumper_data_spinlock_;
    pthread_spinlock_t infrd_data_spinlock_;
    pthread_spinlock_t emergencystop_data_spinlock_;
    pthread_spinlock_t gps_data_spinlock_;
    pthread_spinlock_t map_server_spinlock_;
    pthread_spinlock_t map_cache_spinlock_;
    pthread_spinlock_t sensor_status_spinlock_;
    pthread_spinlock_t get_naviparam_spinlock_;
    pthread_spinlock_t set_naviparam_spinlock_;
    pthread_spinlock_t get_infparam_spinlock_;
    pthread_spinlock_t direct_trans_footprint_vertices_spinlock_;
    pthread_spinlock_t geo_trans_server_spinlock_;

    /// map receiving
    bool is_map_caching_ = true;
    ImgMap imgmap_cache_[2];
    Map2D map2d_cache_[2];
    GeoMap geomap_cache_[2];
    int map_cache_index_;

    /// point&pose transform
    ImgMap current_imgmap_head_info_;
    ImgMap current_geomap_head_info_;

//    float path_approach_avenue_factor_;
//    float safety_inflation_factor_;
//    float max_range_;
//    string footprint_str_;
//    float robot_radius_;

    int checkstatus_num_;

    int safe_thread_num_;

    GetFilesInfoCallback *p_get_files_info_;
    const int chunk_size_ = 1024*1024;

    float matching_score_data = 0.0;
    float matching_test =0.1;
    bool matching_identifiation = false;

    bool continue_switch_;

    wr_npu_msgs::RuntimeStatus runtime_status_msgs_;
    ros::Subscriber runtime_status_sub_;
    //MapInfoList map_info_list_;
    ImuData imu_data_;

    pthread_spinlock_t csg_station_list_spinlock_;
    pthread_spinlock_t csg_current_station_spinlock_;
    ros::Subscriber csg_station_list_sub_;
    ros::Subscriber csg_current_station_sub_;
    wr_npu_msgs::StationArray csg_station_list_;
    wr_npu_msgs::Station csg_current_station_;

	bool is_started_csg = false;
    //gps
    sensor_msgs::NavSatFix gps_raw_;

};// class
}// namespace
#endif // WIZROBO_NPU_NPU_SERVER_H
