#ifndef CLIENT_MANAGER_HPP
#define CLIENT_MANAGER_HPP

#include <iostream>
#include <ctime>

template<typename request_type>
class NpuClientManagerMixin
{
public:
    typedef struct _ClientAddress
    {
        std::string ip;
        int port;
        std::time_t active_time;
    }ClientAddress;

    ClientAddress operator_;
    static const int CLIENT_ACT = 1;
    static const int TIME_OUT = 10;

    NpuClientManagerMixin()
    {
        operator_.ip = "";
        operator_.port = -2;
        operator_.active_time = -2;
    }

    virtual ClientAddress GetClientAddress(request_type request)
    {
        ClientAddress client_address;
        client_address.ip = "*";
        client_address.port = -1;
        return client_address;
    }

    bool AddNpuClient(request_type request)
    {
        ClientAddress cur_client = GetClientAddress(request);
        time_t now = time(0);
        int interval = now - operator_.active_time;

        if (operator_.ip == "" || cur_client.ip == operator_.ip
                || interval > TIME_OUT)
        {
            operator_.ip = cur_client.ip;
            operator_.port = cur_client.port;
            operator_.active_time = now;
            return true;
        }
        return false;
    }

    bool CheckNpuClient(request_type request)
    {
        ClientAddress cur_client = GetClientAddress(request);
        if (cur_client.ip == operator_.ip)
        {
            operator_.active_time = time(0);
            return true;
        }
        return false;
    }
};//class NpuClientManagerMixin
#endif // CLIENT_MANAGER_HPP
