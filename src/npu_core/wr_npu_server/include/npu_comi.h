#ifndef NPU_COMI_H
#define NPU_COMI_H

#include "npu_server.h"
#include <ros/ros.h>
#include <com/serial_port.h>
#include "com/com_driver_creator.hpp"

namespace wizrobo {

using namespace std;
using namespace icom_driver;
class NpuComI
{
public:
    NpuComI(string port, int baudrate, int databit, string parity, icom_driver::ComDriverType com_type);
    ~NpuComI();
    void SetNpuServer(wizrobo_npu::NpuServer* ptr);
    void Run();
private:
    funcmap::FuncMap* p_func_map_;
    icom_driver::IComDriver* p_com_;
    icom_driver::ComDriverType com_type_;

    float loop_frequence_;

    std::string current_map_name_;

};

}
#endif // NPU_COMI_H
