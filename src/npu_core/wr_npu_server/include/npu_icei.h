/****************************************
*Maintainer status: developed
*Maintainer: <PERSON> (<EMAIL>)
*Title: WR C++ API
*Version: 0.9.9
*Copyright(c) 2016-2017 LinkMiao CO., Ltd.
*All rights reserved
****************************************/
#ifndef WIZROBO_NPU_NPU_ICEI_H
#define WIZROBO_NPU_NPU_ICEI_H
#include "ros/ros.h"
#include "std_msgs/String.h"
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <geometry_msgs/Twist.h>
#include <nav_msgs/OccupancyGrid.h>
#include <tf/transform_listener.h>

#include <IceUtil/IceUtil.h>
#include <Ice/Ice.h>

#include "npuice_api.h"
#include "npu_server.h"

namespace wizrobo_npu {
using namespace std;

class NpuIceI : public NpuIce
{
private:
    NpuServer *p_npu_server;

public:
    NpuIceI(int argc, char** agrv, NpuServer* ptr);
    virtual ~NpuIceI();


    //// connection
    virtual string GetServerVersion(const ::Ice::Current& = ::Ice::Current());
    virtual void Connect(const string& version, const ::Ice::Current& = ::Ice::Current());
    virtual ServerState GetServerState(const ::Ice::Current& = ::Ice::Current());
    virtual SystemDiagInfo GetSystemDiagInfo(const ::Ice::Current& = ::Ice::Current());
    virtual ActionState GetActionState(const ::Ice::Current& = ::Ice::Current());
    virtual NpuState GetNpuState(const ::Ice::Current& = ::Ice::Current());

    //// power
    virtual void Shutdown(const ::Ice::Current& = ::Ice::Current());
    virtual void Reboot(const ::Ice::Current& = ::Ice::Current());

    //// config management
    virtual StringArray GetConfigIdList(const ::Ice::Current& = ::Ice::Current());
    virtual void SelectConfig(const string& id, const ::Ice::Current& = ::Ice::Current());
    virtual void DeleteConfig(const string& id, const ::Ice::Current& = ::Ice::Current());
    virtual void AddConfig(const string& id, const ::Ice::Current& = ::Ice::Current());

    // npu
    virtual CoreParam GetCoreParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetCoreParam(const CoreParam& param, const ::Ice::Current& = ::Ice::Current());

    // motor
    virtual MotorParam GetMotorParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetMotorParam(const MotorParam& param, const ::Ice::Current& = ::Ice::Current());

    // pid
    virtual PidParam GetPidParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetPidParam(const PidParam& param, const ::Ice::Current& = ::Ice::Current());

    // chassis
    virtual ChassisParam GetChassisParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetChassisParam(const ChassisParam& param, const ::Ice::Current& = ::Ice::Current());

    // footprint
    virtual FootprintParam GetFootprintParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetFootprintParam(const FootprintParam& param, const ::Ice::Current& = ::Ice::Current());

    // base
    virtual BaseParam GetBaseParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetBaseParam(const BaseParam& param, const ::Ice::Current& = ::Ice::Current());

    // sensor
    virtual SensorParam GetSensorParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetSensorParam(const SensorParam& param, const ::Ice::Current& = ::Ice::Current());

    // teleop
    virtual TeleopParam GetTeleopParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetTeleopParam(const TeleopParam& param, const ::Ice::Current& = ::Ice::Current());

    // navi
    virtual NaviParam GetNaviParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetNaviParam(const NaviParam& param, const ::Ice::Current& = ::Ice::Current());

    // slam
    virtual SlamParam GetSlamParam(const ::Ice::Current& = ::Ice::Current());
    virtual void SetSlamParam(const SlamParam& param, const ::Ice::Current& = ::Ice::Current());

    //// slave mode
    virtual void FeedMotorEnc(const MotorEnc& enc, const ::Ice::Current& = ::Ice::Current());
    virtual void FeedActMotorSpd(const MotorSpd& spd, const ::Ice::Current& = ::Ice::Current());

    //// motor data

    // enc
    virtual MotorEnc GetMotorEnc(const ::Ice::Current& = ::Ice::Current());
    virtual void ClearMotorEnc(const ::Ice::Current& = ::Ice::Current());

    // spd
    virtual MotorSpd GetCmdMotorSpd(const ::Ice::Current& = ::Ice::Current());
    virtual MotorSpd GetActMotorSpd(const ::Ice::Current& = ::Ice::Current());

    //// sensor data

    // lidar
    virtual LidarScan GetLidarScan(const ::Ice::Current& = ::Ice::Current());
    virtual ImgLidarScan GetImgLidarScan(const ::Ice::Current& = ::Ice::Current());

    // imu
    virtual ImuData GetImuData(const ::Ice::Current& = ::Ice::Current());

    // sonar
    virtual SonarScan GetSonarScan(const ::Ice::Current& = ::Ice::Current());
    virtual ImgSonarScan GetImgSonarScan(const ::Ice::Current& = ::Ice::Current());

    // infrd
    virtual InfrdScan GetInfrdScan(const ::Ice::Current& = ::Ice::Current());
    virtual ImgInfrdScan GetImgInfrdScan(const ::Ice::Current& = ::Ice::Current());

    // bumper
    virtual BumperArray GetBumperArray(const ::Ice::Current& = ::Ice::Current());

    // battery
    virtual BatteryStatus GetBatteryStatus(const ::Ice::Current& = ::Ice::Current());

    //// manul control
    virtual void SetManualCmd(const ManualCmdType cmd, const ::Ice::Current& = ::Ice::Current());
    virtual void SetManualVel(const float lin_scale, const float ang_scale, const ::Ice::Current& = ::Ice::Current());

    //// map management

    // map
    virtual MapInfoList GetMapInfos(const ::Ice::Current& = ::Ice::Current());
    virtual void SetMapInfos(const MapInfoList& list, const ::Ice::Current& = ::Ice::Current());
    virtual void SelectMap(const string& id, const ::Ice::Current& = ::Ice::Current());

    // station
    virtual StationList GetStations(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual void SetStations(const string& map_id, const StationList& list, const ::Ice::Current& = ::Ice::Current());
    virtual ImgStationList GetImgStations(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual void SetImgStations(const string& map_id, const ImgStationList& list, const ::Ice::Current& = ::Ice::Current());
    virtual GeoStationList GetGeoStations(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual void SetGeoStations(const string& map_id, const GeoStationList& list, const ::Ice::Current& = ::Ice::Current());

    // task
    virtual TaskList GetTaskList(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual void ExecuteTask(const TaskList& list, const ::Ice::Current& = ::Ice::Current());
    virtual void SetTasks(const string& map_id, const TaskList& list, const ::Ice::Current& = ::Ice::Current());

    // path
    virtual PathList GetPaths(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual void SetPaths(const string& map_id, const PathList& list, const ::Ice::Current& = ::Ice::Current());
    virtual ImgPathList GetImgPaths(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual void SetImgPaths(const string& map_id, const ImgPathList& list, const ::Ice::Current& = ::Ice::Current());
    virtual GeoPathList GetGeoPaths(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual GeoPoseList GetGeoPath(const ::Ice::Current& = ::Ice::Current());
    virtual void SetGeoPaths(const string& map_id, const GeoPathList& list, const ::Ice::Current& = ::Ice::Current());
    virtual void SetGeoPath(const GeoPath& geo_path, const ::Ice::Current& = ::Ice::Current());

    // virtual wall
    virtual VirtualWallList GetVirtualWalls(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual void SetVirtualWalls(const string& map_id, const VirtualWallList& virtual_walls, const ::Ice::Current& = ::Ice::Current());
    virtual ImgVirtualWallList GetImgVirtualWalls(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual void SetImgVirtualWalls(const string& map_id, const ImgVirtualWallList& virtual_walls, const ::Ice::Current& = ::Ice::Current());
    virtual GeoVirtualWallList GetGeoVirtualWalls(const string& map_id, const ::Ice::Current& = ::Ice::Current());
    virtual void SetGeoVirtualWalls(const string& map_id, const GeoVirtualWallList& virtual_walls, const ::Ice::Current& = ::Ice::Current());

    //// common runtime data

    // vel
    virtual Vel3D GetCmdVel(const ::Ice::Current& = ::Ice::Current());
    virtual Vel3D GetActVel(const ::Ice::Current& = ::Ice::Current());

    // TODEL: be replaced by GetActVel()
    virtual Vel3D GetCurrentVel(const ::Ice::Current& = ::Ice::Current());

    // acc
    virtual Acc3D GetAcc(const ::Ice::Current& = ::Ice::Current());

    // TODEL: be replaced by GetAcc
    virtual Acc3D GetCurrentAcc(const ::Ice::Current& = ::Ice::Current());

    // pose
    virtual Pose3D GetCmdPose(const ::Ice::Current& = ::Ice::Current());
    virtual Pose3D GetActPose(const ::Ice::Current& = ::Ice::Current());
    virtual ImgPose GetCmdImgPose(const ::Ice::Current& = ::Ice::Current());
    virtual ImgPose GetActImgPose(const ::Ice::Current& = ::Ice::Current());
    virtual GeoPose GetCmdGeoPose(const ::Ice::Current& = ::Ice::Current());
    virtual GeoPose GetActGeoPose(const ::Ice::Current& = ::Ice::Current());

    // TODEL: be replaced by GetActPose()
    virtual Pose3D GetCurrentPose(const ::Ice::Current& = ::Ice::Current());

    // TODEL: be replaced by GetActImgPose()
    virtual ImgPose GetCurrentImgPose(const ::Ice::Current& = ::Ice::Current());

    // path
    virtual Path GetGlobalPath(const ::Ice::Current& = ::Ice::Current());
    virtual Path GetLocalPath(const ::Ice::Current& = ::Ice::Current());
    virtual Path GetActPath(const ::Ice::Current& = ::Ice::Current());
    virtual ImgPath GetGlobalImgPath(const ::Ice::Current& = ::Ice::Current());
    virtual ImgPath GetLocalImgPath(const ::Ice::Current& = ::Ice::Current());
    virtual ImgPath GetActImgPath(const ::Ice::Current& = ::Ice::Current());
    virtual GeoPath GetGlobalGeoPath(const ::Ice::Current& = ::Ice::Current());
    virtual GeoPath GetLocalGeoPath(const ::Ice::Current& = ::Ice::Current());
    virtual GeoPath GetActGeoPath(const ::Ice::Current& = ::Ice::Current());

    // TODEL: replaced by GetGlobalPath()
    virtual Path GetCurrentPath(const ::Ice::Current& = ::Ice::Current());

    // TODEL: replaced by GetGlobalImgPath()
    virtual ImgPath GetCurrentImgPath(const ::Ice::Current& = ::Ice::Current());

    // map
    virtual Map2D GetMap(const ::Ice::Current& = ::Ice::Current());
    virtual ImgMap GetImgMap(const ::Ice::Current& = ::Ice::Current());
    virtual GeoMap GetGeoMap(const ::Ice::Current& = ::Ice::Current());

    // TODEL: be replaced by GetMap()
    virtual Map2D GetCurrentMap(const ::Ice::Current& = ::Ice::Current());

    // TODEL: be replaced by GetImgMap()
    virtual ImgMap GetCurrentImgMap(const ::Ice::Current& = ::Ice::Current());

    // footprint
    virtual Point3DList GetFootprintVertices(const ::Ice::Current& = ::Ice::Current());
    virtual ImgPointList GetFootprintImgVertices(const ::Ice::Current& = ::Ice::Current());
    virtual GeoPointList GetFootprintGeoVertices(const ::Ice::Current& = ::Ice::Current());

    //// telop
    virtual void StartTelop(const ::Ice::Current& = ::Ice::Current());
    virtual void StopTelop(const ::Ice::Current& = ::Ice::Current());

    //// navi

    /// navi.common

    // initial pose
    virtual void SetInitPose(const Pose3D& pose, const ::Ice::Current& = ::Ice::Current());
    virtual void SetInitPoseArea(const InitPoseArea& pose, const ::Ice::Current& = ::Ice::Current());
    virtual void SetInitImgPose(const ImgPose& pose, const ::Ice::Current& = ::Ice::Current());
    virtual void SetInitImgPoseArea(const InitImgPoseArea& pose, const ::Ice::Current& = ::Ice::Current());
    virtual void SetInitGeoPose(const GeoPose& pose, const ::Ice::Current& = ::Ice::Current());
    virtual void SetInitGeoPoseArea(const InitGeoPoseArea& pose, const ::Ice::Current& = ::Ice::Current());
    virtual float GetMatchingScore(const ::Ice::Current& = ::Ice::Current());

    // start & stop
    virtual NaviMode GetNaviMode(const ::Ice::Current& = ::Ice::Current());
    virtual void StartNavi(const NaviMode mode, const ::Ice::Current& = ::Ice::Current());
    virtual void StopNavi(const ::Ice::Current& = ::Ice::Current());

    // task control
    virtual void PauseTask(const ::Ice::Current& = ::Ice::Current());
    virtual void ContinueTask(const ::Ice::Current& = ::Ice::Current());
    virtual void CancelTask(const ::Ice::Current& = ::Ice::Current());
    virtual float GetTaskProgress(const ::Ice::Current& = ::Ice::Current());

    // status
    virtual NaviState GetNaviState(const ::Ice::Current& = ::Ice::Current());

    /// navi.p2p
    virtual void GotoPose(const Pose3D& pose, const ::Ice::Current& = ::Ice::Current());
    virtual void GotoImgPose(const ImgPose& pose, const ::Ice::Current& = ::Ice::Current());
    virtual void GotoGeoPose(const GeoPose& pose, const ::Ice::Current& = ::Ice::Current());

    //TODEL: replaced by GotoPose(Pose3D pose);
    virtual void GotoGoal(const Pose3D& goal, const ::Ice::Current& = ::Ice::Current());

    // TODEL: replaced by GotoImgPose(Pose3D pose);
    virtual void GotoImgGoal(const ImgPose& goal, const ::Ice::Current& = ::Ice::Current());
    virtual void GotoStation(const string& map_id, const string& station_id, const ::Ice::Current& = ::Ice::Current());

    /// navi.pf
    virtual void FollowTempPath(const Pose3DList& poses, const ::Ice::Current& = ::Ice::Current());
    virtual void FollowTempImgPath(const ImgPoseList& poses, const ::Ice::Current& = ::Ice::Current());
    virtual void FollowTempGeoPath(const GeoPoseList& poses, const ::Ice::Current& = ::Ice::Current());
    virtual void FollowPath(const string& map_id, const string& path_id, const ::Ice::Current& = ::Ice::Current());

    //// navi.ccp
    virtual Pose3DList PlanCoveragePath(const Point3DList& vertices, const ::Ice::Current& = ::Ice::Current());
    virtual ImgPoseList PlanCoverageImgPath(const ImgPointList& vertices, const ::Ice::Current& = ::Ice::Current());
    virtual GeoPoseList PlanCoverageGeoPath(const GeoPointList& vertices, const ::Ice::Current& = ::Ice::Current());

    //// slam
    virtual SlamMode GetSlamMode(const ::Ice::Current& = ::Ice::Current());
    virtual void StartSlam(const SlamMode mode, const ::Ice::Current& = ::Ice::Current());
    virtual void StopSlam(const string& map_id, const ::Ice::Current& = ::Ice::Current());

    /// save map
    virtual void SaveMapImg(const string& map_id, const ::Ice::Current& = ::Ice::Current());

    //// file
    virtual ZipFile ExportConfigFile(const string& file_name, const ::Ice::Current& = ::Ice::Current());
    virtual void ImportConfigFile(const ZipFile& file, const string& file_name, const ::Ice::Current& = ::Ice::Current());
    virtual ZipFile ExportMapFile(const string& file_name, const ::Ice::Current& = ::Ice::Current());
    virtual void ImportMapFile(const ZipFile& file, const string& file_name, const ::Ice::Current& = ::Ice::Current());
    virtual void GetExportFileInfo_async(const ::wizrobo_npu::AMD_NpuIce_GetExportFileInfoPtr& ptr, const string& fileName, const ::Ice::Current& = ::Ice::Current());
    virtual FileData GetExportFiledata(const string& fileName, const int chunk_index, const ::Ice::Current& = ::Ice::Current());
    virtual void SendImportFileData(const FileData& data, const ::Ice::Current& = ::Ice::Current());

    ////sensorstatus
    virtual void CheckSensorStatus_async(const ::wizrobo_npu::AMD_NpuIce_CheckSensorStatusPtr& ptr, const CheckMode mode, const ::Ice::Current& = ::Ice::Current());
    virtual SensorStatus GetSensorStatus(const ::Ice::Current& = ::Ice::Current());

    ////exce_info
    virtual void CheckAbnormalInfo(const ::Ice::Current& = ::Ice::Current());

    ////task_system

    //void StopTaskSystem() throws NpuException;
    virtual RuntimeStatusList GetRuntimeStatus(const ::Ice::Current& = ::Ice::Current());


};
}
#endif// WIZROBO_NPU_NPU_ICEI_H
