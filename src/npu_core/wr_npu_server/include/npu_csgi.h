#ifndef NPU_CSGI_H
#define NPU_CSGI_H

#include <ros/ros.h>
#include "npu_server.h"
#include "wr_npu_msgs/CsgTsNpu.h"
#include <npuice_api.h>
#include <npuice_map.h>

namespace wizrobo_npu {
using namespace std;

class NpuCsgI
{
public:
    NpuCsgI();
    void SetNpuServer(NpuServer* ptr);
private:
    NpuServer *p_npu_server;
    ros::ServiceServer server_;

    NaviMode navi_mode_;
    NpuState npu_state_;
    string v;
    string current_map_name_;
    int npu_state_d_;

    bool CsgTransferNpu(wr_npu_msgs::CsgTsNpu::Request &req, wr_npu_msgs::CsgTsNpu::Response &rep);
    bool ConnectNpu();
    bool GetCurrentMapName();
    bool StartNavi();
    bool StopNavi();
    bool SelectMap(string ss);
    bool GetNpuState();
    bool CancelTask();
};
}
#endif // NPU_CSGI_H
