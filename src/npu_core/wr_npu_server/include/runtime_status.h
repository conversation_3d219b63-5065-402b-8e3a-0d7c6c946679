#ifndef WIZROBO_RUNTIMESTATUS_H
#define WIZROBO_RUNTIMESTATUS_H

#include <stdlib.h>
#include "boost/thread.hpp"

#include <ros/ros.h>
#include <ros/rostime_decl.h>
#include <sensor_msgs/LaserScan.h>
#include <sensor_msgs/Imu.h>
#include <sensor_msgs/NavSatFix.h>
#include <sensor_msgs/Range.h>
#include <nav_msgs/Odometry.h>
#include <nav_msgs/OccupancyGrid.h>

#include <npu.h>
#include <wr_npu_msgs/VsensorStatus.h>
#include <wr_npu_msgs/SensorStatus.h>
#include <wr_npu_msgs/ExceptionInformation.h>
#include <wr_npu_msgs/RuntimeStatus.h>
#include <wr_npu_msgs/MotorEnc.h>
#include <wr_npu_msgs/EmergencyStopData.h>
#include <wr_npu_msgs/BumperData.h>
#include <wr_npu_msgs/VoltageData.h>
#include <wr_npu_msgs/CollisionState.h>
#include <wr_npu_msgs/KeyaStatus.h>


namespace wizrobo {

typedef const wr_npu_msgs::RuntimeStatus::ConstPtr RuntimeStusPtr;

template<class MessageType>
class DataChecker
{
public:
    ros::Subscriber sub_;
    std::string topic_;
    unsigned int status_value_;
    ros::Time last_stamp_;

public:
    //virtual void Callback() = 0;
    DataChecker()
    {
    }

    DataChecker(unsigned int status_value, const std::string &topic)
    {
        init(status_value, topic);
    }

    void init(unsigned int status_value, const std::string &topic)
    {
        ros::NodeHandle nh;
        status_value_ = status_value;
        topic_ = topic;
        sub_ = nh.subscribe(topic_, 1, &DataChecker::Callback, this);
    }

    void Callback(const typename MessageType::ConstPtr& msg)
    {
        last_stamp_ = ros::Time::now();
    }

    unsigned int GetStatus()
    {
        if (ros::Time::now()-last_stamp_ > ros::Duration(1.0)) {
            return status_value_;
        }
        return 0;
    }
};//class DataChecker

class EmbChecker : public DataChecker<wr_npu_msgs::EmergencyStopData>
{
private:
    bool is_triggered_;
public:
    EmbChecker()
    {
        ros::NodeHandle nh;
        status_value_ = wr_npu_msgs::RuntimeStatus::RT1_NAVI_EMB_TRIGGERED;
        topic_ = STD_ESB_TOPIC_NAME;
        sub_ = nh.subscribe(topic_, 1, &EmbChecker::Callback, this);
        is_triggered_ = false;
    }

    void Callback(const wr_npu_msgs::EmergencyStopData::ConstPtr& msg)
    {
        last_stamp_ = ros::Time::now();
        is_triggered_ = (msg->status==0)?false:true;
    }

    unsigned int GetStatus()
    {
        //if (ros::Time::now()-last_stamp_ > ros::Duration(1.0)) {
        if (is_triggered_) {
            return status_value_;
        }
        return 0;
    }
}; //class EmbChecker

class BumperChecker : public DataChecker<wr_npu_msgs::EmergencyStopData>
{
private:
    int bumper_status_;
public:
    BumperChecker()
    {
        ros::NodeHandle nh;
        status_value_ = wr_npu_msgs::RuntimeStatus::RT1_NAVI_BUMPER_TRIGGERED;
        topic_ = STD_BUMPER_TOPIC_NAME;
        sub_ = nh.subscribe(topic_, 1, &BumperChecker::Callback, this);
    }

    void Callback(const wr_npu_msgs::BumperData::ConstPtr& msg)
    {
        last_stamp_ = ros::Time::now();
        bumper_status_ = 0;
        for (int i=0;i<msg->states.size();i++) {
            bumper_status_ <<= 1;
            bumper_status_ |= msg->states[i];
        }
    }
    unsigned int GetStatus()
    {
        //if (ros::Time::now()-last_stamp_ > ros::Duration(1.0)) {
        if (bumper_status_ != 0) {
            return status_value_;
        }
        return 0;
    }
}; //class BumperChecker

class EntrpChecker : public DataChecker<sensor_msgs::Range>
{
private:
    bool is_triggered_;
public:
    EntrpChecker()
    {
        ros::NodeHandle nh;
        status_value_ = wr_npu_msgs::RuntimeStatus::RT1_NAVI_ENTRP_DETECTED;
        topic_ = "/entrp_detect";
        sub_ = nh.subscribe(topic_, 1, &EntrpChecker::Callback, this);
    }

    void Callback(const wr_npu_msgs::EmergencyStopData::ConstPtr& msg)
    {
        last_stamp_ = ros::Time::now();
        is_triggered_ = (msg->status==0)?false:true;
    }

    unsigned int GetStatus()
    {
        //if (ros::Time::now()-last_stamp_ > ros::Duration(1.0)) {
        if (is_triggered_) {
            return status_value_;
        }
        return 0;
    }
}; //class EntrpChecker

class CollisionChecker : public DataChecker<wr_npu_msgs::CollisionState>
{
private:
    bool in_collision_;
public:
    CollisionChecker()
    {
        ros::NodeHandle nh;
        status_value_ = wr_npu_msgs::RuntimeStatus::RT1_NAVI_OBSTACLE_BLOCK;
        topic_ = "/pathfollower/collision_state";
        sub_ = nh.subscribe(topic_, 1, &CollisionChecker::Callback, this);
        in_collision_ = false;
    }

    void Callback(const wr_npu_msgs::CollisionState::ConstPtr& msg)
    {
        last_stamp_ = ros::Time::now();
        in_collision_ = (msg->state==0)?false:true;
    }

    unsigned int GetStatus()
    {
        if (in_collision_) {
            return status_value_;
        }
        return 0;
    }
};//class CollisionChecker

class KeyaChecker : public DataChecker<wr_npu_msgs::KeyaStatus>
{
private:
    bool in_collision_;
    int status_value_;
public:
    KeyaChecker()
    {
        ros::NodeHandle nh;
        status_value_ = 0;
        topic_ = "/keya_status";
        sub_ = nh.subscribe(topic_, 1, &KeyaChecker::Callback, this);
    }

    void Callback(const wr_npu_msgs::KeyaStatus::ConstPtr& msg)
    {
        last_stamp_ = ros::Time::now();
        status_value_ = static_cast<int>(msg->keya_status);
    }

    unsigned int GetStatus()
    {
        return status_value_;
    }
};//class CollisionChecker

class RuntimeStatus
{
private:
    bool is_inited_;
    boost::thread *ptr_spin_thread_;

    ros::Publisher runtime_status_pub_;
    wr_npu_msgs::RuntimeStatus runtime_status_msg_;

    DataChecker<sensor_msgs::LaserScan> lidar_data_checker_;
    DataChecker<sensor_msgs::Imu> imu_data_checker_;
    DataChecker<wr_npu_msgs::MotorEnc> encoder_data_checker_;
    DataChecker<sensor_msgs::NavSatFix> gps_data_checker_;
    DataChecker<nav_msgs::OccupancyGrid> map_data_checker_;
    EmbChecker emb_triggered_checker_;
    BumperChecker bumper_triggered_checker_;
    EntrpChecker entrp_detected_checker_;
    CollisionChecker pf_collision_checker_;
    KeyaChecker keya_checker_;

    ros::Subscriber power_voltage_sub_;
    float power_vol_;
    float power_threshold_;
    float battery_vol_;
    float battery_threshold_;

public:
    RuntimeStatus(int argc, char** argv);
    void Init();
    void Run();
    void SpinThreadLoop();
    void PowerVoltageCallback(const wr_npu_msgs::VoltageData::ConstPtr& msg);
};
}// namespace wizoro
#endif // WIZROBO_RUNTIMESTATUS_H
