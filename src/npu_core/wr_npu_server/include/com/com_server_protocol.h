#ifndef COM_SERVER_PROTOCOL_H
#define COM_SERVER_PROTOCOL_H

#include "npu.h"
#include "stdio.h"
#include "iostream"
#include "string"
#include "string.h"
#include <vector>
#include <npu_enum_ext.h>
#include "npuice_enum_ext.h"

namespace wizrobo { namespace icom_driver {

enum ComDriverType
{
    SERIAL_MODBUSRTU = 0u,
    SERIAL_LM = 1u
};

enum CmdType
{
    WRITE = 0u,
    READ = 1u
};

struct funcdata
{
    funcdata()
    {
        cmd = WRITE;
        func = 0;
    }
    ~funcdata(){
        delete parameter;
        parameter = NULL;
    }
    CmdType cmd;
    short func;
    unsigned char* parameter = new unsigned char;
};
}}
namespace wizrobo { namespace enum_ext {
using namespace wizrobo::icom_driver;

// string support of ComDriverType
BEGIN_ENUM_STRING(ComDriverType)
{
    ENUM_STRING(SERIAL_MODBUSRTU);
    ENUM_STRING(SERIAL_LM);
}
END_ENUM_STRING;

}}// namespace


#endif // COM_SERVER_PROTOCOL_H
