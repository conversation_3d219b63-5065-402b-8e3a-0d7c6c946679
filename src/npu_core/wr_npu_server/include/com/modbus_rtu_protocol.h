#ifndef MODBUS_RTU_PROTOCOL_H
#define MODBUS_RTU_PROTOCOL_H
//#include "com/com_driver_interface.h"

namespace wizrobo { namespace icom_driver { namespace modbusrtu {

const char HEAD = 0x01;
const char READCMD = 0x03;
const char WRITECMD = 0x10;

const int DATABIT = 8;
const int BAUDRATE = B19200;
const std::string PARITY("ODD");

const unsigned int STARTNAVITASK    = 1; //LBIT(0)
const unsigned int MANUALCONTROL    = 2; //LBIT(1)
const unsigned int SETCHARGEPOSE    = 4; //LBIT(2)
const unsigned int STARTCHARGE      = 8; //LBIT(3)
const unsigned int CHARGEDONE       = 16; //LBIT(4)
const unsigned int STARTTEACHMODE   = 32; //LBIT(5)
const unsigned int SETTASKPOSE      = 64;//LBIT(6)
const unsigned int STOPTEACHMODE    = 128; //HBIT(7)

const unsigned int RESETNPU         = 256; //HBIT(8)
const unsigned int REACHEDCHARGE    = 512; //HBIT(9)
const unsigned int STARTP2PNAVI     = 1024; //HBIT(10)
const unsigned int STARTPFNAVI      = 2048; //HBIT(11)
const unsigned int STOPNAVI         = 4096; //HBIT(12)
const unsigned int STARTSLAM        = 8192; //HBIT(13)
const unsigned int STOPSLAMSAVEMAP  = 16384;//HBIT(14)
const unsigned int STOPSLAMONLY     = 32768;//HBIT(15)

const unsigned int ROCKERRANGE             = 1600;
const unsigned int ROCKERLEFTSTART         = 3000;
const unsigned int ROCKERRIGHTSTART        = 2600;
const unsigned int ROCKERFORWARDSTART      = 3000;
const unsigned int ROCKERBACKWARDSTART     = 2600;
}//namespace modbusrtu
}//namespace icom_driver
}//namespace wizrobol

#endif // MODBUS_RTU_PROTOCOL_H
