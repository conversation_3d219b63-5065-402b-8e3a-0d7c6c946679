#ifndef COM_DRIVER_INTERFACE_H
#define COM_DRIVER_INTERFACE_H
#include "com/serial_port.h"
#include "com/com_func_map.h"
#include "com/com_server_protocol.h"

namespace wizrobo { namespace icom_driver {
using namespace serial_port;

class IComDriver
{
protected:
    IComDriver():is_inited_(0)
    {}

public:
    virtual ~IComDriver()
    {}

public:
    inline int IsInited()
    {
        return is_inited_;
    }

    virtual void Run() = 0;

//    virtual int GetCmd(std::vector<funcdata>& data) = 0;

    virtual void SetFuncPtr(funcmap::FuncMap* p_func_map) = 0;

//    virtual char* MakeFrame(std::vector<unsigned char*> data, char cmd) = 0;

    virtual int SetPortParam(std::string port, int baudrate, int databit, std::string parity) = 0;

protected:
    int is_inited_;
};

}}// namespace



#endif // COM_DRIVER_INTERFACE_H
