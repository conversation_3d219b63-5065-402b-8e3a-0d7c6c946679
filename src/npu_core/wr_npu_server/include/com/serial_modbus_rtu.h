#ifndef SERIAL_MODBUS_RTU_H
#define SERIAL_MODBUS_RTU_H

#include "com/com_driver_interface.h"
#include "com/modbus_rtu_protocol.h"

namespace wizrobo { namespace icom_driver { namespace modbusrtu {

using namespace serial_port;

class ModBusRtu: public IComDriver
{
public:
    ModBusRtu();
    ~ModBusRtu();


    void Run();

    void SetFuncPtr(funcmap::FuncMap* p_func_map);

    int SetPortParam(std::string port, int baudrate, int databit, std::string parity);

private:
    char* Short2Char(short data);
    int GetCmd(std::vector<funcdata>& data);
    char* MakeFrame(std::vector<unsigned char> data, char cmd);
    int ParseCmd(std::vector<funcdata>& v_func_data);
    void ReadBuf2Vector(int len);
    int ReadCmd();
    int CheckCmd(std::vector<unsigned char> cmd);
    int GetReadCmd();
    int GetWriteCmd();
    short Byte2Short(unsigned char h, unsigned char l);
    unsigned short Byte2UShort(unsigned char h, unsigned char l);
    unsigned short CalcCRC_TAB(unsigned char* pDataBuffer,
                                      unsigned long usDataLen);
    unsigned short CalcCRCbyAlgorithm(unsigned char* pDataBuffer,
                                             unsigned long usDataLen);
    void PrintXCmd(std::string name, std::vector<unsigned char> cmd);
    void PrintXCmd(std::string name, char* cmd, int len);
    void PrintVCmd(std::string name, std::vector<funcdata> cmd);
public:

private:
    std::vector<unsigned char> cmd_;
    SerialPort port_;
    std::vector<funcdata> func_data_;
    funcmap::FuncMap* p_func_map_;

};
}//namespace modbusrtu
}//namespace icom_driver
}//namespace wizrobo
#endif // SERIAL_MODBUS_RTU_H
