#ifndef SERIAL_LM_DRIVER_PROTOCOL_H
#define SERIAL_LM_DRIVER_PROTOCOL_H
namespace wizrobo { namespace icom_driver { namespace serial_lm {

const unsigned short FLAG_AAAA = 0x55AA;

struct Header
{
    unsigned short flag;
    unsigned char cmd;
    unsigned char data_length;
    unsigned char function;
};


const unsigned int HEADER_LENGTH = 5;

}//namespace serial_lm
}//namespace icom_driver
}//namespace wizrobo

#endif // SERIAL_LM_DRIVER_PROTOCOL_H
