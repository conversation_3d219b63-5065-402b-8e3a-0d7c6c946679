#ifndef SERIAL_LM_SERVER_H
#define SERIAL_LM_SERVER_H

#include "com/com_driver_interface.h"
#include "com/serial_lm_driver_protocol.h"

namespace wizrobo { namespace icom_driver { namespace serial_lm {

class ComLMDriver : public IComDriver
{
public:
    ComLMDriver();
    ~ComLMDriver();

    void Run();

    void SetFuncPtr(funcmap::FuncMap* p_func_map);

    int SetPortParam(std::string port, int baudrate, int databit, std::string parity);

private:
    bool ReceiveCmd();
    void ParseCmd();
    void ReadFunc(unsigned char func);
    void WriteFunc(unsigned char func, int data_len);
    unsigned char CheckSum(unsigned char *ptr, int length);

private:
    SerialPort port_;
    funcmap::FuncMap* p_func_map_;

    unsigned char *p_read_buffer_char_;
    unsigned char *p_write_buffer_char_;

    static const int receive_buffer_length_ = 64;
    static const int send_buffer_length_ = 256;
    unsigned char receive_buffer_[receive_buffer_length_];
    int receive_buffer_rear_;
    unsigned char send_buffer_[send_buffer_length_];
};

}//namespace serial_lm
}//namespace icom_driver
}//namespace wizrobo
#endif // SERIAL_LM_SERVER_H
