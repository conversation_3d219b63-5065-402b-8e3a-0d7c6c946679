#ifndef COM_FUNC_MAP_HPP
#define COM_FUNC_MAP_HPP

#include "npu.h"
#include <functional>
#include <typeinfo>
#include <string>
#include <string.h>
#include <iostream>
#include "npu_server.h"
#include <map>
#include <std_msgs/Bool.h>
#include <geometry_msgs/Twist.h>
#include <geometry_msgs/PoseStamped.h>
#include "com/modbus_rtu_protocol.h"
#include "com/com_server_protocol.h"

namespace wizrobo { namespace funcmap {

using namespace icom_driver::modbusrtu;
using namespace icom_driver;
using namespace wizrobo_npu;

struct ManualVel{
    float v_lin;
    float v_ang;
};

struct ComMotorParam
{
    ComMotorParam(wizrobo_npu::MotorParam param)
    {
        motor_num_ =  static_cast<char>(param.motor_num);
        unsigned char *p_char = (unsigned char*)param.motor_dir_str.c_str();
        motor_dir_str_l_ = (*p_char);
        motor_dir_str_r_ = (*(p_char+1));
        motor_max_spd_rpm_ = param.motor_max_spd_rpm;
        motor_rdc_ratio_ = param.motor_rdc_ratio;
        motor_enc_res_ppr_ = param.motor_enc_res_ppr;
        motor_pwm_frq_hz_ = param.motor_pwm_frq_hz;
        enb_vol_ = static_cast<char>(param.enb_vol);
        dir_vol_ = static_cast<char>(param.dir_vol);
        pwm_vol_ = static_cast<char>(param.pwm_vol);
        brk_vol_ = static_cast<char>(param.brk_vol);
        enb_auxdir_mode_ = static_cast<char>(param.enb_auxdir_mode);
        enb_throttle_mode_ = static_cast<char>(param.enb_throttle_mode);
        throttle_zero_pos_fac_ = param.throttle_zero_pos_fac;
        throttle_zero_neg_fac_ = param.throttle_zero_neg_fac;
        end_left_right_switch_ = static_cast<char>(param.end_left_right_switch);
    }

    wizrobo_npu::MotorParam CreatMotorParam()
    {
        wizrobo_npu::MotorParam param;
        param.motor_num =  static_cast<int>(motor_num_);
        unsigned char* p_dir = new unsigned char;
        *p_dir = motor_dir_str_l_;
        *(p_dir + 1) = motor_dir_str_r_;
        param.motor_dir_str = (char*)p_dir;
        param.motor_max_spd_rpm = motor_max_spd_rpm_;
        param.motor_rdc_ratio = motor_rdc_ratio_;
        param.motor_enc_res_ppr = motor_enc_res_ppr_;
        param.motor_pwm_frq_hz = motor_pwm_frq_hz_;
        param.enb_vol = static_cast<ValidOutputLevel>(enb_vol_);
        param.dir_vol = static_cast<ValidOutputLevel>(dir_vol_);
        param.pwm_vol = static_cast<ValidOutputLevel>(pwm_vol_);
        param.brk_vol = static_cast<ValidOutputLevel>(brk_vol_);
        param.enb_auxdir_mode = static_cast<bool>(enb_auxdir_mode_);
        param.enb_throttle_mode = static_cast<bool>(enb_throttle_mode_);
        param.throttle_zero_pos_fac = throttle_zero_pos_fac_;
        param.throttle_zero_neg_fac = throttle_zero_neg_fac_;
        param.end_left_right_switch = static_cast<bool>(end_left_right_switch_);
        return param;
    }

    char motor_num_;
    char motor_dir_str_l_;
    char motor_dir_str_r_;
    int motor_max_spd_rpm_;
    float motor_rdc_ratio_;
    int motor_enc_res_ppr_;
    int motor_pwm_frq_hz_;
    char enb_vol_;
    char dir_vol_;
    char pwm_vol_;
    char brk_vol_;
    char enb_auxdir_mode_;
    char enb_throttle_mode_;
    float throttle_zero_pos_fac_;
    float throttle_zero_neg_fac_;
    char end_left_right_switch_;
};


struct ComBaseParam
{
    ComBaseParam(BaseParam param)
    {

    }

    string base_id;
    bool enb_slave_mode;
    MotorParam motor_param;
    PidParam pid_param;
    ChassisParam chassis_param;
    FootprintParam footprint_param;
};

struct ComMotorSpd
{
    ComMotorSpd(MotorSpd spd)
    {
        motor_num_ = static_cast<char>(spd.motor_num);
        steer_angle_deg_ = spd.steer_angle_deg;
        for(int i = 0;i < motor_num_;i++){
            rpms_[i] = spd.rpms[i];
        }
    }
    char motor_num_;
    float steer_angle_deg_;
    float rpms_[10];
};

struct ComBumperArray
{
    ComBumperArray(BumperArray bumper)
    {
        int num = static_cast<int>(bumper.states.size());
        short data = 0x00;
        for(int i = 0;i < num;i++){
            data = static_cast<short>(bumper.states[i].location);
            if(!bumper.states[i].state){
                data = - data;
            }
            state_[i] = data;
        }
    }
    short state_[20];
};

struct ComRunTimeStatus
{
    ComRunTimeStatus(RuntimeStatusList status)
    {
        run_time_status_0 = status[0];
        run_time_status_1 = status[0];
        run_time_status_2 = status[0];
    }
    int run_time_status_0;
    int run_time_status_1;
    int run_time_status_2;
};

struct ComMapInfos
{

};

struct ComMotorEnc
{
    ComMotorEnc(MotorEnc enc)
    {
        motor_num = static_cast<char>(enc.motor_num);
        int ticks[8];
        for(int i = 0;i < 8;i++){
            ticks[i] = 0;
        }
        for(int i = 0;i < enc.motor_num;i++){
            ticks[i] = enc.ticks[i];
        }
        tick_1 = ticks[0];
        tick_2 = ticks[1];
        tick_3 = ticks[2];
        tick_4 = ticks[3];
        tick_5 = ticks[4];
        tick_6 = ticks[5];
        tick_7 = ticks[6];
        tick_8 = ticks[7];
        steer_angle_deg = enc.steer_angle_deg;
    }
    char motor_num;
    int tick_1;
    int tick_2;
    int tick_3;
    int tick_4;
    int tick_5;
    int tick_6;
    int tick_7;
    int tick_8;
    float steer_angle_deg;
};

class FuncMap
{
public:
    FuncMap();
    ~FuncMap();

    void InitFuncMap();
    void SetManualSpd();
    void SetNpuServer(NpuServer* ptr);

private:
    ////com read
    short GetActPose();
    short GetRobotState();//40001
    short GetCurrentMapId();//40004
    short GetNaviStatus();//40010
    ///40010 read func
    short GetStationSettingDoneStatus();//40010.9 TODO
    short GetSaveMapDoneStatus();//40010.10 TODO
    ///end
    short GetActLineSpd();//40011
    short GetActAngSpd();//40012
    short GetAngle();//40013
    //14~16 TODO
    short GetLeftMotorCurrent();//40017
    short GetRightMotorCurrent();//40018
    short GetCurrentGoalId();//40019
    short GetSumOfStation();//40020

    ////com write
    short SetRobotState(unsigned char *data);//40001
    ///set robot state func 40001
    bool StartTask();
    bool ManualControl();//40001.1
    bool SetTaskPose(int last_cmd);//40001.6
    bool StopTeachMode();//40001.7
    ///end
    short SetLineSpd(unsigned char *data);//40002
    short SetAngSpd(unsigned char *data);//40003
    short SetCurrentMapId(unsigned char *data);//40004
    short GotoStation(unsigned char *data);//40005

    ////kunshang
    short SetSpeedGearLevel(unsigned char *data);
    short SafeButtonStatus(unsigned char *data);
    short NormalLampStatus();


    short Byte2Short(unsigned char h, unsigned char l);
    unsigned short Byte2UShort(unsigned char h, unsigned char l);

    bool SelectMap();
    bool SetMapId(int id);
    void CreatAction();
    void CreatStation();
    bool StartNavi(NaviMode mode);
    void UpdateRobotStatus();

    void CurrentGoalCallBack(const geometry_msgs::PoseStampedConstPtr& msg);

    bool CreatStation(string id);
    bool DeletStation(string id);

    ////lm_func
    /// read
    int LMGetNpuVersion(unsigned char *data);
    int LMGetRobotState(unsigned char *data);
    int LMGetCurrentMapId(unsigned char *data);
    int LMGetNaviStatus(unsigned char *data);
    int LMGetNaviMode(unsigned char *data);

    int LMGetSlamMode(unsigned char *data);
    int LMGetSystemDiagInfo(unsigned char *data);
    int LMGetActionState(unsigned char *data);
    int LMGetConfigIDList(unsigned char *data);
    int LMGetActPose(unsigned char *data);

    int LMGetActVel(unsigned char *data);
    int LMGetCmdVel(unsigned char *data);
    int LMGetCmdPose(unsigned char *data);
    int LMGetCoreParam(unsigned char *data);
    int LMGetMotorParam(unsigned char *data);

    int LMGetPidParam(unsigned char *data);
    int LMGetChassisParam(unsigned char *data);
    int LMGetFootprintParam(unsigned char *data);
    int LMGetBaseParam(unsigned char *data);
    int LMGetSensorParam(unsigned char *data);

    int LMGetTeleopParam(unsigned char *data);
    int LMGetNaviParam(unsigned char *data);
    int LMGetSlamParam(unsigned char *data);
    int LMGetMotorEnc(unsigned char *data);

    int LMGetCmdMotorSpd(unsigned char *data);
    int LMGetActMotorSpd(unsigned char *data);
    int LMGetImuData(unsigned char *data);
    int LMGetSonarScan(unsigned char *data);
    int LMGetInfrdScan(unsigned char *data);

    int LMGetBumperArray(unsigned char *data);
    int LMGetBatteryStatus(unsigned char *data);
    int LMGetMapInfos(unsigned char *data);
    int LMGetStations(unsigned char *data);
    int LMGetTaskList(unsigned char *data);

    int LMGetPaths(unsigned char *data);
    int LMGetVirtualWalls(unsigned char *data);
    int LMGetGlobalPath(unsigned char *data);
    int LMGetLocalPath(unsigned char *data);
    int LMGetActPath(unsigned char *data);

    int LMGetFootprintVertices(unsigned char *data);
    int LMGetMatchingScore(unsigned char *data);
    int LMGetTaskProgress(unsigned char *data);
    int LMGetRuntimeStatus(unsigned char *data);


    /// write
    ////1--5
    bool LMConnect(unsigned char *data);
    bool LMShutDown(unsigned char *data);
    bool LMReBoot(unsigned char *data);
    bool LMStartNavi(unsigned char *data);
    bool LMStopNavi(unsigned char *data);

    ////6--10
    bool LMStartSlam(unsigned char *data);
    bool LMStopSlam(unsigned char *data);
    bool LMSelectMap(unsigned char *data);
    bool LMSelectConfig(unsigned char *data);
    bool LMDeleteConfig(unsigned char *data);

    ////11--15
    bool LMAddConfig(unsigned char *data);
    bool LMSetCoreParam(unsigned char *data);
    bool LMSetMotorParam(unsigned char *data);
    bool LMSetPidParam(unsigned char *data);
    bool LMSetChassisParam(unsigned char *data);

    ////16--20
    bool LMSetFootprintParam(unsigned char *data);
    bool LMSetBaseParam(unsigned char *data);
    bool LMSetSensorParam(unsigned char *data);
    bool LMSetTeleopParam(unsigned char *data);
    bool LMSetNaviParam(unsigned char *data);

    ////21--25
    bool LMSetSlamParam(unsigned char *data);
    bool LMFeedMotorEnc(unsigned char *data);
    bool LMFeedActMotorSpd(unsigned char *data);
    bool LMClearMotorEnc(unsigned char *data);
    bool LMSetManualCmd(unsigned char *data);

    ////26--30
    bool LMSetManualVel(unsigned char *data);
    bool LMSetMapInfos(unsigned char *data);
    bool LMSetStations(unsigned char *data);
    bool LMExecuteTask(unsigned char *data);
    bool LMSetTasks(unsigned char *data);

    ////31--35
    bool LMSetPaths(unsigned char *data);
    bool LMSetInitPose(unsigned char *data);
    bool LMSetInitArea(unsigned char *data);
    bool LMPauseTask(unsigned char *data);
    bool LMContinueTask(unsigned char *data);

    ////36--40
    bool LMCancelTask(unsigned char *data);
    bool LMGotoPose(unsigned char *data);
    bool LMGotoStation(unsigned char *data);
    bool LMFollowTempPath(unsigned char *data);
    bool LMFollowPath(unsigned char *data);

    ////41--44
    bool LMPlanCoveragePath(unsigned char *data);
    bool LMGotoGeoPose(unsigned char *data);
    bool LMDeletStation(unsigned char *data);
    bool LMSetPathPoint(unsigned char *data);
public:
    typedef std::function<short(void)> PReadFunc;
    typedef std::function<short(unsigned char *)> PWriteFunc;
    typedef std::function<int (unsigned char *)> LMReadFunc;
    typedef std::function<bool(unsigned char *)> LMWriteFunc;
    std::map<int, PReadFunc> read_func_map_;
    std::map<int, PWriteFunc> write_func_map_;
    std::map<int, LMReadFunc> lm_read_func_map_;
    std::map<int, LMWriteFunc> lm_write_func_map_;

private:
    int goal_id_;
    int map_id_no_;
    int task_goal_id_;
    int normal_goal_id_;
    float ang_vel_;
    float line_vel_;
    float max_ang_vel_;
    float max_line_vel_;
    float speed_gear_level_;
    float ang_gear_level_;
    float left_motor_current_;
    float right_motor_current_;
    bool teach_mode_;//40001.5
    bool station_setting_done_; //40010.9
    bool map_save_done_;//40010.10
    bool is_task_goal_;
    bool set_cmd_vel_;
    short cmd_line_vel_;
    short cmd_ang_vel_;
    short is_active_;//40010.0
    short is_forward_;//40010.1
    short is_backward_;//40010.2
    short is_turn_left_;//40010.3
    short is_turn_right_;//40010.4
    unsigned short com_status_;

    bool d20_1_on_;
    bool d20_2_on_;
    bool myo_started_;
    bool voice_started_;
    bool follow_started_;
    bool safe_button_is_on_;

    string map_id_;

    boost::mutex goal_cb_mutex_;
    boost::thread* update_npu_data_;

    ros::Publisher cmd_pub_;
    ros::Publisher voice_enb_pub_;
    ros::Publisher gesture_enb_pub_;
    ros::Publisher follow_enb_pub_;
    ros::Publisher motor_enb_pub_;
    ros::Publisher motor_power_pub_;
    ros::Subscriber goal_cb_;

    MotorEnc enc_;
    Pose3D act_pose_;
    NaviMode navi_mode_;
    SlamMode slam_mode_;
    NpuServer *p_npu_server_;
    TaskList navi_task_list_;
    Station station_;
    TaskAction action_;
    TaskInfo task_info_;
    StationList station_list_;
    PathList paths_;
    Pose3DList path_pose_;
    TaskActionList navi_task_action_list_;

};
}//namespace funcmap
}//namespace wizrobo
#endif // COM_FUNC_MAP_HPP
