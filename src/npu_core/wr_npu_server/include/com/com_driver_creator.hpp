#include "com/serial_modbus_rtu.h"
#include "com/serial_lm_driver.h"
#include <stdio.h>
namespace wizrobo { namespace icom_driver{

class ComDriverCreator
{
public:
    IComDriver* creator(ComDriverType type)
    {
        IComDriver* p_comdriver = NULL;
        switch (type) {
        case SERIAL_MODBUSRTU:
            p_comdriver = new modbusrtu::ModBusRtu();
            break;
        case SERIAL_LM:
            p_comdriver = new serial_lm::ComLMDriver();
            break;
        default:
            break;
        }
        return p_comdriver;
    }
};
}//namespace icom_driver
}//namespace wizrobo
