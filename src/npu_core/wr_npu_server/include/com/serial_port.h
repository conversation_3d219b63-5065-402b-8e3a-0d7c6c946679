#ifndef WIZROBO_NPU_COM_DRIVER_SERIAL_PORT_UBUNTU_H
#define WIZROBO_NPU_COM_DRIVER_SERIAL_PORT_UBUNTU_H

#include <stdio.h>
#include <string>
#include <string.h>
#include <iostream>
#include <termios.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <time.h>
#include <errno.h>
#include <iomanip>

namespace wizrobo { namespace serial_port {

const int TRUE = 1;
const int FALSE = 0;

#define SERIAL_DEBUG_PORT 0

class SerialPort{
public:
    SerialPort();
    ~SerialPort();
    bool Open(std::string port_name, int baud_rate,int data_bits, std::string parity);
    inline bool IsOpen() const
    {
        return is_open;
    }
    void Close();

    int Write(const char* p_buf, int buf_len);
    int WriteByte(char ch);

    int BytesWaiting();
    int Read(char* p_buf, int buf_len);
    int ReadByte(char& ch);
    inline void ClearReadBuffer()
    {
        if (file_dev > -1)
        {
            tcflush(file_dev, TCIOFLUSH);
        }
    }
private:
    void PrintOption(const struct termios &opt) const;
    inline int GetBaud(int define_baud) const
    {
        if (define_baud == B9600)
        {
            return 9600;
        }
        if (define_baud == B115200)
        {
            return 115200;
        }
        return 0;
    }
private:
    bool is_open;
    int file_dev;
};


}}// namespace serial_port
#endif// WIZROBO_NPU_COM_DRIVER_SERIAL_PORT_UBUNTU_H
