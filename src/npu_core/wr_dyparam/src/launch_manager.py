#!/usr/bin/env python
# *-* utf-8 *-*

import os
import re
import yaml
import shutil
import subprocess

import rospy

from time import sleep

NODE_NAME = "wr_launch_manage_node"
SETUP_PARAM_NAMESPACE = "core"
CONFIG_ID_PATH="/%s/CONFIG_ID" % (SETUP_PARAM_NAMESPACE)
CONFIG_ID_LIST_PATH="/CONFIG_ID_LIST" # % (SETUP_PARAM_NAMESPACE)

BAG_ID_PATH="/%s/BAG_ID" % (SETUP_PARAM_NAMESPACE)
BAG_ID_LIST_PATH="/BAG_ID_LIST" # % (SETUP_PARAM_NAMESPACE)

NAMESPACE_PREFIX = "npu_param"
SENSOR_PREFIX = "sensor"
SENSOR_PARAMS_LIST = {
    "lidar" : ["type", "itf_type", "itf_id", "enb_filter"],
    "imu"   : ["type", "itf_type", "itf_id"],
    "camera": ["type", "itf_type", "itf_id", "width_pix", "height_pix", "fps_hz"],
    "gps"   : ["type", "itf_type", "itf_id"]
}
BASE_PARAMS = ["base_id", "enb_dau", "enb_slave_mode", "enb_handheld_mode", "enb_3d_compensation"]


def start_dyparam(logger_level):
    os.system("roslaunch wr_app start_dyparam.launch logger_level:=%s &" % logger_level)
    param_is_loaded = False
    cnt = 5
    while param_is_loaded == False and cnt > 0:
        sleep(1)
        param_is_loaded = rospy.get_param("/npu_param/runtime/is_loaded", False)
    return param_is_loaded

def start_servers(logger_level):
    os.system("roslaunch wr_app start_servers.launch logger_level:=%s &" % logger_level)
    return True

def start_sensors(logger_level, sensor_type):
    global NAMESPACE_PREFIX
    global SENSOR_PREFIX
    global SENSOR_PARAMS_LIST
    sensor_num = rospy.get_param("/%s/%s/%s_num" % (NAMESPACE_PREFIX, SENSOR_PREFIX, sensor_type), 0)
    rospy.loginfo("%s_num: %d" % ( sensor_type, sensor_num))
    if sensor_num == 0:
        rospy.logwarn("There is NO %s avaliable!." % sensor_type)
        return True
    sensor_params = SENSOR_PARAMS_LIST[sensor_type]
    for sensor_no in range(sensor_num):
        sensor_param_prefix = "/%s/%s/%s_params/%s_%d" % (NAMESPACE_PREFIX, SENSOR_PREFIX, sensor_type, sensor_type, sensor_no)
        args_str = ""
        for param in sensor_params:
            param_path = sensor_param_prefix + "/" + param
            param_value = rospy.get_param(param_path, None)
            rospy.logdebug("--> %s: %s" % ( param_path, param_value))
            if param_value == None:
                continue
            args_str += "%s_%s:=%s " % (sensor_type, param, str(param_value).lower())
        args_str += "sid:=%d " % sensor_no
        args_str += "main:=%s " % ("true" if sensor_no==0 else "false")
        args_str += "logger_level:=%s" % logger_level
        launch_cmd = "roslaunch wr_app sensor_%s.launch %s &" % (sensor_type, args_str)
        rospy.loginfo("%s" % launch_cmd)
        os.system(launch_cmd)
    return True

def start_base(logger_level):
    global NAMESPACE_PREFIX
    global BASE_PARAMS
    base_param_prefix = "/%s/base" % (NAMESPACE_PREFIX)
    args_str = ""
    is_nbox = False
    for param in BASE_PARAMS:
        param_path = base_param_prefix + "/" + param
        param_value = rospy.get_param(param_path, None)
        if param_value == None:
            continue
        args_str += "%s:=%s " % (param, param_value)
        if param=="base_id" and param_value=="nbox":
            args_str += "is_nbox:=true "
    args_str += "logger_level:=%s" % logger_level
    launch_cmd = "roslaunch wr_app start_base.launch %s &" % (args_str)
    rospy.loginfo("%s" % launch_cmd)
    os.system(launch_cmd)
    return True

def start_others(logger_level):
    args_str = "logger_level:=%s" % logger_level
    launch_cmd = "roslaunch wr_app start_others.launch %s &" % (args_str)
    rospy.loginfo("%s" % launch_cmd)
    os.system(launch_cmd)
    return True

def start_navi(logger_level):
    args_str = "logger_level:=%s" % logger_level
    launch_cmd = "roslaunch wr_app start_navi.launch %s &" % (args_str)
    rospy.loginfo("%s" % launch_cmd)
    os.system(launch_cmd)
    return True

def get_logger_level():
    logger_level = rospy.get_param("/%s/logger_level" % NODE_NAME)
    logger_level = logger_level.lower()
    log_level = {"debug" : rospy.DEBUG,
                 "info" : rospy.INFO,
                 "warn" : rospy.WARN}.get(logger_level, rospy.INFO)  
    return logger_level 

def main():
    rospy.init_node(NODE_NAME, anonymous=False, log_level=rospy.DEBUG)
    try:
        if start_dyparam("info") == False:
            rospy.logerr("launch dyparam FAILD!!!")
            return
        logger_level = get_logger_level()
        if start_servers(logger_level) == False:        
            rospy.logerr("launch npu_server/map_server FAILD!!!")
            return
        if start_sensors(logger_level, "lidar") == False:        
            rospy.logerr("launch lidar FAILD!!!")
            return
        if start_sensors(logger_level, "imu") == False:        
            rospy.logerr("launch imu FAILD!!!")
            return
        if start_sensors(logger_level, "gps") == False:        
            rospy.logerr("launch gps FAILD!!!")
            return
        if start_sensors(logger_level, "camera") == False:        
            rospy.logerr("launch camera FAILD!!!")
            return
        if start_base(logger_level) == False:
            rospy.logerr("launch base FAILD!!!")
            return
	sleep(5)
	if start_others(logger_level) == False:
	    rospy.loggerr("launch others FAILD!!!")
	    return
#        sleep(10)
#        if start_navi(logger_level) == False:
#            rospy.logerr("launch navi FAILD!!!")
#            return
    except Exception, e:
        rospy.logerr("Exception: %s", e)
    rospy.spin()

if __name__ == '__main__':
    main()
