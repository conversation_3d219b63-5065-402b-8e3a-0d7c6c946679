#!/usr/bin/env python
# *-* utf-8 *-*

import os
import re
import yaml
import shutil

import rospy
import dynamic_reconfigure.client

from config_manager import ConfigManager
from wr_dyparam.srv import *

NODE_NAME = "param_server_node"
NAMESPACE_PREFIX = "npu_param"
SETUP_PARAM_NAMESPACE = "core"
CONFIG_ID_PATH="/%s/CONFIG_ID" % (SETUP_PARAM_NAMESPACE)
CONFIG_ID_LIST_PATH="/CONFIG_ID_LIST" # % (SETUP_PARAM_NAMESPACE)

BAG_ID_PATH="/%s/BAG_ID" % (SETUP_PARAM_NAMESPACE)
BAG_ID_LIST_PATH="/BAG_ID_LIST" # % (SETUP_PARAM_NAMESPACE)

class DyparamNode(object):
    def __init__(self):
        self.config_ = None
        self.config_id_ = None
        self.config_path_ = None
        self.config_id_list_ = []
        self.bag_id_ = None
        self.bag_path_ = None
        self.bag_id_list_ = []
        self.FeedBoolParamSrv_ = None
        self.GetBoolParamSrv_ = None
        self.FeedFloatParamSrv_ = None
        self.GetFloatParamSrv_ = None
        self.FeedIntParamSrv_ = None
        self.GetIntParamSrv_ = None
        self.FeedStrParamSrv_ = None
        self.GetStrParamSrv_ = None
        self.AddConfigSrv_ = None
        self.DeleteConfigSrv_ = None
        self.default_file_map_ = {
            "navi_core"  : ("./navi_core.yaml",),
           # "motor"     : ("./motor.yaml",),
           # "chassis"   : ("./chassis.yaml",),
            "base"      : ("./base.yaml",),
            "teleop"      : ("./teleop.yaml",),
            "slam"      : ("./slam.yaml",),
            "navi"      : ("./navi.yaml",),
           # "pid"       : ("./pid.yaml",),
            "sensor"    : ("./sensor.yaml",),
            "lidar_filters"    : ("./lidar_filters.yaml",),
			"bag"    : ("./bag.yaml",),
            "%s"%SETUP_PARAM_NAMESPACE : ("../../%s.yaml"%SETUP_PARAM_NAMESPACE,)
        }
        self.common_file_map_ = {
            "navi_core"  : ("./navi_core.yaml",),
           # "motor"         : ("./motor.yaml",),
           # "chassis"       : ("./chassis.yaml",),
            "base"      : ("./base.yaml",),
            "teleop"      : ("./teleop.yaml",),
            "slam"          : ("./slam.yaml",),
            "navi"          : ("./navi.yaml",),
           # "pid"           : ("./pid.yaml",),
            "sensor"        : ("./sensor.yaml",),
            "lidar_filters"    : ("./lidar_filters.yaml",),
            "bag"        : ("./bag.yaml",),
            "%s"%SETUP_PARAM_NAMESPACE : ("../../%s.yaml"%SETUP_PARAM_NAMESPACE,)
        }
        self.ParamType_ = {
            "DyRec" : ("navi_core", ),
            "Normal" : ("base", "teleop", "motor", "chassis", "slam", "navi", "pid", "sensor", "bag", "lidar_filters", "%s"%SETUP_PARAM_NAMESPACE)
        }
        self.FeedResponseMap_ = {
            type(True) : FeedBoolParamResponse,
            type(0.1) : FeedFloatParamResponse,
            type(1) : FeedIntParamResponse,
            type("0") : FeedStrParamResponse
        }
        self.GetResponseMap_ = {
            type(True) : GetBoolParamResponse,
            type(0.1) : GetFloatParamResponse,
            type(1) : GetIntParamResponse,
            type("0") : GetStrParamResponse
        }
        self.ResponseMap_ = {
            "wr_dyparam/GetBoolParamRequest" : {"func" : GetBoolParamResponse, "default" : GetBoolParamResponse(False)},
            "wr_dyparam/GetFloatParamRequest" : {"func" : GetFloatParamResponse, "default" : GetFloatParamResponse(0.0)},
            "wr_dyparam/GetIntParamRequest" : {"func" : GetIntParamResponse, "default" : GetIntParamResponse(0)},
            "wr_dyparam/GetStrParamRequest" : {"func" : GetStrParamResponse, "default" : GetStrParamResponse("")},
            "wr_dyparam/FeedBoolParamRequest" : {"func" : FeedBoolParamResponse, "default" : FeedBoolParamResponse(0)},
            "wr_dyparam/FeedFloatParamRequest" : {"func" : FeedFloatParamResponse, "default" : FeedFloatParamResponse(0)},
            "wr_dyparam/FeedIntParamRequest" : {"func" : FeedIntParamResponse, "default" : FeedIntParamResponse(0)},
            "wr_dyparam/FeedStrParamRequest" : {"func" : FeedStrParamResponse, "default" : FeedStrParamResponse(0)}
        }

    def ListAllConfigId(self, path):
        user_config_path = path + "/user"
        #rospy.logdebug(user_config_path)

        object_list = os.listdir(user_config_path)
        #rospy.logdebug(object_list)

        config_id_list = []
        for obj in object_list:
            if os.path.isdir(user_config_path + "/" + obj) == False:
                continue
            config_id_list.append(obj)
        return config_id_list

    def UpdateConfigIdList(self):
        if self.config_path_ != "":
            self.config_id_list_ = self.ListAllConfigId(self.config_path_)
        else:
            return

        if (not self.config_id_list_) or ("default" not in self.config_id_list_):
            #copy default config to user config folder
            rospy.logdebug("copy factory config as intial user config")
            self.config_id_list_.append("default")
            shutil.copytree(self.config_path_ + "/factory", self.config_path_ + "/user/default")
        rospy.logdebug("user configs: %s", self.config_id_list_)
        delimiter = ", "
        config_id_list_str = delimiter.join(self.config_id_list_)
        rospy.set_param("/%s%s" % (NAMESPACE_PREFIX, CONFIG_ID_LIST_PATH), config_id_list_str)

    def LoadConfig(self, *args, **kwargs):
        self.config_id_ = kwargs.get("config_id", self.config_id_)
        self.config_path_ = kwargs.get("config_path", self.config_path_)

        self.UpdateConfigIdList()

        if self.config_id_ not in self.config_id_list_:
            rospy.logwarn("config(\"%s\") is NOT avaliable, use default config." % self.config_id_)
            self.config_id_ = "default"
        else:
            rospy.loginfo("Using %s" % self.config_id_)
        if self.config_ == None:
            self.config_ = ConfigManager(
                config_path=self.config_path_ + "/user",
                config_id=self.config_id_,
                default_file_map=self.default_file_map_,
                common_file_map=self.common_file_map_)
        else:
            self.config_.SetConfigId(self.config_id_)
        self.config_.Load()

        all_config = self.config_.GetAllConfig()
        for namespace in all_config.keys():
            namespace_ = namespace if namespace=="navi_core" else NAMESPACE_PREFIX+"/"+namespace
            rospy.set_param(namespace_, all_config[namespace])

    def InitDyParamService(self):
        self.FeedBoolParamSrv_ = rospy.Service("FeedBoolDyParam", FeedBoolParam, self.FeedDyParam)
        self.GetBoolParamSrv_ = rospy.Service("GetBoolDyParam", GetBoolParam, self.GetDyParam)
        self.FeedFloatParamSrv_ = rospy.Service("FeedFloatDyParam", FeedFloatParam, self.FeedDyParam)
        self.GetFloatParamSrv_ = rospy.Service("GetFloatDyParam", GetFloatParam, self.GetDyParam)
        self.FeedIntParamSrv_ = rospy.Service("FeedIntDyParam", FeedIntParam, self.FeedDyParam)
        self.GetIntParamSrv_ = rospy.Service("GetIntDyParam", GetIntParam, self.GetDyParam)
        self.FeedStrParamSrv_ = rospy.Service("FeedStrDyParam", FeedStrParam, self.FeedDyParam)
        self.GetStrParamSrv_ = rospy.Service("GetStrDyParam", GetStrParam, self.GetDyParam)
        self.AddConfigSrv_ = rospy.Service("AddConfig", AddConfig, self.AddConfig)
        self.DeleteConfigSrv_ = rospy.Service("DeleteConfig", DeleteConfig, self.DeleteConfig)

    def FeedRecParam(self, top_namespace, parameter, value):
#        _list = dynamic_reconfigure.find_reconfigure_services()
#        if len(_list):
#            map = { parameter : value }
#            try:
#                dy_reconfig_client = dynamic_reconfigure.client.Client(top_namespace, timeout=0.005)
#            except rospy.exceptions.ROSException, e:
#                rospy.loginfo("%s" % e.__str__())
#                rospy.loginfo("fail to get dynaparameterNamemic reconfigure client!")
#                rospy.set_param(top_namespace + "/" + parameter, value)
#                return True
#            dy_reconfig_client.update_configuration(map)
#            return True
#        else:
#            rospy.logdebug("No ROS Dynamic server exist")
        rospy.set_param(top_namespace + "/" + parameter, value)
        return True

    def GetRecParam(self, top_namespace, parameter):
        _list = dynamic_reconfigure.find_reconfigure_services()
        if _list==[] or top_namespace not in _list:
            rospy.logdebug("No ROS Dynamic server exist")
            key = top_namespace + "/" + parameter
            return rospy.get_param(key, None)
        try:
            dy_reconfig_client = dynamic_reconfigure.client.Client(top_namespace, timeout=0.01)
        except rospy.exceptions.ROSException, e:
            rospy.loginfo("%s" % e.__str__())
            rospy.loginfo("fail to get dynaparameterNamemic reconfigure client!")
            key = top_namespace + "/" + parameter
            return rospy.get_param(key, None)
        map = dy_reconfig_client.get_configuration()
        value = map[parameter]
        return value

    def FeedNormalParam(self, key, value):
        rospy.set_param(key, value)
        return 0

    def GetNormalParam(self, key):
        return rospy.get_param(key, None)

    def _SplitKey(self, key):
        _list = key.split("/")
        top_namespace = "/".join(_list[0:-1])
        parameter = _list[-1]
        return top_namespace, parameter

    def FeedDyParam(self, request):
        key = request.key if request.key[0]!="/" else request.key[1:]
        value = request.value        
        response = self.ResponseMap_[request._type]["func"]
        key_list = key.split("/")
        if key_list[0] == "npu_param":
            rospy.logdebug("Feed dyparam[normal]: key(\"%s\"), value(\"%s\")"% (key, value))
            top_namespace = key_list[1]
            parameter = "/".join(key_list[2:])
            rtn = self.FeedNormalParam(key, value)
            key = top_namespace+"/"+parameter
        else:
            rospy.logdebug("Feed dyparam[rec]: key(\"%s\"), value(\"%s\")"% (key, value))
            top_namespace, parameter = self._SplitKey(key)
            rtn = self.FeedRecParam(top_namespace, parameter, value)
            # top_namespace = "npu_param" + top_namespace
            key = top_namespace+"/"+parameter
            top_namespace_list = top_namespace.split("/")
            top_namespace = top_namespace_list[0]
        self.config_.Set(key, value)
        if top_namespace == "core":
            self.config_.SaveNamespaceImmediately(top_namespace)
        else:
            self.config_.SaveNamespaceLater(top_namespace)
        if key == CONFIG_ID_PATH[1:]:
            self.config_id_ = value
            self.config_.SetConfigId(value)
            if value not in self.config_id_list_:
                rospy.logdebug("New config id(\"%s\")"% (value))
                self.config_.Save()
            self.LoadConfig()
        else:
            pass
        return response(0)

    def GetDyParam(self, request):
        key = request.key if request.key[0]!="/" else request.key[1:]
        response = self.ResponseMap_.get(request._type)
        key_list = key.split("/")
        if key_list[0] == "npu_param":
            rtn = self.GetNormalParam(key)
        else:
            top_namespace, parameter = self._SplitKey(key)
            rtn = self.GetRecParam(top_namespace, parameter)
        value = response["default"] if rtn == None else response["func"](rtn)
        rospy.logdebug("Get parameter: key(%s)=%s" % (key, value.value))
        return value

    def AddConfig(self, request):
        id = request.id;
        self.config_.SetConfigId(id)
        self.config_.Save()
        self.UpdateConfigIdList()
        return AddConfigResponse(True)

    def DeleteConfig(self, request):
        id = request.id;
        if id == "":
            rospy.logdebug("Id is null string.")
            return DeleteConfigResponse(True)
        if id == self.config_.config_id_:
            rospy.logdebug("Can NOT delete the using config.")
            return DeleteConfigResponse(True)
        if id == "default":
            rospy.logdebug("Can NOT delete default config.")
            return DeleteConfigResponse(True)
        path = "/%s/user/%s" % (self.config_path_, id)
        rospy.logdebug("Delete dir: \"%s\"" % path)
        shutil.rmtree(path, True)
        self.UpdateConfigIdList()
        return DeleteConfigResponse(True)

    def Init(self, *args, **kwargs):
        self.LoadConfig(*args, **kwargs)
        self.InitDyParamService()

    def Run(self):
        rate = rospy.Rate(30)
        rospy.set_param("/%s/runtime/is_loaded"%NAMESPACE_PREFIX, True)
        while not rospy.is_shutdown():
            rate.sleep()
            rospy.spin()
        return

if __name__ == "__main__":
    logger_level = rospy.get_param("/%s/logger_level" % NODE_NAME)
    logger_level = logger_level.lower()
    log_level = {"debug" : rospy.DEBUG,
                 "info" : rospy.INFO,
                 "warn" : rospy.WARN}.get(logger_level, rospy.INFO)
    rospy.init_node(NODE_NAME, anonymous=False, log_level=log_level)
    config_path = rospy.get_param("~config_path")

    npu_param_file_path = config_path + "/%s.yaml" % SETUP_PARAM_NAMESPACE
    npu_param_file_fd = open(npu_param_file_path)
    npu_param = yaml.load(npu_param_file_fd)
    npu_param_file_fd.close()
    rospy.set_param("/%s/%s"%(NAMESPACE_PREFIX, SETUP_PARAM_NAMESPACE), npu_param)

    config_id = rospy.get_param("/%s%s" % (NAMESPACE_PREFIX, CONFIG_ID_PATH))

    rospy.logdebug("Config id: %s" % config_id)
    rospy.logdebug("Config path: %s" % config_path)

    dyparam_node = DyparamNode()
    dyparam_node.Init(config_id=config_id, config_path=config_path)

    enb_play = rospy.get_param("/%s/bag/enb_play" % NAMESPACE_PREFIX, False)
    if (enb_play == True):
        rospy.logdebug("enb_play = %s, use sim time." % enb_play)
        rospy.set_param("/use_sim_time", True)
    dyparam_node.Run()
