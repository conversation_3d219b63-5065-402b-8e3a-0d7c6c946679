#!/usr/bin/env python
# *-* utf-8 *-*

import os
import re
import yaml
import threading
import Queue
import time
from time import sleep

class ConfigManager(object):
    def __init__(self, *args, **kwargs):
        self.config_path_ = kwargs.get("config_path", "")
        self.config_id_ = kwargs.get("config_id", "")
        self.path_ = self.config_path_ + "/" + self.config_id_
        self.namespace_default_file_map_ = kwargs.get("default_file_map", {})
        self.namespace_common_file_map_ = kwargs.get("common_file_map", {})
        self.namespaces_ = {}

        self.save_thread = threading.Thread(target=self.save_thread_main)
        self.save_thread.setDaemon(True)
        self.save_thread.start()
        self.save_stamp = -1.0

    def save_thread_main(self):
        saved_stamp = -1.0
        while True:
            sleep(1)
            if self.save_stamp < 0:
                continue
            if saved_stamp == self.save_stamp:
                continue
            if time.time()-self.save_stamp <= 3:
                continue
            for namespace in self.namespaces_.keys():
                self.SaveNamespaceImmediately(namespace)
            saved_stamp = self.save_stamp
        pass

    def SetConfigId(self, config_id):
        self.config_id_ = config_id
        self.path_ = self.config_path_ + "/" + self.config_id_

    def ReadYamlFile(self, yaml_file):
        fd = open(self.path_ + "/" + yaml_file)
        yaml_object = yaml.load(fd)
        fd.close()
        return yaml_object

    def LoadNamespace(self, namespace, filelist):
        for yaml_file in filelist:
            yaml_object = self.ReadYamlFile(yaml_file)
            if yaml_object == None:
                print "yaml_file error: ", yaml_file
                continue
            param_set = self.namespaces_.get(namespace, {})
            if self.namespaces_.get(namespace, {}) == {}:
                self.namespaces_[namespace] = {}
            for key in yaml_object.keys():
                self.namespaces_[namespace][key] = yaml_object[key]
        pass

    def SaveNamespaceImmediately(self, namespace):
        path_list = self.namespace_common_file_map_.get(namespace, ())
        if path_list == ():
            return
        path = self.path_ + "/" + path_list[0]
        content = yaml.dump(self.namespaces_[namespace], default_flow_style=False)
        fd = open(path, "w")
        fd.write(content)
        fd.close()

    def SaveNamespaceLater(self, namespace):
        self.save_stamp = time.time()

    def Load(self):
        if self.config_id_ == "default":
            file_map = self.namespace_default_file_map_
        else:
            file_map = self.namespace_common_file_map_
        for namespace in file_map.keys():
            filelist = file_map[namespace]
            self.LoadNamespace(namespace, filelist)

    def Save(self):
        if self.config_id_ == "default":
            return
        if not os.path.exists(self.path_):
            os.mkdir(self.path_)
        for namespace in self.namespaces_.keys():
            self.SaveNamespaceImmediately(namespace)
        return

    def Delete(self, config_id):
        pass

    def Set(self, key, value):
        route = key.split("/") if key[0]!="/" else key[1:].split("/")
        node = self.namespaces_
        for e in route[:-1]:
            if node.get(e, None) == None:
                node[e] = {}
            node = node[e]
        node[route[-1]] = value

    def Get(self, key):
        route = key.split("/") if key[0]!="/" else key[1:].split("/")
        node = self.namespaces_
        for e in route:
            if node.get(e, None) == None:
                return None
            node = node[e]
        return node

    def GetAllConfig(self):
        return self.namespaces_
