<!-- -*- mode: XML -*- -->
<!-- v1.0 created by armstrong.tu @2017-05-22 -->
<launch>
<!-- wizrobo standard launch arg -->
<arg name="logger_level" default="info"/>
<arg name="config_path" default="$(find wr_app)/config" />
<arg name="config_id" default="default" />

<!-- 1. Functional node -->
<node pkg="wr_dyparam" type="dyparam_node.py" name="wr_dyparam_node" output="screen">
    <param name="logger_level" value="$(arg logger_level)"/>
    <param name="config_path" value="$(arg config_path)" />
    <param name="config_id" value="$(arg config_id)" />
</node>

</launch>
