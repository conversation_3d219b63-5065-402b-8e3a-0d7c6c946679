TestDouble:
  name: double_test
  type: ParamTestDouble
  params: {key: 4.0}
TestInt:
  name: int_test
  type: ParamTestInt
  params: {key: 4}
TestUInt:
  name: uint_test
  type: ParamTestUInt
  params: {key: 4}
TestString:
  name: string_test
  type: ParamTestString
  params: {key: four}
TestDoubleVector:
  name: double_vector_test
  type: ParamTestDoubleVector
  params: {key: [4.0, 4.0, 4.0, 4.0]}
TestStringVector:
  name: string_vector_test
  type: ParamTestStringVector
  params: {key: [four, four, four, four]}
