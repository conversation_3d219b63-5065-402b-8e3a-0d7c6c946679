<class_libraries>
  <library path="lib/libmedian">
    <class name="filters/MultiChannelMedianFilterDouble" type="filters::MultiChannelMedianFilter<double>"
	    base_class_type="filters::MultiChannelFilterBase<double>">
      <description>
	This is a median filter which works on a stream of std::vector of doubles.
      </description>
    </class>
    <class name="filters/MedianFilterDouble" type="filters::MedianFilter<double>"
	    base_class_type="filters::FilterBase<double>">
      <description>
	This is a median filter which works on a stream of doubles.
      </description>
    </class>
    <class name="filters/MultiChannelMedianFilterFloat" type="filters::MultiChannelMedianFilter<float>"
	    base_class_type="filters::MultiChannelFilterBase<float>">
      <description>
	This is a median filter which works on a stream of std::vector of floats.
      </description>
    </class>
    <class name="filters/MedianFilterFloat" type="filters::MedianFilter<float>"
	    base_class_type="filters::FilterBase<float>">
      <description>
	This is a median filter which works on a stream of floats.
      </description>
    </class>
  </library>
  <library path="lib/libmean">
    <class name="filters/MeanFilterDouble" type="filters::MeanFilter<double>"
	    base_class_type="filters::FilterBase<double>">
      <description>
	This is a mean filter which works on a stream of doubles.
      </description>
    </class>
    <class name="filters/MeanFilterFloat" type="filters::MeanFilter<float>"
	    base_class_type="filters::FilterBase<float>">
      <description>
	This is a mean filter which works on a stream of floats.
      </description>
    </class>
    <class name="filters/MultiChannelMeanFilterDouble" type="filters::MultiChannelMeanFilter<double>"
	    base_class_type="filters::MultiChannelFilterBase<double>">
      <description>
	This is a mean filter which works on a stream of vectors of doubles.
      </description>
    </class>
    <class name="filters/MultiChannelMeanFilterFloat" type="filters::MultiChannelMeanFilter<float>"
	    base_class_type="filters::MultiChannelFilterBase<float>">
      <description>
	This is a mean filter which works on a stream of vectors of floats.
      </description>
    </class>
  </library>
  <library path="lib/libtest_param">
    <class name="filters/ParamTest" type="filters::ParamTest<double>"
	    base_class_type="filters::FilterBase<double>">
      <description>
	This is a filter designed to test parameter readings.  It's not useful.
      </description>
    </class>
  </library>
  <library path="lib/libincrement">
    <class name="filters/IncrementFilterInt" type="filters::IncrementFilter<int>"
	    base_class_type="filters::FilterBase<int>">
      <description>
	This is a increment filter which works on a stream of ints.
      </description>
    </class>
    <class name="filters/MultiChannelIncrementFilterInt" type="filters::MultiChannelIncrementFilter<int>"
	    base_class_type="filters::MultiChannelFilterBase<int>">
      <description>
	This is a increment filter which works on a stream of vectors of ints.
      </description>
    </class>
  </library>
  <library path="lib/libtransfer_function">
    <class name="filters/MultiChannelTransferFunctionFilterDouble" type="filters::MultiChannelTransferFunctionFilter<double>"
	    base_class_type="filters::MultiChannelFilterBase<double>">
      <description>
	This is a transfer filter which works on a stream of doubles.
      </description>
    </class>
    <class name="filters/TransferFunctionFilterDouble" type="filters::SingleChannelTransferFunctionFilter<double>"
	    base_class_type="filters::FilterBase<double>">
      <description>
	This is a transfer filter which works on a stream of doubles.
      </description>
    </class>
  </library>
</class_libraries>
