<package>
  <name>filters</name>
  <version>1.7.5</version>
  <description>
    This library provides a standardized interface for processing data as a sequence 
    of filters.  This package contains a base class upon which to build specific implementations
    as well as an interface which dynamically loads filters based on runtime parameters.  
  </description>

  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <license>BSD</license>
  <url>http://ros.org/wiki/filters</url>

  <buildtool_depend version_gte="0.5.68">catkin</buildtool_depend>

  <build_depend>roslib</build_depend>
  <build_depend>rosconsole</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>pluginlib</build_depend>
  <build_depend>rostest</build_depend>

  <run_depend>roslib</run_depend>
  <run_depend>rosconsole</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>pluginlib</run_depend>


  <export>
    <filters plugin="${prefix}/default_plugins.xml" />
  </export>

</package>
