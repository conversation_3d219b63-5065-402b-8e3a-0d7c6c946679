cmake_minimum_required(VERSION 2.8.3)
project(wr_hasp)

## Specify additional locations of header files
## Your package locations should be listed before other locations

set(_platform "x86_64")

include_directories(include)
link_directories(lib/${_platform})

## Add static library

## Declare a C++ library
add_library(usblock_${_platform} STATIC src/usblock.cpp src/errorprinter.cpp)

## Add cmake target dependencies of the library
## as an example, code may need to be generated before libraries
## either from message generation or dynamic reconfigure
# add_dependencies(wr_hasp ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})


## Declare a C++ executable
add_executable(usblock_demo_${_platform} src/usblock_demo.cpp)

#if(${WR_LOCK})
# add_executable(hasp_demo src/hasp_demo.cpp src/errorprinter.cpp
# )
# add_executable(hasp_update src/hasp_update.cpp src/errorprinter.cpp
# )
#endif()

## Add cmake target dependencies of the executable
## same as for the library above
# add_dependencies(wr_hasp_node ${${PROJECT_NAME}_EXPORTED_TARGETS} ${catkin_EXPORTED_TARGETS})

## Specify libraries to link a library or executable target against
target_link_libraries(usblock_${_platform}
    hasp_cpp_linux_${_platform}
    hasp_c_linux_${_platform}
    pthread
)

target_link_libraries(usblock_demo_${_platform}
  usblock_${_platform}
)

#############
## Install ##
#############

# all install targets should use catkin DESTINATION variables
# See http://ros.org/doc/api/catkin/html/adv_user_guide/variables.html

## Mark executable scripts (Python etc.) for installation
## in contrast to setup.py, you can choose the destination
# install(PROGRAMS
#   scripts/my_python_script
#   DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark executables and/or libraries for installation
# install(TARGETS wr_hasp wr_hasp_node
#   ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark cpp header files for installation
# install(DIRECTORY include/${PROJECT_NAME}/
#   DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
#   FILES_MATCHING PATTERN "*.h"
#   PATTERN ".svn" EXCLUDE
# )

## Mark other files for installation (e.g. launch and bag files, etc.)
# install(FILES
#   # myfile1
#   # myfile2
#   DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
# )

#############
## Testing ##
#############

## Add gtest based cpp test target and link libraries
# catkin_add_gtest(${PROJECT_NAME}-test test/test_wr_hasp.cpp)
# if(TARGET ${PROJECT_NAME}-test)
#   target_link_libraries(${PROJECT_NAME}-test ${PROJECT_NAME})
# endif()

## Add folders to be run by python nosetests
# catkin_add_nosetests(test)
