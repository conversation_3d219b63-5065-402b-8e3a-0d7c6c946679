/////////////////////////////////////////////////////////////////////////////
//
// 
// Copyright (C) 2016, Wizrobo, Inc. All rights reserved.
//
//
//////////////////////////////////////////////////////////////////////////////

#ifndef USB_LOCK_H
#define USB_LOCK_H

#include <ros/ros.h>
#include <std_msgs/String.h>
#include <iostream>
#include <algorithm>
#include <sstream>
#include <stdio.h>
#include <string.h>

#include "hasp_api_cpp.h"
#include "usblock_key.h"
#include "errorprinter.h"

namespace wizrobo {

class UsbLocker
{
public:
    //UsbLock();
    //virtual ~UsbLock();
    //bool Unlock();
    static bool Unlock();
};

} //namespace
#endif //USB_LOCK_H
