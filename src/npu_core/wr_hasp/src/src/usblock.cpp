/////////////////////////////////////////////////////////////////////////////
//
// 
// Copyright (C) 2016, Wizrobo, Inc. All rights reserved.
//
//
//////////////////////////////////////////////////////////////////////////////

#include "usblock.h"

namespace wizrobo {

bool UsbLocker::Unlock()
{
    //Prints the error messages for the return values of the functions
    ErrorPrinter errorPrinter;

    //Used to hold the return value of the called functions
    haspStatus status;
    
    //Demonstrates the login to the default feature of a key
    //Searches both locally and remotely for it
    std::cout << "Login to default feature: " << std::endl;

    Chasp hasp1(ChaspFeature::defaultFeature());
    status = hasp1.login(vendorCode);
    errorPrinter.printError(status);

    return (HASP_SUCCEEDED(status))?true:false;
}

} //namespace

