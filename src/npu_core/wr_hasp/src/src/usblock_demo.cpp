/////////////////////////////////////////////////////////////////////////////
//
// 
// Copyright (C) 2016, Wizrobo, Inc. All rights reserved.
//
//
//////////////////////////////////////////////////////////////////////////////

#include <iostream>
#include "usblock.h"

int main(int argc, char** argv)
{
    if (wizrobo::UsbLocker::Unlock())
    {
        std::cout << "UnLock() succeeds" << std::endl;
    }
    else
    {
        std::cout << "UnLock fails" << std::endl;
    }
    return 0;
}
