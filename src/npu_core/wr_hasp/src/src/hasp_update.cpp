//////////////////////////////////////////////////////////////////////////////
//
// Demo program for Sentinel LDK update & rehost process
//
//
// Copyright (C) 2014, SafeNet, Inc. All rights reserved.
//
//
// Sentinel LDK DEMOMA key required
//
//////////////////////////////////////////////////////////////////////////////

#include <iostream>
#include <fstream>
#include <vector>
#include <algorithm>
#include <stdio.h>

#include <string.h>

#include "hasp_api_cpp.h"
#include "errorprinter.h"
#include "vendor_code.h"

using namespace std;

//The memory size reserved for the update
const int approxUpdateSize = 20000;

//The file to which the acknowledge data
//generated by the update function is stored
const char* ackFileName = "hasp_ack.c2v";

//The file to which the output data
//generated by the rehost function is stored
const char* rehostedFileName = "hasp_rehost.v2c";


//Prints the error messages for the return values of the functions
ErrorPrinter errorPrinter;

void displayCloseMessage()
{
	cout<<endl<<"press ENTER to close the sample"<<endl;

    fflush(stdin);
    while (getchar() == EOF);
}

int doGetInfoDemo()
{   
	const char* scope = 
		"<?xml version=\"1.0\" encoding=\"UTF-8\" ?>"
		"<haspscope>"
		"    <license_manager hostname =\"localhost\" />"
		"</haspscope>";


    cout<<"getting update information       : ";

    string updateInfo;
    haspStatus status = Chasp::getInfo(scope, Chasp::updateInfo(), vendorCode, updateInfo);
    errorPrinter.printError(status);
    cout<<updateInfo;
    cout<<endl;

    if (!HASP_SUCCEEDED(status))
    {
        return 1;
    }

    string fileName;
    cout<<"please enter CustomerToVendor file name: ";
    cin>>fileName;
    cout<<endl<<endl;

    //Write the update information to a file
    ofstream f(fileName.c_str());
    if(!f.write(updateInfo.c_str(), static_cast<streamsize>(updateInfo.length())))
    {
        cout<<"Error: could not write "<<fileName<<endl;
        return 1;
    }
    
    cout<<"Sentinel key information stored into file "<<fileName<<endl;
    return 0;
}

int doUpdateDemo()
{
    string fileName;
    cout<<"please enter the name of an available VendorToCustomer file: ";
    cin>>fileName;
    cout<<endl<<endl;

    //Read the file fileName into the string updateData
    ifstream f(fileName.c_str());
    string updateData;
    updateData.reserve(approxUpdateSize);
    getline(f, updateData, '\0'); //Reads the whole f, because f is base64 coded
    if(updateData.empty())
    {
        cout<<"Error: could not read "<<fileName<<endl;
        return 1;
    }

    cout<<"updating key                     : ";

    string ackData;
    haspStatus status = Chasp::update(updateData, ackData);
    errorPrinter.printError(status);
    cout<<endl;

    if (!HASP_SUCCEEDED(status))
    {
        return 1;
    }

    if(!ackData.empty())
    {
        //Write the acknowledge data to a file
        ofstream of(ackFileName);
        if(!of.write(ackData.c_str(), static_cast<streamsize>(ackData.length())))
        {
            cout<<"Error: could not write "<<ackFileName<<endl;
            return 1;
        }

        cout<<"acknowledge data written to file "<<ackFileName<<endl;
    }

    return 0;
}


int doRehostDemo()
{

    const char* scope = 
        "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>"
        "<haspscope>"
        "    <license_manager hostname =\"localhost\" />"
        "</haspscope>";

    cout<<"getting recipient information       : ";

    string recipientInfo;
    haspStatus status = Chasp::getInfo(scope, Chasp::recipientInfo(), vendorCode, recipientInfo);
    errorPrinter.printError(status);
    cout<<recipientInfo;
    cout<<endl;

    if (!HASP_SUCCEEDED(status))
    {
        return 1;
    }
    
    //Perform rehost action on specified key_id
    cout<<"rehosting key                     : ";

    string v2c;
    const char* action = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>"
                            "<rehost><hasp id=\"**********  \"/></rehost>";

    const char* rehostscope = "<haspscope><hasp id=\"**********  \"/></haspscope>";

    status = Chasp::transfer(action,
                       rehostscope ,
                      (hasp_vendor_code_t *)vendorCode, 
                      recipientInfo.c_str(),
                      v2c);

    errorPrinter.printError(status);
    cout<<endl;

    if (!HASP_SUCCEEDED(status))
    {
        return 1;
    }

     if(!v2c.empty())
    {
        //Write the rehost info data to a file
        ofstream of(rehostedFileName);
        if(!of.write(v2c.c_str(), static_cast<streamsize>(v2c.length())))
        {
            cout<<"Error: could not write "<<rehostedFileName<<endl;
            return 1;
        }

        cout<<"Rehost data written to file "<<rehostedFileName<<endl;
    }

    return 0;
}

int main()
{
    cout<<endl;
    cout<<"This is a simple demo program for Sentinel LDK update & rehost functions."<<endl;
    cout<<"Copyright (C) SafeNet, Inc. All rights reserved."<<endl<<endl;

    cout<<"please choose whether to retrieve Sentinel key (i)nformation"<<endl;
    cout<<"or to (u)pdate a key "<<endl;
    cout<<"or to (r)ehost a license  (i/u/r): ";

    char input;
    do cin>>input; while((input != 'i') && (input != 'u')&& (input != 'r'));
    cout<<endl;

    int retValue;

    if(input == 'i')
    {
        retValue = doGetInfoDemo();
    }
    else if (input == 'u')
    {
        retValue = doUpdateDemo();
    }
    else
    {
        retValue = doRehostDemo();
    }
    
   	displayCloseMessage();
    return retValue;
}
