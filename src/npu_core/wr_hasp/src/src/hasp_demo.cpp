/////////////////////////////////////////////////////////////////////////////
//
// 
// Copyright (C) 2016, Wizrobo, Inc. All rights reserved.
//
//
//////////////////////////////////////////////////////////////////////////////

#include <ros/ros.h>
#include <std_msgs/String.h>
#include <iostream>
#include <algorithm>
#include <sstream>
#include <stdio.h>

#include <string.h>

#include "hasp_api_cpp.h"
#include "vendor_code.h"
#include "errorprinter.h"

using namespace std;


void displayCloseMessage()
{
	cout<<endl<<"------NPU system is clocking------"<<endl;
    fflush(stdin);
    while (getchar() == EOF); 
}

void displayStartMessage()
{
    cout<<endl<<"------NPU system is starting-----"<<endl;
}

/*class WREnvelope
{   
    public:
        WREnvelope();
        ~WREnvelope();
    private:
        bool envelopeKey;
};*/

int main(int argc, char** argv)
{
    //bool envelopeKey;    
    cout<<"Copyright (C) Wizrobo, Inc. All rights reserved."<<endl<<endl;

    //Prints the error messages for the return values of the functions
    ErrorPrinter errorPrinter;

    //Used to hold the return value of the called functions
    haspStatus status;

    
    //Demonstrates the login to the default feature of a key
    //Searches both locally and remotely for it
    cout<<"Login to default feature         : ";

    Chasp hasp1(ChaspFeature::defaultFeature());
    status = hasp1.login(vendorCode);
    errorPrinter.printError(status);


    //WREnvelope::WREnvelope()
{

    if (!HASP_SUCCEEDED(status))
    {
        displayCloseMessage();
        //envelopeKey = false;
        return 1;
    }
    if (HASP_SUCCEEDED(status))
    {
        displayStartMessage();
        //envelopeKey = true;
        return 1;
    } 

} 
    return 0;
}
