#include "npu_ros_ext.h"

namespace wizrobo { namespace ros_ext {

void GetFloatVecParam(const ros::NodeHandle &nh, const std::string &param_name, std::vector<float> &param_vec, int default_size, float default_elm_val)
{
    std::string str;
    nh.param<std::string>(param_name, str, "");
    str = wizrobo::str_ext::ExtracString(str, "[", "]");
    if (!str.empty())
    {
        param_vec = str_ext::StrToFloatVec(str, ", ");
    }
    if (param_vec.size() < default_size)
    {
        for (int i = 0; i < default_size - param_vec.size(); i++)
        {
            param_vec.push_back(default_elm_val);
        }
    }
}

void GetFloatMatParam(const ros::NodeHandle &nh, const std::string &param_name, std::vector<std::vector<float> > &param_mat, int default_size, const std::vector<float> &default_elm_vec)
{
    std::string mat_str;
    nh.param<std::string>(param_name, mat_str, "");
    //ROS_WARN("\"%s\" = \"%s\"", param_name.c_str(), mat_str.c_str());
    mat_str = str_ext::ExtracString(mat_str, "[", "]");
    //ROS_WARN("mat_str = \"%s\"", mat_str.c_str());
    if (!mat_str.empty())
    {
        param_mat = str_ext::StrToFloatMat(mat_str, ", ", "; ");
    }
    if (param_mat.size() < default_size)
    {
        for (int i = 0; i < default_size - param_mat.size(); i++)
        {
            param_mat.push_back(default_elm_vec);
        }
    }
}

bool ExpandNavMap(const int &dst_xmin, const int &dst_ymin, const int &dst_xmax, const int &dst_ymax, const int &min_exp_size, nav_msgs::OccupancyGrid &map)
{
    int w0 = map.info.width;
    int h0 = map.info.height;
    geometry_msgs::Pose old_origin = map.info.origin;
    ROS_WARN("dst_range = BL(%d,%d) -> TR(%d:%d) [pix]", dst_xmin, dst_ymin, dst_xmax, dst_ymax);

    if (dst_xmax > w0 || dst_xmin < 0 || dst_ymax > h0 || dst_ymin < 0)
    {
        int xmax = (dst_xmax > w0 ? MAX(dst_xmax - w0, min_exp_size) + w0 : w0);
        int xmin = (dst_xmin < 0  ? MIN(dst_xmin - 0, -min_exp_size) + 0  : 0);
        int ymax = (dst_ymax > h0 ? MAX(dst_ymax - h0, min_exp_size) + h0 : h0);
        int ymin = (dst_ymin < 0  ? MIN(dst_ymin - 0, -min_exp_size) + 0  : 0);
        ROS_WARN("modified dst_range = BL(%d,%d) -> TR(%d:%d) [pix]", xmin, ymin, xmax, ymax);

        int w1 = xmax - xmin;
        int h1 = ymax - ymin;
        int x_offset = -xmin;
        int y_offset = -ymin;
        ROS_WARN("Needs expanding: (%d,%d)[pix] -> (%d,%d)[pix]", w0, h0, w1, h1);
        int8_t* old_arr = &(map.data[0]);
        int8_t* new_arr = new int8_t[w1 * h1];
        int8_t* zero_arr = new int8_t[w1];
        for (int x = 0; x < w1; x++)
        {
            zero_arr[x] = -1;
        }
        for (int y = 0; y < h1; y++)
        {
            memcpy(&(new_arr[y * w1]), &(zero_arr[0]), w1*sizeof(int8_t));
        }
        for (int y = 0; y < h0; y++)
        {
            memcpy((new_arr + (y + y_offset) * w1 + x_offset), (old_arr + y * w0), w0*sizeof(int8_t));
        }
        map.data.resize(w1 * h1);
        memcpy(&(map.data[0]), new_arr, w1 * h1 * sizeof(int8_t));
        delete[] zero_arr;
        delete[] new_arr;

        map.info.width = w1;
        map.info.height = h1;
        map.info.origin.position.x -= x_offset * map.info.resolution;
        map.info.origin.position.y -= y_offset * map.info.resolution;
        ROS_WARN("Expanded size: (%d, %d)[pix] -> (%d, %d)[pix], origin: (%.4f, %.4f)[m]->(%.4f, %.4f)[m], res: %.2f[m/pix]"
                 , w0, h0, map.info.width, map.info.height
                 , old_origin.position.x, old_origin.position.y, map.info.origin.position.x, map.info.origin.position.y
                 , map.info.resolution);
        return true;
    }
    return false;
}

geometry_msgs::Vector3 GeoTransform(const tf::Transform& tf, const geometry_msgs::Vector3& p_in)
{
    tf::Vector3 tv3_in(p_in.x, p_in.y, p_in.z);
    tf::Vector3 tv3_out = tf * tv3_in;
    geometry_msgs::Vector3 p_out;
    p_out.x = tv3_out.x();
    p_out.y = tv3_out.y();
    p_out.z = tv3_out.z();
    return p_out;
}

geometry_msgs::TransformStamped TfTf2GeoTf(const tf::StampedTransform &tf_tf)
{
    geometry_msgs::TransformStamped geo_tf;
    geo_tf.header.stamp = tf_tf.stamp_;
    geo_tf.header.frame_id = tf_tf.frame_id_;
    geo_tf.child_frame_id = tf_tf.child_frame_id_;
    geo_tf.transform.translation.x = tf_tf.getOrigin().x();
    geo_tf.transform.translation.y = tf_tf.getOrigin().y();
    geo_tf.transform.translation.z = tf_tf.getOrigin().z();
    geo_tf.transform.rotation.x = tf_tf.getRotation().x();
    geo_tf.transform.rotation.y = tf_tf.getRotation().y();
    geo_tf.transform.rotation.z = tf_tf.getRotation().z();
    geo_tf.transform.rotation.w = tf_tf.getRotation().w();
    return geo_tf;
}

Eigen::Matrix3f GeoPoseCov2EigMat(const boost::array<double, 36>& geo_pose_cov)
{
    Eigen::Matrix3f eig_mat;
    eig_mat(0,0) = geo_pose_cov[M66_IDX(0,0)];
    eig_mat(1,1) = geo_pose_cov[M66_IDX(1,1)];
    eig_mat(2,2) = geo_pose_cov[M66_IDX(5,5)];
    eig_mat(0,1) = eig_mat(1,0) = geo_pose_cov[M66_IDX(0,1)];
    eig_mat(0,2) = eig_mat(2,0) = geo_pose_cov[M66_IDX(0,5)];
    eig_mat(1,2) = eig_mat(2,1) = geo_pose_cov[M66_IDX(1,5)];
    for (int i = 0; i < 3; i++)
    {
        if (eig_mat(i,i) == 0)
        {
            eig_mat(i,i) = KNOWN_VAR_VAL;
        }
        else if (eig_mat(i,i) >= UNKNOWN_VAR_VAL)
        {
            eig_mat(i,i) = UNKNOWN_VAR_VAL;
        }
    }
    return eig_mat;
}

Eigen::Matrix3f GeoTwistCov2EigMat(const boost::array<double, 36>& geo_twist_cov)
{
    return GeoPoseCov2EigMat(geo_twist_cov);
}

Eigen::Vector3f GeoPose2EigVec(const geometry_msgs::Pose &geo_pose)
{
    Eigen::Vector3f eig_vec;
    eig_vec[0] = geo_pose.position.x;
    eig_vec[1] = geo_pose.position.y;
    eig_vec[2] = ros_ext::GeoQuat2Yaw(geo_pose.orientation);
    return eig_vec;
}
Eigen::Vector3f GeoTwist2EigVec(const geometry_msgs::Twist &geo_twist)
{
    Eigen::Vector3f eig_vec;
    eig_vec[0] = geo_twist.linear.x;
    eig_vec[1] = geo_twist.linear.y;
    eig_vec[2] = geo_twist.angular.z;
    return eig_vec;
}

void SetGeoPoseCovByEigMat(boost::array<double, 36>& geo_pose_cov, const Eigen::Matrix3f& eig_mat)
{
    geo_pose_cov.assign(0);
    geo_pose_cov[M66_IDX(0,0)] = eig_mat(0,0);// x-x
    geo_pose_cov[M66_IDX(1,1)] = eig_mat(1,1);// y-y
    geo_pose_cov[M66_IDX(5,5)] = eig_mat(2,2);// yaw-yaw

    geo_pose_cov[M66_IDX(2,2)] = wizrobo::UNKNOWN_VAR_VAL;// z-z
    geo_pose_cov[M66_IDX(3,3)] = wizrobo::UNKNOWN_VAR_VAL;// roll-roll
    geo_pose_cov[M66_IDX(4,4)] = wizrobo::UNKNOWN_VAR_VAL;// pitch-pitch

    geo_pose_cov[M66_IDX(0,1)] = geo_pose_cov[M66_IDX(1,0)] = eig_mat(0,1);// x-y & y-x
    geo_pose_cov[M66_IDX(0,5)] = geo_pose_cov[M66_IDX(5,0)] = eig_mat(0,2);// x-yaw & yaw-x
    geo_pose_cov[M66_IDX(1,5)] = geo_pose_cov[M66_IDX(5,1)] = eig_mat(1,2);// y-yaw & yaw-y

}
void SetGeoTwistCovByEigMat(boost::array<double, 36>& geo_twist_cov, const Eigen::Matrix3f& eig_mat)
{
    SetGeoPoseCovByEigMat(geo_twist_cov, eig_mat);
}

void SetGeoVecCovByEigMat(boost::array<double, 9>& geo_vec_cov, const Eigen::Matrix3f& eig_mat)
{
    geo_vec_cov.assign(0);
    geo_vec_cov[M33_IDX(0,0)] = eig_mat(0,0);// x-x
    geo_vec_cov[M33_IDX(1,1)] = eig_mat(1,1);// y-y
    geo_vec_cov[M33_IDX(2,2)] = eig_mat(2,2);// yaw-yaw

    geo_vec_cov[M33_IDX(0,1)] = geo_vec_cov[M33_IDX(1,0)] = eig_mat(0,1);// x-y & y-x
    geo_vec_cov[M33_IDX(0,2)] = geo_vec_cov[M33_IDX(2,0)] = eig_mat(0,2);// x-yaw & yaw-x
    geo_vec_cov[M33_IDX(1,2)] = geo_vec_cov[M33_IDX(2,1)] = eig_mat(1,2);// y-yaw & yaw-y
}

geometry_msgs::Pose EigVec2GeoPose(const Eigen::Vector3f& eig_vec)
{
    geometry_msgs::Pose geo_pose;
    geo_pose.position = XYZ2GeoPoint(eig_vec[0], eig_vec[1], 0);
    geo_pose.orientation = Yaw2GeoQuat(eig_vec[2]);
    return geo_pose;
}
geometry_msgs::Twist EigVec2GeoTwist(const Eigen::Vector3f& eig_vec)
{
    geometry_msgs::Twist geo_twist;
    //geo_twist.linear = XYZ2GeoVec(eig_vec[0], eig_vec[1], 0);
    geo_twist.linear = XYZ2GeoVec(eig_vec[0], 0, 0);
    geo_twist.angular = XYZ2GeoVec(0, 0, eig_vec[2]);
    return geo_twist;
}
geometry_msgs::Vector3 EigVec2GeoVec(const Eigen::Vector3f& eig_vec)
{
    geometry_msgs::Vector3 geo_vec;
    geo_vec.x = eig_vec[0];
    geo_vec.y = eig_vec[1];
    geo_vec.z = eig_vec[2];
}

geometry_msgs::Quaternion GeoQuatWeightedSum(double w1, const geometry_msgs::Quaternion &geo_q1, double w2, const geometry_msgs::Quaternion &geo_q2)
{
    ROS_ASSERT(w1 != 0 && w2 != 0);
    tf::Quaternion tf_q1;
    tf::quaternionMsgToTF(geo_q1, tf_q1);
    tf::Quaternion tf_q2;
    tf::quaternionMsgToTF(geo_q1, tf_q2);
    tf::Quaternion tf_quat = tf::slerp(tf_q1, tf_q2, w1/(w1+w2));
    geometry_msgs::Quaternion geo_quat;
    tf::quaternionTFToMsg(tf_quat, geo_quat);
    return geo_quat;
}

std::string RosTimeToStr(const ros::Time &time)
{
    std::stringstream ss;
    ss << std::setiosflags(std::ios::fixed) << time.toSec();
    return ss.str();
}

geometry_msgs::PoseWithCovariance EigEst2GeoPoseEst(const Eigen::Vector3f &eig_vec, const Eigen::Matrix3f &eig_cov)
{
    geometry_msgs::PoseWithCovariance pose_est;
    pose_est.pose = EigVec2GeoPose(eig_vec);
    SetGeoPoseCovByEigMat(pose_est.covariance, eig_cov);
    return pose_est;
}

void CalGeoTwistEstByGeoPoseEst(const geometry_msgs::PoseWithCovarianceStamped &last_pose_est
                                , const geometry_msgs::TwistWithCovarianceStamped &last_twist_est
                                , const geometry_msgs::PoseWithCovarianceStamped &new_pose_est
                                , geometry_msgs::TwistWithCovarianceStamped &new_twist_est)
{
    new_twist_est = last_twist_est;
    Eigen::Vector3f last_pose_mean = GeoPose2EigVec(last_pose_est.pose.pose);
    Eigen::Matrix3f last_pose_cov = GeoPoseCov2EigMat(last_pose_est.pose.covariance);
    Eigen::Vector3f last_twist_mean = GeoTwist2EigVec(last_twist_est.twist.twist);
    Eigen::Matrix3f last_twist_cov = GeoPoseCov2EigMat(last_twist_est.twist.covariance);

    Eigen::Vector3f new_pose_mean = GeoPose2EigVec(new_pose_est.pose.pose);
    Eigen::Matrix3f new_pose_cov = GeoPoseCov2EigMat(new_pose_est.pose.covariance);
    Eigen::Vector3f new_twist_mean;
    Eigen::Matrix3f new_twist_cov;
    CalEigTwistEstByEigEst(last_pose_est.header.stamp
                           , last_pose_mean, last_pose_cov
                           , last_twist_mean, last_twist_cov
                           , new_pose_est.header.stamp
                           , new_pose_mean, new_pose_cov
                           , new_twist_mean, new_twist_cov);

    new_twist_est.twist.twist = EigVec2GeoTwist(new_twist_mean);
    SetGeoPoseCovByEigMat(new_twist_est.twist.covariance, new_twist_cov);
    new_twist_est.header.stamp = new_pose_est.header.stamp;
}

void CalEigTwistEstByEigEst(const ros::Time &last_time
                            , const Eigen::Vector3f &last_pose_mean, const Eigen::Matrix3f &last_pose_cov
                            , const Eigen::Vector3f &last_twist_mean, const Eigen::Matrix3f &last_twist_cov
                            , const ros::Time &new_time
                            , const Eigen::Vector3f &new_pose_mean, const Eigen::Matrix3f &new_pose_cov
                            , Eigen::Vector3f &new_twist_mean, Eigen::Matrix3f &new_twist_cov)
{
    double dt = (new_time - last_time).toSec();
    if (dt > 0.001)
    {
        // mean
        new_twist_mean[0] = (new_pose_mean[0] - last_pose_mean[0])/dt;
        new_twist_mean[1] = (new_pose_mean[1] - last_pose_mean[1])/dt;
        new_twist_mean[2] = NormRad(new_pose_mean[2] - last_pose_mean[2])/dt;
        // cov
        new_twist_cov(0,0)                      = (fabs(new_pose_cov(0,0))  + fabs(last_pose_cov(0,0))) / SQU(dt);
        new_twist_cov(1,1)                      = (fabs(new_pose_cov(1,1))  + fabs(last_pose_cov(1,1))) / SQU(dt);
        new_twist_cov(2,2)                      = (fabs(new_pose_cov(2,2))  + fabs(last_pose_cov(2,2))) / SQU(dt);
        new_twist_cov(0,1) = new_twist_cov(1,0) = (new_pose_cov(0,1)        + last_pose_cov(0,1))       / SQU(dt);
        new_twist_cov(0,2) = new_twist_cov(2,0) = (new_pose_cov(0,2)        + last_pose_cov(0,2))       / SQU(dt);
        new_twist_cov(1,2) = new_twist_cov(2,1) = (new_pose_cov(1,2)        + last_pose_cov(1,2))       / SQU(dt);
    }
    else
    {
        new_twist_mean = last_twist_mean;
        new_twist_cov = last_twist_cov;
    }
}

//tf::Quaternion TfVec2TfQuat(const tf::Vector3 &tf_vec)
//{
//    tf::Vector3 ux(1,0,0);
//    tf::Vector3 tf_vec_ = tf_vec;
//    return tf::shortestArcQuatNormalize2(ux, tf_vec_);
//}

//geometry_msgs::Quaternion GeoVec2GeoQuat(const geometry_msgs::Vector3 &geo_vec)
//{
////    double yaw = atan2(geo_dir.y, geo_dir.x);
////    double r = sqrt(SQU(geo_dir.x)+SQU(geo_dir.y));
////    double pitch = -atan2(geo_dir.z, r);
////    return RPY2GeoQuat(0, pitch, yaw);
//    tf::Vector3 tf_vec(geo_vec.x, geo_vec.y, geo_vec.z);
//    return TfQuat2GeoQuat(TfVec2TfQuat(tf_vec));
//}

void PublishOdomTf(const nav_msgs::Odometry &odom, tf::TransformListener &tf_sub, tf::TransformBroadcaster &tf_pub
                   , std::string &map_frame, std::string &odom_frame, std::string &base_frame, bool pub_map_to_odom_tf, bool pub_odom_to_base_tf)
{
    /// tf
    if (pub_odom_to_base_tf)
    {
        tf::StampedTransform map_to_odom_tf_msg;
        try
        {
            tf_sub.waitForTransform(map_frame, odom_frame, ros::Time::now(), ros::Duration(0.2));
            tf_sub.lookupTransform(map_frame, odom_frame, ros::Time(0), map_to_odom_tf_msg);
        }
        catch(tf::TransformException e)
        {
            ROS_ERROR("LidarOdom::PublishOdom(): Transform failed for {%s} -> {%s}: %s", map_frame.c_str(), odom_frame.c_str(), e.what());
            map_to_odom_tf_msg.setIdentity();
        }

        tf::Transform map_to_base_tf = ros_ext::GeoPose2TfTf(odom.pose.pose);
        tf::Transform odom_to_base_tf = map_to_odom_tf_msg.inverse() * map_to_base_tf;
        tf_pub.sendTransform(tf::StampedTransform(odom_to_base_tf, ros::Time::now(), odom_frame, base_frame));
    }
    else if (pub_map_to_odom_tf)
    {
        tf::StampedTransform odom_to_base_tf_msg;
        try
        {
            tf_sub.waitForTransform(odom_frame, base_frame, ros::Time::now(), ros::Duration(0.2));
            tf_sub.lookupTransform(odom_frame, base_frame, ros::Time(0), odom_to_base_tf_msg);
        }
        catch(tf::TransformException e)
        {
            ROS_ERROR("LidarOdom::PublishOdom(): Transform failed for {%s} -> {%s}: %s", odom_frame.c_str(), base_frame.c_str(), e.what());
            odom_to_base_tf_msg.setIdentity();
        }

        tf::Transform map_to_base_tf = ros_ext::GeoPose2TfTf(odom.pose.pose);
        tf::Transform map_to_odom_tf = map_to_base_tf * odom_to_base_tf_msg.inverse();
        tf_pub.sendTransform(tf::StampedTransform(map_to_odom_tf, ros::Time::now(), map_frame, odom_frame));
    }
}

double GeoPntDistDiff(const geometry_msgs::Point &geo_pnt0, const geometry_msgs::Point &geo_pnt1)
{
    return sqrt(SQU(geo_pnt1.x - geo_pnt0.x) + SQU(geo_pnt1.y - geo_pnt0.y) + SQU(geo_pnt1.z - geo_pnt0.z));
}

double GeoQuatOriDiff(const geometry_msgs::Quaternion &geo_quat0, const geometry_msgs::Quaternion &geo_quat1)
{
    return NormRad(GeoQuat2Yaw(geo_quat1) - GeoQuat2Yaw(geo_quat0));
}

std::string FootprintParamToStr(geometry_msgs::Polygon &polygon)
{
    std::stringstream ss;
    ss << "[";
    for (int i = 0; i < polygon.points.size();i++)
    {
        ss << "[" << polygon.points[i].x << ", " << polygon.points[i].y << "]";
        if (i < polygon.points.size() - 1)
        {
            ss << ", ";
        }
        else
        {
            ss << "]";
        }
    }
    return ss.str();
}

void GetFootprintParam(const ros::NodeHandle &nh, const std::string &param_name, geometry_msgs::Polygon &footprint)
{
    ROS_ERROR("GetFootprintParam(): Not implemented.");
}


//nav_msgs::Odometry CalculateOdom(const Eigen::Vector3f &pose_mean, const Eigen::Matrix3f &pose_cov
//                                 , const Eigen::Vector3f &twist_mean, const Eigen::Matrix3f &twist_cov
//                                 , std::string& map_frame, std::string& base_frame)
//{
//    nav_msgs::Odometry odom;
//    odom.header.stamp = ros::Time::now();
//    odom.header.frame_id = map_frame;
//    odom.child_frame_id = base_frame;
//    // odom.pose
//    odom.pose.pose.position = ros_ext::XYZ2GeoPoint(pose_mean[0], pose_mean[1], 0);
//    odom.pose.pose.orientation = ros_ext::Yaw2GeoQuat(pose_mean[2]);
//    SetGeoPoseCovByEigMat(odom.pose.covariance, pose_cov);
//    // odom.twist
//    odom.twist.twist.linear = XYZ2GeoVec(twist_mean[0], twist_mean[1], 0);
//    odom.twist.twist.angular = XYZ2GeoVec(0, 0, twist_mean[2]);
//    SetGeoPoseCovByEigMat(odom.twist.covariance, twist_cov);
//    return odom;
//}
bool IsQuaternionValid(const geometry_msgs::Quaternion& q)
{
    //first we need to check if the quaternion has nan's or infs
    if (!std::isfinite(q.x) || !std::isfinite(q.y)
            || !std::isfinite(q.z) || !std::isfinite(q.w)) {
        return false;
    }

    tf::Quaternion tf_q(q.x, q.y, q.z, q.w);

    //next, we need to check if the length of the quaternion is close to zero
    if (tf_q.length2() < 1e-6) {
        return false;
    }

    //next, we'll normalize the quaternion and check that it transforms the vertical vector correctly
    tf_q.normalize();

    tf::Vector3 up(0, 0, 1);

    double dot = up.dot(up.rotate(tf_q.getAxis(), tf_q.getAngle()));

    if (std::fabs(dot - 1) > 1e-3) {
        return false;
    }

    return true;
}

}}// namespace
