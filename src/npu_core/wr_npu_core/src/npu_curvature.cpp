#include "npu_curvature.h"
#include <cmath>

static inline double __sqr__(double x)
{
    return x*x;
}

static inline double __cube__(double x)
{
    return x*x*x;
}


namespace wizrobo{

void PathDiff1(const std::vector<geometry_msgs::PoseStamped>& path, std::vector<geometry_msgs::Pose2D>& path_diff1)
{
    path_diff1.clear();
    for(unsigned long i=1; i<path.size(); i++)
    {
        geometry_msgs::Pose2D temp_point;
        temp_point.x = path.at(i).pose.position.x - path.at(i-1).pose.position.x;
        temp_point.y = path.at(i).pose.position.y - path.at(i-1).pose.position.y;
        path_diff1.push_back(temp_point);
    }
}

void PathDiff1(const std::vector<geometry_msgs::Pose2D>& path, std::vector<geometry_msgs::Pose2D>& path_diff1)
{
    path_diff1.clear();
    for(unsigned long i=1; i<path.size(); i++)
    {
        geometry_msgs::Pose2D temp_point;
        temp_point.x = path.at(i).x - path.at(i-1).x;
        temp_point.y = path.at(i).y - path.at(i-1).y;
        path_diff1.push_back(temp_point);
    }
}

void PathDiff2(const std::vector<geometry_msgs::PoseStamped>& path, std::vector<geometry_msgs::Pose2D>& path_diff2)
{
    std::vector<geometry_msgs::Pose2D> temp_path;
    PathDiff1(path, temp_path);
    PathDiff1(temp_path, path_diff2);
}

void PathCurva(const std::vector<geometry_msgs::PoseStamped>& path, std::vector<geometry_msgs::Pose2D>& path_curva)
{
    std::vector<geometry_msgs::Pose2D> path_diff1;
    std::vector<geometry_msgs::Pose2D> path_diff2;
    PathDiff1(path, path_diff1);
    PathDiff2(path, path_diff2);
    path_curva.clear();
    for(unsigned long i=0; i<path_diff2.size(); i++)
    {
        geometry_msgs::Pose2D temp_point;
        temp_point.x = fabs(path_diff2.at(i).x)/sqrt(__cube__(1 + __sqr__(path_diff1.at(i).x)));
        temp_point.y = fabs(path_diff2.at(i).y)/sqrt(__cube__(1 + __sqr__(path_diff1.at(i).y)));
        path_curva.push_back(temp_point);
    }

}

void PathDiffSum(const std::vector<geometry_msgs::PoseStamped>& path, float& x, float& y)
{
    std::vector<geometry_msgs::Pose2D> temp_path;
    PathDiff2(path, temp_path);
    for(unsigned long i=0; i<temp_path.size(); i++)
    {
        x += temp_path.at(i).x;
        y += temp_path.at(i).y;
    }
}

}//namespace wizrobo
