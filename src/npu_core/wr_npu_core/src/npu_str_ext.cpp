#include "npu_str_ext.h"

namespace wizrobo { namespace str_ext {

std::vector<std::string> SplitString(std::string str, std::string sep)
{
    std::vector<std::string> str_vec;
    size_t start = 0, idx = str.find(sep, 0);
    while (idx != str.npos)
    {
        if (start != idx)
        {
            str_vec.push_back(str.substr(start, idx - start));
        }
        start = idx + sep.size();
        idx = str.find(sep, start);
    }
    if (!str.substr(start).empty())
    {
        str_vec.push_back(str.substr(start));
    }
    return str_vec;
}

std::string ExtracString(std::string str, std::string left_sep, std::string right_sep)
{
    size_t idx0 = str.find(left_sep);
    if (idx0 >= 0)
    {
        str = str.substr(idx0 + left_sep.size(), str.size() - idx0 - left_sep.size());
    }
    size_t idx1 = str.rfind(right_sep);
    if (idx1 >= 0)
    {
        str = str.substr(0, idx1);
    }
    return str;
}

std::string FloatVecToStr(const std::vector<float> &vec, std::string sep)
{
    std::stringstream ss;
    for (int i = 0; i < vec.size(); i++)
    {
        ss << std::fixed << std::setprecision(4) << vec[i];
        if (i < vec.size() - 1)
        {
            ss << sep;
        }
    }
    return ss.str();
}

std::vector<float> StrToFloatVec(const std::string str, std::string sep)
{
    std::vector<std::string> str_vec = SplitString(str, sep);
    std::vector<float> vec;
    for (int i = 0; i < str_vec.size(); i++)
    {
        vec.push_back(atof(str_vec[i].c_str()));
    }
    return vec;
}

std::string FloatMatToStr(const std::vector<std::vector<float> > &mat, std::string elm_sep, std::string vec_sep)
{
    std::stringstream ss;
    for (int i = 0; i < mat.size(); i++)
    {
        ss << "[" << FloatVecToStr(mat[i], elm_sep) << "]";
        if (i < mat.size() - 1)
        {
            ss << vec_sep;
        }
    }
    return ss.str();
}

std::vector<std::vector<float> > StrToFloatMat(const std::string str, std::string elm_sep, std::string vec_sep)
{
    std::vector<std::string> str_mat = SplitString(str, vec_sep);
    std::vector<std::vector<float> > mat;
    for (int i = 0; i < str_mat.size(); i++)
    {
        std::string vec_str = ExtracString(str_mat[i], "[", "]");
        std::vector<float> vec = StrToFloatVec(vec_str, elm_sep);
        mat.push_back(vec);
    }
    return mat;
}

std::string EigVecToStr(const Eigen::Vector3f eig_vec)
{
    boost::format f = boost::format("(%.4f, %.4f, %.4f)") % eig_vec(0) % eig_vec(1) % eig_vec(2);
    return f.str();
}

std::string EigCovToStr(const Eigen::Matrix3f eig_cov)
{
    boost::format f = boost::format("(%.6f, %.6f, %.6f)") % eig_cov(0,0) % eig_cov(1,1) % eig_cov(2,2);
    return f.str();
}

std::string EigRoiToStr(const Eigen::Matrix2f eig_roi)
{
    boost::format f = boost::format("BL(%.4f, %.4f)->TR(%.4f, %.4f)") % eig_roi(0,0) % eig_roi(1,0) % eig_roi(0,1) % eig_roi(1,1);
    return f.str();
}

}}
