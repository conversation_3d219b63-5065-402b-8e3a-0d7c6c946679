#include "npu_math.h"

namespace wizrobo {

bool WeightingFusePoseEst(const Eigen::Vector3f &new_mean, const Eigen::Matrix3f &new_cov
                 , const Eigen::Vector3f &cur_mean, const Eigen::Matrix3f &cur_cov
                 , Eigen::Vector3f &fus_mean, Eigen::Matrix3f &fus_cov, double new_est_scaling)
{
    new_est_scaling = MAX(fabs(new_est_scaling), 1e-6);

    fus_mean = cur_mean;
    fus_cov = cur_cov;
    Eigen::Vector3f w_new, w_cur;
    for (int i = 0; i < 3; i++) {
        double cur_cov_i = MAX(cur_cov(i,i), 1e-6);
        double new_cov_i = MAX(new_cov(i,i), 1e-6);
        double cov_sum = cur_cov_i + new_cov_i;
        w_new(i) = cur_cov_i / cov_sum;
        w_cur(i) = 1 - w_new(i);
        if (i < 2) {// pos
            fus_mean(i) = w_new(i) * new_mean(i) + w_cur(i) * cur_mean(i) ;
        } else { // ori
            double dth = NormRad(new_mean(i) - cur_mean(i));
            fus_mean(i) = NormRad(w_new(i) * dth + cur_mean(i));
        }
        fus_cov(i,i) = MAX(new_cov_i * cur_cov_i / cov_sum, 1e-6);
    }
    ROS_WARN("WeightingFusePoseEst(): fus_est{%s/%s} = (%s)*new_est + (%s)*cur_est"
              , str_ext::EigVecToStr(fus_mean).c_str(), str_ext::EigCovToStr(fus_cov).c_str()
              , str_ext::EigVecToStr(w_new).c_str(), str_ext::EigVecToStr(w_cur).c_str());
    return true;
}

bool SelectiveFusePoseEst(const Eigen::Vector3f &new_mean, const Eigen::Matrix3f &new_cov
                          , const Eigen::Vector3f &cur_mean, const Eigen::Matrix3f &cur_cov
                          , Eigen::Vector3f &fus_mean, Eigen::Matrix3f &fus_cov, double new_est_scaling)
{
    new_est_scaling = MAX(fabs(new_est_scaling), 1e-6);

    fus_mean = cur_mean;
    fus_cov = cur_cov;

    double new_cov_0 = MAX(new_cov(0,0), 1e-6);
    double new_cov_1 = MAX(new_cov(1,1), 1e-6);
    double new_cov_2 = MAX(new_cov(2,2), 1e-6);

    double cur_cov_0 = MAX(cur_cov(0,0), 1e-6);
    double cur_cov_1 = MAX(cur_cov(1,1), 1e-6);
    double cur_cov_2 = MAX(cur_cov(2,2), 1e-6);
#define POS_COV(x,y) ((x)+(y)+0.5*(fabs((x)-(y))))

    double new_pos_cov = POS_COV(new_cov_0, new_cov_1);
    double new_ori_cov = new_cov_2;
    double cur_pos_cov = POS_COV(cur_cov_0, cur_cov_1);
    double cur_ori_cov = cur_cov_2;

    bool flag_better_pos = new_pos_cov * new_est_scaling <= cur_pos_cov;
    bool flag_better_ori = new_ori_cov * new_est_scaling <= cur_ori_cov;
    if (flag_better_pos && flag_better_ori)
    {
        fus_mean = new_mean;
        fus_cov(0,0) = new_cov_0;
        fus_cov(1,1) = new_cov_1;
        fus_cov(2,2) = new_cov_2;
        ROS_WARN("SelectiveFusePoseEst(): new_est is %.2f times better, fus_est{%s/%s} = new_est"
                  , new_est_scaling, str_ext::EigVecToStr(fus_mean).c_str(), str_ext::EigCovToStr(fus_cov).c_str());
        return true;
    }
//    else if (flag_better_pos)
//    {
//        fus_mean(0) = new_mean(0);
//        fus_mean(1) = new_mean(1);
//        fus_cov(0,0) = new_cov_0;
//        fus_cov(1,1) = new_cov_1;
//        fus_cov(2,2) = cur_cov_2;
//        ROS_WARN("SelectiveFusePoseEst(): new_est has better pos, fus_est{%s/%s} = {new_est.pos, cur_est.ori}"
//                  , str_ext::EigVecToStr(fus_mean).c_str(), str_ext::EigCovToStr(fus_cov).c_str());
//        return true;
//    }
//    else if (flag_better_ori)
//    {
//        fus_mean(2) = new_mean(2);
//        fus_cov(0,0) = cur_cov_0;
//        fus_cov(1,1) = cur_cov_1;
//        fus_cov(2,2) = new_cov_2;
//        ROS_WARN("SelectiveFusePoseEst(): new_est has better ori, fus_est{%s/%s} = {new_est.ori, cur_est.pos}"
//                  , str_ext::EigVecToStr(fus_mean).c_str(), str_ext::EigCovToStr(fus_cov).c_str());
//        return true;
//    }
//    else
//    {
//        ROS_WARN("SelectiveFusePoseEst(): cur_est has better pos & ori, fus_est{%s/%s} = cur_est"
//                  , str_ext::EigVecToStr(fus_mean).c_str(), str_ext::EigCovToStr(fus_cov).c_str());
//        return false;
//    }
}

bool HasNaN(const Eigen::Vector3f &vec)
{
    bool has_nan = false;
    for (int i = 0; i < 3; i++)
    {
        if (isnan(vec(i)))
        {
            ROS_ERROR("HasNaN(): found nan: vec(%d)", i);
            has_nan = true;
        }
    }
    if (has_nan)
    {
        ROS_ERROR_STREAM("HasNaN(): vec = " << vec);
    }
    return has_nan;
}

bool HasNaN(const Eigen::Matrix3f &mat)
{
    bool has_nan = false;
    for (int i = 0; i < 3; i++)
    {
        for (int j = 0; j < 3; j++)
        {
            if (isnan(mat(i, j)))
            {
                ROS_ERROR("HasNaN(): found nan: mat(%d, %d)", i, j);
                has_nan = true;
            }
        }
    }
    if (has_nan)
    {
        ROS_ERROR_STREAM("HasNaN(): mat = " << mat);
    }
    return has_nan;
}


bool FusePoseEst(const Eigen::Vector3f &new_mean, const Eigen::Matrix3f &new_cov
                 , const Eigen::Vector3f &cur_mean, const Eigen::Matrix3f &cur_cov
                 , Eigen::Vector3f &fus_mean, Eigen::Matrix3f &fus_cov, PoseFusionMode fus_mode, double new_est_scaling)
{
    ROS_WARN("FusePoseEst(): cur_est = {%s/%s}, new_est = {%s/%s}"
              , str_ext::EigVecToStr(cur_mean).c_str(), str_ext::EigCovToStr(cur_cov).c_str()
              , str_ext::EigVecToStr(new_mean).c_str(), str_ext::EigCovToStr(new_cov).c_str());
    ROS_FATAL_COND(HasNaN(new_mean) || HasNaN(new_cov), "FusePoseEst(): found NaN in new_est!");
    if (HasNaN(cur_mean) || HasNaN(cur_cov))
    {
        fus_mean = new_mean;
        fus_cov = new_cov;
        ROS_WARN("FusePoseEst(): cuv_est has nan, fus_est{%s/%s} = new_est"
                  , str_ext::EigVecToStr(fus_mean).c_str(), str_ext::EigCovToStr(fus_cov).c_str());
        return true;
    }
    switch (fus_mode)
    {
    case SELECTIVE_FUSION:
        if (!SelectiveFusePoseEst(new_mean, new_cov, cur_mean, cur_cov, fus_mean, fus_cov, new_est_scaling))
        {
            return false;
        }
        break;
    case WEIGHTING_FUSION:
        if (!WeightingFusePoseEst(new_mean, new_cov, cur_mean, cur_cov, fus_mean, fus_cov, new_est_scaling))
        {
            return false;
        }
        break;
    case HYBRID_FUSION:
        if (!WeightingFusePoseEst(new_mean, new_cov, cur_mean, cur_cov, fus_mean, fus_cov, new_est_scaling))
        {
            return false;
        }
        break;
    }
    ROS_FATAL_COND(HasNaN(fus_mean) && HasNaN(fus_cov), "FusePoseEst(): found NaN in fus_est!");
    return true;
}

double NormRad(double rad)
{
    while (rad > M_PI) {
        rad -= 2 * M_PI;
    }
    while (rad < -M_PI) {
        rad += 2 * M_PI;
    }
    return rad;
}

double NormDeg(double deg)
{
    while (deg > 180) {
        deg -= 360;
    }
    while (deg < -180) {
        deg += 360;
    }
    return deg;
}

float TrimFloat(const float &val, int precision)
{
    int aux_num = 1;
    for (int i = 0; i < precision; i++) {
        aux_num *= 10;
    }
    int int_val = (int)(val * aux_num);
    float float_val = (float)(1.0 * int_val / aux_num);
    //WR_DEBUG("int_val = %d, float_val = %f", int_val, float_val);
    return float_val;
}

std::vector<geometry_msgs::PoseStamped> SimplePathSmoothing(
        std::vector<geometry_msgs::PoseStamped>* path, int smoothing_samples)
{

    int s = smoothing_samples;
    if(path->size() < (s+1))
        return *path;

    std::vector<geometry_msgs::PoseStamped> newpath;

    //Add first point
    newpath.emplace_back(path->at(0));

    //Smoothing
    for (unsigned int i=1; i < path->size()-1; i++) {
        newpath.emplace_back(path->at(i));
        geometry_msgs::Pose2D p;
        p.x = 0.0;
        p.y = 0.0;
        p.theta = 0.0;
        int cont = 0;
        int half = (int)floor(s/2 + 0.5);
        if (i < half) {
            half = i;
        } else if ((i+half) > path->size()) {
            half = path->size()-i;
        }

        for (unsigned int j = i-half; j < (i+half); j++) {
            p.x += path->at(j).pose.position.x;
            p.y += path->at(j).pose.position.y;
            p.theta += tf::getYaw(path->at(j).pose.orientation);
            cont++;
        }
        //ROS_DEBUG("i:%i, Half:%i, Cont: %i\n", i, half, cont);
        p.x = p.x/cont;
        p.y = p.y/cont;
//        p.theta = NormalizeAngle((p.theta/cont), -M_PI, M_PI);
        p.theta = NormalizeAngleDifference(p.theta/cont);

        newpath[i].pose.position.x = p.x;
        newpath[i].pose.position.y = p.y;
        newpath[i].pose.orientation = tf::createQuaternionMsgFromYaw(p.theta);

    }

    //Add last point
    newpath.emplace_back(path->at(path->size()-1));
    return newpath;
}

void Cart2Sph(const Eigen::Vector2f& cart, Eigen::Vector2f& sph)
{
    sph[0] = sqrt(SQU(cart[0])+SQU(cart[1]));
    sph[1] = atan2(cart[1],cart[0]);
}

void GetIntersection(const Eigen::Vector2f& p1,
                     const Eigen::Vector2f& p2,
                     const Eigen::Vector2f& q1,
                     const Eigen::Vector2f& q2,
                     Eigen::Vector2f& cp)
{
    Eigen::Vector2f dp = p1 - p2;
    Eigen::Vector2f dq = q1 - q2;
    double denom = dp[0]*dq[1] - dp[1]*dq[0];
    cp[0] = ((p1[0]*p2[1] - p1[1]*p2[0])*dq[0]
            - (q1[0]*q2[1] - q1[1]*q2[0])*dp[0])/denom;
    cp[1] = ((p1[0]*p2[1] - p1[1]*p2[0])*dq[1]
            - (q1[0]*q2[1] - q1[1]*q2[0])*dp[1])/denom;
}

}// namespace wizrobo
