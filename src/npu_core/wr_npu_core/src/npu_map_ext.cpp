#include "npu_map_ext.h"

namespace wizrobo {

void FilterMap(MapFilterType filter_type, int filter_size, nav_msgs::OccupancyGrid &map)
{
    //NPU_WARN("FilterMap()");
    cv::Mat mat;
    ros_ext::NavMapToCvMat(map, mat);
    if (filter_type == MEDIAN_FILTER)
    {
        cv::medianBlur(mat, mat, filter_size);
        //NPU_DEBUG("FilterMap(): MEDIAN_FILTER: filter_size = %d", filter_size);
    }
    else if (filter_type == GAUSSIAN_FILTER)
    {
        cv::GaussianBlur(mat, mat, cv::Size(filter_size, filter_size), 0);
        //NPU_DEBUG("FilterMap(): GAUSSIAN_FILTER: filter_size = %d", filter_size);
    }
    ros_ext::CvMatToNavMap(mat, map);
}

void ClearTrace(const nav_msgs::Path &traj, const geometry_msgs::Polygon &footprint, nav_msgs::OccupancyGrid &map, bool enb_trj_intp)
{
    //NPU_WARN("ClearTrace()");
    int vertex_num = footprint.points.size();
    if (vertex_num < 3)
    {
        //NPU_WARN("ClearTrace(): vertext_num < 3, not a polygon, abort");
        return;
    }
    cv::Mat mat;
    ros_ext::NavMapToCvMat(map, mat);
    cv::Point* p_vertices = new cv::Point[vertex_num];

    nav_msgs::Path traj_intp;
    if (enb_trj_intp)
    {
        double dist_thrs = map.info.resolution;
        double circum_radius = 0;
        for (int i = 0; i < footprint.points.size(); i++)
        {
            circum_radius = MAX(circum_radius, sqrt(SQU(footprint.points[i].x)+SQU(footprint.points[i].y)));
        }
        double ori_thrs = (circum_radius == 0) ? M_PI / 6 : map.info.resolution / circum_radius;
        InterpolateTraj(traj, dist_thrs, ori_thrs, traj_intp);
    }
    else
    {
        traj_intp = traj;
    }
    for (int i = 0; i < traj_intp.poses.size(); i++)
    {
        const geometry_msgs::PoseStamped& ps = traj_intp.poses[i];
        double yaw = ros_ext::GeoQuat2Yaw(ps.pose.orientation);
        for (int j = 0; j < vertex_num; j++)
        {
            const geometry_msgs::Point32& p = footprint.points[j];
            double x_w = cos(yaw) * p.x - sin(yaw) * p.y + ps.pose.position.x - map.info.origin.position.x;
            double y_w = sin(yaw) * p.x + cos(yaw) * p.y + ps.pose.position.y - map.info.origin.position.y;
            int x_i = static_cast<int>(x_w / map.info.resolution);
            int y_i = static_cast<int>(y_w / map.info.resolution);
            p_vertices[j].x = x_i;
            p_vertices[j].y = y_i;
        }
        cv::fillConvexPoly(mat, p_vertices, vertex_num, cv::Scalar(0));
    }
    ros_ext::CvMatToNavMap(mat, map);
    delete[] p_vertices;
}

void ClearFootprint(const geometry_msgs::Pose &pose, const geometry_msgs::Polygon &footprint, nav_msgs::OccupancyGrid &map)
{
    //TODO
}

void InterpolatePose(const geometry_msgs::Pose &p0, const geometry_msgs::Pose &p1, double dist_thrs_m, double ori_thrs_rad, std::vector<geometry_msgs::Pose> &p_list)
{
    //TODO
}

void InterpolatePoseStamped(const geometry_msgs::PoseStamped &ps0, const geometry_msgs::PoseStamped &ps1, double dist_thrs_m, double ori_thrs_rad, std::vector<geometry_msgs::PoseStamped> &ps_list)
{
    //TODO
}

void InterpolateTraj(const nav_msgs::Path &traj, double dist_thrs_m, double ori_thrs_rad, nav_msgs::Path &traj_intp)
{
    //NPU_WARN("InterpolateTraj(): dist_thrs_m = %.3f, ori_thrs_rad = %.3f", dist_thrs_m, ori_thrs_rad);
    traj_intp = traj;
    traj_intp.poses.clear();
    if (traj.poses.size() > 0)
    {
        for (int i = 0; i < traj.poses.size() - 1; i++)
        {
            const geometry_msgs::PoseStamped& ps0 = traj.poses[i];
            const geometry_msgs::PoseStamped& ps1 = traj.poses[i+1];

            double dx = ps1.pose.position.x - ps0.pose.position.x;
            double dy = ps1.pose.position.y - ps0.pose.position.y;
            double ds = sqrt(SQU(dx)+SQU(dy));
            int n_s = (dist_thrs_m == 0) ? 0 : (ceil(fabs(ds) / dist_thrs_m)) - 1;
            double dyaw = NormRad(GeoQuat2Yaw(ps1.pose.orientation) - GeoQuat2Yaw(ps0.pose.orientation));
            int n_yaw = (ori_thrs_rad == 0) ? 0 : (ceil(fabs(dyaw) / ori_thrs_rad) - 1);
            int n_intp = MAX(n_s, n_yaw);
            traj_intp.poses.push_back(ps0);
            if (n_intp > 0)
            {
                for (int j = 0; j < n_intp; j++)
                {
                    geometry_msgs::PoseStamped ps;
                    double t = 1.0 * (j + 1) / (n_intp + 1);
                    tf::Vector3 tf_pnt = tf::lerp(GeoPnt2TfVec(ps0.pose.position), GeoPnt2TfVec(ps1.pose.position), t);
                    ps.pose.position = TfVec2GeoPoint(tf_pnt);
                    tf::Quaternion tf_ori = tf::slerp(GeoQuat2TfQuat(ps0.pose.orientation), GeoQuat2TfQuat(ps1.pose.orientation), t);
                    ps.pose.orientation = TfQuat2GeoQuat(tf_ori);
                    ps.header.frame_id = ps0.header.frame_id;
                    ps.header.stamp = ros::Time((1 - t) * ps0.header.stamp.toSec() + t * ps1.header.stamp.toSec());
                    traj_intp.poses.push_back(ps);
                }
            }
        }
        traj_intp.poses.push_back(traj.poses.back());
//        NPU_WARN("InterpolateTraj(): traj.poses.size() = %d, traj_intp.poses.size() = %d"
//                  , static_cast<int>(traj.poses.size()), static_cast<int>(traj_intp.poses.size()));
    }
}
}// namespace
