#include "npu_dynamic_param_opt.h"

namespace wizrobo_npu {

using namespace std;

template<typename T>
bool DyParamOpt::_SetDyParam(const string &service_name, const string &key, typename T::RequestType::_value_type value)
{
    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<T>(service_name);
    T srv;
    srv.request.key = key;
    srv.request.value = value;
    bool rtn;
    if (client.isValid() == true) {
        rtn = client.call(srv);
    } else {
        rtn = false;
    }
    return rtn;
}

template<typename T>
typename T::ResponseType::_value_type DyParamOpt::_GetDyParam(const string &service_name, const string &key)
{
    ros::NodeHandle nh;
    ros::ServiceClient client = nh.serviceClient<T>(service_name);
    T srv;
    srv.request.key = key;

    if (client.isValid() == true && client.call(srv) == true) {
        return srv.response.value;
    }
    //return T::ResponseType::_value_type();
}

bool DyParamOpt::SetDyBoolParam(char *key, bool value)
{
    return _SetDyParam<SrvFedBoolParam>("FeedBoolDyParam", string(key), value);
}

bool DyParamOpt::SetDyFloatParam(char* key, float value)
{
    return _SetDyParam<SrvFedFloatParam>("FeedFloatDyParam", string(key), value);
}

bool DyParamOpt::SetDyIntParam(char* key, int value)
{
    return _SetDyParam<SrvFedIntParam>("FeedIntDyParam", string(key), value);
}

bool DyParamOpt::SetDyStrParam(char* key, const string &value)
{
    return _SetDyParam<SrvFedStrParam>("FeedStrDyParam", string(key), value);
}

bool DyParamOpt::GetDyBoolParam(char* key)
{
    return _GetDyParam<SrvGetBoolParam>("GetBoolDyParam", string(key));
}

float DyParamOpt::GetDyFloatParam(char* key)
{
    return _GetDyParam<SrvGetFloatParam>("GetFloatDyParam", string(key));
}

int DyParamOpt::GetDyIntParam(char* key)
{
    return _GetDyParam<SrvGetIntParam>("GetIntDyParam", string(key));
}

string DyParamOpt::GetDyStrParam(char* key)
{
    return _GetDyParam<SrvGetStrParam>("GetStrDyParam", string(key));
}

bool DyParamOpt::SetDyBoolParam(const string &key, bool value)
{
    return _SetDyParam<SrvFedBoolParam>("FeedBoolDyParam", key, value);
}

bool DyParamOpt::SetDyFloatParam(const string &key, float value)
{
    return _SetDyParam<SrvFedFloatParam>("FeedFloatDyParam", key, value);
}

bool DyParamOpt::SetDyIntParam(const string &key, int value)
{
    return _SetDyParam<SrvFedIntParam>("FeedIntDyParam", key, value);
}

bool DyParamOpt::SetDyStrParam(const string &key, const string &value)
{
    return _SetDyParam<SrvFedStrParam>("FeedStrDyParam", key, value);
}

bool DyParamOpt::GetDyBoolParam(const string &key)
{
    return _GetDyParam<SrvGetBoolParam>("GetBoolDyParam", key);
}

float DyParamOpt::GetDyFloatParam(const string &key)
{
    return _GetDyParam<SrvGetFloatParam>("GetFloatDyParam", key);
}

int DyParamOpt::GetDyIntParam(const string &key)
{
    return _GetDyParam<SrvGetIntParam>("GetIntDyParam", key);
}

string DyParamOpt::GetDyStrParam(const string &key)
{
    return _GetDyParam<SrvGetStrParam>("GetStrDyParam", key);
}

}
