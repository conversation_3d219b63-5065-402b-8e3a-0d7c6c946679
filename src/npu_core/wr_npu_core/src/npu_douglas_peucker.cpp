#include <cmath>

#include "npu_douglas_peucker.h"

#include "ros/ros.h"

namespace wizrobo {
using namespace std;

static inline double __abs__(double x)
{
    return (x>0)?x:-x;
}

static inline double __sqr__(double x)
{
    return x*x;
}

static inline double __dist__(double x0, double y0, double x1, double y1)
{
    return sqrt(__sqr__(x0-x1) + __sqr__(y0-y1));
}

DouglasPeucker::<PERSON><PERSON><PERSON><PERSON>(SMList *ptr, double d_threshold, bool is_circled)
    : ptr_curve_(ptr)
    , d_threshold_(d_threshold)
    , is_circled_(is_circled)
{
}

int DouglasPeucker::Decompose(SMList &decomposed_curve)
{
    if (is_circled_) {
        Curve curve1, curve2;
        curve1.f = (*ptr_curve_).begin();
        curve1.b = (*ptr_curve_).begin();
        int length = (*ptr_curve_).size()/2;
        for (int i=0;i<length;++i) {
            ++curve1.b;
        }
        curve2.f = curve1.b;
        curve2.b = curve1.f;
        SMList decomposed_curve1, decomposed_curve2;
        DoDecompose(curve1, decomposed_curve1);
        DoDecompose(curve2, decomposed_curve2);
        Append(decomposed_curve, decomposed_curve1);
        Append(decomposed_curve, decomposed_curve2);
        decomposed_curve.pop_back();
    } else {

        Curve curve1;
        SMList decomposed_curve1;
        curve1.f = (*ptr_curve_).begin();
        curve1.b = (*ptr_curve_).begin();
        for(int i=0; i<(*ptr_curve_).size()-1; i++){
            curve1.b++;
        }
//        ROS_WARN("curve1(%f, %f)->(%f, %f)",
//                 curve1.f->pose.position.x, curve1.f->pose.position.y,
//                 curve1.b->pose.position.x, curve1.b->pose.position.y);

        DoDecompose(curve1, decomposed_curve1);
        Append(decomposed_curve, decomposed_curve1);
    }
    return 0;
}

int DouglasPeucker::Decompose(SMList* curve_in, SMList &curve_out, int& count, std::vector<int>& index_list)
{
    Curve curve1;
    SMList decomposed_curve1;
    std::vector<int> index_list1;
    curve1.f = (*curve_in).begin();
    curve1.f_index = 0;
    curve1.b = (*curve_in).begin();
    for(int i=0; i<(*curve_in).size()-1; i++){
        curve1.b++;
    }
    curve1.b_index = (*curve_in).size()-1;
    DoDecompose(curve1, decomposed_curve1, index_list1);
    Append(curve_out, decomposed_curve1, index_list, index_list1);
    count = index_list.size();
    return 0;
}

//int DouglasPeucker::FindClosePoint(int& index, const geometry_msgs::Pose2D& current_pose)
//{
//    SMList decomposed_curve;
//    int count;
//    std::vector<int> index_list;
//    geometry_msgs::Pose2D back_point1, back_point2;
//    Decompose(decomposed_curve, count, index_list);
//    double dist1 = CalculateDist2Segment(decomposed_curve[0], decomposed_curve[1], current_pose, back_point1);
//    if (dist1 == 999) {
//        double min_dist;
//        double start_dist = DIST(decomposed_curve[0].pose.position.x, decomposed_curve[0].pose.position.y,
//                current_pose.x, current_pose.y);
//        double end_dist = DIST(decomposed_curve[1].pose.position.x, decomposed_curve[1].pose.position.y,
//                current_pose.x, current_pose.y);
//        if (start_dist > end_dist) {
//            min_dist = end_dist;
//            index = index_list[1];
//        } else {
//            min_dist = start_dist;
//            index = index_list[0];
//        }
//        double dist2 = CalculateDist2Segment(decomposed_curve[1], decomposed_curve[2], current_pose, back_point2);
//        if (dist2 == 999) {

//        } else {
//            if (dist2<min_dist) {
//                for (unsigned int i = index_list[1]; i < index_list[2]-1; i++) {
//                    if ((back_point2.x - (*ptr_curve_).at(i).pose.position.x)*(back_point2.x - (*ptr_curve_).at(i+1).pose.position.x) <= 0) {
//                        index = i;
//                        return 1;
//                    }
//                }
//            }
//            return 1;
//        }
//    } else {
//        for (unsigned int i = index_list[0]; i < index_list[1]-1; i++) {
//            if ((back_point1.x - (*ptr_curve_).at(i).pose.position.x)*(back_point1.x - (*ptr_curve_).at(i+1).pose.position.x) <= 0) {
//                index = i;
//                return 1;
//            }
//        }
//    }
//    return 0;
//}


double DouglasPeucker::CalculateDist2Segment(
        const geometry_msgs::PoseStamped& start_point, const geometry_msgs::PoseStamped& end_point,
        const geometry_msgs::Pose2D& current_pose, geometry_msgs::Pose2D& back_point)
{
    double A = end_point.pose.position.y - start_point.pose.position.y;
    double B = start_point.pose.position.x - end_point.pose.position.x;
    double C = start_point.pose.position.y*(end_point.pose.position.x - start_point.pose.position.x)
            - start_point.pose.position.x*(end_point.pose.position.y - start_point.pose.position.y);
    back_point.x = ( B*B*current_pose.x - A*B*current_pose.y - A*C ) / ( A*A + B*B );
    back_point.y = ( -A*B*current_pose.x + A*A*current_pose.y - B*C ) / ( A*A + B*B );
    if ((DIST(start_point.pose.position.x, start_point.pose.position.y, end_point.pose.position.x, end_point.pose.position.y)>
            DIST(start_point.pose.position.x, start_point.pose.position.y, back_point.x, back_point.y))
         && (DIST(start_point.pose.position.x, start_point.pose.position.y, end_point.pose.position.x, end_point.pose.position.y)>
            DIST(end_point.pose.position.x, end_point.pose.position.y, back_point.x, back_point.y))) {
        return DIST(back_point.x, back_point.y, current_pose.x, current_pose.y);
    } else {
        return 999;
    }
}

int DouglasPeucker::Append(SMList &decomposed_curve1, SMList &decomposed_curve2)
{
    if (decomposed_curve1.size() == 0) {
        decomposed_curve1 = decomposed_curve2;
        return 0;
    }
    if (__abs__(decomposed_curve1.back().pose.position.x - decomposed_curve2.front().pose.position.x) < 0.001
            && __abs__(decomposed_curve1.back().pose.position.y - decomposed_curve2.front().pose.position.y) < 0.001) {
//        decomposed_curve2.pop_front();
        decomposed_curve2.erase(decomposed_curve2.begin());

    }
    while (decomposed_curve2.size() != 0) {
        geometry_msgs::PoseStamped& sml = decomposed_curve2.front();
//        decomposed_curve2.pop_front();
        decomposed_curve1.push_back(sml);
        decomposed_curve2.erase(decomposed_curve2.begin());
    }
    return 0;
}

int DouglasPeucker::Append(SMList &decomposed_curve1, SMList &decomposed_curve2,
                           std::vector<int>& index_list1, std::vector<int>& index_list2)
{
    if (decomposed_curve1.size() == 0) {
        decomposed_curve1 = decomposed_curve2;
        index_list1 = index_list2;
        return 0;
    }

    if (__abs__(decomposed_curve1.back().pose.position.x - decomposed_curve2.front().pose.position.x) < 0.001
            && __abs__(decomposed_curve1.back().pose.position.y - decomposed_curve2.front().pose.position.y) < 0.001) {
//        decomposed_curve2.pop_front();
        decomposed_curve2.erase(decomposed_curve2.begin());
        index_list2.erase(index_list2.begin());

    }
    while (decomposed_curve2.size() != 0) {
        geometry_msgs::PoseStamped& sml = decomposed_curve2.front();
        int& sml_index = index_list2.front();
//        decomposed_curve2.pop_front();
        decomposed_curve1.push_back(sml);
        decomposed_curve2.erase(decomposed_curve2.begin());
        index_list1.push_back(sml_index);
        index_list2.erase(index_list2.begin());
    }
    return 0;
}

int DouglasPeucker::DoDecompose(const Curve& curve_in, SMList& decomposed_curve)
{
    //LM_DEBUG("HERE20 curve_in(%d, %d) -> (%d, %d)",
    //curve_in.f, curve_in.f->y, curve_in.b->x, curve_in.b->y);
    SMLIter peek;
    if (FindPeek(curve_in, peek) < 0) {
        decomposed_curve.push_back(*curve_in.f);        
        decomposed_curve.push_back(*curve_in.b);
        return 0;
    }
    Curve curve1(curve_in.f, peek), curve2(peek, curve_in.b);
    SMList decomposed_curve1, decomposed_curve2;
    DoDecompose(curve1, decomposed_curve1);
    DoDecompose(curve2, decomposed_curve2);
    Append(decomposed_curve, decomposed_curve1);
    Append(decomposed_curve, decomposed_curve2);
    return 0;
}

int DouglasPeucker::DoDecompose(
        const Curve& curve_in, SMList& decomposed_curve, std::vector<int>& index_list)
{
    //LM_DEBUG("HERE20 curve_in(%d, %d) -> (%d, %d)",
    //curve_in.f->x, curve_in.f->y, curve_in.b->x, curve_in.b->y);
    SMLIter peek;
    int peek_index;
    if (FindPeek(curve_in, peek, peek_index) < 0) {
        decomposed_curve.push_back(*curve_in.f);
        index_list.push_back(curve_in.f_index);
        decomposed_curve.push_back(*curve_in.b);
        index_list.push_back(curve_in.b_index);
        return 0;
    }
    Curve curve1(curve_in.f, peek), curve2(peek, curve_in.b);
    curve1.f_index = curve_in.f_index;
    curve1.b_index = peek_index;
    curve2.f_index = peek_index;
    curve2.b_index = curve_in.b_index;
    SMList decomposed_curve1, decomposed_curve2;
    std::vector<int> index_list1, index_list2;
    DoDecompose(curve1, decomposed_curve1, index_list1);
    DoDecompose(curve2, decomposed_curve2, index_list2);
    Append(decomposed_curve, decomposed_curve1, index_list, index_list1);
    Append(decomposed_curve, decomposed_curve2, index_list, index_list2);
    return 0;
}

int DouglasPeucker::FindPeek(const Curve& curve, SMLIter& peek) const
{

    SMLIter iter = curve.f;
    double alpha = atan2(curve.b->pose.position.y - curve.f->pose.position.y,
                         curve.b->pose.position.x - curve.f->pose.position.x);
    double beta, theta;
    double d, d_max = -9999;
    while (iter != curve.b) {
        beta = atan2(iter->pose.position.y - curve.f->pose.position.y,
                     iter->pose.position.x - curve.f->pose.position.x);
        theta = beta - alpha;
//        ROS_WARN("alpha[%.4f], beta[%.4f], theta[%.4f]",
//                 alpha, beta, theta);
        d = __abs__(__dist__(iter->pose.position.x, iter->pose.position.y,
                             curve.f->pose.position.x, curve.f->pose.position.y)
                    * sin(theta));
        if ((d - d_max) >= -0.0001) {
            ROS_DEBUG("change d_max");
            peek = iter;
            d_max = d;
        }
//        ROS_WARN("d: %.4f/%.4f peek(%f, %f)",
//                 d, d_max, peek->pose.position.x, peek->pose.position.y);
        iter++;
        if (iter == (*ptr_curve_).end()) {
            iter = (*ptr_curve_).begin();
        }
    }

//    ROS_WARN("curve(%f, %f) -> (%f, %f)  peek(%f, %f)",
//             curve.f->pose.position.x, curve.f->pose.position.y,
//             curve.b->pose.position.x, curve.b->pose.position.y,
//             peek->pose.position.x, peek->pose.position.y);

    return (d_max>=d_threshold_)?0:-1;
}

int DouglasPeucker::FindPeek(const Curve& curve, SMLIter& peek, int& index) const
{
    SMLIter iter = curve.f;
    int temp_index = curve.f_index;
    double alpha = atan2(curve.b->pose.position.y - curve.f->pose.position.y, curve.b->pose.position.x - curve.f->pose.position.x);
    double beta, theta;
    double d, d_max = -9999;
    while (iter != curve.b) {
        beta = atan2(iter->pose.position.y - curve.f->pose.position.y, iter->pose.position.x - curve.f->pose.position.x);
        theta = beta - alpha;
        d = __abs__(__dist__(iter->pose.position.x, iter->pose.position.y, curve.f->pose.position.x, curve.f->pose.position.y) * sin(theta));
        if ((d - d_max) >= -0.0001) {
            peek = iter;
            index = temp_index;
            d_max = d;
        }
        iter++;
        temp_index++;
        if (iter == (*ptr_curve_).end()) {
            iter = (*ptr_curve_).begin();
        }
    }
    return (d_max>=d_threshold_)?0:-1;
}

}
