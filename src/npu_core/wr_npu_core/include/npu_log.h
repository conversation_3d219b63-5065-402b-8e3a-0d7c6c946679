/*
 * Copyright 2016 The Cartographer Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef WIZROBO_NPU_LOG_H_
#define WIZROBO_NPU_LOG_H_

#include <ctime>

#include "glog/logging.h"
#include <ros/ros.h>
#define __FILENAME__ (strrchr(__FILE__, '/') ? (strrchr(__FILE__, '/') + 1):__FILE__)
//google debug
#define DLOG_INFO DLOG(INFO)
#define LOG_INFO LOG(INFO)
#define LOG_INFO_IF(condition) LOG_IF(INFO,condition)
#define LOG_INFO_EVERY(freq) LOG_EVERY_N(INFO, freq)

#define DLOG_WARN DLOG(WARNING)
#define LOG_WARN LOG(WARNING)
#define LOG_WARN_IF(condition) LOG_IF(WARNING,condition)
#define LOG_WARN_EVERY(freq) LOG_EVERY_N(WARNING, freq)
#define LOG_WARN_FIRST_N(times) LOG_FIRST_N(WARNING, times)

#define LOG_ERROR LOG(ERROR)
#define LOG_ERROR_IF(condition) LOG_IF(ERROR,condition)
#define LOG_ERROR_EVERY(freq) LOG_EVERY_N(ERROR, freq)

#define LOG_FATAL LOG(FATAL)
#define LOG_FATAL_IF(condition) LOG_IF(FATAL,condition)

//linkmiao debug
// _LM_NAME_PREFIX_
#define LM_DEBUG(fmt, arg...) ROS_DEBUG_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_DEBUG_ONCE(fmt, arg...) ROS_DEBUG_ONCE_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_DEBUG_DELAYED(delay, fmt, arg...) ROS_DEBUG_DELAYED_THROTTLE_NAMED(delay, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)
#define LM_DEBUG_STREAM(args) ROS_DEBUG_STREAM_NAMED(__FUNCTION__, "[" << __FILENAME__ << ":" << __LINE__ << "]<" << __FUNCTION__ << "> " << args)
#define LM_DEBUG_COND(cond, fmt, arg...) ROS_DEBUG_COND_NAMED(cond, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)

#define LM_INFO(fmt, arg...) ROS_INFO_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_INFO_ONCE(fmt, arg...) ROS_INFO_ONCE_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_INFO_DELAYED(delay, fmt, arg...) ROS_INFO_DELAYED_THROTTLE_NAMED(delay, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)
#define LM_INFO_STREAM(args) ROS_INFO_STREAM_NAMED(__FUNCTION__, "[" << __FILENAME__ << ":" << __LINE__ << "]<" << __FUNCTION__ << "> " << args)
#define LM_INFO_COND(cond, fmt, arg...) ROS_INFO_COND_NAMED(cond, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)

#define LM_WARN(fmt, arg...) ROS_WARN_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_WARN_ONCE(fmt, arg...) ROS_WARN_ONCE_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_WARN_DELAYED(delay, fmt, arg...) ROS_WARN_DELAYED_THROTTLE_NAMED(delay, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)
#define LM_WARN_STREAM(args) ROS_WARN_STREAM_NAMED(__FUNCTION__, "[" << __FILENAME__ << ":" << __LINE__ << "]<" << __FUNCTION__ << "> " << args)
#define LM_WARN_COND(cond, fmt, arg...) ROS_WARN_COND_NAMED(cond, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)

#define LM_ERROR(fmt, arg...) ROS_ERROR_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_ERROR_ONCE(fmt, arg...) ROS_ERROR_ONCE_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_ERROR_DELAYED(delay, fmt, arg...) ROS_ERROR_DELAYED_THROTTLE_NAMED(delay, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)
#define LM_ERROR_STREAM(args) ROS_ERROR_STREAM_NAMED(__FUNCTION__, "[" << __FILENAME__ << ":" << __LINE__ << "]<" << __FUNCTION__ << "> " << args)
#define LM_ERROR_COND(cond, fmt, arg...) ROS_ERROR_COND_NAMED(cond, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)

#define LM_FATAL(fmt, arg...) ROS_FATAL_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_FATAL_ONCE(fmt, arg...) ROS_FATAL_ONCE_NAMED(__FUNCTION__,  "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__, ##arg)
#define LM_FATAL_DELAYED(delay, fmt, arg...) ROS_FATAL_DELAYED_THROTTLE_NAMED(delay, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)
#define LM_FATAL_STREAM(args) ROS_FATAL_STREAM_NAMED(__FUNCTION__, "[" << __FILENAME__ << ":" << __LINE__ << "]<" << __FUNCTION__ << "> " << args)
#define LM_FATAL_COND(cond, fmt, arg...) ROS_FATAL_COND_NAMED(cond, __FUNCTION__, "[%s:%d]<%s()> " fmt, __FILENAME__, __LINE__, __FUNCTION__,##arg)

///function debug
#define _LM_DEBUG LOG_WARN << "Function : " << __FUNCTION__ << "() . Line : " << __LINE__ << " !";
#define _LM_FUNCTION LOG_WARN << "This functions is not implemented yet!";

namespace wizrobo {

// Makes Google logging use ROS logging for output while an instance of this
// class exists.
/*
class ScopedRosLogSink : public ::google::LogSink {
 public:
  ScopedRosLogSink();
  ~ScopedRosLogSink() override;

  void send(::google::LogSeverity severity, const char* filename,
            const char* base_filename, int line, const struct std::tm* tm_time,
            const char* message, size_t message_len) override;

  void WaitTillSent() override;

 private:
  bool will_die_;
};
*/
}  // namespace wizrobo

#endif  // WIZROBO_NPU_LOG_H_
