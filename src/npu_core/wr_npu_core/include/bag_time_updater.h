#ifndef WIZROBO_BAG_TIME_UPDATER_H
#define WIZROBO_BAG_TIME_UPDATER_H
#include <vector>
#include <ros/ros.h>
#include <rosbag/bag.h>
#include <rosbag/view.h>
#include <sensor_msgs/LaserScan.h>
#include <nav_msgs/Odometry.h>
#include <sensor_msgs/Imu.h>

#include <boost/foreach.hpp>

#include <npu.h>

namespace wizrobo { namespace sensor {

class BagTimeUpdater
{
public:
    BagTimeUpdater(int argc, char** argv)
    {
        ros::init(argc, argv, "bag_time_updater_node");
        ros::start();
    }
    virtual ~BagTimeUpdater()
    {
    }
    inline void Init()
    {

    }
    inline void Run()
    {
        ros::NodeHandle nh;

        ros::NodeHandle prv_nh("~");
        std::string bag_id;
        prv_nh.param<std::string>("bag_id", bag_id, "");
        std::string bag_dir;
        prv_nh.param<std::string>("bag_dir", bag_dir, "/npu/bag/");

        bool fix_scan_ang_max;
        prv_nh.param<bool>("fix_scan_ang_max", fix_scan_ang_max, false);

        rosbag::Bag src_bag;
        src_bag.open(bag_dir + bag_id + ".bag", rosbag::bagmode::Read);
        rosbag::Bag dst_bag;
        dst_bag.open(bag_dir + bag_id + ".new" + ".bag", rosbag::bagmode::Write);

        std::vector<std::string> topics;
        std::string imu_topic, lidar_topic, odom_topic;
        prv_nh.param<std::string>("imu_topic", imu_topic, STD_IMU_TOPIC_NAME);
        prv_nh.param<std::string>("lidar_topic", lidar_topic, STD_SCAN_TOPIC_NAME);
        prv_nh.param<std::string>("odom_topic", odom_topic, STD_ODOM_TOPIC_NAME);

        topics.push_back(imu_topic);
        topics.push_back(lidar_topic);
        topics.push_back(odom_topic);

        rosbag::View view(src_bag, rosbag::TopicQuery(topics));

        int cnt = 0;
        ros::Time start_time = ros::Time::now();
        ros::Duration time_offset;
        bool flag_got_offset = false;
        BOOST_FOREACH(rosbag::MessageInstance const m, view)
        {
            ROS_DEBUG("record#%d", cnt++);
            if (!flag_got_offset)
            {
                time_offset = ros::Time::now() - m.getTime();
                ROS_INFO("time_offset = %lf[s]", time_offset.toSec());
                flag_got_offset = true;
            }
            if (m.getTopic() == lidar_topic)
            {
                ROS_INFO("update %s", m.getTopic().c_str());
                sensor_msgs::LaserScan::Ptr scan_ptr = m.instantiate<sensor_msgs::LaserScan>();
                if (scan_ptr != NULL)
                {
                    //d_info_pub.publish(*d_info_ptr);
                    scan_ptr->header.stamp += time_offset;
                    if (fix_scan_ang_max)
                    {
                        int beam_num = scan_ptr->ranges.size();
                        double delta = scan_ptr->angle_max - scan_ptr->angle_min - (beam_num - 1)*scan_ptr->angle_increment;
                        if (fabs(delta) > 0.1)
                        {
                            ROS_INFO("fix ang_max: old: ang_min = %f, ang_max = %f, ang_inc = %f, ranges.size() = %d"
                                     , scan_ptr->angle_min, scan_ptr->angle_max, scan_ptr->angle_increment
                                     , static_cast<int>(scan_ptr->ranges.size()));
                            scan_ptr->angle_max = scan_ptr->angle_min + scan_ptr->angle_increment * (beam_num - 1);
                            ROS_INFO("fix ang_max: new: ang_min = %f, ang_max = %f, ang_inc = %f, ranges.size() = %d"
                                     , scan_ptr->angle_min, scan_ptr->angle_max, scan_ptr->angle_increment
                                     , static_cast<int>(scan_ptr->ranges.size()));
                        }
                    }
                    dst_bag.write(STD_SCAN_TOPIC_NAME, scan_ptr->header.stamp, *scan_ptr);
                }
            }
            if (m.getTopic() == odom_topic)
            {
                ROS_INFO("update %s", m.getTopic().c_str());
                nav_msgs::Odometry::Ptr msg_ptr = m.instantiate<nav_msgs::Odometry>();
                if (msg_ptr != NULL)
                {
                    //d_info_pub.publish(*d_info_ptr);
                    msg_ptr->header.stamp += time_offset;
                    dst_bag.write(STD_ODOM_TOPIC_NAME, msg_ptr->header.stamp, *msg_ptr);
                }
            }
            if (m.getTopic() == imu_topic)
            {
                ROS_INFO("update %s", m.getTopic().c_str());
                sensor_msgs::Imu::Ptr msg_ptr = m.instantiate<sensor_msgs::Imu>();
                if (msg_ptr != NULL)
                {
                    //d_info_pub.publish(*d_info_ptr);
                    msg_ptr->header.stamp += time_offset;
                    dst_bag.write(STD_IMU_TOPIC_NAME, msg_ptr->header.stamp, *msg_ptr);
                }
            }
        }
        dst_bag.close();
        src_bag.close();
        //ROS_INFO("finished, press Ctrl+c to quit.");
        ROS_INFO("Succeed.");
        //ros::spin();
    }
};
}}// namespace

#endif // WIZROBO_BAG_TIME_UPDATER_H
