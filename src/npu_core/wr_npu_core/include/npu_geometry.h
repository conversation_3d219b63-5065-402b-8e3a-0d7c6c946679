#ifndef WIZROBO_NPU_GEOMETRY_H
#define WIZROBO_NPU_GEOMETRY_H

#include <string>
#include <vector>
#include <map>

#include <geometry_msgs/Polygon.h>
#include <geometry_msgs/PoseArray.h>

#include <npuice/npuice_param.h>
#include <npu_enum_ext.h>

namespace wizrobo { namespace geometry {

enum WrPolyType {
    POLYLINE,
    STATION,
    PATH,
    VIRTUAL_WALL,
    MISSION,
    REGION,
    TRAP,
    SCRB_PATH,
    DEFAULTID,
    INVALID
};

enum SubType {
    RESERVE
};

struct WrInfo {
    std::string id;
    WrPolyType type;
    int sub_type;
    bool is_closed;

    WrInfo()
        : id("")
        , type(POLYLINE)
        , sub_type(0)
        , is_closed(false)
    {}
};

class WrPolyline {
public:
    WrInfo info;
    std::string additional_data;
    std::vector<float> poses;

public:
    WrPolyline()
        : info()
        , additional_data()
        , poses()
    {}

};

typedef std::vector<WrPolyline> WrPolylineList;
typedef std::map<std::string, WrPolyline> WrPolylineMap;

}}

namespace wizrobo { namespace enum_ext {

using namespace wizrobo::geometry;

BEGIN_ENUM_STRING(WrPolyType) {
    ENUM_STRING(POLYLINE);
    ENUM_STRING(STATION);
    ENUM_STRING(PATH);
    ENUM_STRING(VIRTUAL_WALL);
    ENUM_STRING(MISSION);
    ENUM_STRING(REGION);
    ENUM_STRING(TRAP);
    ENUM_STRING(SCRB_PATH);
    ENUM_STRING(DEFAULTID);
    ENUM_STRING(INVALID);
} END_ENUM_STRING;

}}

#endif // WIZROBO_NPU_GEOMETRY_H
