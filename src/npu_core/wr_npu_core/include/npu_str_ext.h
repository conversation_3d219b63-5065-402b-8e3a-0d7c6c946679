#ifndef WIZROBO_NPU_STR_EXT_H
#define WIZROBO_NPU_STR_EXT_H
#include <vector>
#include <string>
#include <bitset>
#include <deque>

#include <boost/format.hpp>
#include <eigen3/Eigen/Core>
#include <ros/ros.h>

#include "npu_common.h"

namespace wizrobo {  namespace str_ext {

/// eigen -> string
std::string EigVecToStr(const Eigen::Vector3f eig_vec);
std::string EigCovToStr(const Eigen::Matrix3f eig_cov);
std::string EigRoiToStr(const Eigen::Matrix2f eig_roi);

/// string splitting
std::vector<std::string> SplitString(std::string str, std::string sep);
std::string ExtracString(std::string str, std::string left_sep, std::string right_sep);

////// float_vec <-> string
std::string FloatVecToStr(const std::vector<float>& vec, std::string sep);
std::vector<float> StrToFloatVec(const std::string str, std::string sep);

//// float_mat <-> string
std::string FloatMatToStr(const std::vector<std::vector<float> >& mat, std::string elm_sep, std::string vec_sep);
std::vector<std::vector<float> > StrToFloatMat(const std::string str, std::string elm_sep, std::string vec_sep);

////// byte -> string
inline std::string BoolToStr(bool b)
{
    if (b)
    {
        return "true";
    }
    else
    {
        return "false";
    }
}
inline std::string ByteToStr(byte b)
{
    std::stringstream ss;
    ss << std::hex << std::setfill('0') << std::setw(2)
       << std::setiosflags(std::ios::uppercase)
       << static_cast<short>(b & 0xFF);
    return ss.str();
}
inline std::string ByteToBinStr(byte b)
{
    std::stringstream ss;
    ss << std::bitset<8>(b & 0xFF);
    return ss.str();
}
inline std::string ByteArrToStr(const byte* arr, int arr_len)
{
    std::stringstream ss;
    for (int i = 0; i < arr_len; i++)
    {
        ss << std::hex << std::setfill('0') << std::setw(2)
           << std::setiosflags(std::ios::uppercase)
           << static_cast<short>(arr[i]) << " ";
    }
    return ss.str();
}
inline std::string ByteVecToStr(const std::vector<byte> vec)
{
    std::stringstream ss;
    for (int i = 0; i < vec.size(); i++)
    {
        ss << std::hex << std::setfill('0') << std::setw(2)
           << std::setiosflags(std::ios::uppercase)
           << static_cast<short>(vec[i]) << " ";
    }
    return ss.str();
}
inline std::string ByteQueToStr(const std::deque<byte> que)
{
    std::stringstream ss;
    for (int i = 0; i < que.size(); i++)
    {
        ss << std::hex << std::setfill('0') << std::setw(2)
           << std::setiosflags(std::ios::uppercase)
           << static_cast<short>(que[i]) << " ";
    }
    return ss.str();
}

////// int <-> string
inline std::string IntToStr(int val)
{
    std::stringstream ss;
    ss << val;
    return ss.str();
}
inline int StrToInt(std::string str)
{
    return atoi(str.c_str());
}
}}
#endif// WIZROBO_NPU_STR_EXT_H
