#ifndef WIZROBO_NPU_ROS_EXT_H
#define WIZROBO_NPU_ROS_EXT_H
#include <iomanip>

#include <eigen3/Eigen/Core>
#include <ros/node_handle.h>
#include <nav_msgs/OccupancyGrid.h>
#include <sensor_msgs/image_encodings.h>
#include <std_msgs/ColorRGBA.h>
#include <geometry_msgs/Point.h>
#include <geometry_msgs/Point32.h>
#include <geometry_msgs/Polygon.h>
#include <geometry_msgs/Pose.h>
#include <geometry_msgs/Pose2D.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/PoseWithCovariance.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <geometry_msgs/TwistWithCovarianceStamped.h>
#include <geometry_msgs/TwistStamped.h>
#include <nav_msgs/Odometry.h>
#include <tf/LinearMath/Quaternion.h>
#include <tf/LinearMath/Matrix3x3.h>
#include <tf/transform_listener.h>
#include <tf/transform_broadcaster.h>
#include <tf/tf.h>
#include <cv_bridge/cv_bridge.h>
#include <opencv2/imgproc/imgproc.hpp>

#include "npu_common.h"
#include "npu_str_ext.h"
#include "npu_enum_ext.h"
#include "npu_std.h"
#include "npu_math.h"

namespace wizrobo { namespace ros_ext {

//#define NPU_DEBUG(fmt, arg...) ROS_DEBUG("[%s]::" fmt, GetNodeName().c_str(), ##arg)
//#define NPU_DEBUG_ONCE(fmt, arg...) ROS_DEBUG_ONCE("[%s]::" fmt, GetNodeName().c_str(), ##arg)

//#define NPU_INFO(fmt, arg...) ROS_INFO("[%s]::" fmt, GetNodeName().c_str(), ##arg)
//#define NPU_INFO_ONCE(fmt, arg...) ROS_INFO_ONCE("[%s]::" fmt, GetNodeName().c_str(), ##arg)

//#define NPU_WARN(fmt, arg...) ROS_WARN("[%s]::" fmt, GetNodeName().c_str(), ##arg)
//#define NPU_WARN_ONCE(fmt, arg...) ROS_WARN_ONCE("[%s]::" fmt, GetNodeName().c_str(), ##arg)

//#define NPU_ERROR(fmt, arg...) ROS_ERROR("[%s]::" fmt, GetNodeName().c_str(), ##arg)
//#define NPU_ERROR_ONCE(fmt, arg...) ROS_ERROR_ONCE("[%s]::" fmt, GetNodeName().c_str(), ##arg)

//#define NPU_FATAL(fmt, arg...) ROS_FATAL("[%s]::" fmt, GetNodeName().c_str(), ##arg)
//#define NPU_FATAL_ONCE(fmt, arg...) ROS_FATAL_ONCE("[%s]::" fmt, GetNodeName().c_str(), ##arg)

/// ros::Time -> str
std::string RosTimeToStr(const ros::Time& time);

//// eigen pose & twist -> odometry
//nav_msgs::Odometry CalculateOdom(const Eigen::Vector3f &pose_mean, const Eigen::Matrix3f &pose_cov
//                                 , const Eigen::Vector3f &twist_mean, const Eigen::Matrix3f &twist_cov
//                                 , std::string& map_frame, std::string& base_frame);

void PublishOdomTf(const nav_msgs::Odometry& odom
                   , tf::TransformListener& tf_sub, tf::TransformBroadcaster& tf_pub
                   , std::string& map_frame, std::string& odom_frame, std::string& base_frame
                   , bool pub_map_to_odom_tf, bool pub_odom_to_base_tf);

/// geometry_msgs est -> geoemtry_msgs twist
void CalGeoTwistEstByGeoPoseEst(const geometry_msgs::PoseWithCovarianceStamped& last_pose_est
                                , const geometry_msgs::TwistWithCovarianceStamped& last_twist_est
                                , const geometry_msgs::PoseWithCovarianceStamped& new_pose_est
                                , geometry_msgs::TwistWithCovarianceStamped& new_twist_est);

/// eigen est -> eigen twist
void CalEigTwistEstByEigEst(const ros::Time& last_time
                            , const Eigen::Vector3f& last_pose_mean, const Eigen::Matrix3f& last_pose_cov
                            , const Eigen::Vector3f& last_twist_mean, const Eigen::Matrix3f& last_twist_cov
                            , const ros::Time& new_time
                            , const Eigen::Vector3f& new_pose_mean, const Eigen::Matrix3f& new_pose_cov
                            , Eigen::Vector3f& new_twist_mean, Eigen::Matrix3f& new_twist_cov);
/// eigen est & twist -> geometry_msgs odom
//nav_msgs::Odometry FormOdomEstByEigEst

/// geometry_msgs <-> Eigen
Eigen::Vector3f GeoPose2EigVec(const geometry_msgs::Pose& geo_pose);
Eigen::Matrix3f GeoPoseCov2EigMat(const boost::array<double, 36>& geo_pose_cov);
Eigen::Matrix3f GeoTwistCov2EigMat(const boost::array<double, 36>& geo_twist_cov);

void SetGeoPoseCovByEigMat(boost::array<double, 36>& geo_pose_cov, const Eigen::Matrix3f& eig_mat);
void SetGeoTwistCovByEigMat(boost::array<double, 36>& geo_twist_cov, const Eigen::Matrix3f& eig_mat);
void SetGeoVecCovByEigMat(boost::array<double, 9>& geo_vec_cov, const Eigen::Matrix3f& eig_mat);

geometry_msgs::Vector3 EigVec2GeoVec(const Eigen::Vector3f& eig_vec);
geometry_msgs::Pose EigVec2GeoPose(const Eigen::Vector3f& eig_vec);
geometry_msgs::Twist EigVec2GeoTwist(const Eigen::Vector3f& eig_vec);

geometry_msgs::PoseWithCovariance EigEst2GeoPoseEst(const Eigen::Vector3f& eig_vec, const Eigen::Matrix3f& eig_cov);

/* This function is only useful to have the whole code work
 * with old rosbags that have trailing slashes for their frames
 */
inline std::string StripSlash(const std::string& in)
{
  std::string out = in;
  if ( ( !in.empty() ) && (in[0] == '/') )
    out.erase(0,1);
  return out;
}

/// geometry_msgs <-> tf
inline tf::Vector3 GeoPnt2TfVec(const geometry_msgs::Point& geo_pnt)
{
    return tf::Vector3(geo_pnt.x, geo_pnt.y, geo_pnt.z);
}
inline tf::Quaternion GeoQuat2TfQuat(const geometry_msgs::Quaternion& geo_quat)
{
    return tf::Quaternion(geo_quat.x, geo_quat.y, geo_quat.z, geo_quat.w);
}
inline tf::Transform GeoPose2TfTf(const geometry_msgs::Pose& geo_pose)
{
    return tf::Transform(GeoQuat2TfQuat(geo_pose.orientation), GeoPnt2TfVec(geo_pose.position));
}
geometry_msgs::Vector3 GeoTransform(const tf::Transform& tf, const geometry_msgs::Vector3& p_in);

geometry_msgs::TransformStamped TfTf2GeoTf(const tf::StampedTransform& tf_tf);

inline geometry_msgs::Quaternion TfQuat2GeoQuat(const tf::Quaternion& tf_quat)
{
    geometry_msgs::Quaternion geo_quat;
    geo_quat.x = tf_quat.x();
    geo_quat.y = tf_quat.y();
    geo_quat.z = tf_quat.z();
    geo_quat.w = tf_quat.w();
    return geo_quat;
}
inline geometry_msgs::Point TfVec2GeoPoint(const tf::Vector3& tf_vec)
{
    geometry_msgs::Point geo_pnt;
    geo_pnt.x = tf_vec.x();
    geo_pnt.y = tf_vec.y();
    geo_pnt.z = tf_vec.z();
    return geo_pnt;
}
inline geometry_msgs::Pose TfTf2GeoPose(const tf::Transform& tf_tf)
{
    geometry_msgs::Pose geo_pose;
    geo_pose.position = TfVec2GeoPoint(tf_tf.getOrigin());
    geo_pose.orientation = TfQuat2GeoQuat(tf_tf.getRotation());
    return geo_pose;
}
inline tf::Quaternion DiffTfQuat(const tf::Quaternion& q0, const tf::Quaternion& q1)
{
    return q0.inverse() * q1;
//    tf::Vector3 vec0 = tf::quatRotate(q0, tf::Vector3(1,1,1));
//    tf::Vector3 vec1 = tf::quatRotate(q1, tf::Vector3(1,1,1));
//    tf::Quaternion dquat = tf::shortestArcQuatNormalize2(vec0, vec1);
//    return dquat;
}
inline geometry_msgs::Quaternion DiffGeoQuat(const geometry_msgs::Quaternion& q0, const geometry_msgs::Quaternion& q1)
{
    return ros_ext::TfQuat2GeoQuat(DiffTfQuat(ros_ext::GeoQuat2TfQuat(q0), ros_ext::GeoQuat2TfQuat(q1)));
}
double GeoPntDistDiff(const geometry_msgs::Point &geo_pnt0, const geometry_msgs::Point &geo_pnt1);

double GeoQuatOriDiff(const geometry_msgs::Quaternion& geo_quat0, const geometry_msgs::Quaternion& geo_quat1);

/// rgba -> color
static inline std_msgs::ColorRGBA RGBA2StdColor(float r, float g, float b, float a)
{
    std_msgs::ColorRGBA std_clr;
    std_clr.r = r;
    std_clr.g = g;
    std_clr.b = b;
    std_clr.a = a;
    return std_clr;
}
static inline std_msgs::ColorRGBA RGB2StdColor(float r, float g, float b)
{
    std_msgs::ColorRGBA std_clr;
    std_clr.r = r;
    std_clr.g = g;
    std_clr.b = b;
    std_clr.a = 1.0;
    return std_clr;
}

/// xyz -> point
static inline geometry_msgs::Point XYZ2GeoPoint(double x, double y, double z)
{
    geometry_msgs::Point geo_pnt;
    geo_pnt.x = x;
    geo_pnt.y = y;
    geo_pnt.z = z;
    return geo_pnt;
}
static inline geometry_msgs::Point32 XYZ2GeoPoint32(double x, double y, double z)
{
    geometry_msgs::Point32 geo_pnt32;
    geo_pnt32.x = x;
    geo_pnt32.y = y;
    geo_pnt32.z = z;
    return geo_pnt32;
}
static inline geometry_msgs::Vector3 XYZ2GeoVec(double x, double y, double z)
{
    geometry_msgs::Vector3 geo_vec;
    geo_vec.x = x;
    geo_vec.y = y;
    geo_vec.z = z;
    return geo_vec;
}

//// tf quaternion <-> rpy
static inline tf::Quaternion TfVec2TfQuat(const tf::Vector3& tf_vec)
{
    tf::Vector3 ux(1,0,0);
    tf::Vector3 tf_vec_ = tf_vec;
    return tf::shortestArcQuatNormalize2(ux, tf_vec_);
}

static inline tf::Quaternion RPY2TfQuat(double roll, double pitch, double yaw)
{
    tf::Quaternion tf_quat;
    tf_quat.setRPY(roll, pitch, yaw);
    return tf_quat;
}
static inline void TfQuat2RPY(const tf::Quaternion& tf_quat, double& roll, double& pitch, double& yaw)
{
    tf::Matrix3x3(tf_quat).getRPY(roll, pitch, yaw);
}
static inline tf::Vector3 TfQuat2RPY(const tf::Quaternion& tf_quat)
{
    double roll, pitch, yaw;
    TfQuat2RPY(tf_quat, roll, pitch, yaw);
    return tf::Vector3(roll, pitch, yaw);
}
static inline tf::Quaternion Yaw2TfQuat(double yaw)
{
    tf::Quaternion tf_quat;
    tf_quat.setRPY(0.0, 0.0, yaw);
    return tf_quat;
}
static inline double TfQuat2Yaw(tf::Quaternion tf_quat)
{
    double yaw = tf::getYaw(tf_quat);
    return yaw;
}
static inline tf::Quaternion RotateTfQuat(const tf::Quaternion tf_quat, const tf::Vector3& axis, double angle)
{
    tf::Quaternion rlt_tf_quat = tf_quat;
    rlt_tf_quat.setRotation(axis, angle);
    return rlt_tf_quat;
}

//// geo quaternion <-> rpy
static inline geometry_msgs::Quaternion GeoVec2GeoQuat(const geometry_msgs::Vector3& geo_vec)
{
    tf::Vector3 tf_vec(geo_vec.x, geo_vec.y, geo_vec.z);
    return TfQuat2GeoQuat(TfVec2TfQuat(tf_vec));
}
static inline geometry_msgs::Quaternion RPY2GeoQuat(double roll, double pitch, double yaw)
{
    geometry_msgs::Quaternion geo_quat;
    tf::Quaternion tf_quat;
    tf_quat.setRPY(roll, pitch, yaw);
    geo_quat.x = tf_quat.x();
    geo_quat.y = tf_quat.y();
    geo_quat.z = tf_quat.z();
    geo_quat.w = tf_quat.w();
    return geo_quat;
}
static inline void GeoQuat2RPY(geometry_msgs::Quaternion geo_quat, double& roll, double& pitch, double& yaw)
{
    tf::Quaternion tf_quat(geo_quat.x, geo_quat.y, geo_quat.z, geo_quat.w);
    tf::Matrix3x3(tf_quat).getRPY(roll, pitch, yaw);
}
static inline geometry_msgs::Quaternion Yaw2GeoQuat(double yaw)
{
    geometry_msgs::Quaternion geo_quat;
    tf::Quaternion tf_quat;
    tf_quat.setRPY(0.0, 0.0, yaw);
    geo_quat.x = tf_quat.x();
    geo_quat.y = tf_quat.y();
    geo_quat.z = tf_quat.z();
    geo_quat.w = tf_quat.w();
    return geo_quat;
}
static inline double GeoQuat2Yaw(geometry_msgs::Quaternion geo_quat)
{
    tf::Quaternion tf_quat(geo_quat.x, geo_quat.y, geo_quat.z, geo_quat.w);
    double yaw = tf::getYaw(tf_quat);
    return yaw;
}
static inline geometry_msgs::Vector3 GeoQuat2RPY(geometry_msgs::Quaternion geo_quat)
{
    geometry_msgs::Vector3 geo_rpy;
    GeoQuat2RPY(geo_quat, geo_rpy.x, geo_rpy.y, geo_rpy.z);
    return geo_rpy;
}
static inline geometry_msgs::Quaternion RotateGeoQuat(const geometry_msgs::Quaternion geo_quat, const tf::Vector3& axis, double angle)
{
    tf::Quaternion tf_quat;
    tf::quaternionMsgToTF(geo_quat, tf_quat);
    tf_quat.setRotation(axis, angle);
    geometry_msgs::Quaternion rlt_geo_quat;
    tf::quaternionTFToMsg(tf_quat, rlt_geo_quat);
    return rlt_geo_quat;
}

/// zero point & pose
static inline geometry_msgs::Point ZeroPoint()
{
    return XYZ2GeoPoint(0,0,0);
}
static inline geometry_msgs::Pose ZeroPose()
{
    geometry_msgs::Pose geo_pose;
    geo_pose.position = XYZ2GeoPoint(0,0,0);
    geo_pose.orientation = RPY2GeoQuat(0,0,0);
    return geo_pose;
}
static inline geometry_msgs::Pose2D ZeroPose2D()
{
    geometry_msgs::Pose2D geo_pose2d;
    geo_pose2d.x = 0;
    geo_pose2d.y = 0;
    geo_pose2d.theta = 0;
    return geo_pose2d;
}

geometry_msgs::Quaternion GeoQuatWeightedSum(double w1, const geometry_msgs::Quaternion& q1
                                             , double w2, const geometry_msgs::Quaternion& q2);

//// rosparam extension
template<typename EnumType>
inline void GetEnumParam(const ros::NodeHandle& nh, const std::string& param_name, EnumType& param_val, const EnumType& default_val)
{
    std::string default_val_str;
    bool rlt = enum_ext::EnumString<EnumType>::TryEnumToStr(default_val, default_val_str);
    ROS_ASSERT_MSG(rlt, "Failed to parse rosparam \"%s\": Illegal default_val: %d", param_name.c_str(), static_cast<int>(default_val));

    std::string val_str;
    nh.param<std::string>(param_name, val_str, default_val_str);

    rlt = enum_ext::EnumString<EnumType>::TryStrToEnum(val_str, param_val);
    if (!rlt)
    {
        ROS_WARN("Failed to parsing rosparam \"%s\": Illegal val_str: \"%s\". Apply default value.", param_name.c_str(), val_str.c_str());
        param_val = default_val;
    }
}
inline void GetFloatParam(const ros::NodeHandle& nh, const std::string& param_name, float& param_val, const float& default_val)
{
    double double_val;
    double double_default_val = static_cast<double>(default_val);
    nh.param<double>(param_name, double_val, double_default_val);
    param_val = double_val;
}
void GetFloatVecParam(const ros::NodeHandle& nh, const std::string& param_name, std::vector<float>& param_vec
                             , int default_size, float default_elm_val);
inline std::string FloatVecParamToStr(const std::vector<float>& float_vec)
{
    return "[" + str_ext::FloatVecToStr(float_vec, ", ") + "]";
}
void GetFloatMatParam(const ros::NodeHandle& nh, const std::string& param_name, std::vector<std::vector<float> >& param_mat
                             , int default_size, const std::vector<float>& default_elm_vec);
inline std::string FloatMatParamToStr(const std::vector<std::vector<float> >& float_mat)
{
    return "[" + str_ext::FloatMatToStr(float_mat, ", ", "; ") + "]";
}
void GetFootprintParam(const ros::NodeHandle& nh, const std::string& param_name, geometry_msgs::Polygon& footprint);

std::string FootprintParamToStr(geometry_msgs::Polygon& footprint);

//// map extension
static inline void NavMapToCvMat(const nav_msgs::OccupancyGrid map, cv::Mat& mat)
{
    cv::Mat(map.info.height, map.info.width, CV_8U, (char*)&(map.data[0])).copyTo(mat);
}
static inline void CvMatToNavMap(const cv::Mat& mat, nav_msgs::OccupancyGrid& map)
{
    if (map.data.size() != mat.rows * mat.cols)
    {
        map.data.resize(mat.rows * mat.cols);
    }
    memcpy(&(map.data[0]), mat.data, sizeof(char) * mat.rows * mat.cols);
}
static inline void CvMatToDispImg(const cv::Mat& mat, sensor_msgs::Image& img)
{
    cv::Mat disp_mat;
    cv::resize(mat, disp_mat, cv::Size(mat.size().width/2, mat.size().height/2));
    cv::bitwise_xor(mat, cv::Scalar(255), disp_mat);
    cv::flip(disp_mat, disp_mat, 0);
    cv_bridge::CvImage disp_cvi;
    disp_cvi.header.stamp = ros::Time::now();
    disp_cvi.header.frame_id = "map_frame";
    disp_cvi.encoding = sensor_msgs::image_encodings::MONO8;
    disp_cvi.image = disp_mat;
    disp_cvi.toImageMsg(img);
}
static bool ExpandNavMap(const int& dst_xmin, const int& dst_ymin, const int& dst_xmax, const int& dst_ymax
                           , const int& min_exp_size, nav_msgs::OccupancyGrid& map);

bool IsQuaternionValid(const geometry_msgs::Quaternion& q);

//// logger level
enum LoggerLevel
{
    DEBUG = ros::console::levels::Debug,
    INFO = ros::console::levels::Info,
    WARN = ros::console::levels::Warn,
    ERROR = ros::console::levels::Error,
    FATAL = ros::console::levels::Fatal
};
inline void SetLoggerLevel(const LoggerLevel level)
{
    ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, static_cast<ros::console::levels::Level>(level));
}
/// footprint

//// topic sync callback
enum TopicSyncMode
{
    NON_SYNC,
    EXACT_SYNC,
    APPRX_SYNC,
};

//// node name
inline std::string GetNodeName()
{
    return ros::NodeHandle("~").getNamespace();
}
}}// namespace

namespace wizrobo { namespace enum_ext {
using namespace wizrobo::ros_ext;

// String support for ChassisType
BEGIN_ENUM_STRING(LoggerLevel)
{
    ENUM_STRING(DEBUG);
    ENUM_STRING(INFO);
    ENUM_STRING(WARN);
    ENUM_STRING(ERROR);
    ENUM_STRING(FATAL);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(TopicSyncMode)
{
    ENUM_STRING(NON_SYNC);
    ENUM_STRING(EXACT_SYNC);
    ENUM_STRING(APPRX_SYNC);
}
END_ENUM_STRING;

}}// namespace

#endif// WIZROBO_NPU_ROS_EXT_H
