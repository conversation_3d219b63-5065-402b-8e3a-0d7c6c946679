/**************************************************************************
** WizRobo NPU (Navigation Processing Unit) SDK
**	Version: Hellokitty 1.0
**	Date: 2015.10 - 2017.05
**	Creator: <EMAIL>
**	Copyright: Shenzhen Linkmiao Robotics Co., Ltd. All rights reserved.
** Supported OS: WINXP, WIN7, UBUNTU
** Supported Architecture: Win32, MFC, Linux API
** Code Style: Google-like
**************************************************************************/
#ifndef WIZROBO_NPU_H
#define WIZROBO_NPU_H

#include "npu_common.h"
#include "npu_math.h"
#include "npu_str_ext.h"
#include "npu_enum_ext.h"
#include "npu_ros_ext.h"
#include "npu_robot_model.h"
#include "npu_sensor.h"
#include "npu_std.h"
#include "npu_log.h"
#include "npu_curvature.h"
#include "npu_douglas_peucker.h"

namespace wizrobo {
using namespace str_ext;
using namespace enum_ext;
using namespace ros_ext;
}// namespace
#endif// WIZROBO_NPU_H
