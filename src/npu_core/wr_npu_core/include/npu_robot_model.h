#ifndef WIZROBO_NPU_ROBOT_MODEL_H
#define WIZROBO_NPU_ROBOT_MODEL_H

#include "npu_common.h"

namespace wizrobo { namespace robot_model {

//inline float CalMaxLinSpdMps(float motor_max_spd_rpm, float motor_rdc_ratio, float wheel_rdc_ratio, float wheel_radius_m)
//{
//    return fabs(0.8 * motor_max_spd_rpm / 60 / motor_rdc_ratio
//                / wheel_rdc_ratio * 2 * M_PI * wheel_radius_m);
//}
//inline float CalMaxAngSpdRps(float max_lin_spd_mps, float wheel_span_m)
//{
//    return max_lin_spd_mps / M_PI / wheel_span_m;
//}
//inline void LimiteSpd(float max_move_spd_mps, float wheel_span_m, float& lin_spd, float& ang_spd)
//{
//    ang_spd = RANGE(ang_spd, -max_move_spd_mps / (M_PI * wheel_span_m)
//                        , max_move_spd_mps / (M_PI * wheel_span_m));
//    lin_spd = RANGE(lin_spd, -max_move_spd_mps + fabs(ang_spd * wheel_span_m / 2)
//                    , max_move_spd_mps - fabs(ang_spd * wheel_span_m / 2));
//}
}}// namespace
#endif // WIZROBO_NPU_ROBOT_MODEL_H
