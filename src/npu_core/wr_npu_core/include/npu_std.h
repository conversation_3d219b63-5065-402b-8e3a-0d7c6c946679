#ifndef WIZROBO_NPU_STD_H
#define WIZROBO_NPU_STD_H

#include <string>

#include "npu_common.h"


namespace wizrobo {
/// standard topic names
static const std::string STD_SCAN_TOPIC_NAME = "/scan";
static const std::string STD_ODOM_TOPIC_NAME = "/odom";
static const std::string STD_ENCODER_TOPIC_NAME = "/motor_enc";
static const std::string STD_WHEEL_ODOM_TOPIC_NAME = "/wo_odom";
static const std::string STD_VISUAL_ODOM_TOPIC_NAME = "/vo_odom";
static const std::string STD_LIDAR_ODOM_TOPIC_NAME = "/lo_odom";
static const std::string STD_SLAM_ODOM_TOPIC_NAME = "/slam_odom";
static const std::string STD_AMCL_ODOM_TOPIC_NAME = "/amcl_odom";
static const std::string STD_IMU_TOPIC_NAME = "/imu";
static const std::string STD_IMU_HZT_TOPIC_NAME = "/imu_hzt";
static const std::string STD_ARTAG_TOPIC_NAME = "/artag";
static const std::string STD_GPS_TOPIC_NAME = "/gps";
static const std::string STD_SONAR_TOPIC_NAME = "/sonar_scan";
static const std::string STD_ESB_TOPIC_NAME = "/emergency_stop_status";
static const std::string STD_INFRD_TOPIC_NAME = "/infrd";
static const std::string STD_BUMPER_TOPIC_NAME = "/bumper";

static const std::string STD_RPY_TOPIC_NAME = "/rpy";
static const std::string STD_TF_STATIC_TOPIC_NAME = "/tf_static";

static const std::string STD_MAP_TOPIC_NAME = "/map";
static const std::string STD_VIRTUALWALL_TOPIC_NAME = "/virtualwall";
static const std::string STD_FOOTPRINT_TOPIC_NAME = "/footprint";
static const std::string STD_ACT_POSE_TOPIC_NAME = "/wizrobo/pose";
static const std::string STD_CMD_POSE_TOPIC_NAME = "/navi_core_simple/goal";
static const std::string STD_ACT_PATH_TOPIC_NAME = "/act_path";
static const std::string STD_CMD_PATH_TOPIC_NAME = "/cmd_path";

static const std::string STD_TELEOP_CMD_VEL_TOPIC_NAME = "/manual_cmd_vel";
static const std::string STD_AUTO_CMD_VEL_TOPIC_NAME = "/auto_cmd_vel";

static const std::string STD_INITIAL_POSE_TOPIC_NAME = "/initial_pose";
static const std::string STD_INITIAL_POSE_EST_TOPIC_NAME = "/initialpose";

static const std::string STD_SLAM_POSE_TOPIC_NAME = "/slam_pose";
static const std::string STD_SLAM_POSE_EST_TOPIC_NAME = "/slam_pose_est";
static const std::string STD_SLAM_TRAJ_TOPIC_NAME = "/slam_traj";

static const std::string STD_POSE_UPDATE_TOPIC_NAME = "/pose_update";
static const std::string STD_TWIST_UPDATE_TOPIC_NAME = "/twist_update";

/// standard frame ids
static const std::string STD_IMU_FRAME_ID = "imu_frame";
static const std::string STD_LIDAR_FRAME_ID = "lidar_frame";
static const std::string STD_BASE_FTP_FRAME_ID = "base_ftp_frame";
static const std::string STD_BASE_HZT_FRAME_ID = "base_hzt_frame";
static const std::string STD_BASE_FRAME_ID = "base_frame";
static const std::string STD_ODOM_FRAME_ID = "odom_frame";
static const std::string STD_MAP_FRAME_ID = "map_frame";
static const std::string STD_LO_BASE_FRAME_ID = "lo_base_frame";

/// standard param ns
static const std::string STD_PARAM_ROOT_NS = "/npu_param";
// runtime
static const std::string STD_RUNTIME_PARAM_NS = (STD_PARAM_ROOT_NS + "/runtime");
// core
static const std::string STD_CORE_PARAM_NS = (STD_PARAM_ROOT_NS + "/core");
// base
static const std::string STD_BASE_PARAM_NS = (STD_PARAM_ROOT_NS + "/base");
static const std::string STD_MOTOR_PARAM_NS = (STD_BASE_PARAM_NS + "/motor");
static const std::string STD_PID_PARAM_NS = (STD_BASE_PARAM_NS + "/pid");
static const std::string STD_CHASSIS_PARAM_NS = (STD_BASE_PARAM_NS + "/chassis");
static const std::string STD_FOOTPRINT_PARAM_NS = (STD_BASE_PARAM_NS + "/footprint");
// sensor
static const std::string STD_SENSOR_PARAM_NS = (STD_PARAM_ROOT_NS + "/sensor");
// teleop
static const std::string STD_TELEOP_PARAM_NS = (STD_PARAM_ROOT_NS + "/teleop");
// navi
static const std::string STD_NAVI_PARAM_NS = (STD_PARAM_ROOT_NS + "/navi");
static const std::string STD_NAVI_P2P_PLANNER_PARAM_NS = (STD_NAVI_PARAM_NS + "/p2p_planner");
static const std::string STD_NAVI_PF_PLANNER_PARAM_NS = (STD_NAVI_PARAM_NS + "/pf_planner");
static const std::string STD_NAVI_CCP_PLANNER_PARAM_NS = (STD_NAVI_PARAM_NS + "/ccp_planner");
// navi_core
//static const std::string STD_NAVI_CORE_PARAM_NS = (STD_PARAM_ROOT_NS + "/navi_core");
static const std::string STD_NAVI_CORE_PARAM_NS = ("/navi_core");
//path planner
static const std::string STD_LOCAL_PLANNER_PARAM_NS = (STD_NAVI_CORE_PARAM_NS + "/PurePlannerROS");
static const std::string STD_GLOBAL_PLANNER_PARAM_NS = (STD_NAVI_CORE_PARAM_NS + "/StdGlobalPlanner");
// slam
static const std::string STD_SLAM_PARAM_NS = (STD_PARAM_ROOT_NS + "/slam");
static const std::string STD_SLAM_OPTIMIZER_PARAM_NS = (STD_SLAM_PARAM_NS + "/optimizer");
//lidar filters 
static const std::string STD_LIDAR_FILTERS_PARAM_NS = (STD_PARAM_ROOT_NS + "/lidar_filters");
// bag
static const std::string STD_BAG_PARAM_NS = (STD_PARAM_ROOT_NS + "/bag");

/// std plugins
static const std::string STD_GLOBAL_PLANNER_TYPE = "wr_std_global_planner/StdGlobalPlanner";
static const std::string STD_LOCAL_PLANNER_TYPE = "wr_dwa_local_planner/DwaLocalPlanner";

/// std constants
static const float KNOWN_VAR_VAL = 0.01f;
static const float HALF_KNOWN_VAR_VAL = 0.09f;
static const int UNKNOWN_VAR_VAL = 999999;

}// namespace
#endif // WIZROBO_NPU_STD_H
