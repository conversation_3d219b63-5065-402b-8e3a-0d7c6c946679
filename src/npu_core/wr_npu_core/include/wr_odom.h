#ifndef WIZROBO_WR_ODOM_H
#define WIZROBO_WR_ODOM_H

#include "npu_common.h"
#include "npu_str_ext.h"
#include "npu_std.h"
#include "npu_math.h"
#include "npu_ros_ext.h"

namespace wizrobo {
struct WrPoseEst
{
    Eigen::Vector3f mean;
    Eigen::Matrix3f cov;

    WrPoseEst()
    {
        Reset();
    }
    WrPoseEst(double x, double y, double yaw)
    {
        mean(0) = x;
        mean(1) = y;
        mean(2) = yaw;
        cov = Eigen::Matrix3f::Ones() * UNKNOWN_VAR_VAL;
    }
    void Reset()
    {
        mean = Eigen::Vector3f::Zero();
        cov = Eigen::Matrix3f::Ones() * UNKNOWN_VAR_VAL;
    }

    geometry_msgs::PoseWithCovarianceStamped ToGeoPoseEst(const std::string& global_frame, const ros::Time& time)
    {
        geometry_msgs::PoseWithCovarianceStamped geo_pose_est;
        geo_pose_est.header.stamp = time;
        geo_pose_est.header.frame_id = global_frame;
        geo_pose_est.pose.pose = ros_ext::EigVec2GeoPose(mean);
        ros_ext::SetGeoPoseCovByEigMat(geo_pose_est.pose.covariance, cov);
        return geo_pose_est;
    }
    geometry_msgs::PoseStamped ToGeoPose(const std::string& global_frame, const ros::Time& time)
    {
        geometry_msgs::PoseStamped geo_pose;
        geo_pose.header.stamp = time;
        geo_pose.header.frame_id = global_frame;
        geo_pose.pose = ros_ext::EigVec2GeoPose(mean);
        return geo_pose;
    }
};
struct WrTwistEst
{
    Eigen::Vector3f mean;
    Eigen::Matrix3f cov;

    WrTwistEst()
    {
        Reset();
    }
    WrTwistEst(double x, double y, double yaw)
    {
        mean(0) = x;
        mean(1) = y;
        mean(2) = yaw;
        cov = Eigen::Matrix3f::Ones() * UNKNOWN_VAR_VAL;
    }
    void Reset()
    {
        mean = Eigen::Vector3f::Zero();
        cov = Eigen::Matrix3f::Ones() * UNKNOWN_VAR_VAL;
    }
    geometry_msgs::TwistWithCovarianceStamped ToGeoTwistEst(const std::string& global_frame, const ros::Time& time)
    {
        geometry_msgs::TwistWithCovarianceStamped geo_twist_est;
        geo_twist_est.header.stamp = time;
        geo_twist_est.header.frame_id = global_frame;
        geo_twist_est.twist.twist = ros_ext::EigVec2GeoTwist(mean);
        ros_ext::SetGeoTwistCovByEigMat(geo_twist_est.twist.covariance, cov);
        return geo_twist_est;
    }
    geometry_msgs::TwistStamped ToGeoTwist(const std::string& global_frame, const ros::Time& time)
    {
        geometry_msgs::TwistStamped geo_twist;
        geo_twist.header.stamp = time;
        geo_twist.header.frame_id = global_frame;
        geo_twist.twist = ros_ext::EigVec2GeoTwist(mean);
        return geo_twist;
    }
};

struct WrOdom
{
    ros::Time time;
    WrPoseEst pose_est;
    WrTwistEst twist_est;

    WrOdom()
    {
        time = ros::Time::now();
    }
    void Reset()
    {
        time = ros::Time::now();
        pose_est.Reset();
        twist_est.Reset();
    }
    void Update(const ros::Time& new_time, const WrPoseEst& new_pose_est)
    {
        WrTwistEst new_twist_est;
        ros_ext::CalEigTwistEstByEigEst(this->time
                                        , this->pose_est.mean, this->pose_est.cov
                                        , this->twist_est.mean, this->twist_est.cov
                                        , new_time
                                        , new_pose_est.mean, new_pose_est.cov
                                        , new_twist_est.mean, new_twist_est.cov);
        this->pose_est = new_pose_est;
        this->twist_est = new_twist_est;
    }
    nav_msgs::Odometry ToGeoOdom(const std::string& global_frame, const std::string& base_frame, const ros::Time& time)
    {
        nav_msgs::Odometry geo_odom;
        geo_odom.header.stamp = time;
        geo_odom.header.frame_id = global_frame;
        geo_odom.child_frame_id = base_frame;
        // odom.pose
        geo_odom.pose.pose = ros_ext::EigVec2GeoPose(pose_est.mean);
        ros_ext::SetGeoPoseCovByEigMat(geo_odom.pose.covariance, pose_est.cov);
        // odom.twist
        geo_odom.twist.twist = ros_ext::EigVec2GeoTwist(twist_est.mean);
        ros_ext::SetGeoTwistCovByEigMat(geo_odom.twist.covariance, twist_est.cov);
        return geo_odom;
    }
};
}// namespace
#endif // WIZROBO_WR_ODOM_H
