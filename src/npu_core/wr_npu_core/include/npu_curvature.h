#ifndef CURVATURE_H
#define CURVATURE_H

#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Pose2D.h>

namespace wizrobo{

void PathDiff1(const std::vector<geometry_msgs::PoseStamped>& path, std::vector<geometry_msgs::Pose2D>& path_diff1);
void PathDiff1(const std::vector<geometry_msgs::Pose2D>& path, std::vector<geometry_msgs::Pose2D>& path_diff1);
void PathDiff2(const std::vector<geometry_msgs::PoseStamped>& path, std::vector<geometry_msgs::Pose2D>& path_diff2);
void PathCurva(const std::vector<geometry_msgs::PoseStamped>& path, std::vector<geometry_msgs::Pose2D>& path_curva);
void PathDiffSum(const std::vector<geometry_msgs::PoseStamped>& path, float& x, float& y);

}//namespace


#endif // CURVATURE_H
