#ifndef WIZROBO_NPU_SENSOR_H
#define WIZROBO_NPU_SENSOR_H
#include "npu_common.h"
#include "npu_enum_ext.h"

namespace wizrobo {
/// officially supported sensors
enum LidarType {RPLIDAR, RSLIDAR, LSLIDAR, FSLIDAR,
                SICK_TIM551, SICK_TIM561, SICK_TIM571,
                SICK_LMS111, SICK_LMS141, SICK_LMS151, SICK_LMS511,
                SICK_S300,
                HOKUYO_UST10LX, HOKUYO_UTM30LX,
                PF_R2000_20HZ, PF_R2000_50HZ,
                VELODYNE_VLP16,
                XTI<PERSON>_LIDAR, SIM_LIDAR, GAZEBO, FKLIDAR};

enum ImuType {URANUS_IMU, JY61_IMU, SCC2000_IMU,
              SIM_IMU};
enum CameraType {Astra, REALSENSE_R200, PS3EYE_BIG_FOV, PS3EYE_SMALL_FOV,
              SIM_CAMERA};
enum GpsType {SIM_GPS};

}// namespace
namespace wizrobo { namespace enum_ext {
BEGIN_ENUM_STRING(LidarType)
{
    ENUM_STRING(RPLIDAR);
    ENUM_STRING(RSLIDAR);
    ENUM_STRING(LSLIDAR);
    ENUM_STRING(FSLIDAR);
    ENUM_STRING(SICK_TIM551);
    ENUM_STRING(SICK_TIM561);
    ENUM_STRING(SICK_TIM571);
    ENUM_STRING(SICK_LMS111);
    ENUM_STRING(SICK_LMS141);
    ENUM_STRING(SICK_LMS151);
    ENUM_STRING(SICK_LMS511);
    ENUM_STRING(SICK_S300);
    ENUM_STRING(HOKUYO_UST10LX);
    ENUM_STRING(HOKUYO_UTM30LX);
    ENUM_STRING(PF_R2000_20HZ);
    ENUM_STRING(PF_R2000_50HZ);
    ENUM_STRING(VELODYNE_VLP16);
    ENUM_STRING(XTION_LIDAR);
    ENUM_STRING(SIM_LIDAR);
    ENUM_STRING(GAZEBO);
    ENUM_STRING(FKLIDAR);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(ImuType)
{
    ENUM_STRING(URANUS_IMU);
    ENUM_STRING(JY61_IMU);
    ENUM_STRING(SCC2000_IMU);
    ENUM_STRING(SIM_IMU);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(CameraType)
{
    ENUM_STRING(REALSENSE_R200);
    ENUM_STRING(Astra);
    ENUM_STRING(PS3EYE_BIG_FOV);
    ENUM_STRING(PS3EYE_SMALL_FOV);
    ENUM_STRING(SIM_CAMERA);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(GpsType)
{
    ENUM_STRING(SIM_GPS);
}
END_ENUM_STRING;
}}// namespace
#endif // WIZROBO_NPU_SENSOR_H
