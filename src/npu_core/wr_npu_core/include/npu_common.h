/**************************************************************************
** WizRobo NPU (Navigation Processing Unit) SDK
**	Version: Hellokitty 1.0
**	Date: 2015.10 - 2017.05
**	Creator: <EMAIL>
**	Copyright: Shenzhen Linkmiao Robotics Co., Ltd. All rights reserved.
** Supported OS: WINXP, WIN7, UBUNTU
** Supported Architecture: Win32, MFC, Linux API
** Code Style: Google-like
**************************************************************************/
#ifndef WIZROBO_NPU_COMMON_H
#define WIZROBO_NPU_COMMON_H

//// definitions
// [1] Choose OS: WINNT or Ubuntu?
#define WR_WINNT 0
#define WR_UBUNTU 1
// [2] If on WINNT, choose architecture: WIN32 or MFC?
#define WR_WIN32 1
#define WR_MFC 0
// [3] If is debugging or not?
#define ENB_DEBUG 0
//// [4] Whethr use ros msg
#define USE_ROS_MSG 1
//// head files

//// logging
#if WR_WINNT
    #define WR_SLEEP_MS(t) Sleep(static_cast<DWORD>(t))
    #if WR_WIN32 || WR_MFC
        #if ENB_DEBUG
            #define WR_DEBUG(s,...) fprintf_s(stderr, "[WR_DEBUG] " s "\r\n", ##__VA_ARGS__)
        #else
            #define WR_DEBUG(s,...)
        #endif
        #define WR_INFO(s,...)   fprintf_s(stdout, "[ WR_INFO] " s "\r\n", ##__VA_ARGS__)
        #define WR_WARN(s,...)  fprintf_s(stdout, "[ WR_WARN] " s "\r\n", ##__VA_ARGS__)
        #define WR_ERROR(s,...)  fprintf_s(stderr, "[WR_ERROR] " s "\r\n", ##__VA_ARGS__)
        #define WR_ASSERT(val,...) if(!(val)){fprintf_s(stderr, "[WR_FATAL] " #val "\r\n", ##__VA_ARGS__); assert(val);}
    #endif
#elif WR_UBUNTU
    #define WR_SLEEP_MS(t) usleep((t) * 1000)
    #if USE_ROS_MSG
        #define WR_DEBUG ROS_DEBUG
        #define WR_DEBUG_ONCE ROS_DEBUG_ONCE
        #define WR_INFO ROS_INFO
        #define WR_WARN ROS_WARN
        #define WR_ERROR ROS_ERROR
        #define WR_ASSERT(val) if (!(val)){ROS_FATAL("Assertion failed: " #val); assert(val);}
    #else
        #if ENB_DEBUG
            #define WR_DEBUG(s,...) fprintf(stdout, "\033[32m[WR_DEBUG] " s "\r\n\033[0m", ##__VA_ARGS__)
        #else
            #define WR_DEBUG(s,...)
        #endif
        #define WR_DEBUG_ONCE(s,...) {static int _cnt = 0; if (cnt++ == 0){fprintf(stdout, "[ WR_DEBUG] " s "\r\n", ##__VA_ARGS__)}
        #define WR_INFO(s,...)  fprintf(stdout, "[ WR_INFO] " s "\r\n", ##__VA_ARGS__)
        #define WR_WARN(s,...)  fprintf(stdout, "\033[33m[ WR_WARN] " s "\r\n\033[0m", ##__VA_ARGS__)
        #define WR_ERROR(s,...) fprintf(stderr, "\033[31m[WR_ERROR] " s "\r\n\033[0m", ##__VA_ARGS__)
        #define WR_ASSERT(val) if(!(val)){fprintf(stderr,"\033[31m[WR_FATAL] Assertion failed: " #val "\r\n\033[0m"); assert(val);}
    #endif
#endif

typedef unsigned int uint;
typedef unsigned short ushort;
typedef unsigned char byte;
typedef unsigned char uchar;

#ifndef RELEASE_POINTER
#define RELEASE_POINTER(p) if(p){delete p; p=0;}
#endif
#ifndef SAFE_RELEASE
#define SAFE_RELEASE(p) if(p){delete p; p=0;}
#endif
//template<typename T>
//inline void RELEASE_POINTER(T* p)
//{
//    if (p)
//    {
//        delete p;
//        p = 0;
//    }
//}
//template<typename T>
//inline void SAFE_RELEASE(T* p)
//{
//    if (p)
//    {
//        delete p;
//        p = 0;
//    }
//}
#endif// WIZROBO_NPU_COMMON_H
