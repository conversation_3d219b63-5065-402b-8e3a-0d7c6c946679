#ifndef _SCRUBBER_STRUCT_H_
#define _SCRUBBER_STRUCT_H_

#include "npu_enum_ext.h"
#include <vector>
#include <sensor_msgs/BatteryState.h>

namespace wizrobo_scrubber {

const std::string SCRB_NPU_STATUS_TOPIC           = "/npu_status";
const std::string SCRB_BATTERY_STATUS_TOPIC       = "/scrubber_battery_status";
const std::string SCRB_SWITCH_STATUS_TOPIC        = "/scrubber_switch_status";
const std::string SCRB_INNER_TMP_DATA_TOPIC       = "/scrubber_inner_tmp_data";
const std::string SCRB_INNER_HMDT_DATA_TOPIC      = "/scrubber_inner_hmdt_data";
const std::string SCRB_WORKING_STATUS_TOPIC       = "/scrubber_working_status";
const std::string SCRB_OPT_TOPIC                  = "/scrubber_opt";
const std::string SCRB_STR_OPT_TOPIC              = "/scrubber_str_opt";
const std::string SCRB_SYSTEM_ERROR_TOPIC         = "/scrubber_system_status";
const std::string SCRB_SYSTEM_STATUS_TOPIC        = "/scrubber_system_status";
const std::string SCRB_MOTOR_STATE_TOPIC          = "/scrubber_motor_state";
const std::string SCRB_MOTOR_STATUS_TOPIC         = "/scrubber_motor_status";
const std::string SCRB_WATER_VALVE_STATUS_TOPIC   = "/scrubber_water_valve_status";
const std::string SCRB_WATER_LEVEL_TOPIC          = "/scrubber_water_level";
const std::string SCRB_WATER_LEVEL_TOPIC_ASTA     = "/scrubber_water_level_asta";
const std::string SCRB_USAGE_LEVEL_TOPIC          = "/scrubber_usage_time";
const std::string SCRB_CLEAR_WATER_LEVEL_TOPIC    = "/scrubber_clear_water_level";
const std::string SCRB_DIRTY_WATER_LEVEL_TOPIC    = "/scrubber_dirty_water_level";
const std::string SCRB_SECURITY_STATUS_TOPIC      = "/scrubber_security_status";
const std::string SCRB_RESTORE_STATUS_TOPIC       = "/obstacle_stop_restore";



const std::string SCRB_EMERG_STOP_TOPIC      = "/scrubber_emergency_stop";
const std::string SCRB_EXEC_CTRL_TOPIC       = "/scrubber_exec_ctrl";
const std::string SCRB_MISSION_REPORT_TOPIC  = "/scrubber_mission_report";

const std::string SCRB_SERVICE_NAME          = "scrubber_service";
const std::string SCRB_LIFECLE_SERVICE_NAME  = "scrubber_mission_reports_service";
const std::string SCRB_SET_MISSION_URL       = "/scrubber_set_mission";
const std::string SCRB_GET_REPORT            = SCRB_LIFECLE_SERVICE_NAME;

const std::string SCRB_CHARGER_PTN           = "WR_CHARGER_\\d{3}";

const std::string SCRB_PARAM_PATH            = "/scrubber_param";
const std::string SCRB_MISSION_RECORD_DIR    = "/npu.x86_64/mission_record";
const std::string SCRB_MISSION_RECORD_FILE   = SCRB_MISSION_RECORD_DIR + "/mission_database.db";

const int SCRB_TYPE_UPDATE = 0;
const int SCRB_TYPE_BATTERY = 1;
const int SCRB_TYPE_WATER_TANK = 2;
const int SCRB_TYPE_FAN_MOTOR = 3;
const int SCRB_TYPE_BRUSH_MOTOR = 4;
const int SCRB_TYPE_LIFT_MOTOR = 5;
const int SCRB_TYPE_EMERGENCY_SWITCH = 6;
const int SCRB_TYPE_POWER_SWITCH = 7;
const int SCRB_TYPE_BREATH_LIGHT = 8;
const int SCRB_TYPE_WATER_VALVE = 9;
const int SCRB_TYPE_TEMP_HUMI = 10;
const int SCRB_TYPE_PARAM = 11;

enum TankLevel {
    EMPTY,
    HALF,
    FULL
};

enum SpeedLevel {
    STOP = 0u,
    LOW = 1u,
    MEDIUM = 2u,
    HIGH = 3u,
    MAXSPD = 4u
};

enum Switch {
    ON,
    OFF
};

enum LiftStatus {
    DOWN = 0u,
    UP = 1u
};

enum BreathLightStatus {
    BLN_WHITE_BREATHE    = 0u,
    BLN_ALL_BREATHE      = 1u,
    BLN_RED_BLINK_1HZ    = 2u,
    BLN_RED_BLINK_2HZ    = 3u,
    BLN_RED_ALWAYS_ON    = 4u,
    BLN_ALL_ALWAYS_ON    = 5u
};

enum BeepStatus {
    BEEP_CLOSE = 0u,
    BUZZING_1HZ = 1u,
    BUZZING_2HZ = 2u,
    BLEW_10S = 3u
};

enum SystemStatusReminder {
    BATTERY_NORMAL = 0,
    BATTERY_LOW_POWER = 1,
    BATTERY_TEMPERATURE_HIGH = 2,
    HIGH_WATER_LEVEL_OF_POLLUTED_WATER_TANK = 10,
    HIGH_WATER_LEVEL_OF_PURE_WATER_TANK = 20,
    LOW_WATER_LEVEL_OF_PURE_WATER_TANK = 21,
    TEMPERATURE_ABNORMAL = 100,
    HUMIDITY_ABNORMAL = 101,
    EMERGENCY_STOP_ABNORMAL = 200,
    BUMP_ABNORMAL = 201,
    ROLLING_BRUSH_ABNORMAL = 1000,
    BLOWER_ABNORMAL = 1001,
    BROOM_ABNORMAL = 1002,
    VALVE_ABNORMAL = 1003,
    WHEEL_HUB_MOTOR_ABNORMAL = 2000,
    LIFT_MOTOR_ABNORMAL = 2001,
    IMU_ABNORMAL = 3000,
    RADAR_ABNORMAL = 3001,
    DEPTH_CAMERA_ABNORMAL = 3002,
    ULTRASONIC_PARTS_ABNORMAL = 3003,
    INFRARED_PARTS_ABNORMAL = 3004,
	SLIPPING_ABNORMAL = 3005,
    ENWINDED_ABNORMAL = 3006,
	STOLEN_ABNORMAL = 9998,
    POSITIONING_ABNORMAL = 9999
};

typedef std::vector<SystemStatusReminder> SystemIndicators;

struct LifeTime {
    int lastest_start_time;  //unit : second
    int cumulative_using_time;    //unit : second
    int times;
    int lifetime;

    LifeTime()
        : lastest_start_time(0)
        , cumulative_using_time(0)
        , times(0)
        , lifetime(0)
    {}
};

struct Threshold {
    float level1;
    float level2;

    Threshold()
        : level1(0)
        , level2(0)
    {}
};

const int PANEL_MASK_ALL   = 0xFFFFFFFF;
const int PANEL_MASK_BL    = 0x00000001;
const int PANEL_MASK_LIFT  = 0x00000002;
const int PANEL_MASK_FAN   = 0x00000004;
const int PANEL_MASK_BRUSH = 0x00000008;
const int PANEL_MASK_VALVE = 0x00000010;

struct ScrbCtrlPanel
{
    BreathLightStatus bl_status;
    LiftStatus lift_status;
    SpeedLevel fan_lv;
    SpeedLevel brush_lv;
    SpeedLevel valve_lv;
};

struct MotorState
{
    std::string id;
    float rpm;
    float voltage;
    float current;
    float power;

    MotorState()
        : id("")
        , rpm(0.0)
        , voltage(0.0)
        , current(0.0)
        , power(0.0)
    {}
};

struct MotorParam
{
    Threshold current;
    Threshold voltage;
    Threshold rotation_spd;  //level 1 is work default speed, level 2 for rated speed
    LifeTime lifetime;

    MotorParam()
        : current()
        , voltage()
        , rotation_spd()
        , lifetime()
    {}
};

const int MOTOR_INDEX_FAN   = 0;
const int MOTOR_INDEX_BRUSH = 1;
const int MOTOR_INDEX_LIFT  = 2;
const int MOTOR_INDEX_WHEEL = 3;
const int MOTOR_CNT         = 4;

struct UserMotorState
{
    int abnormal_state;
    bool is_located;
    MotorState motor;
    SpeedLevel speed_level;
    LiftStatus lift_state;  //only using in lift motor

    UserMotorState()
        : abnormal_state(0)
        , is_located(true)
        , motor()
        , speed_level(STOP)
        , lift_state(UP)
    {}
};

struct ScrubberMotorState
{
    UserMotorState parts[MOTOR_CNT];
};

struct ScrubberMotorParam
{
    MotorParam params[MOTOR_CNT];
};

struct ScrubberReport
{
    SystemIndicators battery;
    SystemIndicators water_tank;
    SystemIndicators actuator;
    SystemIndicators sensor;

    ScrubberReport()
    {
        battery.clear();
        water_tank.clear();
        actuator.clear();
        sensor.clear();
    }
};

struct PartsUpdateInfo
{
    bool back_to_charge;  //active when battery level under the setting threshold
    bool change_new_battery;
    bool change_new_rolling_brush;
    bool change_new_valve;
    bool pwt_disinfected;  //pwt : polluted water tank
    bool change_new_broom;
    bool change_new_carbon_brush;
    bool change_new_filter;

    PartsUpdateInfo()
        : back_to_charge(false)
        , change_new_battery(false)
        , change_new_rolling_brush(false)
        , change_new_valve(false)
        , pwt_disinfected(false)
        , change_new_broom(false)
        , change_new_carbon_brush(false)
        , change_new_filter(false)
    {}
};

struct WaterValve
{
    SpeedLevel ratio;

    WaterValve()
        : ratio(STOP) 
    {}
};

struct WaterTankStatus
{
    TankLevel pure_water_tank_level;
    TankLevel polluted_water_tank_level;

    WaterTankStatus()
        : pure_water_tank_level(EMPTY)
        , polluted_water_tank_level(EMPTY) 
    {}
};

struct ScrubberPartsStatus
{
    WaterTankStatus water_tank;
    ScrubberMotorState motors;
    Switch emergency_stop_status;
    Switch emergency_stop_switch;
    Switch power_switch;
    BreathLightStatus breath_light;
    WaterValve valve;
    float inside_temperature;
    float inside_humidity;

    ScrubberPartsStatus()
        : water_tank()
        , motors()
        , emergency_stop_status(OFF)
        , emergency_stop_switch(OFF)
        , power_switch(OFF)
        , breath_light(BLN_WHITE_BREATHE)
        , valve()
        , inside_temperature(0.0)
        , inside_humidity(0.0)
    {}
};

struct ScrubberBatteryParam
{
    LifeTime battery;
    float battery_temperture_threshold;

    ScrubberBatteryParam()
        : battery()
        , battery_temperture_threshold(0.0)
    {}
};

struct Mixin
{
    LifeTime polluted_water_tank;// [day]
    LifeTime filter_lifetime;// [day]
    LifeTime water_valve_lifetime;
    float inside_temperature_threshold_cen;
    float inside_humidity_threshold;

    Mixin()
        : polluted_water_tank()
        , filter_lifetime()
        , water_valve_lifetime()
        , inside_temperature_threshold_cen(0.0)
        , inside_humidity_threshold(0.0)
    {}
};

struct ScrubberPartsParam
{
    ScrubberBatteryParam battery;
    ScrubberMotorParam motors;
    Mixin mix_param;

    ScrubberPartsParam()
        : battery()
        , motors()
        , mix_param()
    {}
};

struct ConsumablesEndurance
{
    LifeTime fan;
    LifeTime brush;
    LifeTime rake;
    LifeTime filter;
};

}

namespace wizrobo {
namespace enum_ext {

using namespace wizrobo_scrubber;

BEGIN_ENUM_STRING(TankLevel)
{
    ENUM_STRING(EMPTY);
    ENUM_STRING(HALF);
    ENUM_STRING(FULL);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(SpeedLevel)
{
    ENUM_STRING(STOP);
    ENUM_STRING(LOW);
    ENUM_STRING(MEDIUM);
    ENUM_STRING(HIGH);
    ENUM_STRING(MAXSPD);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(BeepStatus)
{
    ENUM_STRING(BEEP_CLOSE);
    ENUM_STRING(BUZZING_1HZ);
    ENUM_STRING(BUZZING_2HZ);
    ENUM_STRING(BLEW_10S);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(Switch)
{
    ENUM_STRING(ON);
    ENUM_STRING(OFF);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(LiftStatus)
{
    ENUM_STRING(UP);
    ENUM_STRING(DOWN);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(BreathLightStatus)
{
    ENUM_STRING(BLN_WHITE_BREATHE);
    ENUM_STRING(BLN_ALL_BREATHE);
    ENUM_STRING(BLN_RED_BLINK_1HZ);
    ENUM_STRING(BLN_RED_BLINK_2HZ);
    ENUM_STRING(BLN_RED_ALWAYS_ON);
    ENUM_STRING(BLN_ALL_ALWAYS_ON);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(SystemStatusReminder)
{
    ENUM_STRING(BATTERY_NORMAL);
    ENUM_STRING(BATTERY_LOW_POWER);
    ENUM_STRING(BATTERY_TEMPERATURE_HIGH);
    ENUM_STRING(HIGH_WATER_LEVEL_OF_POLLUTED_WATER_TANK);
    ENUM_STRING(HIGH_WATER_LEVEL_OF_PURE_WATER_TANK);
    ENUM_STRING(LOW_WATER_LEVEL_OF_PURE_WATER_TANK);
    ENUM_STRING(TEMPERATURE_ABNORMAL);
    ENUM_STRING(HUMIDITY_ABNORMAL);
    ENUM_STRING(EMERGENCY_STOP_ABNORMAL);
    ENUM_STRING(BUMP_ABNORMAL);
    ENUM_STRING(ROLLING_BRUSH_ABNORMAL);
    ENUM_STRING(BLOWER_ABNORMAL);
    ENUM_STRING(BROOM_ABNORMAL);
    ENUM_STRING(VALVE_ABNORMAL);
    ENUM_STRING(WHEEL_HUB_MOTOR_ABNORMAL);
    ENUM_STRING(LIFT_MOTOR_ABNORMAL);
    ENUM_STRING(IMU_ABNORMAL);
    ENUM_STRING(RADAR_ABNORMAL);
    ENUM_STRING(DEPTH_CAMERA_ABNORMAL);
    ENUM_STRING(ULTRASONIC_PARTS_ABNORMAL);
    ENUM_STRING(INFRARED_PARTS_ABNORMAL);
    ENUM_STRING(SLIPPING_ABNORMAL);
    ENUM_STRING(STOLEN_ABNORMAL);
    ENUM_STRING(POSITIONING_ABNORMAL);
}
END_ENUM_STRING;

} // namespace enum_ext
} // namespace wizrobo


#endif
