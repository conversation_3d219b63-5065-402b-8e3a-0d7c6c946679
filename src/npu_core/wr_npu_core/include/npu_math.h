#ifndef WIZROBO_NPU_MATH_H
#define WIZROBO_NPU_MATH_H

#include <cmath>
#include <vector>

#include <eigen3/Eigen/Core>
//#include "ceres/ceres.h"

#include <ros/console.h>
#include <geometry_msgs/Quaternion.h>
#include <tf/tf.h>

#include "npu_common.h"
#include "npu_ros_ext.h"

namespace wizrobo {

/* Common define functions
 */

#ifndef ABS
#define ABS(x) ((x)<(0)?-(x):(x))
#endif

#ifndef MAX
#define MAX(x,y) ((x)>=(y)?(x):(y))
#endif

#ifndef MIN
#define MIN(x,y) ((x)<=(y)?(x):(y))
#endif

#ifndef RANGE
#define RANGE(x,min_x,max_x) (MIN(MAX(x,min_x),max_x))
#endif

#ifndef SIGN
#define SIGN(x)   ((x)==0?0:((x)>0?1:(-1)))
#endif

#ifndef RAD2DEG
#define RAD2DEG(x) ((x)*180.0/M_PI)
#endif

#ifndef DEG2RAD
#define DEG2RAD(x) ((x)/180.0*M_PI)
#endif

#ifndef SQU
#define SQU(x) ((x)*(x))
#endif

#ifndef DIST
#define DIST(x0,y0,x1,y1) sqrt(SQU((x0)-(x1))+SQU((y0)-(y1)))
#endif

#ifndef M33_IDX
#define M33_IDX(i,j) (3*(i)+j)
#endif

#ifndef M66_IDX
#define M66_IDX(i,j) (6*(i)+j)
#endif

/**
  * @brief Clamps 'value' to be in the range ['min', 'max'].
  */
template <typename T>
T Clamp(const T value, const T min, const T max) {
  if (value > max) {
    return max;
  }
  if (value < min) {
    return min;
  }
  return value;
}

/**
  * @brief Calculates 'base'^'exponent'.
  */
template <typename T>
T Power(T base, int exponent) {
  return (exponent != 0) ? base * Power(base, exponent - 1) : T(1);
}

/**
  * @brief Calculates a^2.
  */
template <typename T>
T Pow2(T a) {
  return Power(a, 2);
}

/**
  * @brief Converts from degrees to radians.
  */
inline double DegToRad(double deg) { return M_PI * deg / 180.; }

/**
  * @brief Converts form radians to degrees.
  */
inline double RadToDeg(double rad) { return 180. * rad / M_PI; }

/**
  * @brief Bring the 'difference' between two angles into [-pi; pi].
  */
template <typename T>
T NormalizeAngleDifference(T difference) {
  const T kPi = T(M_PI);
  while (difference > kPi) difference -= 2. * kPi;
  while (difference < -kPi) difference += 2. * kPi;
  return difference;
}

template <typename T>
void QuaternionProduct(const double* const z, const T* const w,
                              T* const zw) {
  zw[0] = z[0] * w[0] - z[1] * w[1] - z[2] * w[2] - z[3] * w[3];
  zw[1] = z[0] * w[1] + z[1] * w[0] + z[2] * w[3] - z[3] * w[2];
  zw[2] = z[0] * w[2] - z[1] * w[3] + z[2] * w[0] + z[3] * w[1];
  zw[3] = z[0] * w[3] + z[1] * w[2] - z[2] * w[1] + z[3] * w[0];
}

/**
  * @brief ToDo
  */
inline bool IsEqual(double x, double y)
{
    if (fabs(x - y) < 1e-5) {
        return true;
    } else {
        return false;
    }
}

enum PoseFusionMode
{
    SELECTIVE_FUSION = 0u,
    WEIGHTING_FUSION = 1u,
    HYBRID_FUSION = 2u,
};
/**
  * @brief ToDo
  */
double NormRad(double rad);
/**
  * @brief ToDo
  */
double NormDeg(double deg);
/**
  * @brief ToDo
  */
float TrimFloat(const float& val, int precision);
/**
  * @brief ToDo
  */
double TrimDouble(const double& val, int precision);
/**
  * @brief ToDo
  */
bool HasNaN(const Eigen::Vector3f& vec);
/**
  * @brief ToDo
  */
bool HasNaN(const Eigen::Matrix3f& mat);
/**
  * @brief ToDo
  */
bool FusePoseEst(const Eigen::Vector3f& new_mean,
                 const Eigen::Matrix3f& new_cov
                 , const Eigen::Vector3f& cur_mean,
                 const Eigen::Matrix3f& cur_cov
                 , Eigen::Vector3f& fus_mean, Eigen::Matrix3f& fus_cov,
                 PoseFusionMode fus_mode, double new_est_scaling = 1.0);
/**
  * @brief ToDo
  */
bool WeightingFusePoseEst(const Eigen::Vector3f& new_mean,
                          const Eigen::Matrix3f& new_cov
                          , const Eigen::Vector3f& cur_mean,
                          const Eigen::Matrix3f& cur_cov
                          , Eigen::Vector3f& fus_mean,
                          Eigen::Matrix3f& fus_cov,
                          double new_est_scaling);
/**
  * @brief ToDo
  */
bool SelectiveFusePoseEst(const Eigen::Vector3f& new_mean,
                          const Eigen::Matrix3f& new_cov
                          , const Eigen::Vector3f& cur_mean,
                          const Eigen::Matrix3f& cur_cov
                          , Eigen::Vector3f& fus_mean, Eigen::Matrix3f& fus_cov,
                          double new_est_scaling);
/**
  * @brief This function is used to smooth the trajectory.
  * @param  path The path to be smoothed.
  */
std::vector<geometry_msgs::PoseStamped> SimplePathSmoothing(
        std::vector<geometry_msgs::PoseStamped>* path, int smoothing_samples);

void Cart2Sph(const Eigen::Vector2f& cart, Eigen::Vector2f& sph);

void GetIntersection(const Eigen::Vector2f& p1,
                     const Eigen::Vector2f& p2,
                     const Eigen::Vector2f& q1,
                     const Eigen::Vector2f& q2,
                     Eigen::Vector2f& cp);

/**
  * @brief ToDo
  * @param
  */
inline float NormalizeAngle(float val, float min, float max) {
    float norm = 0.0;
    if (val >= min)
        norm = min + fmod((val - min), (max-min));
    else
        norm = max - fmod((min - val), (max-min));

    return norm;
}

/**
  * @brief Calculate Euclidean distance between two 2D point datatypes
  * @param point1 object containing fields x and y
  * @param point2 object containing fields x and y
  * @return Euclidean distance: ||point2-point1||
  */
template <typename P1, typename P2>
inline double DistancePoints2d(const P1& point1, const P2& point2)
{
  return std::sqrt( std::pow(point2.x-point1.x,2) + std::pow(point2.y-point1.y,2) );
}

}// namespace wizrobo
namespace wizrobo {
namespace enum_ext {
using namespace wizrobo;
BEGIN_ENUM_STRING(PoseFusionMode)
{
    ENUM_STRING(SELECTIVE_FUSION);
    ENUM_STRING(WEIGHTING_FUSION);
    ENUM_STRING(HYBRID_FUSION);
}
END_ENUM_STRING;

} //namespace enum_ext
}// namespace wizrobo

#endif// WIZROBO_NPU_MATH_H
