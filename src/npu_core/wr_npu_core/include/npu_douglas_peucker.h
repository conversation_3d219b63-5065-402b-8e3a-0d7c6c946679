#ifndef _H_DOUGLAS_PEUCKER_
#define _H_DOUGLAS_PEUCKER_

#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Pose2D.h>
#include "npu_math.h"


typedef std::vector<geometry_msgs::PoseStamped> SMList;
typedef std::vector<geometry_msgs::PoseStamped>::iterator SMLIter;


namespace wizrobo {
using namespace std;

class <PERSON>Peucker {
private:
    struct Curve {
        Curve()
        {}
        Curve(SMLIter begin, SMLIter end, bool is_circled=false)
            : f(begin)
            , b(end)
        {}
        SMLIter f; // begin
        int f_index;
        SMLIter b; // end
        int b_index;
    };
private:
    double d_threshold_;
    SMList* ptr_curve_;
    bool is_circled_;

public:
    <PERSON><PERSON><PERSON><PERSON>( SMList* ptr, double d_threshold = 0.01, bool is_circled = false);
    int Decompose(SMList& decomposed_curve);
    int Decompose(SMList* curve_in, SMList &curve_out, int& count, std::vector<int>& index_list);
    //int FindClosePoint(int& index, const geometry_msgs::Pose2D& current_pose);

private:
    double CalculateDist2Segment(
            const geometry_msgs::PoseStamped& start_point, const geometry_msgs::PoseStamped& end_point,
            const geometry_msgs::Pose2D& current_pose, geometry_msgs::Pose2D& back_point);
    int Append(SMList& decomposed_curve1, SMList& decomposed_curve2, std::vector<int>& index_list1, std::vector<int>& index_list2);
    int DoDecompose(const Curve& curve_in, SMList& decomposed_curve, std::vector<int>& index_list);
    int FindPeek(const Curve& curve, SMLIter& peek, int& index) const;

    int Append(SMList& decomposed_curve1, SMList& decomposed_curve2);
    int DoDecompose(const Curve& curve_in, SMList& decomposed_curve);
    int FindPeek(const Curve& curve, SMLIter& peek) const;
};

}

#endif
