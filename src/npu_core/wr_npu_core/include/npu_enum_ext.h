//  Based on the work of
//  EnumString - A utility to provide stringizing support for C++ enums
//  Author: <PERSON>
//
//  This code is free software: you can do whatever you want with it,
//	although I would appreciate if you gave credit where it's due.
//
//  This code is distributed in the hope that it will be useful,
//  but WITHOUT ANY WARRANTY; without even the implied warranty of
//  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
/* Usage example

    // WeekEnd enumeration
    enum WeekEnd
    {
        Sunday = 1,
        Saturday = 7
    };

    // String support for WeekEnd
    Begin_Enum_String( WeekEnd )
    {
        Enum_String( Sunday );
        Enum_String( Saturday );
    }
    End_Enum_String;

    // Convert from WeekEnd to string
    const std::string &str = EnumString<WeekEnd>::From( Saturday );
    // str should now be "Saturday"

    // Convert from string to WeekEnd
    WeekEnd w;
    EnumString<WeekEnd>::To( w, "Sunday" );
    // w should now be Sunday
*/
#ifndef WIZROBO_NPU_ENUM_EXT_H
#define WIZROBO_NPU_ENUM_EXT_H
#include <string>
#include <map>
#include <algorithm>
#include <cassert>

#include "npu_common.h"

// Helper macros
#define BEGIN_ENUM_STRING(EnumerationName)                                      \
    template <> struct EnumString<EnumerationName> :                            \
        public EnumStringBase< EnumString<EnumerationName>, EnumerationName >   \
    {                                                                           \
        static void RegisterEnumerators()
//      {

#define ENUM_STRING(EnumeratorName)                                             \
            RegisterEnumerator( EnumeratorName, #EnumeratorName );
//      }

#define END_ENUM_STRING                                                         \
    }

namespace wizrobo {  namespace enum_ext {

class EnumExtException : public std::exception {
private:
    std::string error_msg;
public:
    EnumExtException(const std::string& msg) throw()
        : error_msg(msg)
    {
    }

    virtual ~EnumExtException() throw() {}

    const char* what() const throw()
    {
        return error_msg.c_str();
    }
};

// The EnumString base class
template <class DerivedType, class EnumType>
class EnumStringBase
{
// Types
protected:
    typedef std::map<std::string, EnumType> AssocMap;

protected:
// Constructor / Destructor
    explicit EnumStringBase();
    ~EnumStringBase();

private:
// Copy Constructor / Assignment Operator
    EnumStringBase(const EnumStringBase &);
    const EnumStringBase &operator =(const EnumStringBase &);

// Functions
private:
    static AssocMap &GetMap();

protected:
    // Use this helper function to register each enumerator
    // and its string representation.
    static void RegisterEnumerator(const EnumType e, const std::string &eStr);

public:
    static const bool TryEnumToStr(const EnumType e, std::string& str);
    static const bool TryStrToEnum(const std::string& str, EnumType& e);
    static const bool TryStrToEnum(const std::string& str, EnumType& e, EnumType default_e)
    {
        EnumType tmp_e;
        bool rlt = TryStrToEnum(str, tmp_e);
        if (rlt)
        {
            e = tmp_e;
        }
        else
        {
            e = default_e;
        }
        return rlt;
    }

    inline static const std::string EnumToStr(const EnumType e)
    {
        std::string str;
        bool rlt = TryEnumToStr(e, str);
        assert(rlt);
        return str;
    }
    inline static const std::string EnumToLowerStr(const EnumType e)
    {
        std::string str;
        bool rlt = TryEnumToStr(e, str);
        std::string lower_str = str;
        std::transform(str.begin(), str.end(), lower_str.begin(), ::tolower);
        assert(rlt);
        return lower_str;
    }
    static const EnumType StrToEnum(const std::string& str)
    {
        EnumType e;
        bool rlt = TryStrToEnum(str, e);
        //assert(rlt);
        if (!rlt) {
            throw EnumExtException("StrToEnum FAILED (\""+str+"\")");
        }
        return e;
    }
    static const EnumType StrToEnum(const std::string& str, EnumType default_e)
    {
        EnumType tmp_e;
        bool rlt = TryStrToEnum(str, tmp_e);
        if (rlt)
        {
            return tmp_e;
        }
        else
        {
            return default_e;
        }
    }

    static const bool IsEqual(const EnumType& e, const std::string& str)
    {
        EnumType str_e;
        bool rlt = TryStrToEnum(str, str_e);
        if (rlt)
        {
            return (str_e == e);
        }
        else
        {
            return false;
        }
    }
};

// The EnumString class
// Note: Specialize this class for each enumeration, and implement
//       the RegisterEnumerators() function.
template <class EnumType>
struct EnumString : public EnumStringBase< EnumString<EnumType>, EnumType >
{
    static void RegisterEnumerators();
};

// Function definitions
template <class D, class E>
typename EnumStringBase<D,E>::AssocMap &EnumStringBase<D,E>::GetMap()
{
    // A static map of associations from strings to enumerators
    static AssocMap assocMap;
    static bool     bFirstAccess = true;

    // If this is the first time we're accessing the map, then populate it.
    if( bFirstAccess )
    {
        bFirstAccess = false;
        D::RegisterEnumerators();
        assert( !assocMap.empty() );
    }

    return assocMap;
}

template <class D, class E>
void EnumStringBase<D,E>::RegisterEnumerator(const E e, const std::string &eStr)
{
    const bool bRegistered = GetMap().insert( typename AssocMap::value_type( eStr, e ) ).second;
    assert( bRegistered );
    (void)sizeof( bRegistered ); // This is to avoid the pesky 'unused variable' warning in Release Builds.
}

template <class D, class E>
const bool EnumStringBase<D,E>::TryEnumToStr(const E e, std::string& str)
{
    for(;;) // Code block
    {
        // Search for the enumerator in our map
        typename AssocMap::const_iterator i;
        for (i = GetMap().begin(); i != GetMap().end(); ++i)
        {
            if ((*i).second == e)
            {
                break;
            }
        }
        // If we didn't find it, we can't do this conversion
        if(i == GetMap().end())
        {
            break;
        }
        // Keep searching and see if we find another one with the same value
        typename AssocMap::const_iterator j(i);
        for(++j; j != GetMap().end(); ++j)
        {
            if((*j).second == e)
            {
                break;
            }
        }
        // If we found another one with the same value, we can't do this conversion
        if(j != GetMap().end())
        {
            break;
        }
        // We found exactly one string which matches the required enumerator
        str = (*i).first;
        return true;
    }
    // We couldn't do this conversion; return an empty string.
    str.clear();
    return false;
}

template <class D, class E>
const bool EnumStringBase<D,E>::TryStrToEnum(const std::string &str, E &e)
{
    // Search for the string in our map.
    std::string upper_str = str;
    std::transform(str.begin(), str.end(), upper_str.begin(), ::toupper);
    //WR_WARN("upper_str=%s", upper_str.c_str());
    const typename AssocMap::const_iterator itr(GetMap().find(upper_str));
    // If we have it, then return the associated enumerator.
    if (itr != GetMap().end())
    {
        e = (*itr).second;
        return true;
    }
    // We don't have it; the conversion failed.
    e = static_cast<E>(-1);
    return false;
}

}}// namespace
#endif// WIZROBO_NPU_ENUM_EXT_H
