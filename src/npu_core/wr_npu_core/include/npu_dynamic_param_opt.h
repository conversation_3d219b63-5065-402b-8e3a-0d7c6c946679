#ifndef _WR_DYBAMIC_PARAM_OPT_H_
#define _WR_DYBAMIC_PARAM_OPT_H_

#include <string>

#include <ros/ros.h>

#include <wr_dyparam/FeedBoolParam.h>
#include <wr_dyparam/FeedFloatParam.h>
#include <wr_dyparam/FeedIntParam.h>
#include <wr_dyparam/FeedStrParam.h>
#include <wr_dyparam/GetBoolParam.h>
#include <wr_dyparam/GetFloatParam.h>
#include <wr_dyparam/GetIntParam.h>
#include <wr_dyparam/GetStrParam.h>

namespace wizrobo_npu {

using namespace std;

typedef wr_dyparam::FeedBoolParam SrvFedBoolParam;
typedef wr_dyparam::FeedFloatParam SrvFedFloatParam;
typedef wr_dyparam::FeedIntParam SrvFedIntParam;
typedef wr_dyparam::FeedStrParam SrvFedStrParam;
typedef wr_dyparam::GetBoolParam SrvGetBoolParam;
typedef wr_dyparam::GetFloatParam SrvGetFloatParam;
typedef wr_dyparam::GetIntParam SrvGetIntParam;
typedef wr_dyparam::GetStrParam SrvGetStrParam;

class DyParamOpt {
private:
    template<typename T>
    bool _SetDyParam(const string& service_name, const string& key, typename T::RequestType::_value_type value);
    template<typename T>
    typename T::ResponseType::_value_type _GetDyParam(const string& service_name, const string& key);

public:
    bool SetDyBoolParam(char* key, bool value);
    bool SetDyFloatParam(char* key, float value);
    bool SetDyIntParam(char* key, int value);
    bool SetDyStrParam(char* key, const string& value);

    bool GetDyBoolParam(char* key);
    float GetDyFloatParam(char* key);
    int GetDyIntParam(char* key);
    string GetDyStrParam(char* key);

    bool SetDyBoolParam(const string& key, bool value);
    bool SetDyFloatParam(const string& key, float value);
    bool SetDyIntParam(const string& key, int value);
    bool SetDyStrParam(const string& key, const string& value);

    bool GetDyBoolParam(const string& key);
    float GetDyFloatParam(const string& key);
    int GetDyIntParam(const string& key);
    string GetDyStrParam(const string& key);
};

}

#endif
