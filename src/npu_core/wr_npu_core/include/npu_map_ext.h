#ifndef WIZROBO_MAP_RECTIFIER_H
#define WIZROBO_MAP_RECTIFIER_H

#include <nav_msgs/Path.h>
#include <geometry_msgs/Pose.h>
#include <geometry_msgs/Polygon.h>
#include <nav_msgs/OccupancyGrid.h>

#include "npu.h"
#include "npu_enum_ext.h"

namespace wizrobo {
enum MapFilterType
{
    MEDIAN_FILTER,
    GAUSSIAN_FILTER,
};
}// namespace

namespace wizrobo { namespace enum_ext {
using namespace wizrobo;
BEGIN_ENUM_STRING(MapFilterType)
{
    ENUM_STRING(MEDIAN_FILTER);
    ENUM_STRING(GAUSSIAN_FILTER);
}
END_ENUM_STRING;
}}// namespace

namespace wizrobo {
void FilterMap(MapFilterType filter_type, int filter_size, nav_msgs::OccupancyGrid& map);

void ClearTrace(const nav_msgs::Path& traj, const geometry_msgs::Polygon& footprint, nav_msgs::OccupancyGrid& map, bool enb_trj_intp = true);

void ClearFootprint(const geometry_msgs::Pose& pose, const geometry_msgs::Polygon& footprint, nav_msgs::OccupancyGrid& map);

void InterpolatePose(const geometry_msgs::Pose& p0, const geometry_msgs::Pose& p1, double dist_thrs_m, double ori_thrs_rad, std::vector<geometry_msgs::Pose>& p_list);
void InterpolatePoseStamped(const geometry_msgs::PoseStamped& ps0, const geometry_msgs::PoseStamped& ps1, double dist_thrs_m, double ori_thrs_rad, std::vector<geometry_msgs::PoseStamped>& ps_list);
void InterpolateTraj(const nav_msgs::Path& traj, double dist_thrs_m, double ori_thrs_rad, nav_msgs::Path& traj_intp);

}// namespace


#endif // WIZROBO_MAP_RECTIFIER_H
