<?xml version="1.0"?>
<package format="2">
  <name>wr_npu_core</name>
  <version>1.0.0</version>
  <description>The wr_npu_core package</description>

  <!-- One maintainer tag required, multiple allowed, one person per tag --> 
  <!-- Example:  -->
  <!-- <maintainer email="<EMAIL>"><PERSON></maintainer> -->
  <maintainer email="<EMAIL>">lhan</maintainer>


  <!-- One license tag required, multiple allowed, one license per tag -->
  <!-- Commonly used license strings: -->
  <!--   BSD, MIT, Boost Software License, GPLv2, GPLv3, LGPLv2.1, LGPLv3 -->
  <license>TODO</license>

  <buildtool_depend>catkin</buildtool_depend>
  <build_depend>google-mock</build_depend>
  <build_depend>g++-static</build_depend>
  <build_depend>protobuf-dev</build_depend>
  <build_depend>python-sphinx</build_depend>

  <depend>boost</depend>
  <depend>ceres-solver</depend>
  <depend>libgflags-dev</depend>
  <depend>libgoogle-glog-dev</depend>
  <depend>eigen</depend>
  <depend>rosconsole</depend>
  <depend>geometry_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>roscpp</depend>
  <depend>std_msgs</depend>
  <depend>tf</depend>

  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <!-- You can specify that this package is a metapackage here: -->
    <!-- <metapackage/> -->

    <!-- Other tools can request additional information be placed here -->

  </export>
</package>
