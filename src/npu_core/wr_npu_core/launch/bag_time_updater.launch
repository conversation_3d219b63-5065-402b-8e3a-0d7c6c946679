<?xml version="1.0"?>
<!-- -*- mode: XML -*- -->
<!-- v1.0 created by lawrence.han @2017-04-02 -->
<!-- v1.0.1 modified by lawrence.han @2017-04-03 -->
<launch>
  <!-- standard launch arg -->
  <arg name="logger_level" default="info"/>
  <!-- custom launch arg -->
  <arg name="bag_id"/>
  <arg name="bag_dir" default="/npu/bag/" />
  <arg name="imu_topic" default="/imu"/>
  <arg name="lidar_topic" default="/scan"/>
  <arg name="odom_topic" default="/odom"/>

  <node pkg="wr_npu_core" type="bag_time_updater_node" name="bag_time_updater_node" output="screen">
      <param name="logger_level" value="$(arg logger_level)"/>
      <param name="bag_id" value="$(arg bag_id)"/>
      <param name="bag_dir" value="$(arg bag_dir)"/>
      <param name="fix_scan_ang_max" value="true"/>
      <param name="imu_topic" value="$(arg imu_topic)"/>
      <param name="lidar_topic" value="$(arg lidar_topic)"/>
      <param name="odom_topic" value="$(arg odom_topic)"/>
  </node>

</launch>
