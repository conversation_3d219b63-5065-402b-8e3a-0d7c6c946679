#ifndef WIZROBO_NPU_ENUM_H
#define WIZROBO_NPU_ENUM_H

#include <npuice_api.h>
#include <npu_enum_ext.h>

namespace wizrobo { namespace enum_ext {
using namespace wizrobo_npu;
#define USE_NPU_API_V_0_9_12 false

//// api
BEGIN_ENUM_STRING(ServerState)
{
    ENUM_STRING(UNKNOWN);
    ENUM_STRING(CONNECTED);
    ENUM_STRING(TIMEOUT);
    ENUM_STRING(DISCONNECTED);
    ENUM_STRING(MEM_EXCEED);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(NpuState)
{
    ENUM_STRING(IDLE_STATE);
    ENUM_STRING(SLAM_STATE);
    ENUM_STRING(NAVI_STATE);
    ENUM_STRING(TELEOP_STATE);
    ENUM_STRING(SWITCH_STATE);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(ManualCmdType)
{
    ENUM_STRING(MOVE_FWD);
    ENUM_STRING(MOVE_BCK);
    ENUM_STRING(TURN_LFT);
    ENUM_STRING(TURN_RGT);
    ENUM_STRING(MOVE_FWD_LFT);
    ENUM_STRING(MOVE_FWD_RGT);
    ENUM_STRING(STOP_MOVE);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(NaviMode)
{
    ENUM_STRING(P2P_NAVI);
    ENUM_STRING(PF_NAVI);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(NaviState)
{
    ENUM_STRING(PENDING);
    ENUM_STRING(ACTIVE);
    ENUM_STRING(PREEMPTED);
    ENUM_STRING(SUCCEEDED);
    ENUM_STRING(ABORTED);
    ENUM_STRING(REJECTED);
    ENUM_STRING(PREEMPTING);
    ENUM_STRING(RECALLING);
    ENUM_STRING(RECALLED);
    ENUM_STRING(LOST);
    ENUM_STRING(IDLE);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(CcpMode)
{
    ENUM_STRING(ZIGZAG_CCP);
    ENUM_STRING(SPIRAL_CCP);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(SlamMode)
{
    ENUM_STRING(ICP_SLAM);
    ENUM_STRING(PF_SLAM);
    ENUM_STRING(GH_SLAM);
}
END_ENUM_STRING;


////// param

BEGIN_ENUM_STRING(ChassisModelType)
{
    ENUM_STRING(CARLIKE);
    ENUM_STRING(DIFFDRV);
    ENUM_STRING(UNIVWHEEL);
    ENUM_STRING(OMNIWHEEL);
    ENUM_STRING(CSGDRV);
    ENUM_STRING(STAGEDIFF);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(SteerEncLocation)
{
    ENUM_STRING(CENTER);
    ENUM_STRING(LEFT);
    ENUM_STRING(RIGHT);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(ShapeType)
{
    ENUM_STRING(ROUND);
    ENUM_STRING(RECTANGLE);
    ENUM_STRING(POLYGON);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(InterfaceType)
{
    ENUM_STRING(ETHERNET);
    ENUM_STRING(SERIAL);
#if USE_NPU_API_V_0_9_12
    ENUM_STRING(USB);
    ENUM_STRING(CAN);
    ENUM_STRING(ETHERCAT);
    ENUM_STRING(BLUETOOTH);
#endif
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(BumperLocation)
{
    ENUM_STRING(FRONT_BUMPER);
    ENUM_STRING(FRONT_LEFT_BUMPER);
    ENUM_STRING(FRONT_RIGHT_BUMPER);
    ENUM_STRING(BACK_BUMPER);
    ENUM_STRING(BACK_LEFT_BUMPER);
    ENUM_STRING(BACK_RIGHT_BUMPER);
    ENUM_STRING(LEFT_BUMPER);
    ENUM_STRING(RIGHT_BUMPER);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(BrakeType)
{
    ENUM_STRING(SOFT_BRK);
    ENUM_STRING(HARD_BRK);
}
END_ENUM_STRING;

BEGIN_ENUM_STRING(ValidOutputLevel)
{
    ENUM_STRING(LOW_VALID);
    ENUM_STRING(HIGH_VALID)
}
END_ENUM_STRING;

}}

#endif
