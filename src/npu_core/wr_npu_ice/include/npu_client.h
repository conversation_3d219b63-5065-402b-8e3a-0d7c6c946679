#ifndef WIZROBO_NPU_NPU_CLIENT_H
#define WIZROBO_NPU_NPU_CLIENT_H
#include <netinet/in.h>

#include <Ice/Ice.h>

#include <npuice_api.h>

namespace wizrobo_npu {
typedef NpuIcePrx NpuClient;
class NpuConnector
{
public:
    NpuConnector();
    bool GetServerIp(std::string& ip_str);
    bool Connect(const std::string& ip_str, NpuClient& npu_client);
    bool Disconnect(NpuClient& npu_client);
private:
    std::string ip_str_;
    Ice::CommunicatorPtr ic;
    Ice::ObjectPrx base;
};

}// namespace
#endif // WIZROBO_NPU_NPU_CLIENT_H
