// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_geometry.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __npuice_geometry_h__
#define __npuice_geometry_h__

#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <IceUtil/ScopedArray.h>
#include <IceUtil/Optional.h>
#include <Ice/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace wizrobo_npu
{

const ::Ice::Float INVALID_POSE3D_VALUE = -1000.0F;

struct Point3D
{
    ::Ice::Float x;
    ::Ice::Float y;
    ::Ice::Float z;
};

typedef ::std::vector< ::wizrobo_npu::Point3D> Point3DList;

struct ImgPoint
{
    ::Ice::Int u;
    ::Ice::Int v;

    bool operator==(const ImgPoint& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(u != __rhs.u)
        {
            return false;
        }
        if(v != __rhs.v)
        {
            return false;
        }
        return true;
    }

    bool operator<(const ImgPoint& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(u < __rhs.u)
        {
            return true;
        }
        else if(__rhs.u < u)
        {
            return false;
        }
        if(v < __rhs.v)
        {
            return true;
        }
        else if(__rhs.v < v)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const ImgPoint& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const ImgPoint& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const ImgPoint& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const ImgPoint& __rhs) const
    {
        return !operator<(__rhs);
    }
};

typedef ::std::vector< ::wizrobo_npu::ImgPoint> ImgPointList;

struct Vector3D
{
    ::Ice::Double x;
    ::Ice::Double y;
    ::Ice::Double z;
};

struct Quaternion
{
    ::Ice::Double x;
    ::Ice::Double y;
    ::Ice::Double z;
    ::Ice::Double w;
};

typedef ::std::vector< ::Ice::Double> DoubleArray;

struct Pose3D
{
    ::Ice::Float x;
    ::Ice::Float y;
    ::Ice::Float z;
    ::Ice::Float roll;
    ::Ice::Float pitch;
    ::Ice::Float yaw;
};

typedef ::std::vector< ::wizrobo_npu::Pose3D> Pose3DList;

struct ImgPose
{
    ::Ice::Int u;
    ::Ice::Int v;
    ::Ice::Float theta;
};

typedef ::std::vector< ::wizrobo_npu::ImgPose> ImgPoseList;

struct GeoPoint
{
    ::Ice::Double latitude;
    ::Ice::Double longitude;
    ::Ice::Double altitude;
};

typedef ::std::vector< ::wizrobo_npu::GeoPoint> GeoPointList;

struct GeoPose
{
    ::Ice::Double latitude;
    ::Ice::Double longitude;
    ::Ice::Double altitude;
    ::Ice::Float roll;
    ::Ice::Float pitch;
    ::Ice::Float yaw;
};

typedef ::std::vector< ::wizrobo_npu::GeoPose> GeoPoseList;

struct Vel2D
{
    ::Ice::Float v_x;
    ::Ice::Float v_y;
    ::Ice::Float v_yaw;
};

struct Vel3D
{
    ::Ice::Float v_x;
    ::Ice::Float v_y;
    ::Ice::Float v_z;
    ::Ice::Float v_roll;
    ::Ice::Float v_pitch;
    ::Ice::Float v_yaw;
};

struct Acc2D
{
    ::Ice::Float a_x;
    ::Ice::Float a_y;
    ::Ice::Float a_yaw;
};

struct Acc3D
{
    ::Ice::Float a_x;
    ::Ice::Float a_y;
    ::Ice::Float a_z;
    ::Ice::Float a_roll;
    ::Ice::Float a_pitch;
    ::Ice::Float a_yaw;
};

struct InitPoseArea
{
    ::wizrobo_npu::Pose3D pose;
    ::Ice::Float width;
    ::Ice::Float height;
};

struct InitImgPoseArea
{
    ::wizrobo_npu::ImgPose pose;
    ::Ice::Int width;
    ::Ice::Int height;
};

struct InitGeoPoseArea
{
    ::wizrobo_npu::GeoPose pose;
    ::Ice::Double width;
    ::Ice::Double height;
};

}

namespace Ice
{
template<>
struct StreamableTraits< ::wizrobo_npu::Point3D>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 12;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Point3D, S>
{
    static void write(S* __os, const ::wizrobo_npu::Point3D& v)
    {
        __os->write(v.x);
        __os->write(v.y);
        __os->write(v.z);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Point3D, S>
{
    static void read(S* __is, ::wizrobo_npu::Point3D& v)
    {
        __is->read(v.x);
        __is->read(v.y);
        __is->read(v.z);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImgPoint>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 8;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImgPoint, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImgPoint& v)
    {
        __os->write(v.u);
        __os->write(v.v);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImgPoint, S>
{
    static void read(S* __is, ::wizrobo_npu::ImgPoint& v)
    {
        __is->read(v.u);
        __is->read(v.v);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Vector3D>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 24;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Vector3D, S>
{
    static void write(S* __os, const ::wizrobo_npu::Vector3D& v)
    {
        __os->write(v.x);
        __os->write(v.y);
        __os->write(v.z);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Vector3D, S>
{
    static void read(S* __is, ::wizrobo_npu::Vector3D& v)
    {
        __is->read(v.x);
        __is->read(v.y);
        __is->read(v.z);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Quaternion>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 32;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Quaternion, S>
{
    static void write(S* __os, const ::wizrobo_npu::Quaternion& v)
    {
        __os->write(v.x);
        __os->write(v.y);
        __os->write(v.z);
        __os->write(v.w);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Quaternion, S>
{
    static void read(S* __is, ::wizrobo_npu::Quaternion& v)
    {
        __is->read(v.x);
        __is->read(v.y);
        __is->read(v.z);
        __is->read(v.w);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Pose3D>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 24;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Pose3D, S>
{
    static void write(S* __os, const ::wizrobo_npu::Pose3D& v)
    {
        __os->write(v.x);
        __os->write(v.y);
        __os->write(v.z);
        __os->write(v.roll);
        __os->write(v.pitch);
        __os->write(v.yaw);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Pose3D, S>
{
    static void read(S* __is, ::wizrobo_npu::Pose3D& v)
    {
        __is->read(v.x);
        __is->read(v.y);
        __is->read(v.z);
        __is->read(v.roll);
        __is->read(v.pitch);
        __is->read(v.yaw);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImgPose>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 12;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImgPose, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImgPose& v)
    {
        __os->write(v.u);
        __os->write(v.v);
        __os->write(v.theta);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImgPose, S>
{
    static void read(S* __is, ::wizrobo_npu::ImgPose& v)
    {
        __is->read(v.u);
        __is->read(v.v);
        __is->read(v.theta);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::GeoPoint>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 24;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::GeoPoint, S>
{
    static void write(S* __os, const ::wizrobo_npu::GeoPoint& v)
    {
        __os->write(v.latitude);
        __os->write(v.longitude);
        __os->write(v.altitude);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::GeoPoint, S>
{
    static void read(S* __is, ::wizrobo_npu::GeoPoint& v)
    {
        __is->read(v.latitude);
        __is->read(v.longitude);
        __is->read(v.altitude);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::GeoPose>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 36;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::GeoPose, S>
{
    static void write(S* __os, const ::wizrobo_npu::GeoPose& v)
    {
        __os->write(v.latitude);
        __os->write(v.longitude);
        __os->write(v.altitude);
        __os->write(v.roll);
        __os->write(v.pitch);
        __os->write(v.yaw);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::GeoPose, S>
{
    static void read(S* __is, ::wizrobo_npu::GeoPose& v)
    {
        __is->read(v.latitude);
        __is->read(v.longitude);
        __is->read(v.altitude);
        __is->read(v.roll);
        __is->read(v.pitch);
        __is->read(v.yaw);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Vel2D>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 12;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Vel2D, S>
{
    static void write(S* __os, const ::wizrobo_npu::Vel2D& v)
    {
        __os->write(v.v_x);
        __os->write(v.v_y);
        __os->write(v.v_yaw);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Vel2D, S>
{
    static void read(S* __is, ::wizrobo_npu::Vel2D& v)
    {
        __is->read(v.v_x);
        __is->read(v.v_y);
        __is->read(v.v_yaw);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Vel3D>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 24;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Vel3D, S>
{
    static void write(S* __os, const ::wizrobo_npu::Vel3D& v)
    {
        __os->write(v.v_x);
        __os->write(v.v_y);
        __os->write(v.v_z);
        __os->write(v.v_roll);
        __os->write(v.v_pitch);
        __os->write(v.v_yaw);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Vel3D, S>
{
    static void read(S* __is, ::wizrobo_npu::Vel3D& v)
    {
        __is->read(v.v_x);
        __is->read(v.v_y);
        __is->read(v.v_z);
        __is->read(v.v_roll);
        __is->read(v.v_pitch);
        __is->read(v.v_yaw);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Acc2D>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 12;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Acc2D, S>
{
    static void write(S* __os, const ::wizrobo_npu::Acc2D& v)
    {
        __os->write(v.a_x);
        __os->write(v.a_y);
        __os->write(v.a_yaw);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Acc2D, S>
{
    static void read(S* __is, ::wizrobo_npu::Acc2D& v)
    {
        __is->read(v.a_x);
        __is->read(v.a_y);
        __is->read(v.a_yaw);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Acc3D>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 24;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Acc3D, S>
{
    static void write(S* __os, const ::wizrobo_npu::Acc3D& v)
    {
        __os->write(v.a_x);
        __os->write(v.a_y);
        __os->write(v.a_z);
        __os->write(v.a_roll);
        __os->write(v.a_pitch);
        __os->write(v.a_yaw);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Acc3D, S>
{
    static void read(S* __is, ::wizrobo_npu::Acc3D& v)
    {
        __is->read(v.a_x);
        __is->read(v.a_y);
        __is->read(v.a_z);
        __is->read(v.a_roll);
        __is->read(v.a_pitch);
        __is->read(v.a_yaw);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::InitPoseArea>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 32;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::InitPoseArea, S>
{
    static void write(S* __os, const ::wizrobo_npu::InitPoseArea& v)
    {
        __os->write(v.pose);
        __os->write(v.width);
        __os->write(v.height);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::InitPoseArea, S>
{
    static void read(S* __is, ::wizrobo_npu::InitPoseArea& v)
    {
        __is->read(v.pose);
        __is->read(v.width);
        __is->read(v.height);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::InitImgPoseArea>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 20;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::InitImgPoseArea, S>
{
    static void write(S* __os, const ::wizrobo_npu::InitImgPoseArea& v)
    {
        __os->write(v.pose);
        __os->write(v.width);
        __os->write(v.height);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::InitImgPoseArea, S>
{
    static void read(S* __is, ::wizrobo_npu::InitImgPoseArea& v)
    {
        __is->read(v.pose);
        __is->read(v.width);
        __is->read(v.height);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::InitGeoPoseArea>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 52;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::InitGeoPoseArea, S>
{
    static void write(S* __os, const ::wizrobo_npu::InitGeoPoseArea& v)
    {
        __os->write(v.pose);
        __os->write(v.width);
        __os->write(v.height);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::InitGeoPoseArea, S>
{
    static void read(S* __is, ::wizrobo_npu::InitGeoPoseArea& v)
    {
        __is->read(v.pose);
        __is->read(v.width);
        __is->read(v.height);
    }
};

}

#endif
