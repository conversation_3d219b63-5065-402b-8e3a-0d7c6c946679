// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_param.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __npuice_param_h__
#define __npuice_param_h__

#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <IceUtil/ScopedArray.h>
#include <IceUtil/Optional.h>
#include <npuice_geometry.h>
#include <Ice/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace wizrobo_npu
{

enum NpuMode
{
    MASTER,
    SLAVE
};

struct CoreParam
{
    ::wizrobo_npu::NpuMode npu_mode;
    ::std::string config_id;
    ::std::string map_id;
    ::std::string record_id;

    bool operator==(const CoreParam& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(npu_mode != __rhs.npu_mode)
        {
            return false;
        }
        if(config_id != __rhs.config_id)
        {
            return false;
        }
        if(map_id != __rhs.map_id)
        {
            return false;
        }
        if(record_id != __rhs.record_id)
        {
            return false;
        }
        return true;
    }

    bool operator<(const CoreParam& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(npu_mode < __rhs.npu_mode)
        {
            return true;
        }
        else if(__rhs.npu_mode < npu_mode)
        {
            return false;
        }
        if(config_id < __rhs.config_id)
        {
            return true;
        }
        else if(__rhs.config_id < config_id)
        {
            return false;
        }
        if(map_id < __rhs.map_id)
        {
            return true;
        }
        else if(__rhs.map_id < map_id)
        {
            return false;
        }
        if(record_id < __rhs.record_id)
        {
            return true;
        }
        else if(__rhs.record_id < record_id)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const CoreParam& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const CoreParam& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const CoreParam& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const CoreParam& __rhs) const
    {
        return !operator<(__rhs);
    }
};

enum BrakeType
{
    SOFT_BRK,
    HARD_BRK
};

enum ValidOutputLevel
{
    LOW_VALID,
    HIGH_VALID
};

struct MotorParam
{
    ::Ice::Int motor_num;
    ::std::string motor_dir_str;
    ::Ice::Int motor_max_spd_rpm;
    ::Ice::Float motor_rdc_ratio;
    ::Ice::Int motor_enc_res_ppr;
    ::Ice::Int motor_pwm_frq_hz;
    ::wizrobo_npu::BrakeType motor_brk_type;
    ::wizrobo_npu::ValidOutputLevel enb_vol;
    ::wizrobo_npu::ValidOutputLevel dir_vol;
    ::wizrobo_npu::ValidOutputLevel pwm_vol;
    ::wizrobo_npu::ValidOutputLevel brk_vol;
    bool enb_auxdir_mode;
    bool enb_throttle_mode;
    ::Ice::Float throttle_zero_pos_fac;
    ::Ice::Float throttle_zero_neg_fac;
    bool end_left_right_switch;
};

struct PidParam
{
    bool enb_pid;
    ::Ice::Float kp_acc;
    ::Ice::Float kp_dec;
    ::Ice::Float ki_acc;
    ::Ice::Float ki_dec;
    ::Ice::Float kd_acc;
    ::Ice::Float kd_dec;
};

enum ShapeType
{
    ROUND,
    RECTANGLE,
    POLYGON
};

struct FootprintParam
{
    ::wizrobo_npu::ShapeType shape_type;
    ::Ice::Float rot_center_offset_m;
    ::Ice::Float round_radius_m;
    ::Ice::Float rectangle_width_m;
    ::Ice::Float rectangle_length_m;
    ::Ice::Int polygon_vertex_num;
    ::wizrobo_npu::Point3DList polygon_vertices;
};

enum ChassisModelType
{
    CARLIKE,
    DIFFDRV,
    UNIVWHEEL,
    OMNIWHEEL,
    CSGDRV,
    STAGEDIFF
};

enum SteerEncLocation
{
    CENTER,
    LEFT,
    RIGHT
};

struct ChassisParam
{
    ::wizrobo_npu::ChassisModelType model_type;
    ::Ice::Float wheel_rdc_ratio;
    ::Ice::Float wheel_radius_m;
    ::Ice::Float wheel_span_m;
    ::Ice::Float max_lin_acc_time_s;
    ::Ice::Float max_ang_acc_time_s;
    ::Ice::Float autoset_max_lin_spd_mps;
    ::Ice::Float autoset_max_ang_spd_rps;
    ::wizrobo_npu::SteerEncLocation carlike_steer_enc_location;
    ::Ice::Float carlike_axle_dist_m;
};

struct BaseParam
{
    ::std::string base_id;
    bool enb_slave_mode;
    ::wizrobo_npu::MotorParam motor_param;
    ::wizrobo_npu::PidParam pid_param;
    ::wizrobo_npu::ChassisParam chassis_param;
    ::wizrobo_npu::FootprintParam footprint_param;
};

enum LidarType
{
    RPLIDAR,
    RSLIDAR,
    LSLIDAR,
    FSLIDAR,
    SICK_TIM551,
    SICK_TIM561,
    SICK_TIM571,
    SICK_LMS111,
    SICK_LMS141,
    SICK_LMS151,
    SICK_LMS511,
    SICK_S300,
    HOKUYO_UST10LX,
    HOKUYO_UTM30LX,
    PF_R2000_20HZ,
    PF_R2000_50HZ,
    VELODYNE_VLP16,
    XTION_LIDAR,
    SIM_LIDAR,
    GAZEBO,
    FKLIDAR
};

enum InterfaceType
{
    ETHERNET,
    SERIAL,
    RECORD
};

struct LidarParam
{
    ::wizrobo_npu::LidarType type;
    ::wizrobo_npu::InterfaceType itf_type;
    ::std::string serial_port_id;
    ::std::string ethernet_ip;
    bool enb_itl_filter;
    ::wizrobo_npu::Pose3D install_pose;
    ::std::string record_file_id;
    ::Ice::Double max_angle;
    ::Ice::Double min_angle;
};

typedef ::std::vector< ::wizrobo_npu::LidarParam> LidarParamList;

struct ImuParam
{
    ::wizrobo_npu::Pose3D install_pose;
};

typedef ::std::vector< ::wizrobo_npu::ImuParam> ImuParamList;

struct SonarParam
{
    ::Ice::Float min_range_m;
    ::Ice::Float max_range_m;
    ::Ice::Float scan_freq_hz;
    ::Ice::Float fov_deg;
    ::wizrobo_npu::Pose3D install_pose;
};

typedef ::std::vector< ::wizrobo_npu::SonarParam> SonarParamList;

struct InfrdParam
{
    ::Ice::Float min_range_m;
    ::Ice::Float max_range_m;
    ::Ice::Float fov_deg;
    ::wizrobo_npu::Pose3D install_pose;
};

typedef ::std::vector< ::wizrobo_npu::InfrdParam> InfrdParamList;

enum BumperLocation
{
    FRONT_BUMPER,
    FRONT_LEFT_BUMPER,
    FRONT_RIGHT_BUMPER,
    BACK_BUMPER,
    BACK_LEFT_BUMPER,
    BACK_RIGHT_BUMPER,
    LEFT_BUMPER,
    RIGHT_BUMPER
};

struct BumperParam
{
    ::wizrobo_npu::BumperLocation location;

    bool operator==(const BumperParam& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(location != __rhs.location)
        {
            return false;
        }
        return true;
    }

    bool operator<(const BumperParam& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(location < __rhs.location)
        {
            return true;
        }
        else if(__rhs.location < location)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const BumperParam& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const BumperParam& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const BumperParam& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const BumperParam& __rhs) const
    {
        return !operator<(__rhs);
    }
};

typedef ::std::vector< ::wizrobo_npu::BumperParam> BumperParamList;

struct GpsParam
{
    ::wizrobo_npu::Pose3D install_pose;
};

typedef ::std::vector< ::wizrobo_npu::GpsParam> GpsParamList;

struct CameraParam
{
    ::wizrobo_npu::Pose3D install_pose;
};

typedef ::std::vector< ::wizrobo_npu::CameraParam> CameraParamList;

struct BatteryParam
{
    ::Ice::Float max_voltage_v;
    ::Ice::Float capacity_ah;
};

struct SensorParam
{
    ::Ice::Int lidar_num;
    ::wizrobo_npu::LidarParamList lidar_params;
    ::Ice::Int sonar_num;
    ::wizrobo_npu::SonarParamList sonar_params;
    ::Ice::Int infrd_num;
    ::wizrobo_npu::InfrdParamList infrd_params;
    ::Ice::Int bumper_num;
    ::wizrobo_npu::BumperParamList bumper_params;
    ::Ice::Int imu_num;
    ::wizrobo_npu::ImuParamList imu_params;
    ::Ice::Int gps_num;
    ::wizrobo_npu::GpsParamList gps_params;
    ::Ice::Int camera_num;
    ::wizrobo_npu::CameraParamList camera_params;
};

struct TeleopParam
{
    ::Ice::Float lin_spd_lmt_ratio;
    ::Ice::Float ang_spd_lmt_ratio;
};

struct MapOptimizer
{
    bool enb_map_opt;
    ::Ice::Float map_res_mpp;
    ::Ice::Float update_dist_thrs_m;
    ::Ice::Float update_ori_thrs_rad;
    bool enb_trace_clearing;
    bool enb_filtering;
    ::Ice::Int filter_size;
};

struct SlamParam
{
    ::Ice::Float map_res_mpp;
    ::Ice::Int map_size_m;
    ::Ice::Float update_dist_thrs_m;
    ::Ice::Float update_ori_thrs_rad;
    ::Ice::Float max_lin_spd_mps;
    ::Ice::Float max_ang_spd_rps;
    ::wizrobo_npu::MapOptimizer optimizer_param;
};

struct P2pPlannerParam
{
    ::Ice::Float acc_lim_x_mps2;
    ::Ice::Float acc_lim_theta_rps2;
    ::Ice::Float max_vel_x_mps;
    ::Ice::Float min_vel_x_mps;
    ::Ice::Float max_rot_vel_rps;
    ::Ice::Float min_rot_vel_rps;
    ::Ice::Float plan_strictness;
    ::Ice::Float path_approach_avenue_factor;
    ::Ice::Float safety_inflation_factor;
};

struct PfPlannerParam
{
    bool waypoint_mode;
    ::Ice::Float look_ahead_dist_m;
    ::Ice::Float position_control_tolerance_m;
    ::Ice::Float heading_control_tolerance_rad;
    ::Ice::Float max_lin_vel_mps;
    ::Ice::Float max_ang_vel_rps;
    ::Ice::Float heading_kp;
    ::Ice::Float forward_max_lin_vel;
    ::Ice::Float forward_min_lin_vel;
    ::Ice::Float forward_max_ang_vel;
    ::Ice::Float spin_max_ang_vel;
    ::Ice::Float spin_min_ang_vel;
    ::Ice::Float spin_deflection_kp;
    ::Ice::Float parking_distance_k;
    ::Ice::Float parking_offset;
    ::Ice::Float decelerate_distence_k;
    ::Ice::Float decelerate_offset;
    ::Ice::Float ang_limit_kp;
    ::Ice::Float forward_deflection_kp;
    ::Ice::Float forward_deflection_ki;
    ::Ice::Float forward_deflection_kd;
};

struct CcpPlannerParam
{
    ::Ice::Float coverage_spacing_m;
};

struct NaviParam
{
    ::Ice::Float lin_spd_lmt_ratio;
    ::Ice::Float ang_spd_lmt_ratio;
    ::Ice::Float x_err_tolr_m;
    ::Ice::Float y_err_tolr_m;
    ::Ice::Float yaw_err_tolr_rad;
    ::wizrobo_npu::P2pPlannerParam p2p_planner_param;
    ::wizrobo_npu::PfPlannerParam pf_planner_param;
    ::wizrobo_npu::CcpPlannerParam ccp_planner_param;
};

const ::Ice::Int RT0_CONECT_LIDAR = 1;

const ::Ice::Int RT0_CONECT_IMU = 2;

const ::Ice::Int RT0_CONECT_BCU_MOTOR = 4;

const ::Ice::Int RT0_CONECT_BCU_SENSOR = 8;

const ::Ice::Int RT0_CONECT_GPS = 16;

const ::Ice::Int RT0_DATA_LIDAR = 65536;

const ::Ice::Int RT0_DATA_IMU = 131072;

const ::Ice::Int RT0_DATA_ENCODER = 262144;

const ::Ice::Int RT0_DATA_GPS = 524288;

const ::Ice::Int RT0_DATA_MAP = 1048576;

const ::Ice::Int RT1_INIT_POSE_NOT_SET = 1;

const ::Ice::Int RT1_LOCALIZATION_EXCEPTION = 2;

const ::Ice::Int RT1_SOFTWARE_NODE_CRASH = 4;

const ::Ice::Int RT1_NAVI_OBSTACLE_BLOCK = 65536;

const ::Ice::Int RT1_NAVI_PLANNING_FAILED = 131072;

const ::Ice::Int RT1_NAVI_EMB_TRIGGERED = 262144;

const ::Ice::Int RT1_NAVI_BUMPER_TRIGGERED = 524288;

const ::Ice::Int RT1_NAVI_ENTRP_DETECTED = 1048576;

typedef ::std::vector< ::Ice::Int> RuntimeStatusList;

}

namespace Ice
{
template<>
struct StreamableTraits< ::wizrobo_npu::NpuMode>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 1;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::CoreParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 4;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::CoreParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::CoreParam& v)
    {
        __os->write(v.npu_mode);
        __os->write(v.config_id);
        __os->write(v.map_id);
        __os->write(v.record_id);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::CoreParam, S>
{
    static void read(S* __is, ::wizrobo_npu::CoreParam& v)
    {
        __is->read(v.npu_mode);
        __is->read(v.config_id);
        __is->read(v.map_id);
        __is->read(v.record_id);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::BrakeType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 1;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::ValidOutputLevel>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 1;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::MotorParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 37;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::MotorParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::MotorParam& v)
    {
        __os->write(v.motor_num);
        __os->write(v.motor_dir_str);
        __os->write(v.motor_max_spd_rpm);
        __os->write(v.motor_rdc_ratio);
        __os->write(v.motor_enc_res_ppr);
        __os->write(v.motor_pwm_frq_hz);
        __os->write(v.motor_brk_type);
        __os->write(v.enb_vol);
        __os->write(v.dir_vol);
        __os->write(v.pwm_vol);
        __os->write(v.brk_vol);
        __os->write(v.enb_auxdir_mode);
        __os->write(v.enb_throttle_mode);
        __os->write(v.throttle_zero_pos_fac);
        __os->write(v.throttle_zero_neg_fac);
        __os->write(v.end_left_right_switch);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::MotorParam, S>
{
    static void read(S* __is, ::wizrobo_npu::MotorParam& v)
    {
        __is->read(v.motor_num);
        __is->read(v.motor_dir_str);
        __is->read(v.motor_max_spd_rpm);
        __is->read(v.motor_rdc_ratio);
        __is->read(v.motor_enc_res_ppr);
        __is->read(v.motor_pwm_frq_hz);
        __is->read(v.motor_brk_type);
        __is->read(v.enb_vol);
        __is->read(v.dir_vol);
        __is->read(v.pwm_vol);
        __is->read(v.brk_vol);
        __is->read(v.enb_auxdir_mode);
        __is->read(v.enb_throttle_mode);
        __is->read(v.throttle_zero_pos_fac);
        __is->read(v.throttle_zero_neg_fac);
        __is->read(v.end_left_right_switch);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::PidParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 25;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::PidParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::PidParam& v)
    {
        __os->write(v.enb_pid);
        __os->write(v.kp_acc);
        __os->write(v.kp_dec);
        __os->write(v.ki_acc);
        __os->write(v.ki_dec);
        __os->write(v.kd_acc);
        __os->write(v.kd_dec);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::PidParam, S>
{
    static void read(S* __is, ::wizrobo_npu::PidParam& v)
    {
        __is->read(v.enb_pid);
        __is->read(v.kp_acc);
        __is->read(v.kp_dec);
        __is->read(v.ki_acc);
        __is->read(v.ki_dec);
        __is->read(v.kd_acc);
        __is->read(v.kd_dec);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ShapeType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 2;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::FootprintParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 22;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::FootprintParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::FootprintParam& v)
    {
        __os->write(v.shape_type);
        __os->write(v.rot_center_offset_m);
        __os->write(v.round_radius_m);
        __os->write(v.rectangle_width_m);
        __os->write(v.rectangle_length_m);
        __os->write(v.polygon_vertex_num);
        __os->write(v.polygon_vertices);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::FootprintParam, S>
{
    static void read(S* __is, ::wizrobo_npu::FootprintParam& v)
    {
        __is->read(v.shape_type);
        __is->read(v.rot_center_offset_m);
        __is->read(v.round_radius_m);
        __is->read(v.rectangle_width_m);
        __is->read(v.rectangle_length_m);
        __is->read(v.polygon_vertex_num);
        __is->read(v.polygon_vertices);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ChassisModelType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 5;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::SteerEncLocation>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 2;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::ChassisParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 34;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ChassisParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::ChassisParam& v)
    {
        __os->write(v.model_type);
        __os->write(v.wheel_rdc_ratio);
        __os->write(v.wheel_radius_m);
        __os->write(v.wheel_span_m);
        __os->write(v.max_lin_acc_time_s);
        __os->write(v.max_ang_acc_time_s);
        __os->write(v.autoset_max_lin_spd_mps);
        __os->write(v.autoset_max_ang_spd_rps);
        __os->write(v.carlike_steer_enc_location);
        __os->write(v.carlike_axle_dist_m);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ChassisParam, S>
{
    static void read(S* __is, ::wizrobo_npu::ChassisParam& v)
    {
        __is->read(v.model_type);
        __is->read(v.wheel_rdc_ratio);
        __is->read(v.wheel_radius_m);
        __is->read(v.wheel_span_m);
        __is->read(v.max_lin_acc_time_s);
        __is->read(v.max_ang_acc_time_s);
        __is->read(v.autoset_max_lin_spd_mps);
        __is->read(v.autoset_max_ang_spd_rps);
        __is->read(v.carlike_steer_enc_location);
        __is->read(v.carlike_axle_dist_m);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::BaseParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 120;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::BaseParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::BaseParam& v)
    {
        __os->write(v.base_id);
        __os->write(v.enb_slave_mode);
        __os->write(v.motor_param);
        __os->write(v.pid_param);
        __os->write(v.chassis_param);
        __os->write(v.footprint_param);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::BaseParam, S>
{
    static void read(S* __is, ::wizrobo_npu::BaseParam& v)
    {
        __is->read(v.base_id);
        __is->read(v.enb_slave_mode);
        __is->read(v.motor_param);
        __is->read(v.pid_param);
        __is->read(v.chassis_param);
        __is->read(v.footprint_param);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::LidarType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 20;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::InterfaceType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 2;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::LidarParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 46;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::LidarParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::LidarParam& v)
    {
        __os->write(v.type);
        __os->write(v.itf_type);
        __os->write(v.serial_port_id);
        __os->write(v.ethernet_ip);
        __os->write(v.enb_itl_filter);
        __os->write(v.install_pose);
        __os->write(v.record_file_id);
        __os->write(v.max_angle);
        __os->write(v.min_angle);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::LidarParam, S>
{
    static void read(S* __is, ::wizrobo_npu::LidarParam& v)
    {
        __is->read(v.type);
        __is->read(v.itf_type);
        __is->read(v.serial_port_id);
        __is->read(v.ethernet_ip);
        __is->read(v.enb_itl_filter);
        __is->read(v.install_pose);
        __is->read(v.record_file_id);
        __is->read(v.max_angle);
        __is->read(v.min_angle);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImuParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 24;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImuParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImuParam& v)
    {
        __os->write(v.install_pose);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImuParam, S>
{
    static void read(S* __is, ::wizrobo_npu::ImuParam& v)
    {
        __is->read(v.install_pose);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::SonarParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 40;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::SonarParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::SonarParam& v)
    {
        __os->write(v.min_range_m);
        __os->write(v.max_range_m);
        __os->write(v.scan_freq_hz);
        __os->write(v.fov_deg);
        __os->write(v.install_pose);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::SonarParam, S>
{
    static void read(S* __is, ::wizrobo_npu::SonarParam& v)
    {
        __is->read(v.min_range_m);
        __is->read(v.max_range_m);
        __is->read(v.scan_freq_hz);
        __is->read(v.fov_deg);
        __is->read(v.install_pose);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::InfrdParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 36;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::InfrdParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::InfrdParam& v)
    {
        __os->write(v.min_range_m);
        __os->write(v.max_range_m);
        __os->write(v.fov_deg);
        __os->write(v.install_pose);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::InfrdParam, S>
{
    static void read(S* __is, ::wizrobo_npu::InfrdParam& v)
    {
        __is->read(v.min_range_m);
        __is->read(v.max_range_m);
        __is->read(v.fov_deg);
        __is->read(v.install_pose);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::BumperLocation>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 7;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::BumperParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::BumperParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::BumperParam& v)
    {
        __os->write(v.location);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::BumperParam, S>
{
    static void read(S* __is, ::wizrobo_npu::BumperParam& v)
    {
        __is->read(v.location);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::GpsParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 24;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::GpsParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::GpsParam& v)
    {
        __os->write(v.install_pose);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::GpsParam, S>
{
    static void read(S* __is, ::wizrobo_npu::GpsParam& v)
    {
        __is->read(v.install_pose);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::CameraParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 24;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::CameraParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::CameraParam& v)
    {
        __os->write(v.install_pose);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::CameraParam, S>
{
    static void read(S* __is, ::wizrobo_npu::CameraParam& v)
    {
        __is->read(v.install_pose);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::BatteryParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 8;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::BatteryParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::BatteryParam& v)
    {
        __os->write(v.max_voltage_v);
        __os->write(v.capacity_ah);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::BatteryParam, S>
{
    static void read(S* __is, ::wizrobo_npu::BatteryParam& v)
    {
        __is->read(v.max_voltage_v);
        __is->read(v.capacity_ah);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::SensorParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 35;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::SensorParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::SensorParam& v)
    {
        __os->write(v.lidar_num);
        __os->write(v.lidar_params);
        __os->write(v.sonar_num);
        __os->write(v.sonar_params);
        __os->write(v.infrd_num);
        __os->write(v.infrd_params);
        __os->write(v.bumper_num);
        __os->write(v.bumper_params);
        __os->write(v.imu_num);
        __os->write(v.imu_params);
        __os->write(v.gps_num);
        __os->write(v.gps_params);
        __os->write(v.camera_num);
        __os->write(v.camera_params);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::SensorParam, S>
{
    static void read(S* __is, ::wizrobo_npu::SensorParam& v)
    {
        __is->read(v.lidar_num);
        __is->read(v.lidar_params);
        __is->read(v.sonar_num);
        __is->read(v.sonar_params);
        __is->read(v.infrd_num);
        __is->read(v.infrd_params);
        __is->read(v.bumper_num);
        __is->read(v.bumper_params);
        __is->read(v.imu_num);
        __is->read(v.imu_params);
        __is->read(v.gps_num);
        __is->read(v.gps_params);
        __is->read(v.camera_num);
        __is->read(v.camera_params);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::TeleopParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 8;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::TeleopParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::TeleopParam& v)
    {
        __os->write(v.lin_spd_lmt_ratio);
        __os->write(v.ang_spd_lmt_ratio);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::TeleopParam, S>
{
    static void read(S* __is, ::wizrobo_npu::TeleopParam& v)
    {
        __is->read(v.lin_spd_lmt_ratio);
        __is->read(v.ang_spd_lmt_ratio);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::MapOptimizer>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 19;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::MapOptimizer, S>
{
    static void write(S* __os, const ::wizrobo_npu::MapOptimizer& v)
    {
        __os->write(v.enb_map_opt);
        __os->write(v.map_res_mpp);
        __os->write(v.update_dist_thrs_m);
        __os->write(v.update_ori_thrs_rad);
        __os->write(v.enb_trace_clearing);
        __os->write(v.enb_filtering);
        __os->write(v.filter_size);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::MapOptimizer, S>
{
    static void read(S* __is, ::wizrobo_npu::MapOptimizer& v)
    {
        __is->read(v.enb_map_opt);
        __is->read(v.map_res_mpp);
        __is->read(v.update_dist_thrs_m);
        __is->read(v.update_ori_thrs_rad);
        __is->read(v.enb_trace_clearing);
        __is->read(v.enb_filtering);
        __is->read(v.filter_size);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::SlamParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 43;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::SlamParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::SlamParam& v)
    {
        __os->write(v.map_res_mpp);
        __os->write(v.map_size_m);
        __os->write(v.update_dist_thrs_m);
        __os->write(v.update_ori_thrs_rad);
        __os->write(v.max_lin_spd_mps);
        __os->write(v.max_ang_spd_rps);
        __os->write(v.optimizer_param);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::SlamParam, S>
{
    static void read(S* __is, ::wizrobo_npu::SlamParam& v)
    {
        __is->read(v.map_res_mpp);
        __is->read(v.map_size_m);
        __is->read(v.update_dist_thrs_m);
        __is->read(v.update_ori_thrs_rad);
        __is->read(v.max_lin_spd_mps);
        __is->read(v.max_ang_spd_rps);
        __is->read(v.optimizer_param);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::P2pPlannerParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 36;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::P2pPlannerParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::P2pPlannerParam& v)
    {
        __os->write(v.acc_lim_x_mps2);
        __os->write(v.acc_lim_theta_rps2);
        __os->write(v.max_vel_x_mps);
        __os->write(v.min_vel_x_mps);
        __os->write(v.max_rot_vel_rps);
        __os->write(v.min_rot_vel_rps);
        __os->write(v.plan_strictness);
        __os->write(v.path_approach_avenue_factor);
        __os->write(v.safety_inflation_factor);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::P2pPlannerParam, S>
{
    static void read(S* __is, ::wizrobo_npu::P2pPlannerParam& v)
    {
        __is->read(v.acc_lim_x_mps2);
        __is->read(v.acc_lim_theta_rps2);
        __is->read(v.max_vel_x_mps);
        __is->read(v.min_vel_x_mps);
        __is->read(v.max_rot_vel_rps);
        __is->read(v.min_rot_vel_rps);
        __is->read(v.plan_strictness);
        __is->read(v.path_approach_avenue_factor);
        __is->read(v.safety_inflation_factor);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::PfPlannerParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 81;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::PfPlannerParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::PfPlannerParam& v)
    {
        __os->write(v.waypoint_mode);
        __os->write(v.look_ahead_dist_m);
        __os->write(v.position_control_tolerance_m);
        __os->write(v.heading_control_tolerance_rad);
        __os->write(v.max_lin_vel_mps);
        __os->write(v.max_ang_vel_rps);
        __os->write(v.heading_kp);
        __os->write(v.forward_max_lin_vel);
        __os->write(v.forward_min_lin_vel);
        __os->write(v.forward_max_ang_vel);
        __os->write(v.spin_max_ang_vel);
        __os->write(v.spin_min_ang_vel);
        __os->write(v.spin_deflection_kp);
        __os->write(v.parking_distance_k);
        __os->write(v.parking_offset);
        __os->write(v.decelerate_distence_k);
        __os->write(v.decelerate_offset);
        __os->write(v.ang_limit_kp);
        __os->write(v.forward_deflection_kp);
        __os->write(v.forward_deflection_ki);
        __os->write(v.forward_deflection_kd);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::PfPlannerParam, S>
{
    static void read(S* __is, ::wizrobo_npu::PfPlannerParam& v)
    {
        __is->read(v.waypoint_mode);
        __is->read(v.look_ahead_dist_m);
        __is->read(v.position_control_tolerance_m);
        __is->read(v.heading_control_tolerance_rad);
        __is->read(v.max_lin_vel_mps);
        __is->read(v.max_ang_vel_rps);
        __is->read(v.heading_kp);
        __is->read(v.forward_max_lin_vel);
        __is->read(v.forward_min_lin_vel);
        __is->read(v.forward_max_ang_vel);
        __is->read(v.spin_max_ang_vel);
        __is->read(v.spin_min_ang_vel);
        __is->read(v.spin_deflection_kp);
        __is->read(v.parking_distance_k);
        __is->read(v.parking_offset);
        __is->read(v.decelerate_distence_k);
        __is->read(v.decelerate_offset);
        __is->read(v.ang_limit_kp);
        __is->read(v.forward_deflection_kp);
        __is->read(v.forward_deflection_ki);
        __is->read(v.forward_deflection_kd);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::CcpPlannerParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 4;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::CcpPlannerParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::CcpPlannerParam& v)
    {
        __os->write(v.coverage_spacing_m);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::CcpPlannerParam, S>
{
    static void read(S* __is, ::wizrobo_npu::CcpPlannerParam& v)
    {
        __is->read(v.coverage_spacing_m);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::NaviParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 141;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::NaviParam, S>
{
    static void write(S* __os, const ::wizrobo_npu::NaviParam& v)
    {
        __os->write(v.lin_spd_lmt_ratio);
        __os->write(v.ang_spd_lmt_ratio);
        __os->write(v.x_err_tolr_m);
        __os->write(v.y_err_tolr_m);
        __os->write(v.yaw_err_tolr_rad);
        __os->write(v.p2p_planner_param);
        __os->write(v.pf_planner_param);
        __os->write(v.ccp_planner_param);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::NaviParam, S>
{
    static void read(S* __is, ::wizrobo_npu::NaviParam& v)
    {
        __is->read(v.lin_spd_lmt_ratio);
        __is->read(v.ang_spd_lmt_ratio);
        __is->read(v.x_err_tolr_m);
        __is->read(v.y_err_tolr_m);
        __is->read(v.yaw_err_tolr_rad);
        __is->read(v.p2p_planner_param);
        __is->read(v.pf_planner_param);
        __is->read(v.ccp_planner_param);
    }
};

}

#endif
