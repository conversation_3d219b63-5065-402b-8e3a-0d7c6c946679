// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_data.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __npuice_data_h__
#define __npuice_data_h__

#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <IceUtil/ScopedArray.h>
#include <IceUtil/Optional.h>
#include <npuice_geometry.h>
#include <npuice_param.h>
#include <Ice/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace wizrobo_npu
{

typedef ::std::vector<bool> BoolArray;

typedef ::std::vector< ::Ice::Int> IntArray;

typedef ::std::vector< ::Ice::Float> FloatArray;

typedef ::std::vector< ::std::string> StringArray;

typedef ::std::vector< ::Ice::Byte> ByteArray;

typedef ::std::vector< ::Ice::Byte> ZipFile;

enum DiagnosticsStatus
{
    DISCONNECT,
    CONNECT,
    DATA_RECEIVED,
    DATA_ERROR,
    PROGRAM_NORMAL,
    PROGRAM_ERROR,
    IGNORE
};

struct SensorState
{
    ::std::string sensor_id;
    ::wizrobo_npu::DiagnosticsStatus hardware_status;
    ::wizrobo_npu::DiagnosticsStatus topic_status;
    ::wizrobo_npu::DiagnosticsStatus node_status;

    bool operator==(const SensorState& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(sensor_id != __rhs.sensor_id)
        {
            return false;
        }
        if(hardware_status != __rhs.hardware_status)
        {
            return false;
        }
        if(topic_status != __rhs.topic_status)
        {
            return false;
        }
        if(node_status != __rhs.node_status)
        {
            return false;
        }
        return true;
    }

    bool operator<(const SensorState& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(sensor_id < __rhs.sensor_id)
        {
            return true;
        }
        else if(__rhs.sensor_id < sensor_id)
        {
            return false;
        }
        if(hardware_status < __rhs.hardware_status)
        {
            return true;
        }
        else if(__rhs.hardware_status < hardware_status)
        {
            return false;
        }
        if(topic_status < __rhs.topic_status)
        {
            return true;
        }
        else if(__rhs.topic_status < topic_status)
        {
            return false;
        }
        if(node_status < __rhs.node_status)
        {
            return true;
        }
        else if(__rhs.node_status < node_status)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const SensorState& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const SensorState& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const SensorState& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const SensorState& __rhs) const
    {
        return !operator<(__rhs);
    }
};

typedef ::std::vector< ::wizrobo_npu::SensorState> SensorStatus;

struct SystemDiagInfo
{
    ::std::string todo;

    bool operator==(const SystemDiagInfo& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(todo != __rhs.todo)
        {
            return false;
        }
        return true;
    }

    bool operator<(const SystemDiagInfo& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(todo < __rhs.todo)
        {
            return true;
        }
        else if(__rhs.todo < todo)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const SystemDiagInfo& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const SystemDiagInfo& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const SystemDiagInfo& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const SystemDiagInfo& __rhs) const
    {
        return !operator<(__rhs);
    }
};

struct FileInfo
{
    ::std::string file_name;
    ::Ice::Int chunk_num;
    ::Ice::Int chunk_size_Bytes;

    bool operator==(const FileInfo& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(file_name != __rhs.file_name)
        {
            return false;
        }
        if(chunk_num != __rhs.chunk_num)
        {
            return false;
        }
        if(chunk_size_Bytes != __rhs.chunk_size_Bytes)
        {
            return false;
        }
        return true;
    }

    bool operator<(const FileInfo& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(file_name < __rhs.file_name)
        {
            return true;
        }
        else if(__rhs.file_name < file_name)
        {
            return false;
        }
        if(chunk_num < __rhs.chunk_num)
        {
            return true;
        }
        else if(__rhs.chunk_num < chunk_num)
        {
            return false;
        }
        if(chunk_size_Bytes < __rhs.chunk_size_Bytes)
        {
            return true;
        }
        else if(__rhs.chunk_size_Bytes < chunk_size_Bytes)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const FileInfo& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const FileInfo& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const FileInfo& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const FileInfo& __rhs) const
    {
        return !operator<(__rhs);
    }
};

struct FileData
{
    ::std::string file_name;
    ::Ice::Int chunk_num;
    ::wizrobo_npu::ZipFile data;
    ::Ice::Int chunk_length;
    ::Ice::Int chunk_index;
    ::Ice::Int offset;
};

struct MotorEnc
{
    ::Ice::Int motor_num;
    ::wizrobo_npu::IntArray ticks;
    ::Ice::Float steer_angle_deg;
};

struct MotorSpd
{
    ::Ice::Int motor_num;
    ::wizrobo_npu::FloatArray rpms;
    ::Ice::Float steer_angle_deg;
};

struct LidarScan
{
    ::wizrobo_npu::Point3DList points;
    ::wizrobo_npu::FloatArray intensities;
};

struct ImgLidarScan
{
    ::wizrobo_npu::ImgPointList points;
    ::wizrobo_npu::FloatArray intensities;
};

struct SonarScan
{
    ::wizrobo_npu::Point3DList points;
};

struct ImgSonarScan
{
    ::wizrobo_npu::ImgPointList points;
};

struct InfrdScan
{
    ::wizrobo_npu::Point3DList points;
};

struct ImgInfrdScan
{
    ::wizrobo_npu::ImgPointList points;
};

struct BumperData
{
    ::wizrobo_npu::BumperLocation location;
    bool state;

    bool operator==(const BumperData& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(location != __rhs.location)
        {
            return false;
        }
        if(state != __rhs.state)
        {
            return false;
        }
        return true;
    }

    bool operator<(const BumperData& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(location < __rhs.location)
        {
            return true;
        }
        else if(__rhs.location < location)
        {
            return false;
        }
        if(state < __rhs.state)
        {
            return true;
        }
        else if(__rhs.state < state)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const BumperData& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const BumperData& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const BumperData& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const BumperData& __rhs) const
    {
        return !operator<(__rhs);
    }
};

typedef ::std::vector< ::wizrobo_npu::BumperData> BumperDataList;

struct BumperArray
{
    ::wizrobo_npu::BumperDataList states;
};

struct ImuData
{
    ::Ice::Float roll_deg;
    ::Ice::Float pitch_deg;
    ::Ice::Float yaw_deg;
};

struct GpsData
{
    ::Ice::Float latitude_deg;
    ::Ice::Float longitude_deg;
    ::Ice::Float altitude_m;
};

struct BatteryStatus
{
    ::Ice::Float current_a;
    ::Ice::Float voltage_v;
    ::Ice::Float temperature_deg;
    ::Ice::Float capacity_level;
};

}

namespace Ice
{
template<>
struct StreamableTraits< ::wizrobo_npu::DiagnosticsStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 6;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::SensorState>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 4;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::SensorState, S>
{
    static void write(S* __os, const ::wizrobo_npu::SensorState& v)
    {
        __os->write(v.sensor_id);
        __os->write(v.hardware_status);
        __os->write(v.topic_status);
        __os->write(v.node_status);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::SensorState, S>
{
    static void read(S* __is, ::wizrobo_npu::SensorState& v)
    {
        __is->read(v.sensor_id);
        __is->read(v.hardware_status);
        __is->read(v.topic_status);
        __is->read(v.node_status);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::SystemDiagInfo>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::SystemDiagInfo, S>
{
    static void write(S* __os, const ::wizrobo_npu::SystemDiagInfo& v)
    {
        __os->write(v.todo);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::SystemDiagInfo, S>
{
    static void read(S* __is, ::wizrobo_npu::SystemDiagInfo& v)
    {
        __is->read(v.todo);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::FileInfo>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 9;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::FileInfo, S>
{
    static void write(S* __os, const ::wizrobo_npu::FileInfo& v)
    {
        __os->write(v.file_name);
        __os->write(v.chunk_num);
        __os->write(v.chunk_size_Bytes);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::FileInfo, S>
{
    static void read(S* __is, ::wizrobo_npu::FileInfo& v)
    {
        __is->read(v.file_name);
        __is->read(v.chunk_num);
        __is->read(v.chunk_size_Bytes);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::FileData>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 18;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::FileData, S>
{
    static void write(S* __os, const ::wizrobo_npu::FileData& v)
    {
        __os->write(v.file_name);
        __os->write(v.chunk_num);
        __os->write(v.data);
        __os->write(v.chunk_length);
        __os->write(v.chunk_index);
        __os->write(v.offset);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::FileData, S>
{
    static void read(S* __is, ::wizrobo_npu::FileData& v)
    {
        __is->read(v.file_name);
        __is->read(v.chunk_num);
        __is->read(v.data);
        __is->read(v.chunk_length);
        __is->read(v.chunk_index);
        __is->read(v.offset);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::MotorEnc>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 9;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::MotorEnc, S>
{
    static void write(S* __os, const ::wizrobo_npu::MotorEnc& v)
    {
        __os->write(v.motor_num);
        __os->write(v.ticks);
        __os->write(v.steer_angle_deg);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::MotorEnc, S>
{
    static void read(S* __is, ::wizrobo_npu::MotorEnc& v)
    {
        __is->read(v.motor_num);
        __is->read(v.ticks);
        __is->read(v.steer_angle_deg);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::MotorSpd>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 9;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::MotorSpd, S>
{
    static void write(S* __os, const ::wizrobo_npu::MotorSpd& v)
    {
        __os->write(v.motor_num);
        __os->write(v.rpms);
        __os->write(v.steer_angle_deg);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::MotorSpd, S>
{
    static void read(S* __is, ::wizrobo_npu::MotorSpd& v)
    {
        __is->read(v.motor_num);
        __is->read(v.rpms);
        __is->read(v.steer_angle_deg);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::LidarScan>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 2;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::LidarScan, S>
{
    static void write(S* __os, const ::wizrobo_npu::LidarScan& v)
    {
        __os->write(v.points);
        __os->write(v.intensities);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::LidarScan, S>
{
    static void read(S* __is, ::wizrobo_npu::LidarScan& v)
    {
        __is->read(v.points);
        __is->read(v.intensities);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImgLidarScan>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 2;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImgLidarScan, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImgLidarScan& v)
    {
        __os->write(v.points);
        __os->write(v.intensities);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImgLidarScan, S>
{
    static void read(S* __is, ::wizrobo_npu::ImgLidarScan& v)
    {
        __is->read(v.points);
        __is->read(v.intensities);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::SonarScan>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::SonarScan, S>
{
    static void write(S* __os, const ::wizrobo_npu::SonarScan& v)
    {
        __os->write(v.points);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::SonarScan, S>
{
    static void read(S* __is, ::wizrobo_npu::SonarScan& v)
    {
        __is->read(v.points);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImgSonarScan>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImgSonarScan, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImgSonarScan& v)
    {
        __os->write(v.points);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImgSonarScan, S>
{
    static void read(S* __is, ::wizrobo_npu::ImgSonarScan& v)
    {
        __is->read(v.points);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::InfrdScan>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::InfrdScan, S>
{
    static void write(S* __os, const ::wizrobo_npu::InfrdScan& v)
    {
        __os->write(v.points);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::InfrdScan, S>
{
    static void read(S* __is, ::wizrobo_npu::InfrdScan& v)
    {
        __is->read(v.points);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImgInfrdScan>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImgInfrdScan, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImgInfrdScan& v)
    {
        __os->write(v.points);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImgInfrdScan, S>
{
    static void read(S* __is, ::wizrobo_npu::ImgInfrdScan& v)
    {
        __is->read(v.points);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::BumperData>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 2;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::BumperData, S>
{
    static void write(S* __os, const ::wizrobo_npu::BumperData& v)
    {
        __os->write(v.location);
        __os->write(v.state);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::BumperData, S>
{
    static void read(S* __is, ::wizrobo_npu::BumperData& v)
    {
        __is->read(v.location);
        __is->read(v.state);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::BumperArray>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::BumperArray, S>
{
    static void write(S* __os, const ::wizrobo_npu::BumperArray& v)
    {
        __os->write(v.states);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::BumperArray, S>
{
    static void read(S* __is, ::wizrobo_npu::BumperArray& v)
    {
        __is->read(v.states);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImuData>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 12;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImuData, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImuData& v)
    {
        __os->write(v.roll_deg);
        __os->write(v.pitch_deg);
        __os->write(v.yaw_deg);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImuData, S>
{
    static void read(S* __is, ::wizrobo_npu::ImuData& v)
    {
        __is->read(v.roll_deg);
        __is->read(v.pitch_deg);
        __is->read(v.yaw_deg);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::GpsData>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 12;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::GpsData, S>
{
    static void write(S* __os, const ::wizrobo_npu::GpsData& v)
    {
        __os->write(v.latitude_deg);
        __os->write(v.longitude_deg);
        __os->write(v.altitude_m);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::GpsData, S>
{
    static void read(S* __is, ::wizrobo_npu::GpsData& v)
    {
        __is->read(v.latitude_deg);
        __is->read(v.longitude_deg);
        __is->read(v.altitude_m);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::BatteryStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 16;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::BatteryStatus, S>
{
    static void write(S* __os, const ::wizrobo_npu::BatteryStatus& v)
    {
        __os->write(v.current_a);
        __os->write(v.voltage_v);
        __os->write(v.temperature_deg);
        __os->write(v.capacity_level);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::BatteryStatus, S>
{
    static void read(S* __is, ::wizrobo_npu::BatteryStatus& v)
    {
        __is->read(v.current_a);
        __is->read(v.voltage_v);
        __is->read(v.temperature_deg);
        __is->read(v.capacity_level);
    }
};

}

#endif
