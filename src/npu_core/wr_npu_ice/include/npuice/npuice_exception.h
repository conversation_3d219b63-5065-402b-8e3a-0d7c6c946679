// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_exception.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __npuice_exception_h__
#define __npuice_exception_h__

#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <IceUtil/Optional.h>
#include <Ice/StreamF.h>
#include <Ice/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace wizrobo_npu
{

enum ErrorType
{
    ERR_UNMATCHED_VERSION,
    ERR_NO_KEY,
    ERR_NOT_CONNECTED,
    ERR_BASE_UNINITED,
    ERR_ILLEGAL_PARA,
    ERR_UNKNOWN
};

class NpuException : public ::Ice::UserException
{
public:

    NpuException() {}
    NpuException(const ::std::string&, const ::std::string&, ::wizrobo_npu::ErrorType, const ::std::string&);
    virtual ~NpuException() throw();

    virtual ::std::string ice_name() const;
    virtual NpuException* ice_clone() const;
    virtual void ice_throw() const;

    ::std::string date;
    ::std::string time;
    ::wizrobo_npu::ErrorType type;
    ::std::string msg;

protected:
    virtual void __writeImpl(::IceInternal::BasicStream*) const;
    virtual void __readImpl(::IceInternal::BasicStream*);
    #ifdef __SUNPRO_CC
    using ::Ice::UserException::__writeImpl;
    using ::Ice::UserException::__readImpl;
    #endif
};

static NpuException __NpuException_init;

}

namespace Ice
{
template<>
struct StreamableTraits< ::wizrobo_npu::ErrorType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 5;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::NpuException>
{
    static const StreamHelperCategory helper = StreamHelperCategoryUserException;
};

}

#endif
