// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_scrubber_interaction.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __npuice_scrubber_interaction_h__
#define __npuice_scrubber_interaction_h__

#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/Outgoing.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/Incoming.h>
#include <Ice/Direct.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <IceUtil/Optional.h>
#include <Ice/StreamF.h>
#include <npuice_exception.h>
#include <npuice_geometry.h>
#include <npuice_data.h>
#include <npuice_param.h>
#include <npuice_map.h>
#include <Ice/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace IceProxy
{

namespace wizrobo_scrubber
{

class ScrubberIce;
void __read(::IceInternal::BasicStream*, ::IceInternal::ProxyHandle< ::IceProxy::wizrobo_scrubber::ScrubberIce>&);
::IceProxy::Ice::Object* upCast(::IceProxy::wizrobo_scrubber::ScrubberIce*);

}

}

namespace wizrobo_scrubber
{

class ScrubberIce;
bool operator==(const ScrubberIce&, const ScrubberIce&);
bool operator<(const ScrubberIce&, const ScrubberIce&);
::Ice::Object* upCast(::wizrobo_scrubber::ScrubberIce*);
typedef ::IceInternal::Handle< ::wizrobo_scrubber::ScrubberIce> ScrubberIcePtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::wizrobo_scrubber::ScrubberIce> ScrubberIcePrx;
void __patch(ScrubberIcePtr&, const ::Ice::ObjectPtr&);

}

namespace wizrobo_scrubber
{

enum ScrLampState
{
    SCR_OUTPUT_MODE_KEEP_HIGH,
    SCR_OUTPUT_MODE_KEEP_LOW,
    SCR_OUTPUT_MODE_BLINK_1,
    SCR_OUTPUT_MODE_BLINK_2,
    SCR_OUTPUT_MODE_BLINK_3
};

enum ScrBeepState
{
    SCR_BEEP_CLOSE,
    SCR_BUZZING_1HZ,
    SCR_BUZZING_2HZ,
    SCR_BLEW_10S
};

enum ScrBlState
{
    SCR_BLN_WHITE_BREATHE,
    SCR_BLN_ALL_BREATHE,
    SCR_BLN_RED_BLINK_1HZ,
    SCR_BLN_RED_BLINK_2HZ,
    SCR_BLN_RED_ALWAYS_ON
};

enum ScrLiftState
{
    SCR_LIFT_DOWN,
    SCR_LIFT_UP,
    SCR_LIFT_HALF
};

struct ScrMotorState
{
    ::std::string scr_id;
    ::Ice::Float scr_rpm;
    ::Ice::Float scr_voltage;
    ::Ice::Float scr_current;
    ::Ice::Float scr_power;
};

enum ScrSpeedLevel
{
    SCR_LV_STOP,
    SCR_LV_LOW,
    SCR_LV_MEDIUM,
    SCR_LV_HIGH,
    SCR_LV_MAX
};

struct ScrUserMotorState
{
    ::Ice::Int scr_abnormal_state;
    bool scr_is_located;
    ::wizrobo_scrubber::ScrMotorState scr_motor;
    ::wizrobo_scrubber::ScrSpeedLevel scr_speed_level;
    ::wizrobo_scrubber::ScrLiftState scr_lift_state;
};

struct ScrClearControlUnit
{
    ::wizrobo_scrubber::ScrLampState scr_lmp_state;
    ::wizrobo_scrubber::ScrBeepState scr_beep_state;
    ::wizrobo_scrubber::ScrBlState scr_bl_state;
    ::wizrobo_scrubber::ScrLiftState scr_lift_state;
    ::wizrobo_scrubber::ScrUserMotorState scr_fan_state;
    ::wizrobo_scrubber::ScrUserMotorState scr_brush_state;
    ::wizrobo_scrubber::ScrUserMotorState scr_valve_state;
};

struct ScrBatteryStatus
{
    ::Ice::Float scr_current_a;
    ::Ice::Float scr_current_threshold_a;
    ::Ice::Float scr_voltage_v;
    ::Ice::Float scr_voltage_threshold_v;
    ::Ice::Float scr_temperature_cen;
    ::Ice::Float scr_temperature_threshold_cen;
    ::Ice::Int scr_design_capacity_ah;
    ::Ice::Float scr_capacity_level;
    ::Ice::Int scr_abnormal_state;
    ::Ice::Int scr_charge_discharge_time;
    ::Ice::Int scr_battery_lifetime;
    ::Ice::Int scr_accumulated_time_of_use;
    bool scr_should_back_to_charge;
    bool scr_should_change_new_battery;
};

enum ScrWaterLevel
{
    SCR_L1,
    SCR_L2
};

struct ScrWaterTankStatus
{
    ::wizrobo_scrubber::ScrWaterLevel scr_pure_watter_tank_level;
    ::wizrobo_scrubber::ScrWaterLevel scr_polluted_water_tank_level;
    ::Ice::Int scr_polluted_water_tank_usage_d;
    bool scr_is_polluted_water_tank_need_disinfected;

    bool operator==(const ScrWaterTankStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(scr_pure_watter_tank_level != __rhs.scr_pure_watter_tank_level)
        {
            return false;
        }
        if(scr_polluted_water_tank_level != __rhs.scr_polluted_water_tank_level)
        {
            return false;
        }
        if(scr_polluted_water_tank_usage_d != __rhs.scr_polluted_water_tank_usage_d)
        {
            return false;
        }
        if(scr_is_polluted_water_tank_need_disinfected != __rhs.scr_is_polluted_water_tank_need_disinfected)
        {
            return false;
        }
        return true;
    }

    bool operator<(const ScrWaterTankStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(scr_pure_watter_tank_level < __rhs.scr_pure_watter_tank_level)
        {
            return true;
        }
        else if(__rhs.scr_pure_watter_tank_level < scr_pure_watter_tank_level)
        {
            return false;
        }
        if(scr_polluted_water_tank_level < __rhs.scr_polluted_water_tank_level)
        {
            return true;
        }
        else if(__rhs.scr_polluted_water_tank_level < scr_polluted_water_tank_level)
        {
            return false;
        }
        if(scr_polluted_water_tank_usage_d < __rhs.scr_polluted_water_tank_usage_d)
        {
            return true;
        }
        else if(__rhs.scr_polluted_water_tank_usage_d < scr_polluted_water_tank_usage_d)
        {
            return false;
        }
        if(scr_is_polluted_water_tank_need_disinfected < __rhs.scr_is_polluted_water_tank_need_disinfected)
        {
            return true;
        }
        else if(__rhs.scr_is_polluted_water_tank_need_disinfected < scr_is_polluted_water_tank_need_disinfected)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const ScrWaterTankStatus& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const ScrWaterTankStatus& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const ScrWaterTankStatus& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const ScrWaterTankStatus& __rhs) const
    {
        return !operator<(__rhs);
    }
};

enum ScrSwitch
{
    SCR_ON,
    SCR_OFF
};

struct ScrEmergencyStop
{
    ::wizrobo_scrubber::ScrSwitch scr_stop;

    bool operator==(const ScrEmergencyStop& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(scr_stop != __rhs.scr_stop)
        {
            return false;
        }
        return true;
    }

    bool operator<(const ScrEmergencyStop& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(scr_stop < __rhs.scr_stop)
        {
            return true;
        }
        else if(__rhs.scr_stop < scr_stop)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const ScrEmergencyStop& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const ScrEmergencyStop& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const ScrEmergencyStop& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const ScrEmergencyStop& __rhs) const
    {
        return !operator<(__rhs);
    }
};

struct ScrSensorStatusUnit
{
    ::wizrobo_scrubber::ScrBatteryStatus scr_battery_state;
    ::wizrobo_scrubber::ScrWaterTankStatus scr_water_state;
    ::wizrobo_scrubber::ScrEmergencyStop scr_e_stop;
};

}

namespace Ice
{
template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrLampState>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 4;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrBeepState>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 3;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrBlState>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 4;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrLiftState>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 2;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrMotorState>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 17;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_scrubber::ScrMotorState, S>
{
    static void write(S* __os, const ::wizrobo_scrubber::ScrMotorState& v)
    {
        __os->write(v.scr_id);
        __os->write(v.scr_rpm);
        __os->write(v.scr_voltage);
        __os->write(v.scr_current);
        __os->write(v.scr_power);
    }
};

template<class S>
struct StreamReader< ::wizrobo_scrubber::ScrMotorState, S>
{
    static void read(S* __is, ::wizrobo_scrubber::ScrMotorState& v)
    {
        __is->read(v.scr_id);
        __is->read(v.scr_rpm);
        __is->read(v.scr_voltage);
        __is->read(v.scr_current);
        __is->read(v.scr_power);
    }
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrSpeedLevel>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 4;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrUserMotorState>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 24;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_scrubber::ScrUserMotorState, S>
{
    static void write(S* __os, const ::wizrobo_scrubber::ScrUserMotorState& v)
    {
        __os->write(v.scr_abnormal_state);
        __os->write(v.scr_is_located);
        __os->write(v.scr_motor);
        __os->write(v.scr_speed_level);
        __os->write(v.scr_lift_state);
    }
};

template<class S>
struct StreamReader< ::wizrobo_scrubber::ScrUserMotorState, S>
{
    static void read(S* __is, ::wizrobo_scrubber::ScrUserMotorState& v)
    {
        __is->read(v.scr_abnormal_state);
        __is->read(v.scr_is_located);
        __is->read(v.scr_motor);
        __is->read(v.scr_speed_level);
        __is->read(v.scr_lift_state);
    }
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrClearControlUnit>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 76;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_scrubber::ScrClearControlUnit, S>
{
    static void write(S* __os, const ::wizrobo_scrubber::ScrClearControlUnit& v)
    {
        __os->write(v.scr_lmp_state);
        __os->write(v.scr_beep_state);
        __os->write(v.scr_bl_state);
        __os->write(v.scr_lift_state);
        __os->write(v.scr_fan_state);
        __os->write(v.scr_brush_state);
        __os->write(v.scr_valve_state);
    }
};

template<class S>
struct StreamReader< ::wizrobo_scrubber::ScrClearControlUnit, S>
{
    static void read(S* __is, ::wizrobo_scrubber::ScrClearControlUnit& v)
    {
        __is->read(v.scr_lmp_state);
        __is->read(v.scr_beep_state);
        __is->read(v.scr_bl_state);
        __is->read(v.scr_lift_state);
        __is->read(v.scr_fan_state);
        __is->read(v.scr_brush_state);
        __is->read(v.scr_valve_state);
    }
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrBatteryStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 50;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_scrubber::ScrBatteryStatus, S>
{
    static void write(S* __os, const ::wizrobo_scrubber::ScrBatteryStatus& v)
    {
        __os->write(v.scr_current_a);
        __os->write(v.scr_current_threshold_a);
        __os->write(v.scr_voltage_v);
        __os->write(v.scr_voltage_threshold_v);
        __os->write(v.scr_temperature_cen);
        __os->write(v.scr_temperature_threshold_cen);
        __os->write(v.scr_design_capacity_ah);
        __os->write(v.scr_capacity_level);
        __os->write(v.scr_abnormal_state);
        __os->write(v.scr_charge_discharge_time);
        __os->write(v.scr_battery_lifetime);
        __os->write(v.scr_accumulated_time_of_use);
        __os->write(v.scr_should_back_to_charge);
        __os->write(v.scr_should_change_new_battery);
    }
};

template<class S>
struct StreamReader< ::wizrobo_scrubber::ScrBatteryStatus, S>
{
    static void read(S* __is, ::wizrobo_scrubber::ScrBatteryStatus& v)
    {
        __is->read(v.scr_current_a);
        __is->read(v.scr_current_threshold_a);
        __is->read(v.scr_voltage_v);
        __is->read(v.scr_voltage_threshold_v);
        __is->read(v.scr_temperature_cen);
        __is->read(v.scr_temperature_threshold_cen);
        __is->read(v.scr_design_capacity_ah);
        __is->read(v.scr_capacity_level);
        __is->read(v.scr_abnormal_state);
        __is->read(v.scr_charge_discharge_time);
        __is->read(v.scr_battery_lifetime);
        __is->read(v.scr_accumulated_time_of_use);
        __is->read(v.scr_should_back_to_charge);
        __is->read(v.scr_should_change_new_battery);
    }
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrWaterLevel>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 1;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrWaterTankStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 7;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_scrubber::ScrWaterTankStatus, S>
{
    static void write(S* __os, const ::wizrobo_scrubber::ScrWaterTankStatus& v)
    {
        __os->write(v.scr_pure_watter_tank_level);
        __os->write(v.scr_polluted_water_tank_level);
        __os->write(v.scr_polluted_water_tank_usage_d);
        __os->write(v.scr_is_polluted_water_tank_need_disinfected);
    }
};

template<class S>
struct StreamReader< ::wizrobo_scrubber::ScrWaterTankStatus, S>
{
    static void read(S* __is, ::wizrobo_scrubber::ScrWaterTankStatus& v)
    {
        __is->read(v.scr_pure_watter_tank_level);
        __is->read(v.scr_polluted_water_tank_level);
        __is->read(v.scr_polluted_water_tank_usage_d);
        __is->read(v.scr_is_polluted_water_tank_need_disinfected);
    }
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrSwitch>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 1;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrEmergencyStop>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_scrubber::ScrEmergencyStop, S>
{
    static void write(S* __os, const ::wizrobo_scrubber::ScrEmergencyStop& v)
    {
        __os->write(v.scr_stop);
    }
};

template<class S>
struct StreamReader< ::wizrobo_scrubber::ScrEmergencyStop, S>
{
    static void read(S* __is, ::wizrobo_scrubber::ScrEmergencyStop& v)
    {
        __is->read(v.scr_stop);
    }
};

template<>
struct StreamableTraits< ::wizrobo_scrubber::ScrSensorStatusUnit>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 58;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_scrubber::ScrSensorStatusUnit, S>
{
    static void write(S* __os, const ::wizrobo_scrubber::ScrSensorStatusUnit& v)
    {
        __os->write(v.scr_battery_state);
        __os->write(v.scr_water_state);
        __os->write(v.scr_e_stop);
    }
};

template<class S>
struct StreamReader< ::wizrobo_scrubber::ScrSensorStatusUnit, S>
{
    static void read(S* __is, ::wizrobo_scrubber::ScrSensorStatusUnit& v)
    {
        __is->read(v.scr_battery_state);
        __is->read(v.scr_water_state);
        __is->read(v.scr_e_stop);
    }
};

}

namespace wizrobo_scrubber
{

class Callback_ScrubberIce_GetClearControlData_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_GetClearControlData_Base> Callback_ScrubberIce_GetClearControlDataPtr;

class Callback_ScrubberIce_SetClearControlData_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_SetClearControlData_Base> Callback_ScrubberIce_SetClearControlDataPtr;

class Callback_ScrubberIce_GetSensorState_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_GetSensorState_Base> Callback_ScrubberIce_GetSensorStatePtr;

}

namespace IceProxy
{

namespace wizrobo_scrubber
{

class ScrubberIce : virtual public ::IceProxy::Ice::Object
{
public:

    ::wizrobo_scrubber::ScrClearControlUnit GetClearControlData()
    {
        return GetClearControlData(0);
    }
    ::wizrobo_scrubber::ScrClearControlUnit GetClearControlData(const ::Ice::Context& __ctx)
    {
        return GetClearControlData(&__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_GetClearControlData(const ::IceInternal::Function<void (const ::wizrobo_scrubber::ScrClearControlUnit&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetClearControlData(0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetClearControlData(const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetClearControlData(0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_GetClearControlData(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::wizrobo_scrubber::ScrClearControlUnit&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetClearControlData(&__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetClearControlData(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetClearControlData(&__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_GetClearControlData(const ::Ice::Context* __ctx, const ::IceInternal::Function<void (const ::wizrobo_scrubber::ScrClearControlUnit&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void (const ::wizrobo_scrubber::ScrClearControlUnit&)>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_scrubber::ScrubberIcePrx __proxy = ::wizrobo_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                ::wizrobo_scrubber::ScrClearControlUnit __ret;
                try
                {
                    __ret = __proxy->end_GetClearControlData(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response(__ret);
                }
            }
        
        private:
            
            ::std::function<void (const ::wizrobo_scrubber::ScrClearControlUnit&)> _response;
        };
        return begin_GetClearControlData(__ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_GetClearControlData()
    {
        return begin_GetClearControlData(0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetClearControlData(const ::Ice::Context& __ctx)
    {
        return begin_GetClearControlData(&__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetClearControlData(const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetClearControlData(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetClearControlData(const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetClearControlData(&__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetClearControlData(const ::wizrobo_scrubber::Callback_ScrubberIce_GetClearControlDataPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetClearControlData(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetClearControlData(const ::Ice::Context& __ctx, const ::wizrobo_scrubber::Callback_ScrubberIce_GetClearControlDataPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetClearControlData(&__ctx, __del, __cookie);
    }

    ::wizrobo_scrubber::ScrClearControlUnit end_GetClearControlData(const ::Ice::AsyncResultPtr&);
    
private:

    ::wizrobo_scrubber::ScrClearControlUnit GetClearControlData(const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_GetClearControlData(const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    void SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol)
    {
        SetClearControlData(clearcontrol, 0);
    }
    void SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context& __ctx)
    {
        SetClearControlData(clearcontrol, &__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_SetClearControlData(clearcontrol, 0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_SetClearControlData(clearcontrol, 0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context& __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_SetClearControlData(clearcontrol, &__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_SetClearControlData(clearcontrol, &__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context* __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void ()>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_scrubber::ScrubberIcePrx __proxy = ::wizrobo_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                try
                {
                    __proxy->end_SetClearControlData(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response();
                }
            }
        
        private:
            
            ::std::function<void ()> _response;
        };
        return begin_SetClearControlData(clearcontrol, __ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol)
    {
        return begin_SetClearControlData(clearcontrol, 0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context& __ctx)
    {
        return begin_SetClearControlData(clearcontrol, &__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetClearControlData(clearcontrol, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetClearControlData(clearcontrol, &__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::wizrobo_scrubber::Callback_ScrubberIce_SetClearControlDataPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetClearControlData(clearcontrol, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context& __ctx, const ::wizrobo_scrubber::Callback_ScrubberIce_SetClearControlDataPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetClearControlData(clearcontrol, &__ctx, __del, __cookie);
    }

    void end_SetClearControlData(const ::Ice::AsyncResultPtr&);
    
private:

    void SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit&, const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit&, const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    ::wizrobo_scrubber::ScrSensorStatusUnit GetSensorState()
    {
        return GetSensorState(0);
    }
    ::wizrobo_scrubber::ScrSensorStatusUnit GetSensorState(const ::Ice::Context& __ctx)
    {
        return GetSensorState(&__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_GetSensorState(const ::IceInternal::Function<void (const ::wizrobo_scrubber::ScrSensorStatusUnit&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetSensorState(0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetSensorState(const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetSensorState(0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_GetSensorState(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::wizrobo_scrubber::ScrSensorStatusUnit&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetSensorState(&__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetSensorState(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetSensorState(&__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_GetSensorState(const ::Ice::Context* __ctx, const ::IceInternal::Function<void (const ::wizrobo_scrubber::ScrSensorStatusUnit&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void (const ::wizrobo_scrubber::ScrSensorStatusUnit&)>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_scrubber::ScrubberIcePrx __proxy = ::wizrobo_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                ::wizrobo_scrubber::ScrSensorStatusUnit __ret;
                try
                {
                    __ret = __proxy->end_GetSensorState(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response(__ret);
                }
            }
        
        private:
            
            ::std::function<void (const ::wizrobo_scrubber::ScrSensorStatusUnit&)> _response;
        };
        return begin_GetSensorState(__ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_GetSensorState()
    {
        return begin_GetSensorState(0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetSensorState(const ::Ice::Context& __ctx)
    {
        return begin_GetSensorState(&__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetSensorState(const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetSensorState(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetSensorState(const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetSensorState(&__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetSensorState(const ::wizrobo_scrubber::Callback_ScrubberIce_GetSensorStatePtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetSensorState(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetSensorState(const ::Ice::Context& __ctx, const ::wizrobo_scrubber::Callback_ScrubberIce_GetSensorStatePtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetSensorState(&__ctx, __del, __cookie);
    }

    ::wizrobo_scrubber::ScrSensorStatusUnit end_GetSensorState(const ::Ice::AsyncResultPtr&);
    
private:

    ::wizrobo_scrubber::ScrSensorStatusUnit GetSensorState(const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_GetSensorState(const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_context(const ::Ice::Context& __context) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_context(__context).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_adapterId(const ::std::string& __id) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_adapterId(__id).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_endpoints(const ::Ice::EndpointSeq& __endpoints) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_endpoints(__endpoints).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_locatorCacheTimeout(int __timeout) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_locatorCacheTimeout(__timeout).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_connectionCached(bool __cached) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_connectionCached(__cached).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_endpointSelection(::Ice::EndpointSelectionType __est) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_endpointSelection(__est).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_secure(bool __secure) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_secure(__secure).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_preferSecure(bool __preferSecure) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_preferSecure(__preferSecure).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_router(const ::Ice::RouterPrx& __router) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_router(__router).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_locator(const ::Ice::LocatorPrx& __locator) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_locator(__locator).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_collocationOptimized(bool __co) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_collocationOptimized(__co).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_twoway() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_twoway().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_oneway() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_oneway().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_batchOneway() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_batchOneway().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_datagram() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_datagram().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_batchDatagram() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_batchDatagram().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_compress(bool __compress) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_compress(__compress).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_timeout(int __timeout) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_timeout(__timeout).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_connectionId(const ::std::string& __id) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_connectionId(__id).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_encodingVersion(const ::Ice::EncodingVersion& __v) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_encodingVersion(__v).get());
    }
    
    static const ::std::string& ice_staticId();

private: 

    virtual ::IceInternal::Handle< ::IceDelegateM::Ice::Object> __createDelegateM();
    virtual ::IceInternal::Handle< ::IceDelegateD::Ice::Object> __createDelegateD();
    virtual ::IceProxy::Ice::Object* __newInstance() const;
};

}

}

namespace IceDelegate
{

namespace wizrobo_scrubber
{

class ScrubberIce : virtual public ::IceDelegate::Ice::Object
{
public:

    virtual ::wizrobo_scrubber::ScrClearControlUnit GetClearControlData(const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual void SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit&, const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual ::wizrobo_scrubber::ScrSensorStatusUnit GetSensorState(const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;
};

}

}

namespace IceDelegateM
{

namespace wizrobo_scrubber
{

class ScrubberIce : virtual public ::IceDelegate::wizrobo_scrubber::ScrubberIce,
                    virtual public ::IceDelegateM::Ice::Object
{
public:

    virtual ::wizrobo_scrubber::ScrClearControlUnit GetClearControlData(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual void SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit&, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_scrubber::ScrSensorStatusUnit GetSensorState(const ::Ice::Context*, ::IceInternal::InvocationObserver&);
};

}

}

namespace IceDelegateD
{

namespace wizrobo_scrubber
{

class ScrubberIce : virtual public ::IceDelegate::wizrobo_scrubber::ScrubberIce,
                    virtual public ::IceDelegateD::Ice::Object
{
public:

    virtual ::wizrobo_scrubber::ScrClearControlUnit GetClearControlData(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual void SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit&, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_scrubber::ScrSensorStatusUnit GetSensorState(const ::Ice::Context*, ::IceInternal::InvocationObserver&);
};

}

}

namespace wizrobo_scrubber
{

class ScrubberIce : virtual public ::Ice::Object
{
public:

    typedef ScrubberIcePrx ProxyType;
    typedef ScrubberIcePtr PointerType;

    virtual bool ice_isA(const ::std::string&, const ::Ice::Current& = ::Ice::Current()) const;
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& = ::Ice::Current()) const;
    virtual const ::std::string& ice_id(const ::Ice::Current& = ::Ice::Current()) const;
    static const ::std::string& ice_staticId();

    virtual ::wizrobo_scrubber::ScrClearControlUnit GetClearControlData(const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___GetClearControlData(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual void SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit&, const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___SetClearControlData(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual ::wizrobo_scrubber::ScrSensorStatusUnit GetSensorState(const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___GetSensorState(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual ::Ice::DispatchStatus __dispatch(::IceInternal::Incoming&, const ::Ice::Current&);

protected:
    virtual void __writeImpl(::IceInternal::BasicStream*) const;
    virtual void __readImpl(::IceInternal::BasicStream*);
    #ifdef __SUNPRO_CC
    using ::Ice::Object::__writeImpl;
    using ::Ice::Object::__readImpl;
    #endif
};

inline bool operator==(const ScrubberIce& l, const ScrubberIce& r)
{
    return static_cast<const ::Ice::Object&>(l) == static_cast<const ::Ice::Object&>(r);
}

inline bool operator<(const ScrubberIce& l, const ScrubberIce& r)
{
    return static_cast<const ::Ice::Object&>(l) < static_cast<const ::Ice::Object&>(r);
}

}

namespace wizrobo_scrubber
{

template<class T>
class CallbackNC_ScrubberIce_GetClearControlData : public Callback_ScrubberIce_GetClearControlData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(const ::wizrobo_scrubber::ScrClearControlUnit&);

    CallbackNC_ScrubberIce_GetClearControlData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_scrubber::ScrubberIcePrx __proxy = ::wizrobo_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_scrubber::ScrClearControlUnit __ret;
        try
        {
            __ret = __proxy->end_GetClearControlData(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)(__ret);
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_GetClearControlDataPtr
newCallback_ScrubberIce_GetClearControlData(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_scrubber::ScrClearControlUnit&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetClearControlData<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetClearControlDataPtr
newCallback_ScrubberIce_GetClearControlData(T* instance, void (T::*cb)(const ::wizrobo_scrubber::ScrClearControlUnit&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetClearControlData<T>(instance, cb, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_GetClearControlData : public Callback_ScrubberIce_GetClearControlData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const ::wizrobo_scrubber::ScrClearControlUnit&, const CT&);

    Callback_ScrubberIce_GetClearControlData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_scrubber::ScrubberIcePrx __proxy = ::wizrobo_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_scrubber::ScrClearControlUnit __ret;
        try
        {
            __ret = __proxy->end_GetClearControlData(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(__ret, CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_GetClearControlDataPtr
newCallback_ScrubberIce_GetClearControlData(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_scrubber::ScrClearControlUnit&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetClearControlData<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetClearControlDataPtr
newCallback_ScrubberIce_GetClearControlData(T* instance, void (T::*cb)(const ::wizrobo_scrubber::ScrClearControlUnit&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetClearControlData<T, CT>(instance, cb, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_SetClearControlData : public Callback_ScrubberIce_SetClearControlData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ScrubberIce_SetClearControlData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_scrubber::ScrubberIcePrx __proxy = ::wizrobo_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_SetClearControlData(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)();
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_SetClearControlDataPtr
newCallback_ScrubberIce_SetClearControlData(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetClearControlData<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetClearControlDataPtr
newCallback_ScrubberIce_SetClearControlData(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetClearControlData<T>(instance, 0, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetClearControlDataPtr
newCallback_ScrubberIce_SetClearControlData(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetClearControlData<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetClearControlDataPtr
newCallback_ScrubberIce_SetClearControlData(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetClearControlData<T>(instance, 0, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_SetClearControlData : public Callback_ScrubberIce_SetClearControlData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ScrubberIce_SetClearControlData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_scrubber::ScrubberIcePrx __proxy = ::wizrobo_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_SetClearControlData(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_SetClearControlDataPtr
newCallback_ScrubberIce_SetClearControlData(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetClearControlData<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetClearControlDataPtr
newCallback_ScrubberIce_SetClearControlData(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetClearControlData<T, CT>(instance, 0, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetClearControlDataPtr
newCallback_ScrubberIce_SetClearControlData(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetClearControlData<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetClearControlDataPtr
newCallback_ScrubberIce_SetClearControlData(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetClearControlData<T, CT>(instance, 0, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_GetSensorState : public Callback_ScrubberIce_GetSensorState_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(const ::wizrobo_scrubber::ScrSensorStatusUnit&);

    CallbackNC_ScrubberIce_GetSensorState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_scrubber::ScrubberIcePrx __proxy = ::wizrobo_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_scrubber::ScrSensorStatusUnit __ret;
        try
        {
            __ret = __proxy->end_GetSensorState(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)(__ret);
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_GetSensorStatePtr
newCallback_ScrubberIce_GetSensorState(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_scrubber::ScrSensorStatusUnit&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetSensorState<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetSensorStatePtr
newCallback_ScrubberIce_GetSensorState(T* instance, void (T::*cb)(const ::wizrobo_scrubber::ScrSensorStatusUnit&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetSensorState<T>(instance, cb, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_GetSensorState : public Callback_ScrubberIce_GetSensorState_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const ::wizrobo_scrubber::ScrSensorStatusUnit&, const CT&);

    Callback_ScrubberIce_GetSensorState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_scrubber::ScrubberIcePrx __proxy = ::wizrobo_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_scrubber::ScrSensorStatusUnit __ret;
        try
        {
            __ret = __proxy->end_GetSensorState(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(__ret, CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_GetSensorStatePtr
newCallback_ScrubberIce_GetSensorState(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_scrubber::ScrSensorStatusUnit&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetSensorState<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetSensorStatePtr
newCallback_ScrubberIce_GetSensorState(T* instance, void (T::*cb)(const ::wizrobo_scrubber::ScrSensorStatusUnit&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetSensorState<T, CT>(instance, cb, excb, sentcb);
}

}

#endif
