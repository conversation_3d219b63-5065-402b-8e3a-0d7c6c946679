// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_scrubber.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __npuice_scrubber_h__
#define __npuice_scrubber_h__

#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/Outgoing.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/Incoming.h>
#include <Ice/Direct.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <IceUtil/Optional.h>
#include <Ice/StreamF.h>
#include <npuice_exception.h>
#include <npuice_geometry.h>
#include <npuice_data.h>
#include <npuice_param.h>
#include <npuice_map.h>
#include <Ice/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace IceProxy
{

namespace wizrobo_npu_scrubber
{

class ScrubberIce;
void __read(::IceInternal::BasicStream*, ::IceInternal::ProxyHandle< ::IceProxy::wizrobo_npu_scrubber::ScrubberIce>&);
::IceProxy::Ice::Object* upCast(::IceProxy::wizrobo_npu_scrubber::ScrubberIce*);

}

}

namespace wizrobo_npu_scrubber
{

class ScrubberIce;
bool operator==(const ScrubberIce&, const ScrubberIce&);
bool operator<(const ScrubberIce&, const ScrubberIce&);
::Ice::Object* upCast(::wizrobo_npu_scrubber::ScrubberIce*);
typedef ::IceInternal::Handle< ::wizrobo_npu_scrubber::ScrubberIce> ScrubberIcePtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::wizrobo_npu_scrubber::ScrubberIce> ScrubberIcePrx;
void __patch(ScrubberIcePtr&, const ::Ice::ObjectPtr&);

}

namespace wizrobo_npu_scrubber
{

enum DiagnosticsStatus
{
    DISCONNECT,
    CONNECT,
    DATA_RECEIVED,
    DATA_ERROR,
    PROGRAM_NORMAL,
    PROGRAM_ERROR,
    IGNORE
};

enum WaterLevel
{
    LL1,
    LL2
};

enum Level
{
    STOP,
    LOW,
    MEDIUM,
    HIGH
};

enum Switch
{
    ON,
    OFF
};

enum BreathLightStatus
{
    ALLCLOSE,
    ALLOPEN,
    WHITECLOSE,
    WHITEOPEN,
    REDCLOSE,
    REDOPEN
};

enum SystemStatusReminder
{
    BATTERY_NORMAL = 0,
    BATTERY_LOW_POWER = 1,
    BATTERY_TEMPERATURE_HIGH = 2,
    HIGH_WATER_LEVEL_OF_POLLUTED_WATER_TANK = 10,
    HIGH_WATER_LEVEL_OF_PURE_WATER_TANK = 20,
    LOW_WATER_LEVEL_OF_PURE_WATER_TANK = 21,
    TEMPERATURE_ABNORMAL = 100,
    HUMIDITY_ABNORMAL = 101,
    EMERGENCY_STOP_ABNORMAL = 200,
    BUMP_ABNORMAL = 201,
    ROLLING_BRUSH_ABNORMAL = 1000,
    BLOWER_ABNORMAL = 1001,
    BROOM_ABNORMAL = 1002,
    VALVE_ABNORMAL = 1003,
    WHEEL_HUB_MOTOR_ABNORMAL = 2000,
    LIFT_MOTOR_ABNORMAL = 2001,
    IMU_ABNORMAL = 3000,
    RADAR_ABNORMAL = 3001,
    DEPTH_CAMERA_ABNORMAL = 3002,
    ULTRASONIC_PARTS_ABNORMAL = 3003,
    INFRARED_PARTS_ABNORMAL = 3004,
    POSITIONING_ABNORMAL = 9999
};

typedef ::std::vector< ::wizrobo_npu_scrubber::SystemStatusReminder> SystemIndicators;

struct BatteryStatus
{
    ::Ice::Float current_a;
    ::Ice::Float current_threshold_a;
    ::Ice::Float voltage_v;
    ::Ice::Float voltage_threshold_v;
    ::Ice::Float temperature_cen;
    ::Ice::Float temperature_threshold_cen;
    ::Ice::Int design_capacity_ah;
    ::Ice::Float capacity_level;
    ::Ice::Int abnormal_state;
    ::Ice::Int charge_discharge_time;
    ::Ice::Int battery_lifetime;
    ::Ice::Int accumulated_time_of_use;
    bool should_back_to_charge;
    bool should_change_new_battery;
};

struct BrushStatus
{
    bool is_rolling_brush_located;
    bool should_change_new_rolling_brush;
    ::wizrobo_npu_scrubber::Switch rolling_brush_status;
    ::Ice::Int rolling_brush_usage_h;
    ::Ice::Int rotation_speed_of_rolling_brush;

    bool operator==(const BrushStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(is_rolling_brush_located != __rhs.is_rolling_brush_located)
        {
            return false;
        }
        if(should_change_new_rolling_brush != __rhs.should_change_new_rolling_brush)
        {
            return false;
        }
        if(rolling_brush_status != __rhs.rolling_brush_status)
        {
            return false;
        }
        if(rolling_brush_usage_h != __rhs.rolling_brush_usage_h)
        {
            return false;
        }
        if(rotation_speed_of_rolling_brush != __rhs.rotation_speed_of_rolling_brush)
        {
            return false;
        }
        return true;
    }

    bool operator<(const BrushStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(is_rolling_brush_located < __rhs.is_rolling_brush_located)
        {
            return true;
        }
        else if(__rhs.is_rolling_brush_located < is_rolling_brush_located)
        {
            return false;
        }
        if(should_change_new_rolling_brush < __rhs.should_change_new_rolling_brush)
        {
            return true;
        }
        else if(__rhs.should_change_new_rolling_brush < should_change_new_rolling_brush)
        {
            return false;
        }
        if(rolling_brush_status < __rhs.rolling_brush_status)
        {
            return true;
        }
        else if(__rhs.rolling_brush_status < rolling_brush_status)
        {
            return false;
        }
        if(rolling_brush_usage_h < __rhs.rolling_brush_usage_h)
        {
            return true;
        }
        else if(__rhs.rolling_brush_usage_h < rolling_brush_usage_h)
        {
            return false;
        }
        if(rotation_speed_of_rolling_brush < __rhs.rotation_speed_of_rolling_brush)
        {
            return true;
        }
        else if(__rhs.rotation_speed_of_rolling_brush < rotation_speed_of_rolling_brush)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const BrushStatus& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const BrushStatus& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const BrushStatus& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const BrushStatus& __rhs) const
    {
        return !operator<(__rhs);
    }
};

struct BlowerStatus
{
    ::wizrobo_npu_scrubber::Switch blower_switch;
    ::wizrobo_npu_scrubber::Level blower_speed;

    bool operator==(const BlowerStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(blower_switch != __rhs.blower_switch)
        {
            return false;
        }
        if(blower_speed != __rhs.blower_speed)
        {
            return false;
        }
        return true;
    }

    bool operator<(const BlowerStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(blower_switch < __rhs.blower_switch)
        {
            return true;
        }
        else if(__rhs.blower_switch < blower_switch)
        {
            return false;
        }
        if(blower_speed < __rhs.blower_speed)
        {
            return true;
        }
        else if(__rhs.blower_speed < blower_speed)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const BlowerStatus& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const BlowerStatus& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const BlowerStatus& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const BlowerStatus& __rhs) const
    {
        return !operator<(__rhs);
    }
};

struct WaterValveStatus
{
    ::wizrobo_npu_scrubber::Switch valve_status;
    ::Ice::Int valve_ratio;

    bool operator==(const WaterValveStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(valve_status != __rhs.valve_status)
        {
            return false;
        }
        if(valve_ratio != __rhs.valve_ratio)
        {
            return false;
        }
        return true;
    }

    bool operator<(const WaterValveStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(valve_status < __rhs.valve_status)
        {
            return true;
        }
        else if(__rhs.valve_status < valve_status)
        {
            return false;
        }
        if(valve_ratio < __rhs.valve_ratio)
        {
            return true;
        }
        else if(__rhs.valve_ratio < valve_ratio)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const WaterValveStatus& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const WaterValveStatus& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const WaterValveStatus& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const WaterValveStatus& __rhs) const
    {
        return !operator<(__rhs);
    }
};

struct WaterTankStatus
{
    ::wizrobo_npu_scrubber::WaterLevel pure_watter_tank_level;
    ::wizrobo_npu_scrubber::WaterLevel polluted_water_tank_level;
    ::Ice::Int polluted_water_tank_usage_d;
    bool is_polluted_water_tank_need_disinfected;

    bool operator==(const WaterTankStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(pure_watter_tank_level != __rhs.pure_watter_tank_level)
        {
            return false;
        }
        if(polluted_water_tank_level != __rhs.polluted_water_tank_level)
        {
            return false;
        }
        if(polluted_water_tank_usage_d != __rhs.polluted_water_tank_usage_d)
        {
            return false;
        }
        if(is_polluted_water_tank_need_disinfected != __rhs.is_polluted_water_tank_need_disinfected)
        {
            return false;
        }
        return true;
    }

    bool operator<(const WaterTankStatus& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(pure_watter_tank_level < __rhs.pure_watter_tank_level)
        {
            return true;
        }
        else if(__rhs.pure_watter_tank_level < pure_watter_tank_level)
        {
            return false;
        }
        if(polluted_water_tank_level < __rhs.polluted_water_tank_level)
        {
            return true;
        }
        else if(__rhs.polluted_water_tank_level < polluted_water_tank_level)
        {
            return false;
        }
        if(polluted_water_tank_usage_d < __rhs.polluted_water_tank_usage_d)
        {
            return true;
        }
        else if(__rhs.polluted_water_tank_usage_d < polluted_water_tank_usage_d)
        {
            return false;
        }
        if(is_polluted_water_tank_need_disinfected < __rhs.is_polluted_water_tank_need_disinfected)
        {
            return true;
        }
        else if(__rhs.is_polluted_water_tank_need_disinfected < is_polluted_water_tank_need_disinfected)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const WaterTankStatus& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const WaterTankStatus& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const WaterTankStatus& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const WaterTankStatus& __rhs) const
    {
        return !operator<(__rhs);
    }
};

struct CleanerPartsStatus
{
    ::wizrobo_npu_scrubber::WaterTankStatus water_tank_status;
    bool is_broom_located;
    ::Ice::Int broom_and_carbon_brush_usage_h;
    bool should_change_new_broom;
    bool should_change_new_carbon_brush;
    ::Ice::Int filter_usage_d;
    bool should_change_new_filter;
    ::wizrobo_npu_scrubber::BreathLightStatus rolling_brush;
    ::wizrobo_npu_scrubber::Switch emergency_stop;
    ::wizrobo_npu_scrubber::Switch power_switch;
    ::wizrobo_npu_scrubber::BreathLightStatus breath_light_status;
    ::wizrobo_npu_scrubber::BlowerStatus blower_status;
    ::wizrobo_npu_scrubber::WaterValveStatus valve_status;
    ::wizrobo_npu_scrubber::Switch beep_status;
    ::wizrobo_npu_scrubber::Switch lift_motor_status;
    ::wizrobo_npu::BumperArray bump_status;
};

struct CleanerSensorData
{
    ::Ice::Float inside_temperature;
    ::Ice::Float inside_humidity;
    ::wizrobo_npu::FloatArray infrared_data;
    ::wizrobo_npu::FloatArray sonar_data;
    ::wizrobo_npu::ImuData imu_data;
};

struct CleanerPartsParam
{
    ::Ice::Int polluted_water_tank_maintenance_period_d;
    ::Ice::Int broom_lifetime_h;
    ::Ice::Int carbon_brush_lifetime_h;
    ::Ice::Int filter_lifetime_d;
    ::Ice::Int rolling_brush_lifetime_h;
    ::Ice::Int pure_water_valve_ratio;
    ::Ice::Int brush_rated_spd_rpm;
    ::Ice::Int brush_default_spd_rpm;
    ::Ice::Int blower_rated_spd_rpm;
    ::Ice::Int blower_default_spd_rpm;
    ::Ice::Float inside_temperature_threshold_cen;
    ::Ice::Float inside_humidity_threshold;
};

struct SystemStatus
{
    ::wizrobo_npu_scrubber::SystemIndicators system_report;
};

const ::Ice::Int MARSK_ALL = 0;

const ::Ice::Int MARSK_BL = 1;

const ::Ice::Int MARSK_LIFT = 2;

const ::Ice::Int MARSK_BRUSH = 4;

const ::Ice::Int MARSK_VALVE = 8;

struct ScrbCtrlPanel
{
    ::wizrobo_npu_scrubber::BreathLightStatus bl_status;
    ::wizrobo_npu_scrubber::Switch lift_status;
    ::wizrobo_npu_scrubber::Level fan_lv;
    ::wizrobo_npu_scrubber::Level brush_lv;
    ::wizrobo_npu_scrubber::Level valve_lv;

    bool operator==(const ScrbCtrlPanel& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(bl_status != __rhs.bl_status)
        {
            return false;
        }
        if(lift_status != __rhs.lift_status)
        {
            return false;
        }
        if(fan_lv != __rhs.fan_lv)
        {
            return false;
        }
        if(brush_lv != __rhs.brush_lv)
        {
            return false;
        }
        if(valve_lv != __rhs.valve_lv)
        {
            return false;
        }
        return true;
    }

    bool operator<(const ScrbCtrlPanel& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(bl_status < __rhs.bl_status)
        {
            return true;
        }
        else if(__rhs.bl_status < bl_status)
        {
            return false;
        }
        if(lift_status < __rhs.lift_status)
        {
            return true;
        }
        else if(__rhs.lift_status < lift_status)
        {
            return false;
        }
        if(fan_lv < __rhs.fan_lv)
        {
            return true;
        }
        else if(__rhs.fan_lv < fan_lv)
        {
            return false;
        }
        if(brush_lv < __rhs.brush_lv)
        {
            return true;
        }
        else if(__rhs.brush_lv < brush_lv)
        {
            return false;
        }
        if(valve_lv < __rhs.valve_lv)
        {
            return true;
        }
        else if(__rhs.valve_lv < valve_lv)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const ScrbCtrlPanel& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const ScrbCtrlPanel& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const ScrbCtrlPanel& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const ScrbCtrlPanel& __rhs) const
    {
        return !operator<(__rhs);
    }
};

}

namespace Ice
{
template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::DiagnosticsStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 6;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::WaterLevel>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 1;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::Level>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 3;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::Switch>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 1;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::BreathLightStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 5;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::SystemStatusReminder>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 9999;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::BatteryStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 50;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::BatteryStatus, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::BatteryStatus& v)
    {
        __os->write(v.current_a);
        __os->write(v.current_threshold_a);
        __os->write(v.voltage_v);
        __os->write(v.voltage_threshold_v);
        __os->write(v.temperature_cen);
        __os->write(v.temperature_threshold_cen);
        __os->write(v.design_capacity_ah);
        __os->write(v.capacity_level);
        __os->write(v.abnormal_state);
        __os->write(v.charge_discharge_time);
        __os->write(v.battery_lifetime);
        __os->write(v.accumulated_time_of_use);
        __os->write(v.should_back_to_charge);
        __os->write(v.should_change_new_battery);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::BatteryStatus, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::BatteryStatus& v)
    {
        __is->read(v.current_a);
        __is->read(v.current_threshold_a);
        __is->read(v.voltage_v);
        __is->read(v.voltage_threshold_v);
        __is->read(v.temperature_cen);
        __is->read(v.temperature_threshold_cen);
        __is->read(v.design_capacity_ah);
        __is->read(v.capacity_level);
        __is->read(v.abnormal_state);
        __is->read(v.charge_discharge_time);
        __is->read(v.battery_lifetime);
        __is->read(v.accumulated_time_of_use);
        __is->read(v.should_back_to_charge);
        __is->read(v.should_change_new_battery);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::BrushStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 11;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::BrushStatus, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::BrushStatus& v)
    {
        __os->write(v.is_rolling_brush_located);
        __os->write(v.should_change_new_rolling_brush);
        __os->write(v.rolling_brush_status);
        __os->write(v.rolling_brush_usage_h);
        __os->write(v.rotation_speed_of_rolling_brush);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::BrushStatus, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::BrushStatus& v)
    {
        __is->read(v.is_rolling_brush_located);
        __is->read(v.should_change_new_rolling_brush);
        __is->read(v.rolling_brush_status);
        __is->read(v.rolling_brush_usage_h);
        __is->read(v.rotation_speed_of_rolling_brush);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::BlowerStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 2;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::BlowerStatus, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::BlowerStatus& v)
    {
        __os->write(v.blower_switch);
        __os->write(v.blower_speed);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::BlowerStatus, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::BlowerStatus& v)
    {
        __is->read(v.blower_switch);
        __is->read(v.blower_speed);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::WaterValveStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 5;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::WaterValveStatus, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::WaterValveStatus& v)
    {
        __os->write(v.valve_status);
        __os->write(v.valve_ratio);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::WaterValveStatus, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::WaterValveStatus& v)
    {
        __is->read(v.valve_status);
        __is->read(v.valve_ratio);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::WaterTankStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 7;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::WaterTankStatus, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::WaterTankStatus& v)
    {
        __os->write(v.pure_watter_tank_level);
        __os->write(v.polluted_water_tank_level);
        __os->write(v.polluted_water_tank_usage_d);
        __os->write(v.is_polluted_water_tank_need_disinfected);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::WaterTankStatus, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::WaterTankStatus& v)
    {
        __is->read(v.pure_watter_tank_level);
        __is->read(v.polluted_water_tank_level);
        __is->read(v.polluted_water_tank_usage_d);
        __is->read(v.is_polluted_water_tank_need_disinfected);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::CleanerPartsStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 33;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::CleanerPartsStatus, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::CleanerPartsStatus& v)
    {
        __os->write(v.water_tank_status);
        __os->write(v.is_broom_located);
        __os->write(v.broom_and_carbon_brush_usage_h);
        __os->write(v.should_change_new_broom);
        __os->write(v.should_change_new_carbon_brush);
        __os->write(v.filter_usage_d);
        __os->write(v.should_change_new_filter);
        __os->write(v.rolling_brush);
        __os->write(v.emergency_stop);
        __os->write(v.power_switch);
        __os->write(v.breath_light_status);
        __os->write(v.blower_status);
        __os->write(v.valve_status);
        __os->write(v.beep_status);
        __os->write(v.lift_motor_status);
        __os->write(v.bump_status);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::CleanerPartsStatus, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::CleanerPartsStatus& v)
    {
        __is->read(v.water_tank_status);
        __is->read(v.is_broom_located);
        __is->read(v.broom_and_carbon_brush_usage_h);
        __is->read(v.should_change_new_broom);
        __is->read(v.should_change_new_carbon_brush);
        __is->read(v.filter_usage_d);
        __is->read(v.should_change_new_filter);
        __is->read(v.rolling_brush);
        __is->read(v.emergency_stop);
        __is->read(v.power_switch);
        __is->read(v.breath_light_status);
        __is->read(v.blower_status);
        __is->read(v.valve_status);
        __is->read(v.beep_status);
        __is->read(v.lift_motor_status);
        __is->read(v.bump_status);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::CleanerSensorData>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 22;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::CleanerSensorData, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::CleanerSensorData& v)
    {
        __os->write(v.inside_temperature);
        __os->write(v.inside_humidity);
        __os->write(v.infrared_data);
        __os->write(v.sonar_data);
        __os->write(v.imu_data);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::CleanerSensorData, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::CleanerSensorData& v)
    {
        __is->read(v.inside_temperature);
        __is->read(v.inside_humidity);
        __is->read(v.infrared_data);
        __is->read(v.sonar_data);
        __is->read(v.imu_data);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::CleanerPartsParam>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 48;
    static const bool fixedLength = true;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::CleanerPartsParam, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::CleanerPartsParam& v)
    {
        __os->write(v.polluted_water_tank_maintenance_period_d);
        __os->write(v.broom_lifetime_h);
        __os->write(v.carbon_brush_lifetime_h);
        __os->write(v.filter_lifetime_d);
        __os->write(v.rolling_brush_lifetime_h);
        __os->write(v.pure_water_valve_ratio);
        __os->write(v.brush_rated_spd_rpm);
        __os->write(v.brush_default_spd_rpm);
        __os->write(v.blower_rated_spd_rpm);
        __os->write(v.blower_default_spd_rpm);
        __os->write(v.inside_temperature_threshold_cen);
        __os->write(v.inside_humidity_threshold);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::CleanerPartsParam, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::CleanerPartsParam& v)
    {
        __is->read(v.polluted_water_tank_maintenance_period_d);
        __is->read(v.broom_lifetime_h);
        __is->read(v.carbon_brush_lifetime_h);
        __is->read(v.filter_lifetime_d);
        __is->read(v.rolling_brush_lifetime_h);
        __is->read(v.pure_water_valve_ratio);
        __is->read(v.brush_rated_spd_rpm);
        __is->read(v.brush_default_spd_rpm);
        __is->read(v.blower_rated_spd_rpm);
        __is->read(v.blower_default_spd_rpm);
        __is->read(v.inside_temperature_threshold_cen);
        __is->read(v.inside_humidity_threshold);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::SystemStatus>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::SystemStatus, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::SystemStatus& v)
    {
        __os->write(v.system_report);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::SystemStatus, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::SystemStatus& v)
    {
        __is->read(v.system_report);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu_scrubber::ScrbCtrlPanel>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 5;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu_scrubber::ScrbCtrlPanel, S>
{
    static void write(S* __os, const ::wizrobo_npu_scrubber::ScrbCtrlPanel& v)
    {
        __os->write(v.bl_status);
        __os->write(v.lift_status);
        __os->write(v.fan_lv);
        __os->write(v.brush_lv);
        __os->write(v.valve_lv);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu_scrubber::ScrbCtrlPanel, S>
{
    static void read(S* __is, ::wizrobo_npu_scrubber::ScrbCtrlPanel& v)
    {
        __is->read(v.bl_status);
        __is->read(v.lift_status);
        __is->read(v.fan_lv);
        __is->read(v.brush_lv);
        __is->read(v.valve_lv);
    }
};

}

namespace wizrobo_npu_scrubber
{

class Callback_ScrubberIce_SetScrbCtrlPanel_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_SetScrbCtrlPanel_Base> Callback_ScrubberIce_SetScrbCtrlPanelPtr;

class Callback_ScrubberIce_GetScrbCtrlPanel_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_GetScrbCtrlPanel_Base> Callback_ScrubberIce_GetScrbCtrlPanelPtr;

class Callback_ScrubberIce_SetTrioooCmd_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_SetTrioooCmd_Base> Callback_ScrubberIce_SetTrioooCmdPtr;

class Callback_ScrubberIce_GetBatteryStatus_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_GetBatteryStatus_Base> Callback_ScrubberIce_GetBatteryStatusPtr;

class Callback_ScrubberIce_SetBatteryParam_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_SetBatteryParam_Base> Callback_ScrubberIce_SetBatteryParamPtr;

class Callback_ScrubberIce_GetCleanerPartsStatus_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_GetCleanerPartsStatus_Base> Callback_ScrubberIce_GetCleanerPartsStatusPtr;

class Callback_ScrubberIce_GetCleanerSensorData_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_GetCleanerSensorData_Base> Callback_ScrubberIce_GetCleanerSensorDataPtr;

class Callback_ScrubberIce_GetCleanerPartsParam_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_GetCleanerPartsParam_Base> Callback_ScrubberIce_GetCleanerPartsParamPtr;

class Callback_ScrubberIce_GetSystemStatus_Base : virtual public ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ScrubberIce_GetSystemStatus_Base> Callback_ScrubberIce_GetSystemStatusPtr;

}

namespace IceProxy
{

namespace wizrobo_npu_scrubber
{

class ScrubberIce : virtual public ::IceProxy::Ice::Object
{
public:

    void SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask)
    {
        SetScrbCtrlPanel(panel, mask, 0);
    }
    void SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx)
    {
        SetScrbCtrlPanel(panel, mask, &__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_SetScrbCtrlPanel(panel, mask, 0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_SetScrbCtrlPanel(panel, mask, 0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_SetScrbCtrlPanel(panel, mask, &__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_SetScrbCtrlPanel(panel, mask, &__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void ()>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                try
                {
                    __proxy->end_SetScrbCtrlPanel(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response();
                }
            }
        
        private:
            
            ::std::function<void ()> _response;
        };
        return begin_SetScrbCtrlPanel(panel, mask, __ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask)
    {
        return begin_SetScrbCtrlPanel(panel, mask, 0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx)
    {
        return begin_SetScrbCtrlPanel(panel, mask, &__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetScrbCtrlPanel(panel, mask, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetScrbCtrlPanel(panel, mask, &__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_SetScrbCtrlPanelPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetScrbCtrlPanel(panel, mask, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_SetScrbCtrlPanelPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetScrbCtrlPanel(panel, mask, &__ctx, __del, __cookie);
    }

    void end_SetScrbCtrlPanel(const ::Ice::AsyncResultPtr&);
    
private:

    void SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    void GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask)
    {
        GetScrbCtrlPanel(panel, mask, 0);
    }
    void GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx)
    {
        GetScrbCtrlPanel(panel, mask, &__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetScrbCtrlPanel(panel, mask, 0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetScrbCtrlPanel(panel, mask, 0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetScrbCtrlPanel(panel, mask, &__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetScrbCtrlPanel(panel, mask, &__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void ()>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                try
                {
                    __proxy->end_GetScrbCtrlPanel(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response();
                }
            }
        
        private:
            
            ::std::function<void ()> _response;
        };
        return begin_GetScrbCtrlPanel(panel, mask, __ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask)
    {
        return begin_GetScrbCtrlPanel(panel, mask, 0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx)
    {
        return begin_GetScrbCtrlPanel(panel, mask, &__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetScrbCtrlPanel(panel, mask, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetScrbCtrlPanel(panel, mask, &__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetScrbCtrlPanelPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetScrbCtrlPanel(panel, mask, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context& __ctx, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetScrbCtrlPanelPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetScrbCtrlPanel(panel, mask, &__ctx, __del, __cookie);
    }

    void end_GetScrbCtrlPanel(const ::Ice::AsyncResultPtr&);
    
private:

    void GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    void SetTrioooCmd(::Ice::Int cmd)
    {
        SetTrioooCmd(cmd, 0);
    }
    void SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context& __ctx)
    {
        SetTrioooCmd(cmd, &__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_SetTrioooCmd(::Ice::Int cmd, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_SetTrioooCmd(cmd, 0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_SetTrioooCmd(::Ice::Int cmd, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_SetTrioooCmd(cmd, 0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context& __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_SetTrioooCmd(cmd, &__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_SetTrioooCmd(cmd, &__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context* __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void ()>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                try
                {
                    __proxy->end_SetTrioooCmd(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response();
                }
            }
        
        private:
            
            ::std::function<void ()> _response;
        };
        return begin_SetTrioooCmd(cmd, __ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_SetTrioooCmd(::Ice::Int cmd)
    {
        return begin_SetTrioooCmd(cmd, 0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context& __ctx)
    {
        return begin_SetTrioooCmd(cmd, &__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_SetTrioooCmd(::Ice::Int cmd, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetTrioooCmd(cmd, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetTrioooCmd(cmd, &__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetTrioooCmd(::Ice::Int cmd, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_SetTrioooCmdPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetTrioooCmd(cmd, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context& __ctx, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_SetTrioooCmdPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetTrioooCmd(cmd, &__ctx, __del, __cookie);
    }

    void end_SetTrioooCmd(const ::Ice::AsyncResultPtr&);
    
private:

    void SetTrioooCmd(::Ice::Int, const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_SetTrioooCmd(::Ice::Int, const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    ::wizrobo_npu_scrubber::BatteryStatus GetBatteryStatus()
    {
        return GetBatteryStatus(0);
    }
    ::wizrobo_npu_scrubber::BatteryStatus GetBatteryStatus(const ::Ice::Context& __ctx)
    {
        return GetBatteryStatus(&__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_GetBatteryStatus(const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::BatteryStatus&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetBatteryStatus(0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetBatteryStatus(const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetBatteryStatus(0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_GetBatteryStatus(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::BatteryStatus&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetBatteryStatus(&__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetBatteryStatus(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetBatteryStatus(&__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_GetBatteryStatus(const ::Ice::Context* __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::BatteryStatus&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void (const ::wizrobo_npu_scrubber::BatteryStatus&)>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                ::wizrobo_npu_scrubber::BatteryStatus __ret;
                try
                {
                    __ret = __proxy->end_GetBatteryStatus(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response(__ret);
                }
            }
        
        private:
            
            ::std::function<void (const ::wizrobo_npu_scrubber::BatteryStatus&)> _response;
        };
        return begin_GetBatteryStatus(__ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_GetBatteryStatus()
    {
        return begin_GetBatteryStatus(0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetBatteryStatus(const ::Ice::Context& __ctx)
    {
        return begin_GetBatteryStatus(&__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetBatteryStatus(const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetBatteryStatus(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetBatteryStatus(const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetBatteryStatus(&__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetBatteryStatus(const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetBatteryStatusPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetBatteryStatus(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetBatteryStatus(const ::Ice::Context& __ctx, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetBatteryStatusPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetBatteryStatus(&__ctx, __del, __cookie);
    }

    ::wizrobo_npu_scrubber::BatteryStatus end_GetBatteryStatus(const ::Ice::AsyncResultPtr&);
    
private:

    ::wizrobo_npu_scrubber::BatteryStatus GetBatteryStatus(const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_GetBatteryStatus(const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    void SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param)
    {
        SetBatteryParam(param, 0);
    }
    void SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context& __ctx)
    {
        SetBatteryParam(param, &__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_SetBatteryParam(param, 0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_SetBatteryParam(param, 0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context& __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_SetBatteryParam(param, &__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_SetBatteryParam(param, &__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context* __ctx, const ::IceInternal::Function<void ()>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void ()>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                try
                {
                    __proxy->end_SetBatteryParam(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response();
                }
            }
        
        private:
            
            ::std::function<void ()> _response;
        };
        return begin_SetBatteryParam(param, __ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param)
    {
        return begin_SetBatteryParam(param, 0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context& __ctx)
    {
        return begin_SetBatteryParam(param, &__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetBatteryParam(param, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetBatteryParam(param, &__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_SetBatteryParamPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetBatteryParam(param, 0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context& __ctx, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_SetBatteryParamPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_SetBatteryParam(param, &__ctx, __del, __cookie);
    }

    void end_SetBatteryParam(const ::Ice::AsyncResultPtr&);
    
private:

    void SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus&, const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus&, const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    ::wizrobo_npu_scrubber::CleanerPartsStatus GetCleanerPartsStatus()
    {
        return GetCleanerPartsStatus(0);
    }
    ::wizrobo_npu_scrubber::CleanerPartsStatus GetCleanerPartsStatus(const ::Ice::Context& __ctx)
    {
        return GetCleanerPartsStatus(&__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_GetCleanerPartsStatus(const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::CleanerPartsStatus&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetCleanerPartsStatus(0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetCleanerPartsStatus(const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetCleanerPartsStatus(0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_GetCleanerPartsStatus(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::CleanerPartsStatus&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetCleanerPartsStatus(&__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetCleanerPartsStatus(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetCleanerPartsStatus(&__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_GetCleanerPartsStatus(const ::Ice::Context* __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::CleanerPartsStatus&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void (const ::wizrobo_npu_scrubber::CleanerPartsStatus&)>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                ::wizrobo_npu_scrubber::CleanerPartsStatus __ret;
                try
                {
                    __ret = __proxy->end_GetCleanerPartsStatus(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response(__ret);
                }
            }
        
        private:
            
            ::std::function<void (const ::wizrobo_npu_scrubber::CleanerPartsStatus&)> _response;
        };
        return begin_GetCleanerPartsStatus(__ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_GetCleanerPartsStatus()
    {
        return begin_GetCleanerPartsStatus(0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsStatus(const ::Ice::Context& __ctx)
    {
        return begin_GetCleanerPartsStatus(&__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsStatus(const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerPartsStatus(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsStatus(const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerPartsStatus(&__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsStatus(const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetCleanerPartsStatusPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerPartsStatus(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsStatus(const ::Ice::Context& __ctx, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetCleanerPartsStatusPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerPartsStatus(&__ctx, __del, __cookie);
    }

    ::wizrobo_npu_scrubber::CleanerPartsStatus end_GetCleanerPartsStatus(const ::Ice::AsyncResultPtr&);
    
private:

    ::wizrobo_npu_scrubber::CleanerPartsStatus GetCleanerPartsStatus(const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_GetCleanerPartsStatus(const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    ::wizrobo_npu_scrubber::CleanerSensorData GetCleanerSensorData()
    {
        return GetCleanerSensorData(0);
    }
    ::wizrobo_npu_scrubber::CleanerSensorData GetCleanerSensorData(const ::Ice::Context& __ctx)
    {
        return GetCleanerSensorData(&__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_GetCleanerSensorData(const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::CleanerSensorData&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetCleanerSensorData(0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetCleanerSensorData(const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetCleanerSensorData(0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_GetCleanerSensorData(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::CleanerSensorData&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetCleanerSensorData(&__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetCleanerSensorData(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetCleanerSensorData(&__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_GetCleanerSensorData(const ::Ice::Context* __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::CleanerSensorData&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void (const ::wizrobo_npu_scrubber::CleanerSensorData&)>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                ::wizrobo_npu_scrubber::CleanerSensorData __ret;
                try
                {
                    __ret = __proxy->end_GetCleanerSensorData(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response(__ret);
                }
            }
        
        private:
            
            ::std::function<void (const ::wizrobo_npu_scrubber::CleanerSensorData&)> _response;
        };
        return begin_GetCleanerSensorData(__ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_GetCleanerSensorData()
    {
        return begin_GetCleanerSensorData(0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerSensorData(const ::Ice::Context& __ctx)
    {
        return begin_GetCleanerSensorData(&__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerSensorData(const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerSensorData(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerSensorData(const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerSensorData(&__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerSensorData(const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetCleanerSensorDataPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerSensorData(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerSensorData(const ::Ice::Context& __ctx, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetCleanerSensorDataPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerSensorData(&__ctx, __del, __cookie);
    }

    ::wizrobo_npu_scrubber::CleanerSensorData end_GetCleanerSensorData(const ::Ice::AsyncResultPtr&);
    
private:

    ::wizrobo_npu_scrubber::CleanerSensorData GetCleanerSensorData(const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_GetCleanerSensorData(const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    ::wizrobo_npu_scrubber::CleanerPartsParam GetCleanerPartsParam()
    {
        return GetCleanerPartsParam(0);
    }
    ::wizrobo_npu_scrubber::CleanerPartsParam GetCleanerPartsParam(const ::Ice::Context& __ctx)
    {
        return GetCleanerPartsParam(&__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_GetCleanerPartsParam(const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::CleanerPartsParam&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetCleanerPartsParam(0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetCleanerPartsParam(const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetCleanerPartsParam(0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_GetCleanerPartsParam(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::CleanerPartsParam&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetCleanerPartsParam(&__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetCleanerPartsParam(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetCleanerPartsParam(&__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_GetCleanerPartsParam(const ::Ice::Context* __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::CleanerPartsParam&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void (const ::wizrobo_npu_scrubber::CleanerPartsParam&)>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                ::wizrobo_npu_scrubber::CleanerPartsParam __ret;
                try
                {
                    __ret = __proxy->end_GetCleanerPartsParam(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response(__ret);
                }
            }
        
        private:
            
            ::std::function<void (const ::wizrobo_npu_scrubber::CleanerPartsParam&)> _response;
        };
        return begin_GetCleanerPartsParam(__ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_GetCleanerPartsParam()
    {
        return begin_GetCleanerPartsParam(0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsParam(const ::Ice::Context& __ctx)
    {
        return begin_GetCleanerPartsParam(&__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsParam(const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerPartsParam(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsParam(const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerPartsParam(&__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsParam(const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetCleanerPartsParamPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerPartsParam(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetCleanerPartsParam(const ::Ice::Context& __ctx, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetCleanerPartsParamPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetCleanerPartsParam(&__ctx, __del, __cookie);
    }

    ::wizrobo_npu_scrubber::CleanerPartsParam end_GetCleanerPartsParam(const ::Ice::AsyncResultPtr&);
    
private:

    ::wizrobo_npu_scrubber::CleanerPartsParam GetCleanerPartsParam(const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_GetCleanerPartsParam(const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:

    ::wizrobo_npu_scrubber::SystemStatus GetSystemStatus()
    {
        return GetSystemStatus(0);
    }
    ::wizrobo_npu_scrubber::SystemStatus GetSystemStatus(const ::Ice::Context& __ctx)
    {
        return GetSystemStatus(&__ctx);
    }
#ifdef ICE_CPP11
    ::Ice::AsyncResultPtr
    begin_GetSystemStatus(const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::SystemStatus&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetSystemStatus(0, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetSystemStatus(const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetSystemStatus(0, ::Ice::newCallback(__completed, __sent), 0);
    }
    ::Ice::AsyncResultPtr
    begin_GetSystemStatus(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::SystemStatus&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception = ::IceInternal::Function<void (const ::Ice::Exception&)>(), const ::IceInternal::Function<void (bool)>& __sent = ::IceInternal::Function<void (bool)>())
    {
        return __begin_GetSystemStatus(&__ctx, __response, __exception, __sent);
    }
    ::Ice::AsyncResultPtr
    begin_GetSystemStatus(const ::Ice::Context& __ctx, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __completed, const ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>& __sent = ::IceInternal::Function<void (const ::Ice::AsyncResultPtr&)>())
    {
        return begin_GetSystemStatus(&__ctx, ::Ice::newCallback(__completed, __sent));
    }
    
private:

    ::Ice::AsyncResultPtr __begin_GetSystemStatus(const ::Ice::Context* __ctx, const ::IceInternal::Function<void (const ::wizrobo_npu_scrubber::SystemStatus&)>& __response, const ::IceInternal::Function<void (const ::Ice::Exception&)>& __exception, const ::IceInternal::Function<void (bool)>& __sent)
    {
        class Cpp11CB : public ::IceInternal::Cpp11FnCallbackNC
        {
        public:

            Cpp11CB(const ::std::function<void (const ::wizrobo_npu_scrubber::SystemStatus&)>& responseFunc, const ::std::function<void (const ::Ice::Exception&)>& exceptionFunc, const ::std::function<void (bool)>& sentFunc) :
                ::IceInternal::Cpp11FnCallbackNC(exceptionFunc, sentFunc),
                _response(responseFunc)
            {
                CallbackBase::checkCallback(true, responseFunc || exceptionFunc != nullptr);
            }

            virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
            {
                ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
                ::wizrobo_npu_scrubber::SystemStatus __ret;
                try
                {
                    __ret = __proxy->end_GetSystemStatus(__result);
                }
                catch(::Ice::Exception& ex)
                {
                    Cpp11FnCallbackNC::__exception(__result, ex);
                    return;
                }
                if(_response != nullptr)
                {
                    _response(__ret);
                }
            }
        
        private:
            
            ::std::function<void (const ::wizrobo_npu_scrubber::SystemStatus&)> _response;
        };
        return begin_GetSystemStatus(__ctx, new Cpp11CB(__response, __exception, __sent));
    }
    
public:
#endif

    ::Ice::AsyncResultPtr begin_GetSystemStatus()
    {
        return begin_GetSystemStatus(0, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetSystemStatus(const ::Ice::Context& __ctx)
    {
        return begin_GetSystemStatus(&__ctx, ::IceInternal::__dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_GetSystemStatus(const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetSystemStatus(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetSystemStatus(const ::Ice::Context& __ctx, const ::Ice::CallbackPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetSystemStatus(&__ctx, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetSystemStatus(const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetSystemStatusPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetSystemStatus(0, __del, __cookie);
    }

    ::Ice::AsyncResultPtr begin_GetSystemStatus(const ::Ice::Context& __ctx, const ::wizrobo_npu_scrubber::Callback_ScrubberIce_GetSystemStatusPtr& __del, const ::Ice::LocalObjectPtr& __cookie = 0)
    {
        return begin_GetSystemStatus(&__ctx, __del, __cookie);
    }

    ::wizrobo_npu_scrubber::SystemStatus end_GetSystemStatus(const ::Ice::AsyncResultPtr&);
    
private:

    ::wizrobo_npu_scrubber::SystemStatus GetSystemStatus(const ::Ice::Context*);
    ::Ice::AsyncResultPtr begin_GetSystemStatus(const ::Ice::Context*, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& __cookie = 0);
    
public:
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_context(const ::Ice::Context& __context) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_context(__context).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_adapterId(const ::std::string& __id) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_adapterId(__id).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_endpoints(const ::Ice::EndpointSeq& __endpoints) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_endpoints(__endpoints).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_locatorCacheTimeout(int __timeout) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_locatorCacheTimeout(__timeout).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_connectionCached(bool __cached) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_connectionCached(__cached).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_endpointSelection(::Ice::EndpointSelectionType __est) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_endpointSelection(__est).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_secure(bool __secure) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_secure(__secure).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_preferSecure(bool __preferSecure) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_preferSecure(__preferSecure).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_router(const ::Ice::RouterPrx& __router) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_router(__router).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_locator(const ::Ice::LocatorPrx& __locator) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_locator(__locator).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_collocationOptimized(bool __co) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_collocationOptimized(__co).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_twoway() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_twoway().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_oneway() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_oneway().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_batchOneway() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_batchOneway().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_datagram() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_datagram().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_batchDatagram() const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_batchDatagram().get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_compress(bool __compress) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_compress(__compress).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_timeout(int __timeout) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_timeout(__timeout).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_connectionId(const ::std::string& __id) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_connectionId(__id).get());
    }
    
    ::IceInternal::ProxyHandle<ScrubberIce> ice_encodingVersion(const ::Ice::EncodingVersion& __v) const
    {
        return dynamic_cast<ScrubberIce*>(::IceProxy::Ice::Object::ice_encodingVersion(__v).get());
    }
    
    static const ::std::string& ice_staticId();

private: 

    virtual ::IceInternal::Handle< ::IceDelegateM::Ice::Object> __createDelegateM();
    virtual ::IceInternal::Handle< ::IceDelegateD::Ice::Object> __createDelegateD();
    virtual ::IceProxy::Ice::Object* __newInstance() const;
};

}

}

namespace IceDelegate
{

namespace wizrobo_npu_scrubber
{

class ScrubberIce : virtual public ::IceDelegate::Ice::Object
{
public:

    virtual void SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual void GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual void SetTrioooCmd(::Ice::Int, const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual ::wizrobo_npu_scrubber::BatteryStatus GetBatteryStatus(const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual void SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus&, const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual ::wizrobo_npu_scrubber::CleanerPartsStatus GetCleanerPartsStatus(const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual ::wizrobo_npu_scrubber::CleanerSensorData GetCleanerSensorData(const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual ::wizrobo_npu_scrubber::CleanerPartsParam GetCleanerPartsParam(const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;

    virtual ::wizrobo_npu_scrubber::SystemStatus GetSystemStatus(const ::Ice::Context*, ::IceInternal::InvocationObserver&) = 0;
};

}

}

namespace IceDelegateM
{

namespace wizrobo_npu_scrubber
{

class ScrubberIce : virtual public ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce,
                    virtual public ::IceDelegateM::Ice::Object
{
public:

    virtual void SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual void GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual void SetTrioooCmd(::Ice::Int, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::BatteryStatus GetBatteryStatus(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual void SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus&, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::CleanerPartsStatus GetCleanerPartsStatus(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::CleanerSensorData GetCleanerSensorData(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::CleanerPartsParam GetCleanerPartsParam(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::SystemStatus GetSystemStatus(const ::Ice::Context*, ::IceInternal::InvocationObserver&);
};

}

}

namespace IceDelegateD
{

namespace wizrobo_npu_scrubber
{

class ScrubberIce : virtual public ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce,
                    virtual public ::IceDelegateD::Ice::Object
{
public:

    virtual void SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual void GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual void SetTrioooCmd(::Ice::Int, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::BatteryStatus GetBatteryStatus(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual void SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus&, const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::CleanerPartsStatus GetCleanerPartsStatus(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::CleanerSensorData GetCleanerSensorData(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::CleanerPartsParam GetCleanerPartsParam(const ::Ice::Context*, ::IceInternal::InvocationObserver&);

    virtual ::wizrobo_npu_scrubber::SystemStatus GetSystemStatus(const ::Ice::Context*, ::IceInternal::InvocationObserver&);
};

}

}

namespace wizrobo_npu_scrubber
{

class ScrubberIce : virtual public ::Ice::Object
{
public:

    typedef ScrubberIcePrx ProxyType;
    typedef ScrubberIcePtr PointerType;

    virtual bool ice_isA(const ::std::string&, const ::Ice::Current& = ::Ice::Current()) const;
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& = ::Ice::Current()) const;
    virtual const ::std::string& ice_id(const ::Ice::Current& = ::Ice::Current()) const;
    static const ::std::string& ice_staticId();

    virtual void SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___SetScrbCtrlPanel(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual void GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel&, ::Ice::Int, const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___GetScrbCtrlPanel(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual void SetTrioooCmd(::Ice::Int, const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___SetTrioooCmd(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual ::wizrobo_npu_scrubber::BatteryStatus GetBatteryStatus(const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___GetBatteryStatus(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual void SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus&, const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___SetBatteryParam(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual ::wizrobo_npu_scrubber::CleanerPartsStatus GetCleanerPartsStatus(const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___GetCleanerPartsStatus(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual ::wizrobo_npu_scrubber::CleanerSensorData GetCleanerSensorData(const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___GetCleanerSensorData(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual ::wizrobo_npu_scrubber::CleanerPartsParam GetCleanerPartsParam(const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___GetCleanerPartsParam(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual ::wizrobo_npu_scrubber::SystemStatus GetSystemStatus(const ::Ice::Current& = ::Ice::Current()) = 0;
    ::Ice::DispatchStatus ___GetSystemStatus(::IceInternal::Incoming&, const ::Ice::Current&);

    virtual ::Ice::DispatchStatus __dispatch(::IceInternal::Incoming&, const ::Ice::Current&);

protected:
    virtual void __writeImpl(::IceInternal::BasicStream*) const;
    virtual void __readImpl(::IceInternal::BasicStream*);
    #ifdef __SUNPRO_CC
    using ::Ice::Object::__writeImpl;
    using ::Ice::Object::__readImpl;
    #endif
};

inline bool operator==(const ScrubberIce& l, const ScrubberIce& r)
{
    return static_cast<const ::Ice::Object&>(l) == static_cast<const ::Ice::Object&>(r);
}

inline bool operator<(const ScrubberIce& l, const ScrubberIce& r)
{
    return static_cast<const ::Ice::Object&>(l) < static_cast<const ::Ice::Object&>(r);
}

}

namespace wizrobo_npu_scrubber
{

template<class T>
class CallbackNC_ScrubberIce_SetScrbCtrlPanel : public Callback_ScrubberIce_SetScrbCtrlPanel_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ScrubberIce_SetScrbCtrlPanel(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_SetScrbCtrlPanel(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)();
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_SetScrbCtrlPanelPtr
newCallback_ScrubberIce_SetScrbCtrlPanel(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetScrbCtrlPanel<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetScrbCtrlPanelPtr
newCallback_ScrubberIce_SetScrbCtrlPanel(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetScrbCtrlPanel<T>(instance, 0, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetScrbCtrlPanelPtr
newCallback_ScrubberIce_SetScrbCtrlPanel(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetScrbCtrlPanel<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetScrbCtrlPanelPtr
newCallback_ScrubberIce_SetScrbCtrlPanel(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetScrbCtrlPanel<T>(instance, 0, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_SetScrbCtrlPanel : public Callback_ScrubberIce_SetScrbCtrlPanel_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ScrubberIce_SetScrbCtrlPanel(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_SetScrbCtrlPanel(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_SetScrbCtrlPanelPtr
newCallback_ScrubberIce_SetScrbCtrlPanel(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetScrbCtrlPanel<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetScrbCtrlPanelPtr
newCallback_ScrubberIce_SetScrbCtrlPanel(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetScrbCtrlPanel<T, CT>(instance, 0, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetScrbCtrlPanelPtr
newCallback_ScrubberIce_SetScrbCtrlPanel(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetScrbCtrlPanel<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetScrbCtrlPanelPtr
newCallback_ScrubberIce_SetScrbCtrlPanel(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetScrbCtrlPanel<T, CT>(instance, 0, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_GetScrbCtrlPanel : public Callback_ScrubberIce_GetScrbCtrlPanel_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ScrubberIce_GetScrbCtrlPanel(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_GetScrbCtrlPanel(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)();
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_GetScrbCtrlPanelPtr
newCallback_ScrubberIce_GetScrbCtrlPanel(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetScrbCtrlPanel<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetScrbCtrlPanelPtr
newCallback_ScrubberIce_GetScrbCtrlPanel(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetScrbCtrlPanel<T>(instance, 0, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetScrbCtrlPanelPtr
newCallback_ScrubberIce_GetScrbCtrlPanel(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetScrbCtrlPanel<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetScrbCtrlPanelPtr
newCallback_ScrubberIce_GetScrbCtrlPanel(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetScrbCtrlPanel<T>(instance, 0, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_GetScrbCtrlPanel : public Callback_ScrubberIce_GetScrbCtrlPanel_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ScrubberIce_GetScrbCtrlPanel(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_GetScrbCtrlPanel(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_GetScrbCtrlPanelPtr
newCallback_ScrubberIce_GetScrbCtrlPanel(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetScrbCtrlPanel<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetScrbCtrlPanelPtr
newCallback_ScrubberIce_GetScrbCtrlPanel(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetScrbCtrlPanel<T, CT>(instance, 0, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetScrbCtrlPanelPtr
newCallback_ScrubberIce_GetScrbCtrlPanel(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetScrbCtrlPanel<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetScrbCtrlPanelPtr
newCallback_ScrubberIce_GetScrbCtrlPanel(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetScrbCtrlPanel<T, CT>(instance, 0, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_SetTrioooCmd : public Callback_ScrubberIce_SetTrioooCmd_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ScrubberIce_SetTrioooCmd(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_SetTrioooCmd(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)();
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_SetTrioooCmdPtr
newCallback_ScrubberIce_SetTrioooCmd(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetTrioooCmd<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetTrioooCmdPtr
newCallback_ScrubberIce_SetTrioooCmd(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetTrioooCmd<T>(instance, 0, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetTrioooCmdPtr
newCallback_ScrubberIce_SetTrioooCmd(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetTrioooCmd<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetTrioooCmdPtr
newCallback_ScrubberIce_SetTrioooCmd(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetTrioooCmd<T>(instance, 0, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_SetTrioooCmd : public Callback_ScrubberIce_SetTrioooCmd_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ScrubberIce_SetTrioooCmd(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_SetTrioooCmd(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_SetTrioooCmdPtr
newCallback_ScrubberIce_SetTrioooCmd(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetTrioooCmd<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetTrioooCmdPtr
newCallback_ScrubberIce_SetTrioooCmd(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetTrioooCmd<T, CT>(instance, 0, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetTrioooCmdPtr
newCallback_ScrubberIce_SetTrioooCmd(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetTrioooCmd<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetTrioooCmdPtr
newCallback_ScrubberIce_SetTrioooCmd(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetTrioooCmd<T, CT>(instance, 0, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_GetBatteryStatus : public Callback_ScrubberIce_GetBatteryStatus_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::BatteryStatus&);

    CallbackNC_ScrubberIce_GetBatteryStatus(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::BatteryStatus __ret;
        try
        {
            __ret = __proxy->end_GetBatteryStatus(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)(__ret);
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_GetBatteryStatusPtr
newCallback_ScrubberIce_GetBatteryStatus(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::BatteryStatus&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetBatteryStatus<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetBatteryStatusPtr
newCallback_ScrubberIce_GetBatteryStatus(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::BatteryStatus&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetBatteryStatus<T>(instance, cb, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_GetBatteryStatus : public Callback_ScrubberIce_GetBatteryStatus_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::BatteryStatus&, const CT&);

    Callback_ScrubberIce_GetBatteryStatus(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::BatteryStatus __ret;
        try
        {
            __ret = __proxy->end_GetBatteryStatus(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(__ret, CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_GetBatteryStatusPtr
newCallback_ScrubberIce_GetBatteryStatus(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::BatteryStatus&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetBatteryStatus<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetBatteryStatusPtr
newCallback_ScrubberIce_GetBatteryStatus(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::BatteryStatus&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetBatteryStatus<T, CT>(instance, cb, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_SetBatteryParam : public Callback_ScrubberIce_SetBatteryParam_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ScrubberIce_SetBatteryParam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_SetBatteryParam(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)();
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_SetBatteryParamPtr
newCallback_ScrubberIce_SetBatteryParam(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetBatteryParam<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetBatteryParamPtr
newCallback_ScrubberIce_SetBatteryParam(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetBatteryParam<T>(instance, 0, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetBatteryParamPtr
newCallback_ScrubberIce_SetBatteryParam(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetBatteryParam<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_SetBatteryParamPtr
newCallback_ScrubberIce_SetBatteryParam(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_SetBatteryParam<T>(instance, 0, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_SetBatteryParam : public Callback_ScrubberIce_SetBatteryParam_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ScrubberIce_SetBatteryParam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        try
        {
            __proxy->end_SetBatteryParam(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_SetBatteryParamPtr
newCallback_ScrubberIce_SetBatteryParam(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetBatteryParam<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetBatteryParamPtr
newCallback_ScrubberIce_SetBatteryParam(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetBatteryParam<T, CT>(instance, 0, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetBatteryParamPtr
newCallback_ScrubberIce_SetBatteryParam(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetBatteryParam<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_SetBatteryParamPtr
newCallback_ScrubberIce_SetBatteryParam(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_SetBatteryParam<T, CT>(instance, 0, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_GetCleanerPartsStatus : public Callback_ScrubberIce_GetCleanerPartsStatus_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::CleanerPartsStatus&);

    CallbackNC_ScrubberIce_GetCleanerPartsStatus(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::CleanerPartsStatus __ret;
        try
        {
            __ret = __proxy->end_GetCleanerPartsStatus(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)(__ret);
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_GetCleanerPartsStatusPtr
newCallback_ScrubberIce_GetCleanerPartsStatus(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerPartsStatus&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetCleanerPartsStatus<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetCleanerPartsStatusPtr
newCallback_ScrubberIce_GetCleanerPartsStatus(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerPartsStatus&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetCleanerPartsStatus<T>(instance, cb, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_GetCleanerPartsStatus : public Callback_ScrubberIce_GetCleanerPartsStatus_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::CleanerPartsStatus&, const CT&);

    Callback_ScrubberIce_GetCleanerPartsStatus(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::CleanerPartsStatus __ret;
        try
        {
            __ret = __proxy->end_GetCleanerPartsStatus(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(__ret, CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_GetCleanerPartsStatusPtr
newCallback_ScrubberIce_GetCleanerPartsStatus(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerPartsStatus&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetCleanerPartsStatus<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetCleanerPartsStatusPtr
newCallback_ScrubberIce_GetCleanerPartsStatus(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerPartsStatus&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetCleanerPartsStatus<T, CT>(instance, cb, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_GetCleanerSensorData : public Callback_ScrubberIce_GetCleanerSensorData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::CleanerSensorData&);

    CallbackNC_ScrubberIce_GetCleanerSensorData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::CleanerSensorData __ret;
        try
        {
            __ret = __proxy->end_GetCleanerSensorData(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)(__ret);
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_GetCleanerSensorDataPtr
newCallback_ScrubberIce_GetCleanerSensorData(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerSensorData&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetCleanerSensorData<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetCleanerSensorDataPtr
newCallback_ScrubberIce_GetCleanerSensorData(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerSensorData&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetCleanerSensorData<T>(instance, cb, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_GetCleanerSensorData : public Callback_ScrubberIce_GetCleanerSensorData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::CleanerSensorData&, const CT&);

    Callback_ScrubberIce_GetCleanerSensorData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::CleanerSensorData __ret;
        try
        {
            __ret = __proxy->end_GetCleanerSensorData(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(__ret, CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_GetCleanerSensorDataPtr
newCallback_ScrubberIce_GetCleanerSensorData(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerSensorData&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetCleanerSensorData<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetCleanerSensorDataPtr
newCallback_ScrubberIce_GetCleanerSensorData(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerSensorData&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetCleanerSensorData<T, CT>(instance, cb, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_GetCleanerPartsParam : public Callback_ScrubberIce_GetCleanerPartsParam_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::CleanerPartsParam&);

    CallbackNC_ScrubberIce_GetCleanerPartsParam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::CleanerPartsParam __ret;
        try
        {
            __ret = __proxy->end_GetCleanerPartsParam(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)(__ret);
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_GetCleanerPartsParamPtr
newCallback_ScrubberIce_GetCleanerPartsParam(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerPartsParam&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetCleanerPartsParam<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetCleanerPartsParamPtr
newCallback_ScrubberIce_GetCleanerPartsParam(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerPartsParam&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetCleanerPartsParam<T>(instance, cb, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_GetCleanerPartsParam : public Callback_ScrubberIce_GetCleanerPartsParam_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::CleanerPartsParam&, const CT&);

    Callback_ScrubberIce_GetCleanerPartsParam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::CleanerPartsParam __ret;
        try
        {
            __ret = __proxy->end_GetCleanerPartsParam(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(__ret, CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_GetCleanerPartsParamPtr
newCallback_ScrubberIce_GetCleanerPartsParam(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerPartsParam&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetCleanerPartsParam<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetCleanerPartsParamPtr
newCallback_ScrubberIce_GetCleanerPartsParam(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::CleanerPartsParam&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetCleanerPartsParam<T, CT>(instance, cb, excb, sentcb);
}

template<class T>
class CallbackNC_ScrubberIce_GetSystemStatus : public Callback_ScrubberIce_GetSystemStatus_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::SystemStatus&);

    CallbackNC_ScrubberIce_GetSystemStatus(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::SystemStatus __ret;
        try
        {
            __ret = __proxy->end_GetSystemStatus(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::CallbackNC<T>::callback.get()->*response)(__ret);
        }
    }

    Response response;
};

template<class T> Callback_ScrubberIce_GetSystemStatusPtr
newCallback_ScrubberIce_GetSystemStatus(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::SystemStatus&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetSystemStatus<T>(instance, cb, excb, sentcb);
}

template<class T> Callback_ScrubberIce_GetSystemStatusPtr
newCallback_ScrubberIce_GetSystemStatus(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::SystemStatus&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ScrubberIce_GetSystemStatus<T>(instance, cb, excb, sentcb);
}

template<class T, typename CT>
class Callback_ScrubberIce_GetSystemStatus : public Callback_ScrubberIce_GetSystemStatus_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const ::wizrobo_npu_scrubber::SystemStatus&, const CT&);

    Callback_ScrubberIce_GetSystemStatus(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), response(cb)
    {
    }

    virtual void __completed(const ::Ice::AsyncResultPtr& __result) const
    {
        ::wizrobo_npu_scrubber::ScrubberIcePrx __proxy = ::wizrobo_npu_scrubber::ScrubberIcePrx::uncheckedCast(__result->getProxy());
        ::wizrobo_npu_scrubber::SystemStatus __ret;
        try
        {
            __ret = __proxy->end_GetSystemStatus(__result);
        }
        catch(::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::__exception(__result, ex);
            return;
        }
        if(response)
        {
            (::IceInternal::Callback<T, CT>::callback.get()->*response)(__ret, CT::dynamicCast(__result->getCookie()));
        }
    }

    Response response;
};

template<class T, typename CT> Callback_ScrubberIce_GetSystemStatusPtr
newCallback_ScrubberIce_GetSystemStatus(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::wizrobo_npu_scrubber::SystemStatus&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetSystemStatus<T, CT>(instance, cb, excb, sentcb);
}

template<class T, typename CT> Callback_ScrubberIce_GetSystemStatusPtr
newCallback_ScrubberIce_GetSystemStatus(T* instance, void (T::*cb)(const ::wizrobo_npu_scrubber::SystemStatus&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ScrubberIce_GetSystemStatus<T, CT>(instance, cb, excb, sentcb);
}

}

#endif
