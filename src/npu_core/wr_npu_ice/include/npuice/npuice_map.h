// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_map.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __npuice_map_h__
#define __npuice_map_h__

#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <IceUtil/ScopedArray.h>
#include <IceUtil/Optional.h>
#include <npuice_geometry.h>
#include <npuice_data.h>
#include <Ice/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace wizrobo_npu
{

enum StationType
{
    START,
    CHARGER,
    USER_DEFINED
};

struct StationInfo
{
    ::std::string map_id;
    ::std::string id;
    ::wizrobo_npu::StationType type;
    ::Ice::Int artag_id;

    bool operator==(const StationInfo& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(map_id != __rhs.map_id)
        {
            return false;
        }
        if(id != __rhs.id)
        {
            return false;
        }
        if(type != __rhs.type)
        {
            return false;
        }
        if(artag_id != __rhs.artag_id)
        {
            return false;
        }
        return true;
    }

    bool operator<(const StationInfo& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(map_id < __rhs.map_id)
        {
            return true;
        }
        else if(__rhs.map_id < map_id)
        {
            return false;
        }
        if(id < __rhs.id)
        {
            return true;
        }
        else if(__rhs.id < id)
        {
            return false;
        }
        if(type < __rhs.type)
        {
            return true;
        }
        else if(__rhs.type < type)
        {
            return false;
        }
        if(artag_id < __rhs.artag_id)
        {
            return true;
        }
        else if(__rhs.artag_id < artag_id)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const StationInfo& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const StationInfo& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const StationInfo& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const StationInfo& __rhs) const
    {
        return !operator<(__rhs);
    }
};

struct Station
{
    ::wizrobo_npu::StationInfo info;
    ::wizrobo_npu::Pose3D pose;
};

typedef ::std::vector< ::wizrobo_npu::Station> StationList;

struct ImgStation
{
    ::wizrobo_npu::StationInfo info;
    ::wizrobo_npu::ImgPose pose;
};

typedef ::std::vector< ::wizrobo_npu::ImgStation> ImgStationList;

struct GeoStation
{
    ::wizrobo_npu::StationInfo info;
    ::wizrobo_npu::GeoPose pose;
};

typedef ::std::vector< ::wizrobo_npu::GeoStation> GeoStationList;

struct PathInfo
{
    ::std::string map_id;
    ::std::string id;
    ::Ice::Float length;
    ::Ice::Int pose_num;
};

struct Path
{
    ::wizrobo_npu::PathInfo info;
    ::wizrobo_npu::Pose3DList poses;
};

typedef ::std::vector< ::wizrobo_npu::Path> PathList;

struct ImgPath
{
    ::wizrobo_npu::PathInfo info;
    ::wizrobo_npu::ImgPoseList poses;
};

typedef ::std::vector< ::wizrobo_npu::ImgPath> ImgPathList;

struct GeoPath
{
    ::wizrobo_npu::PathInfo info;
    ::wizrobo_npu::GeoPoseList poses;
};

typedef ::std::vector< ::wizrobo_npu::GeoPath> GeoPathList;

enum actionname
{
    navi,
    follow,
    forward,
    backward,
    turnleft,
    turnright,
    backtopoint
};

struct TaskAction
{
    ::wizrobo_npu::actionname action_name;
    ::std::string action_args;
    ::Ice::Int duration;

    bool operator==(const TaskAction& __rhs) const
    {
        if(this == &__rhs)
        {
            return true;
        }
        if(action_name != __rhs.action_name)
        {
            return false;
        }
        if(action_args != __rhs.action_args)
        {
            return false;
        }
        if(duration != __rhs.duration)
        {
            return false;
        }
        return true;
    }

    bool operator<(const TaskAction& __rhs) const
    {
        if(this == &__rhs)
        {
            return false;
        }
        if(action_name < __rhs.action_name)
        {
            return true;
        }
        else if(__rhs.action_name < action_name)
        {
            return false;
        }
        if(action_args < __rhs.action_args)
        {
            return true;
        }
        else if(__rhs.action_args < action_args)
        {
            return false;
        }
        if(duration < __rhs.duration)
        {
            return true;
        }
        else if(__rhs.duration < duration)
        {
            return false;
        }
        return false;
    }

    bool operator!=(const TaskAction& __rhs) const
    {
        return !operator==(__rhs);
    }
    bool operator<=(const TaskAction& __rhs) const
    {
        return operator<(__rhs) || operator==(__rhs);
    }
    bool operator>(const TaskAction& __rhs) const
    {
        return !operator<(__rhs) && !operator==(__rhs);
    }
    bool operator>=(const TaskAction& __rhs) const
    {
        return !operator<(__rhs);
    }
};

typedef ::std::vector< ::wizrobo_npu::TaskAction> TaskActionList;

struct TaskInfo
{
    ::std::string map_id;
    ::std::string task_id;
    ::wizrobo_npu::TaskActionList action_list;
};

struct Task
{
    ::wizrobo_npu::TaskInfo info;
    bool enb_taskloop;
    ::Ice::Int task_loop_times;
};

typedef ::std::vector< ::wizrobo_npu::Task> TaskList;

struct VirtualWallInfo
{
    ::std::string map_id;
    ::std::string id;
    ::Ice::Float length;
    bool closed;
};

struct VirtualWall
{
    ::wizrobo_npu::VirtualWallInfo info;
    ::wizrobo_npu::Point3DList points;
};

typedef ::std::vector< ::wizrobo_npu::VirtualWall> VirtualWallList;

struct ImgVirtualWall
{
    ::wizrobo_npu::VirtualWallInfo info;
    ::wizrobo_npu::ImgPointList points;
};

typedef ::std::vector< ::wizrobo_npu::ImgVirtualWall> ImgVirtualWallList;

struct GeoVirtualWall
{
    ::wizrobo_npu::VirtualWallInfo info;
    ::wizrobo_npu::GeoPointList points;
};

typedef ::std::vector< ::wizrobo_npu::GeoVirtualWall> GeoVirtualWallList;

enum CellType
{
    OCCUPIED_CELL,
    FREE_CELL,
    UNKNOWN_CELL
};

typedef ::std::vector< ::wizrobo_npu::CellType> CellArray;

struct CellMat
{
    ::Ice::Int width;
    ::Ice::Int height;
    ::wizrobo_npu::CellArray data;
};

typedef ::std::vector< ::Ice::Byte> PixelMap;

struct PixelMat
{
    ::Ice::Int width;
    ::Ice::Int height;
    ::Ice::Float ratio;
    ::wizrobo_npu::ByteArray data;
};

typedef ::std::vector< ::Ice::Int> RgbaPixelMap;

struct RgbaPixelMat
{
    ::Ice::Int width;
    ::Ice::Int height;
    ::Ice::Float ratio;
    ::wizrobo_npu::IntArray data;
};

struct MapInfo
{
    ::Ice::Int index;
    ::std::string id;
    ::std::string creation_time;
    ::Ice::Float resolution;
    ::wizrobo_npu::Vector3D dimension;
    ::wizrobo_npu::Vector3D offset;
    ::wizrobo_npu::PixelMat thumbnail;
    ::Ice::Int station_num;
    ::Ice::Int path_num;
    ::Ice::Int task_num;
};

typedef ::std::vector< ::wizrobo_npu::MapInfo> MapInfoList;

struct Map2D
{
    ::wizrobo_npu::MapInfo info;
    ::wizrobo_npu::CellMat mat;
    ::wizrobo_npu::StationList stations;
    ::wizrobo_npu::TaskList tasks;
    ::wizrobo_npu::PathList paths;
    ::wizrobo_npu::VirtualWallList virtual_walls;
};

typedef ::std::vector< ::wizrobo_npu::Map2D> MapList;

struct ImgMap
{
    ::wizrobo_npu::MapInfo info;
    ::wizrobo_npu::PixelMat mat;
    ::wizrobo_npu::ImgStationList stations;
    ::wizrobo_npu::ImgPathList paths;
    ::wizrobo_npu::ImgVirtualWallList virtual_walls;
};

struct GeoMap
{
    ::wizrobo_npu::MapInfo info;
    ::wizrobo_npu::RgbaPixelMat mat;
    ::wizrobo_npu::GeoStationList stations;
    ::wizrobo_npu::GeoPathList paths;
    ::wizrobo_npu::GeoVirtualWallList virtual_walls;
};

}

namespace Ice
{
template<>
struct StreamableTraits< ::wizrobo_npu::StationType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 2;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::StationInfo>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 7;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::StationInfo, S>
{
    static void write(S* __os, const ::wizrobo_npu::StationInfo& v)
    {
        __os->write(v.map_id);
        __os->write(v.id);
        __os->write(v.type);
        __os->write(v.artag_id);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::StationInfo, S>
{
    static void read(S* __is, ::wizrobo_npu::StationInfo& v)
    {
        __is->read(v.map_id);
        __is->read(v.id);
        __is->read(v.type);
        __is->read(v.artag_id);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Station>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 31;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Station, S>
{
    static void write(S* __os, const ::wizrobo_npu::Station& v)
    {
        __os->write(v.info);
        __os->write(v.pose);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Station, S>
{
    static void read(S* __is, ::wizrobo_npu::Station& v)
    {
        __is->read(v.info);
        __is->read(v.pose);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImgStation>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 19;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImgStation, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImgStation& v)
    {
        __os->write(v.info);
        __os->write(v.pose);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImgStation, S>
{
    static void read(S* __is, ::wizrobo_npu::ImgStation& v)
    {
        __is->read(v.info);
        __is->read(v.pose);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::GeoStation>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 43;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::GeoStation, S>
{
    static void write(S* __os, const ::wizrobo_npu::GeoStation& v)
    {
        __os->write(v.info);
        __os->write(v.pose);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::GeoStation, S>
{
    static void read(S* __is, ::wizrobo_npu::GeoStation& v)
    {
        __is->read(v.info);
        __is->read(v.pose);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::PathInfo>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 10;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::PathInfo, S>
{
    static void write(S* __os, const ::wizrobo_npu::PathInfo& v)
    {
        __os->write(v.map_id);
        __os->write(v.id);
        __os->write(v.length);
        __os->write(v.pose_num);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::PathInfo, S>
{
    static void read(S* __is, ::wizrobo_npu::PathInfo& v)
    {
        __is->read(v.map_id);
        __is->read(v.id);
        __is->read(v.length);
        __is->read(v.pose_num);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Path>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 11;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Path, S>
{
    static void write(S* __os, const ::wizrobo_npu::Path& v)
    {
        __os->write(v.info);
        __os->write(v.poses);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Path, S>
{
    static void read(S* __is, ::wizrobo_npu::Path& v)
    {
        __is->read(v.info);
        __is->read(v.poses);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImgPath>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 11;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImgPath, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImgPath& v)
    {
        __os->write(v.info);
        __os->write(v.poses);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImgPath, S>
{
    static void read(S* __is, ::wizrobo_npu::ImgPath& v)
    {
        __is->read(v.info);
        __is->read(v.poses);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::GeoPath>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 11;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::GeoPath, S>
{
    static void write(S* __os, const ::wizrobo_npu::GeoPath& v)
    {
        __os->write(v.info);
        __os->write(v.poses);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::GeoPath, S>
{
    static void read(S* __is, ::wizrobo_npu::GeoPath& v)
    {
        __is->read(v.info);
        __is->read(v.poses);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::actionname>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 6;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::TaskAction>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 6;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::TaskAction, S>
{
    static void write(S* __os, const ::wizrobo_npu::TaskAction& v)
    {
        __os->write(v.action_name);
        __os->write(v.action_args);
        __os->write(v.duration);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::TaskAction, S>
{
    static void read(S* __is, ::wizrobo_npu::TaskAction& v)
    {
        __is->read(v.action_name);
        __is->read(v.action_args);
        __is->read(v.duration);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::TaskInfo>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 3;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::TaskInfo, S>
{
    static void write(S* __os, const ::wizrobo_npu::TaskInfo& v)
    {
        __os->write(v.map_id);
        __os->write(v.task_id);
        __os->write(v.action_list);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::TaskInfo, S>
{
    static void read(S* __is, ::wizrobo_npu::TaskInfo& v)
    {
        __is->read(v.map_id);
        __is->read(v.task_id);
        __is->read(v.action_list);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Task>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 8;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Task, S>
{
    static void write(S* __os, const ::wizrobo_npu::Task& v)
    {
        __os->write(v.info);
        __os->write(v.enb_taskloop);
        __os->write(v.task_loop_times);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Task, S>
{
    static void read(S* __is, ::wizrobo_npu::Task& v)
    {
        __is->read(v.info);
        __is->read(v.enb_taskloop);
        __is->read(v.task_loop_times);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::VirtualWallInfo>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 7;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::VirtualWallInfo, S>
{
    static void write(S* __os, const ::wizrobo_npu::VirtualWallInfo& v)
    {
        __os->write(v.map_id);
        __os->write(v.id);
        __os->write(v.length);
        __os->write(v.closed);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::VirtualWallInfo, S>
{
    static void read(S* __is, ::wizrobo_npu::VirtualWallInfo& v)
    {
        __is->read(v.map_id);
        __is->read(v.id);
        __is->read(v.length);
        __is->read(v.closed);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::VirtualWall>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 8;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::VirtualWall, S>
{
    static void write(S* __os, const ::wizrobo_npu::VirtualWall& v)
    {
        __os->write(v.info);
        __os->write(v.points);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::VirtualWall, S>
{
    static void read(S* __is, ::wizrobo_npu::VirtualWall& v)
    {
        __is->read(v.info);
        __is->read(v.points);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImgVirtualWall>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 8;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImgVirtualWall, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImgVirtualWall& v)
    {
        __os->write(v.info);
        __os->write(v.points);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImgVirtualWall, S>
{
    static void read(S* __is, ::wizrobo_npu::ImgVirtualWall& v)
    {
        __is->read(v.info);
        __is->read(v.points);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::GeoVirtualWall>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 8;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::GeoVirtualWall, S>
{
    static void write(S* __os, const ::wizrobo_npu::GeoVirtualWall& v)
    {
        __os->write(v.info);
        __os->write(v.points);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::GeoVirtualWall, S>
{
    static void read(S* __is, ::wizrobo_npu::GeoVirtualWall& v)
    {
        __is->read(v.info);
        __is->read(v.points);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::CellType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 2;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::wizrobo_npu::CellMat>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 9;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::CellMat, S>
{
    static void write(S* __os, const ::wizrobo_npu::CellMat& v)
    {
        __os->write(v.width);
        __os->write(v.height);
        __os->write(v.data);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::CellMat, S>
{
    static void read(S* __is, ::wizrobo_npu::CellMat& v)
    {
        __is->read(v.width);
        __is->read(v.height);
        __is->read(v.data);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::PixelMat>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 13;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::PixelMat, S>
{
    static void write(S* __os, const ::wizrobo_npu::PixelMat& v)
    {
        __os->write(v.width);
        __os->write(v.height);
        __os->write(v.ratio);
        __os->write(v.data);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::PixelMat, S>
{
    static void read(S* __is, ::wizrobo_npu::PixelMat& v)
    {
        __is->read(v.width);
        __is->read(v.height);
        __is->read(v.ratio);
        __is->read(v.data);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::RgbaPixelMat>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 13;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::RgbaPixelMat, S>
{
    static void write(S* __os, const ::wizrobo_npu::RgbaPixelMat& v)
    {
        __os->write(v.width);
        __os->write(v.height);
        __os->write(v.ratio);
        __os->write(v.data);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::RgbaPixelMat, S>
{
    static void read(S* __is, ::wizrobo_npu::RgbaPixelMat& v)
    {
        __is->read(v.width);
        __is->read(v.height);
        __is->read(v.ratio);
        __is->read(v.data);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::MapInfo>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 83;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::MapInfo, S>
{
    static void write(S* __os, const ::wizrobo_npu::MapInfo& v)
    {
        __os->write(v.index);
        __os->write(v.id);
        __os->write(v.creation_time);
        __os->write(v.resolution);
        __os->write(v.dimension);
        __os->write(v.offset);
        __os->write(v.thumbnail);
        __os->write(v.station_num);
        __os->write(v.path_num);
        __os->write(v.task_num);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::MapInfo, S>
{
    static void read(S* __is, ::wizrobo_npu::MapInfo& v)
    {
        __is->read(v.index);
        __is->read(v.id);
        __is->read(v.creation_time);
        __is->read(v.resolution);
        __is->read(v.dimension);
        __is->read(v.offset);
        __is->read(v.thumbnail);
        __is->read(v.station_num);
        __is->read(v.path_num);
        __is->read(v.task_num);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::Map2D>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 96;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::Map2D, S>
{
    static void write(S* __os, const ::wizrobo_npu::Map2D& v)
    {
        __os->write(v.info);
        __os->write(v.mat);
        __os->write(v.stations);
        __os->write(v.tasks);
        __os->write(v.paths);
        __os->write(v.virtual_walls);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::Map2D, S>
{
    static void read(S* __is, ::wizrobo_npu::Map2D& v)
    {
        __is->read(v.info);
        __is->read(v.mat);
        __is->read(v.stations);
        __is->read(v.tasks);
        __is->read(v.paths);
        __is->read(v.virtual_walls);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::ImgMap>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 99;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::ImgMap, S>
{
    static void write(S* __os, const ::wizrobo_npu::ImgMap& v)
    {
        __os->write(v.info);
        __os->write(v.mat);
        __os->write(v.stations);
        __os->write(v.paths);
        __os->write(v.virtual_walls);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::ImgMap, S>
{
    static void read(S* __is, ::wizrobo_npu::ImgMap& v)
    {
        __is->read(v.info);
        __is->read(v.mat);
        __is->read(v.stations);
        __is->read(v.paths);
        __is->read(v.virtual_walls);
    }
};

template<>
struct StreamableTraits< ::wizrobo_npu::GeoMap>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 99;
    static const bool fixedLength = false;
};

template<class S>
struct StreamWriter< ::wizrobo_npu::GeoMap, S>
{
    static void write(S* __os, const ::wizrobo_npu::GeoMap& v)
    {
        __os->write(v.info);
        __os->write(v.mat);
        __os->write(v.stations);
        __os->write(v.paths);
        __os->write(v.virtual_walls);
    }
};

template<class S>
struct StreamReader< ::wizrobo_npu::GeoMap, S>
{
    static void read(S* __is, ::wizrobo_npu::GeoMap& v)
    {
        __is->read(v.info);
        __is->read(v.mat);
        __is->read(v.stations);
        __is->read(v.paths);
        __is->read(v.virtual_walls);
    }
};

}

#endif
