// -------------------------------------
// Version: 1.0
// Date: 2017-04-04
// Maintainer: <EMAIL>,
//             <EMAIL>,
//             <EMAIL>,
//             <EMAIL>
// -------------------------------------
#ifndef WIZROBO_NPU_GEOMETRY_ICE
#define WIZROBO_NPU_GEOMETRY_ICE

module wizrobo_npu {

    const float INVALID_POSE3D_VALUE = -1000.0;

    //// position
    // point
    struct Point3D
    {
        float x;// [m]
        float y;// [m]
        float z;// [m]
    };
    sequence<Point3D> Point3DList;
    // imagery point
    struct ImgPoint
    {
        int u;// [pix]
        int v;// [pix]
    };
    sequence<ImgPoint> ImgPointList;

    //// vector
    struct Vector3D
    {
        double x;
        double y;
        double z;
    };

    //// orientation
    struct Quaternion
    {
        double x;
        double y;
        double z;
        double w;
    };
    sequence<double> DoubleArray;

    //// pose
    struct Pose3D
    {
        float x;// [m]
        float y;// [m]
        float z;// [m]
        float roll;// [rad]
        float pitch;// [rad]
        float yaw;// [rad]
    };
    sequence<Pose3D> Pose3DList;
    // imagery pose
    struct ImgPose
    {
        int u;// [pix]
        int v;// [pix]
        float theta;// [rad]
    };
    sequence<ImgPose> ImgPoseList;

    //// velocity
    struct Vel2D
    {
        float v_x;// [m/s]
        float v_y;// [m/s]
        float v_yaw;// [rad/s]
    };
    struct Vel3D
    {
        float v_x;// [m/s]
        float v_y;// [m/s]
        float v_z;// [m/s]
        float v_roll;// [rad/s]
        float v_pitch;// [rad/s]
        float v_yaw;// [rad/s]
    };

    //// acceleration
    struct Acc2D
    {
        float a_x;// [m/s^2]
        float a_y;// [m/s^2]
        float a_yaw;// [rad/s^2]
    };
    struct Acc3D
    {
        float a_x;// [m/s^2]
        float a_y;// [m/s^2]
        float a_z;// [m/s^2]
        float a_roll;// [rad/s^2]
        float a_pitch;// [rad/s^2]
        float a_yaw;// [rad/s^2]
    };

    //// init pose area
    struct InitPoseArea
    {
        Pose3D pose;
        float width;
        float height;
    };
    struct InitImgPoseArea
    {
        ImgPose pose;
        int width;
        int height;
    };
};// module wizrobo

#endif// WIZROBO_NPU_GEOMETRY_ICE
