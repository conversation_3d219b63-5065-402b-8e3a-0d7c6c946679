// -------------------------------------
// Version: 1.0
// Date: 2017-05-19
// Maintainer: <EMAIL>,
//             <EMAIL>,
//             <EMAIL>,
//             <EMAIL>
// -------------------------------------
#ifndef WIZROBO_NPU_PARAM_ICE
#define WIZROBO_NPU_PARAM_ICE
#include <npuice_geometry.ice>

module wizrobo_npu {

    //// npu
    enum NpuMode {MASTER, SLAVE};// TODEL
    struct CoreParam
    {
        NpuMode npu_mode;
        string config_id;
        string map_id;
        string record_id;
    };

    //// motor (sync with [wr_bcu_server]::bcu_driver_param.h
    enum BrakeType {SOFT_BRK, HARD_BRK};
    enum ValidOutputLevel {LOW_VALID, HIGH_VALID};
    struct MotorParam
    {
        /// sync with [wr_bcu_server]/bcu_driver_param.h
        // motor
        int motor_num;//
        string motor_dir_str;// "++--++--"
        int motor_max_spd_rpm;// [Rotation Per Minute]
        float motor_rdc_ratio;
        int motor_enc_res_ppr;// [Pulse Per Rotation]
        int motor_pwm_frq_hz;
        BrakeType motor_brk_type;
        // io valid level
        ValidOutputLevel enb_vol;
        ValidOutputLevel dir_vol;
        ValidOutputLevel pwm_vol;
        ValidOutputLevel brk_vol;
        // aux mode
        bool enb_auxdir_mode;
        // throttle mode
        bool enb_throttle_mode;
        float throttle_zero_pos_fac;
        float throttle_zero_neg_fac;
        // left-right switch
        bool end_left_right_switch;
    };
    //// pid
    struct PidParam
    {
        /// sync with [wr_bcu_server]/bcu_driver_param.h
        bool enb_pid;
        float kp_acc;
        float kp_dec;
        float ki_acc;
        float ki_dec;
        float kd_acc;
        float kd_dec;
    };
    //// footprint
    enum ShapeType {ROUND, RECTANGLE, POLYGON};
    struct FootprintParam
    {
        /// common
        ShapeType shape_type;
        float rot_center_offset_m;
        /// round
        float round_radius_m;
        /// rectangle
        float rectangle_width_m;
        float rectangle_length_m;
        /// polygon
        int polygon_vertex_num;
        Point3DList polygon_vertices;
    };
    //// chassis
    enum ChassisModelType {CARLIKE, DIFFDRV, UNIVWHEEL, OMNIWHEEL, CSGDRV};
    enum SteerEncLocation {CENTER, LEFT, RIGHT};
    struct ChassisParam
    {
        /// sync with [wr_chassis_server]/chassis_model_param.h
        /// common
        ChassisModelType model_type;
        float wheel_rdc_ratio;
        float wheel_radius_m;
        float wheel_span_m;
        float max_lin_acc_time_s;
        float max_ang_acc_time_s;
        float autoset_max_lin_spd_mps;
        float autoset_max_ang_spd_rps;
        /// carlike
        SteerEncLocation carlike_steer_enc_location;
        float carlike_axle_dist_m;
    };

    //// base
    struct BaseParam
    {
        string base_id;
        bool enb_slave_mode;
        MotorParam motor_param;
        PidParam pid_param;
        ChassisParam chassis_param;
        FootprintParam footprint_param;
    };

    //// sensor
    /// sensor/lidar
    enum LidarType {RPLIDAR, RSLIDAR, LSLIDAR, FSLIDAR,
                    SICK_TIM551, SICK_TIM561, SICK_TIM571,
                    SICK_LMS111, SICK_LMS141, SICK_LMS151, SICK_LMS511,
                    SICK_S300,
                    HOKUYO_UST10LX, HOKUYO_UTM30LX,
                    PF_R2000_20HZ, PF_R2000_50HZ,
                    VELODYNE_VLP16,
                    XTION_LIDAR, SIM_LIDAR, GAZEBO, FKLIDAR};
    enum InterfaceType {ETHERNET, SERIAL, RECORD};
    struct LidarParam
    {
        LidarType type;
        InterfaceType itf_type;
        string serial_port_id;
        string ethernet_ip;
        bool enb_itl_filter;
        Pose3D install_pose;
        string record_file_id;
        double max_angle;
        double min_angle;
    };
    sequence<LidarParam> LidarParamList;
    /// sensor/imu
    struct ImuParam
    {
        Pose3D install_pose;
    };
    sequence<ImuParam> ImuParamList;
    /// sensor/sonar
    struct SonarParam
    {
        float min_range_m;// [m]
        float max_range_m;// [m]
        float scan_freq_hz;// [Hz]
        float fov_deg;// [deg]
        Pose3D install_pose;
    };
    sequence<SonarParam> SonarParamList;
    /// sensor/infrd
    struct InfrdParam
    {
        float min_range_m;// [m]
        float max_range_m;// [m]
        float fov_deg;// [deg]
        Pose3D install_pose;
    };
    sequence<InfrdParam> InfrdParamList;
    /// sensor/bumper
    enum BumperLocation
    {
        FRONT_BUMPER, FRONT_LEFT_BUMPER, FRONT_RIGHT_BUMPER,
        BACK_BUMPER, BACK_LEFT_BUMPER, BACK_RIGHT_BUMPER,
        LEFT_BUMPER, RIGHT_BUMPER,
    };
    struct BumperParam
    {
        BumperLocation location;
    };
    sequence<BumperParam> BumperParamList;
    /// sensor/gps
    struct GpsParam
    {
        Pose3D install_pose;
    };
    sequence<GpsParam> GpsParamList;
    /// sensor/camera
    struct CameraParam
    {
        Pose3D install_pose;
    };
    sequence<CameraParam> CameraParamList;
    /// sensor/battery
    struct BatteryParam
    {
        float max_voltage_v;// [v]
        float capacity_ah;// [Ah]
    };
    /// sensor/all
    struct SensorParam
    {
        // lidar
        int lidar_num;
        LidarParamList lidar_params;
        // sonar
        int sonar_num;
        SonarParamList sonar_params;
        // infrd
        int infrd_num;
        InfrdParamList infrd_params;
        // bumper
        int bumper_num;
        BumperParamList bumper_params;
        // imu
        int imu_num;
        ImuParamList imu_params;
        // gps
        int gps_num;
        GpsParamList gps_params;
        // camera
        int camera_num;
        CameraParamList camera_params;
    };

    //// teleop
    struct TeleopParam
    {
        float lin_spd_lmt_ratio;
        float ang_spd_lmt_ratio;
    };

    //// slam
    struct MapOptimizer
    {
        bool enb_map_opt;
        float map_res_mpp;
        float update_dist_thrs_m;
        float update_ori_thrs_rad;
        bool enb_trace_clearing;
        bool enb_filtering;
        int filter_size;
    };
    struct SlamParam
    {
        /// common
        float map_res_mpp;// [m/pixel]
        int map_size_m;// [m]
        float update_dist_thrs_m;
        float update_ori_thrs_rad;
        float max_lin_spd_mps;
        float max_ang_spd_rps;
        /// optimization
        MapOptimizer optimizer_param;
    };

    //// navi
    struct P2pPlannerParam
    {
        float acc_lim_x_mps2;// [m/s^2]
        float acc_lim_theta_rps2;// [rad/s^2]
        float max_vel_x_mps; //[m/s]
        float min_vel_x_mps;//[m/s]
        float max_rot_vel_rps;// [rad/s]
        float min_rot_vel_rps;// [rad/s]
        float plan_strictness;
        float path_approach_avenue_factor;
        float safety_inflation_factor;
    };
    struct PfPlannerParam
    {
        bool waypoint_mode;
//        bool enable_acute_turn;
        float look_ahead_dist_m;
        float position_control_tolerance_m;
        float heading_control_tolerance_rad;
        float max_lin_vel_mps;//[m/s]
        float max_ang_vel_rps;
        float heading_kp;
    };
    struct CcpPlannerParam
    {
        float coverage_spacing_m;
    };
    struct NaviParam
    {
        /// common
        float lin_spd_lmt_ratio;
        float ang_spd_lmt_ratio;
        float x_err_tolr_m;
        float y_err_tolr_m;
        float yaw_err_tolr_rad;

        /// planners
        P2pPlannerParam p2p_planner_param;
        PfPlannerParam pf_planner_param;
        CcpPlannerParam ccp_planner_param;
//        // todel
//        Vel3D max_vel;
//        Vel3D min_vel;
//        Acc3D max_acc;
//        float pos_ctrl_err;// [m]
//        float ori_ctrl_err;// [m]
//        float navi_smooth_level;// [0,1]
    };

    // RuntimeStatus0 {
    const int RT0_CONECT_LIDAR      = 0x1;         // BIT(0)
    const int RT0_CONECT_IMU        = 0x2;         // BIT(1)
    const int RT0_CONECT_BCU_MOTOR  = 0x4;         // BIT(2)
    const int RT0_CONECT_BCU_SENSOR = 0x8;         // BIT(3)
    const int RT0_CONECT_GPS        = 0x10;        // BIT(4)
    const int RT0_DATA_LIDAR        = 0x10000;     // BIT(16)
    const int RT0_DATA_IMU          = 0x20000;     // BIT(17)
    const int RT0_DATA_ENCODER      = 0x40000;     // BIT(18)
    const int RT0_DATA_GPS          = 0x80000;     // BIT(19)
    const int RT0_DATA_MAP          = 0x100000;    // BIT(20)
    // } RuntimeStatus0

    // RuntimeStatus1 {
    const int RT1_INIT_POSE_NOT_SET      = 0x1;      // BIT(0)
    const int RT1_LOCALIZATION_EXCEPTION = 0x2;      // BIT(1)
    const int RT1_SOFTWARE_NODE_CRASH    = 0x4;      // BIT(2)
    const int RT1_NAVI_OBSTACLE_BLOCK    = 0x10000;  // BIT(16)
    const int RT1_NAVI_PLANNING_FAILED   = 0x20000;  // BIT(17)
    const int RT1_NAVI_EMB_TRIGGERED     = 0x40000;  // BIT(18)
    const int RT1_NAVI_BUMPER_TRIGGERED  = 0x80000;  // BIT(19)
    const int RT1_NAVI_ENTRP_DETECTED    = 0x100000; // BIT(20)
    // } RuntimeStatus1

    sequence<int> RuntimeStatusList;

};// module wizrobo

#endif// WIZROBO_NPU_PARAM_ICE
