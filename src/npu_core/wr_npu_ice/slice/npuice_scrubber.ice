#ifndef WIZROBO_NPUICE_SCRUBBER_API_ICE
#define WIZROBO_NPUICE_SCRUBBER_API_ICE

#include <npuice_exception.ice>
#include <npuice_geometry.ice>
#include <npuice_data.ice>
#include <npuice_param.ice>
#include <npuice_map.ice>

module wizrobo_npu_scrubber {

    enum DiagnosticsStatus{DISCONNECT, CONNECT, DATA_RECEIVED , DATA_ERROR , PROGRAM_NORMAL , PROGRAM_ERROR , IGNORE};

    enum WaterLevel {LL1, LL2};
    enum Level { STOP, LOW, MEDIUM, HIGH};
    enum Switch { ON, OFF};
    enum BreathLightStatus {
        ALLCLOSE,
        ALLOPEN,
        WHITECLOSE,
        WHITEOPEN,
        REDCLOSE,
        REDOPEN
    };

    /// system status reminder
    enum SystemStatusReminder{BATTERY_NORMAL = 0, BATTERY_LOW_POWER = 1,
                              BATTERY_TEMPERATURE_HIGH = 2,
                              HIGH_WATER_LEVEL_OF_POLLUTED_WATER_TANK = 10,
                              HIGH_WATER_LEVEL_OF_PURE_WATER_TANK = 20,
                              LOW_WATER_LEVEL_OF_PURE_WATER_TANK = 21,
                              TEMPERATURE_ABNORMAL = 100, HUMIDITY_ABNORMAL = 101,
                              EMERGENCY_STOP_ABNORMAL = 200, BUMP_ABNORMAL = 201,
                              ROLLING_BRUSH_ABNORMAL = 1000, BLOWER_ABNORMAL = 1001,
                              BROOM_ABNORMAL = 1002, VALVE_ABNORMAL = 1003,
                              WHEEL_HUB_MOTOR_ABNORMAL = 2000, LIFT_MOTOR_ABNORMAL = 2001,
                              IMU_ABNORMAL = 3000, RADAR_ABNORMAL = 3001,
                              DEPTH_CAMERA_ABNORMAL = 3002, ULTRASONIC_PARTS_ABNORMAL = 3003,
                              INFRARED_PARTS_ABNORMAL = 3004, POSITIONING_ABNORMAL = 9999};
    sequence<SystemStatusReminder> SystemIndicators;

    struct BatteryStatus
    {
        float current_a;// [A]
        float current_threshold_a;
        float voltage_v;// [V]
        float voltage_threshold_v;
        float temperature_cen;// [centigrade]
        float temperature_threshold_cen; // [centigrade]
        int design_capacity_ah;
        float capacity_level;// [0~1]
        int abnormal_state;
        int charge_discharge_time;
        int battery_lifetime;
        int accumulated_time_of_use;// [hour]
        bool should_back_to_charge;
        bool should_change_new_battery;
    };
    //// cleaner_parts
    struct BrushStatus
    {
        bool is_rolling_brush_located;
        bool should_change_new_rolling_brush;
        Switch rolling_brush_status;
        int rolling_brush_usage_h;// [hour]
        int rotation_speed_of_rolling_brush;// [round/min]
    };

    struct BlowerStatus
    {
        Switch blower_switch;
        Level blower_speed;
    };

    struct WaterValveStatus
    {
        Switch valve_status;
        int valve_ratio;
    };

    struct WaterTankStatus
    {
        WaterLevel pure_watter_tank_level;
        WaterLevel polluted_water_tank_level;
        int polluted_water_tank_usage_d;// [day]
        bool is_polluted_water_tank_need_disinfected;
    };

    struct CleanerPartsStatus
    {
        WaterTankStatus water_tank_status;
        bool is_broom_located;
        int broom_and_carbon_brush_usage_h;// [hour]
        bool should_change_new_broom;
        bool should_change_new_carbon_brush;
        int filter_usage_d;// [day]
        bool should_change_new_filter;
        BreathLightStatus rolling_brush;
        Switch emergency_stop;
        Switch power_switch;
        BreathLightStatus breath_light_status;
        BlowerStatus blower_status;
        WaterValveStatus valve_status;
        Switch beep_status;
        Switch lift_motor_status;
        wizrobo_npu::BumperArray bump_status;
    };

    struct CleanerSensorData
    {
        float inside_temperature;// [deg]
        float inside_humidity;
        wizrobo_npu::FloatArray infrared_data;// [m]
        wizrobo_npu::FloatArray sonar_data;// [m]
        wizrobo_npu::ImuData imu_data;
    };

    struct CleanerPartsParam
    {
        int polluted_water_tank_maintenance_period_d;// [day]
        int broom_lifetime_h;// [hour]
        int carbon_brush_lifetime_h;// [hour]
        int filter_lifetime_d;// [day]
        int rolling_brush_lifetime_h;// [hour]
        int pure_water_valve_ratio;
        int brush_rated_spd_rpm;
        int brush_default_spd_rpm;
        int blower_rated_spd_rpm;
        int blower_default_spd_rpm;
        float inside_temperature_threshold_cen;
        float inside_humidity_threshold;
    };

    struct SystemStatus
    {
        SystemIndicators system_report;
    };

    const int MARSK_ALL   = 0x00000000;
    const int MARSK_BL    = 0x00000001;
    const int MARSK_LIFT  = 0x00000002;
    const int MARSK_BRUSH = 0x00000004;
    const int MARSK_VALVE = 0x00000008;

    struct ScrbCtrlPanel
    {
        BreathLightStatus bl_status;
        Switch lift_status;
        Level fan_lv;
        Level brush_lv;
        Level valve_lv;
    };

    interface ScrubberIce
    {
        void SetScrbCtrlPanel(ScrbCtrlPanel panel, int mask) throws wizrobo_npu::NpuException;
        void GetScrbCtrlPanel(ScrbCtrlPanel panel, int mask) throws wizrobo_npu::NpuException;

        //// cleaner
        void SetTrioooCmd(int cmd) throws wizrobo_npu::NpuException;
        // battery
        BatteryStatus GetBatteryStatus() throws wizrobo_npu::NpuException;
        void SetBatteryParam(BatteryStatus param) throws wizrobo_npu::NpuException;
        // cleaner parts
        CleanerPartsStatus GetCleanerPartsStatus() throws wizrobo_npu::NpuException;
        CleanerSensorData GetCleanerSensorData() throws wizrobo_npu::NpuException;
        CleanerPartsParam GetCleanerPartsParam() throws wizrobo_npu::NpuException;
        // cleaner system
        SystemStatus GetSystemStatus() throws wizrobo_npu::NpuException;
    };
};

#endif
