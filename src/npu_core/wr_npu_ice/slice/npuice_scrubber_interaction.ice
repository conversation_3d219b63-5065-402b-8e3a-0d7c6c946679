#ifndef WIZROBO_NPUICE_SCRUBBER_INTERACTION_ICE
#define WIZROBO_NPUICE_SCRUBBER_INTERACTION_ICE

#include <npuice_exception.ice>
#include <npuice_geometry.ice>
#include <npuice_data.ice>
#include <npuice_param.ice>
#include <npuice_map.ice>
module wizrobo_scrubber {

    enum ScrLampState {
        SCR_OUTPUT_MODE_KEEP_HIGH,
        SCR_OUTPUT_MODE_KEEP_LOW,
        SCR_OUTPUT_MODE_BLINK_1,
        SCR_OUTPUT_MODE_BLINK_2,
        SCR_OUTPUT_MODE_BLINK_3
    };

    enum ScrBeepState {
        SCR_BEEP_CLOSE,
        SCR_BUZZING_1HZ,
        SCR_BUZZING_2HZ,
        SCR_BLEW_10S
    };

    enum ScrBlState {
        SCR_BLN_WHITE_BREATHE,
        SCR_BLN_ALL_BREATHE,
        SCR_BLN_RED_BLINK_1HZ,
        SCR_BLN_RED_BLINK_2HZ,
        SCR_BLN_RED_ALWAYS_ON
    };

    enum ScrLiftState {
        SCR_LIFT_DOWN,
        SCR_LIFT_UP,
        SCR_LIFT_HALF
    };


    struct ScrMotorState {
        string scr_id;
        float scr_rpm;
        float scr_voltage;
        float scr_current;
        float scr_power;
    };

    enum ScrSpeedLevel {
        SCR_LV_STOP,
        SCR_LV_LOW,
        SCR_LV_MEDIUM,
        SCR_LV_HIGH,
        SCR_LV_MAX
    };

    struct ScrUserMotorState {
        int scr_abnormal_state;
        bool scr_is_located;
        ScrMotorState scr_motor;
        ScrSpeedLevel scr_speed_level;
        ScrLiftState scr_lift_state;
    };

    struct ScrClearControlUnit {
        ScrLampState scr_lmp_state;
        ScrBeepState scr_beep_state;
        ScrBlState scr_bl_state;
        ScrLiftState scr_lift_state;
        ScrUserMotorState scr_fan_state;
        ScrUserMotorState scr_brush_state;
        ScrUserMotorState scr_valve_state;
    };

    struct ScrBatteryStatus {
        float scr_current_a;// [A]
        float scr_current_threshold_a;
        float scr_voltage_v;// [V]
        float scr_voltage_threshold_v;
        float scr_temperature_cen;// [centigrade]
        float scr_temperature_threshold_cen; // [centigrade]
        int scr_design_capacity_ah;
        float scr_capacity_level;// [0~1]
        int scr_abnormal_state;
        int scr_charge_discharge_time;
        int scr_battery_lifetime;
        int scr_accumulated_time_of_use;// [hour]
        bool scr_should_back_to_charge;
        bool scr_should_change_new_battery;
    };

    enum ScrWaterLevel {
        SCR_L1,
        SCR_L2
    };

    struct ScrWaterTankStatus {
        ScrWaterLevel scr_pure_watter_tank_level;
        ScrWaterLevel scr_polluted_water_tank_level;
        int scr_polluted_water_tank_usage_d;// [day]
        bool scr_is_polluted_water_tank_need_disinfected;
    };

    enum ScrSwitch {
        SCR_ON,
        SCR_OFF
    };

    struct ScrEmergencyStop {
        ScrSwitch scr_stop;
    };

    struct ScrSensorStatusUnit {
        ScrBatteryStatus scr_battery_state;
        ScrWaterTankStatus scr_water_state;
        ScrEmergencyStop scr_e_stop;
    };

    interface ScrubberIce
    {
        ScrClearControlUnit GetClearControlData() throws wizrobo_npu::NpuException;
        void SetClearControlData(ScrClearControlUnit clearcontrol) throws wizrobo_npu::NpuException;
        ScrSensorStatusUnit GetSensorState() throws wizrobo_npu::NpuException;
    };
};
#endif
