// -------------------------------------
// Version: 1.0.1
// Date: 2017-04-11
// Maintainer: <EMAIL>,
//             <EMAIL>,
//             <EMAIL>,
//             <EMAIL>
// -------------------------------------
#ifndef WIZROBO_NPU_MAP_ICE
#define WIZROBO_NPU_MAP_ICE

#include <npuice_geometry.ice>
#include <npuice_data.ice>

module wizrobo_npu {

    //// station
    enum StationType {START, CHARGER, USER_DEFINED};
    struct StationInfo
    {
        string map_id;
        string id;
        StationType type;
        int artag_id;
    };
    struct Station
    {
        StationInfo info;
        Pose3D pose;
    };
    sequence<Station> StationList;
    // imagery station
    struct ImgStation
    {
        StationInfo info;
        ImgPose pose;
    };
    sequence<ImgStation> ImgStationList;
    // geographic station
    struct GeoStation
    {
        StationInfo info;
        GeoPose pose;
    };
    sequence<GeoStation> GeoStationList;

    //// path
    struct PathInfo
    {
        string map_id;
        string id;
        float length;// [m]
        int pose_num;
    };
    struct Path
    {
        PathInfo info;
        Pose3DList poses;
    };
    sequence<Path> PathList;
    // imagery path
    struct ImgPath
    {
        PathInfo info;
        ImgPoseList poses;
    };
    sequence<ImgPath> ImgPathList;
    // geographic path
    struct GeoPath
    {
        PathInfo info;
        GeoPoseList poses;
    };
    sequence<GeoPath> GeoPathList;
    //// task

    enum actionname{navi, follow, forward, backward, turnleft, turnright, backtopoint};
    struct TaskAction
    {
        actionname action_name;
        string action_args;
        int duration;//[S]
    };
    sequence<TaskAction> TaskActionList;

    struct TaskInfo
    {
        string map_id;
        string task_id;
        TaskActionList action_list;
    };

    struct Task
    {
        TaskInfo info;
        bool enb_taskloop;
        int task_loop_times;
    };
    sequence<Task> TaskList;

    // virtual wall
    struct VirtualWallInfo
    {
        string map_id;
        string id;
        float length;// [m]
        bool closed;
    };
    struct VirtualWall
    {
        VirtualWallInfo info;
        Point3DList points;
    };
    sequence<VirtualWall> VirtualWallList;
    // imagery virtual wall
    struct ImgVirtualWall
    {
        VirtualWallInfo info;
        ImgPointList points;
    };
    sequence<ImgVirtualWall> ImgVirtualWallList;
    // geographic virtual wall
    struct GeoVirtualWall
    {
        VirtualWallInfo info;
        GeoPointList points;
    };
    sequence<GeoVirtualWall> GeoVirtualWallList;

    //// map
    // cell mat
    enum CellType {OCCUPIED_CELL, FREE_CELL, UNKNOWN_CELL};//*
    sequence<CellType> CellArray;//*
    struct CellMat//*
    {
        int width;
        int height;
        CellArray data;
    };
    // pixel mat
    sequence<byte> PixelMap;//*
    struct PixelMat
    {
        int width;
        int height;
        float ratio;
        ByteArray data;// 0=Occupied, 255=Free, 127=Unkonwn
    };
    // pixel mat
    sequence<int> RgbaPixelMap;//*
    struct RgbaPixelMat
    {
        int width;
        int height;
        float ratio;
        IntArray data;// RGBA = 0xFF FF FF FF
    };
    // info
    struct MapInfo//*
    {
        int index;
        string id;
        string creation_time;//[yyyy-mm-dd-HHMMSS]
        float resolution;
        Vector3D dimension;
        Vector3D offset;
        PixelMat thumbnail;
        int station_num;
        int path_num;
        int task_num;
//        StationInfoList station_infos;
//        PathInfoList path_infos;
    };
    sequence<MapInfo> MapInfoList;

    // map2d
    struct Map2D
    {
        MapInfo info;
        CellMat mat;
        StationList stations;
        TaskList tasks;
        PathList paths;
        VirtualWallList virtual_walls;
    };
    sequence<Map2D> MapList;
    // imagery map
    struct ImgMap
    {
        MapInfo info;
        PixelMat mat;
        ImgStationList stations;
        ImgPathList paths;
        ImgVirtualWallList virtual_walls;
    };
    // geographic map
    struct GeoMap
    {
        MapInfo info;
        RgbaPixelMat mat;
        GeoStationList stations;
        GeoPathList paths;
        GeoVirtualWallList virtual_walls;
    };

};// module wizrobo

#endif// WIZROBO_NPU_MAP_ICE
