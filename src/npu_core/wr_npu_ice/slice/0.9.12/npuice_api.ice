// -------------------------------------
// Version: 1.0.1
// Create Date: 2017-04-11
// Maintainer: <EMAIL>,
//             <EMAIL>,
//             <EMAIL>,
//             <EMAIL>
// -------------------------------------
// Change log: 2017-05-19 #lhan
//      1. rm npuice_base.ice & npuice_motor.ice & npuice_sensor.ice
//      2. add npuice_data.ice && npuice_param.ice
// -------------------------------------
// Change log: 2017-04-07 #lhan
//      1. Add imagery geometry
//      2. Change definition files into "geometry, motor, sensor and base"
//      3. Remove all "2D" subfix, all default pose is 3D
//      4. Add exception mechanism
#ifndef WIZROBO_NPUICE_API_ICE
#define WIZROBO_NPUICE_API_ICE

//// compilation setting
#define NPU_PRO 1

#include <npuice_exception.ice>
#include <npuice_geometry.ice>
#include <npuice_data.ice>
#include <npuice_param.ice>
#include <npuice_map.ice>

module wizrobo_npu {

    //// version
    const string NPU_API_VERSION = "0.9.12"; // lhan,sctu@2017-09-25
    const string NPU_ICE_VERSION = "3.5.0";

    //// enum
    // connection
    enum ServerState {UNKNOWN, CONNECTED, TIMEOUT, DISCONNECTED, MEM_EXCEED};
    // action state
    enum ActionState {IDLE_ACTION, SLAM_ACTION, NAVI_ACTION, TELEOP_ACTION, SWITCH_ACTION};
    enum NpuState {IDLE_STATE, SLAM_STATE, NAVI_STATE, TELEOP_STATE, SWITCH_STATE};
    // manual control
    enum ManualCmdType {MOVE_FWD,MOVE_BCK,TURN_LFT,TURN_RGT,MOVE_FWD_LFT,MOVE_FWD_RGT,STOP_MOVE};
    // navi
    enum NaviMode {P2P_NAVI, PF_NAVI, HYBRID_NAVI};// p2p, path_following, traj_tracking]
    enum NaviState {PENDING, ACTIVE, PREEMPTED, SUCCEEDED, ABORTED, REJECTED, PREEMPTING, RECALLING, RECALLED, LOST, IDLE};
    enum CcpMode {ZIGZAG_CCP, SPIRAL_CCP};
    // slam
    enum SlamMode {PF_SLAM, ICP_SLAM, WARE_SLAM};

    enum CheckMode {ALL , BCU , IMU , LIDAR};

    interface NpuIce
    {
        //// connection
        string GetServerVersion();
        void Connect(string version) throws NpuException;
        ServerState GetServerState();
        SystemDiagInfo GetSystemDiagInfo();
        NpuState GetNpuState();

        //// power
        void Shutdown();
        void Reboot();

        //// param
        // npu
        CoreParam GetCoreParam();
        void SetCoreParam(CoreParam param);
        // motor
        MotorParam GetMotorParam();
        void SetMotorParam(MotorParam param);
        // pid
        PidParam GetPidParam();
        void SetPidParam(PidParam param);
        // chassis
        ChassisParam GetChassisParam();
        void SetChassisParam(ChassisParam param);
        // footprint
        FootprintParam GetFootprintParam();
        void SetFootprintParam(FootprintParam param);
        // base
        BaseParam GetBaseParam();
        void SetBaseParam(BaseParam param) throws NpuException;
        // sensor
        SensorParam GetSensorParam();
        void SetSensorParam(SensorParam param);
        StringArray GetSupportedLidarTypes();
        StringArray GetSupportedImuTypes();
        StringArray GetSupportedCameraTypes();
        StringArray GetSupportedGpsTypes();
        // teleop
        TeleopParam GetTeleopParam();
        void SetTeleopParam(TeleopParam param);
        // navi
        NaviParam GetNaviParam();
        void SetNaviParam(NaviParam param);
        // slam
        SlamParam GetSlamParam();
        void SetSlamParam(SlamParam param);
        // record & play
        RecordParam GetRecordParam();
        void SetRecordParam(RecordParam param);
        PlayParam GetPlayParam();
        void SetPlayParam(PlayParam param);

        //// slave mode
        void FeedMotorEnc(MotorEnc enc);
        void FeedActMotorSpd(MotorSpd spd);

        //// motor data
        // enc
        MotorEnc GetMotorEnc();
        void ClearMotorEnc();
        // spd
        MotorSpd GetCmdMotorSpd();
        MotorSpd GetActMotorSpd();

        //// sensor data
        // lidar
        LidarScan GetLidarScan();
        ImgLidarScan GetImgLidarScan();
        // imu
        ImuData GetImuData();
        // sonar
        SonarScan GetSonarScan();
        ImgSonarScan GetImgSonarScan();
        // infrd
        InfrdScan GetInfrdScan();
        ImgInfrdScan GetImgInfrdScan();
        // bumper
        BumperArray GetBumperArray();
        // battery
        BatteryStatus GetBatteryStatus();

        //// manul control
        void SetManualCmd(ManualCmdType cmd);
        void SetManualVel(float lin_scale, float ang_scale);//scale = [0,1]

        //// config management
        StringArray GetConfigIdList();
        void SelectConfig(string id);//重启后生效
        void DeleteConfig(string id);//TODO
        void AddConfig(string id);//TODO

        //// bag management
        StringArray GetBagIdList();
        void SelectBag(string id);
        void DeleteBag(string id);//TODO

        //// map management
        // map
        MapInfoList GetMapInfos();
        void SetMapInfos(MapInfoList list);
        void SelectMap(string id);
        // station
        StationList GetStations(string map_id);
        void SetStations(string map_id, StationList list);
        ImgStationList GetImgStations(string map_id);
        void SetImgStations(string map_id, ImgStationList list) throws NpuException;
        // path
        PathList GetPaths(string map_id);
        void SetPaths(string map_id, PathList list);
        ImgPathList GetImgPaths(string map_id);
        void SetImgPaths(string map_id, ImgPathList list) throws NpuException;
        // virtual wall
        VirtualWallList GetVirtualWalls(string map_id);
        void SetVirtualWalls(string map_id, VirtualWallList virtual_walls);
        ImgVirtualWallList GetImgVirtualWalls(string map_id);
        void SetImgVirtualWalls(string map_id, ImgVirtualWallList virtual_walls) throws NpuException;
        
        //// common runtime data
        // vel
        Vel3D GetCmdVel();
        Vel3D GetActVel();
        // TODEL: be replaced by GetActVel()
        Vel3D GetCurrentVel();
        // acc
        Acc3D GetAcc();
        // TODEL: be replaced by GetAcc
        Acc3D GetCurrentAcc();
        // pose
        Pose3D GetCmdPose();
        ImgPose GetCmdImgPose();
        Pose3D GetActPose();
        ImgPose GetActImgPose();
        // TODEL: be replaced by GetActPose()
        Pose3D GetCurrentPose();
        // TODEL: be replaced by GetActImgPose()
        ImgPose GetCurrentImgPose();
        // path
        Path GetCmdPath();
        ImgPath GetCmdImgPath();
        Path GetActPath();
        ImgPath GetActImgPath();
        // TODEL: replaced by GetCmdPath()
        Path GetCurrentPath();
        // TODEL: replaced by GetCmdImgPath()
        ImgPath GetCurrentImgPath();
        // map
        Map2D GetMap();
        ImgMap GetImgMap();
        // TODEL: be replaced by GetMap()
        Map2D GetCurrentMap();
        // TODEL: be replaced by GetImgMap()
        ImgMap GetCurrentImgMap();
        // footprint
        Point3DList GetFootprintVertices();
        ImgPointList GetFootprintImgVertices();

        //// telop
        void StartTelop();
        void StopTelop();
        //// navi
        /// navi.common
        // initial pose
        void SetInitPose(Pose3D pose);
        void SetInitImgPose(ImgPose pose);
        // start & stop
        NaviMode GetNaviMode();// NEW
        void StartNavi(NaviMode mode);
        void StopNavi();
        // task control
        void PauseTask();
        void ContinueTask();
        void CancelTask();
        float GetTaskProgress();// [0, 1]
        // status
        NaviState GetNaviState();

        /// navi.p2p
        void GotoPose(Pose3D pose);
        void GotoImgPose(ImgPose pose);
        //TODEL: replaced by GotoPose(Pose3D pose);
        void GotoGoal(Pose3D goal);
        // TODEL: replaced by GotoImgPose(Pose3D pose);
        void GotoImgGoal(ImgPose goal);
        void GotoStation(string map_id, string station_id);
        /// navi.pf
        void FollowTempPath(Pose3DList poses);
        void FollowTempImgPath(ImgPoseList poses);
        void FollowPath(string map_id, string path_id);
        //// navi.ccp
        Pose3DList PlanCoveragePath(Point3DList vertices);
        ImgPoseList PlanCoverageImgPath(ImgPointList vertices);

        //// slam
        SlamMode GetSlamMode();// NEW
        void StartSlam(SlamMode mode);
        void StopSlam(string map_id) throws NpuException;// empty string means give up

        //// file
        ZipFile ExportConfigFile(string id);
        void ImportConfigFile(ZipFile file);
        ZipFile ExportMapFile(string id);
        void ImportMapFile(ZipFile file);

        //// record & play
        void StartRecord();
        void StopRecord(string bag_id);// empty string means give up
        void StartPlay();
        void StopPlay();

        ["amd"] FileInfo GetExportFileInfo(string fileName);
        FileData GetExportFiledata(string fileName,int chunk_index);
        void SendImportFileData(FileData data) throws NpuException;

        ////sensorstatus
        ["amd"] SensorStatus CheckSensorStatus(CheckMode mode);
        SensorStatus GetSensorStatus();

    };// interface NpuIce
};
#endif// WIZROBO_NPUICE_API_ICE
