// -------------------------------------
// Version: 1.0
// Date: 2017-05-19
// Maintainer: <EMAIL>,
//             <EMAIL>,
//             <EMAIL>,
//             <EMAIL>
// -------------------------------------
#ifndef WIZROBO_NPU_PARAM_ICE
#define WIZROBO_NPU_PARAM_ICE
#include <npuice_data.ice>
#include <npuice_geometry.ice>

module wizrobo_npu {

    //// npu
    enum NpuMode {MASTER, SLAVE};// TODEL
    struct CoreParam
    {
        string config_id;
        string map_id;
        string bag_id;
    };

    //// motor (sync with [wr_bcu_server]::bcu_driver_param.h
    enum BrakeType {SOFT_BRK, HARD_BRK};
    enum ValidOutputLevel {LOW_VALID, HIGH_VALID};
    struct MotorParam
    {
        /// sync with [wr_bcu_server]/bcu_driver_param.h
        // motor
        int motor_num;//
        string motor_dir_str;// "++--++--"
        int motor_max_spd_rpm;// [Rotation Per Minute]
        float motor_rdc_ratio;
        int motor_enc_res_ppr;// [Pulse Per Rotation]
        int motor_pwm_frq_hz;
        BrakeType motor_brk_type;
        // io valid level
        ValidOutputLevel enb_vol;
        ValidOutputLevel dir_vol;
        ValidOutputLevel pwm_vol;
        ValidOutputLevel brk_vol;
        // aux mode
        bool enb_auxdir_mode;
        // throttle mode
        bool enb_throttle_mode;
        float throttle_zero_pos_fac;
        float throttle_zero_neg_fac;
        // left-right switch
        bool end_left_right_switch;
    };
    //// pid
    struct PidParam
    {
        /// sync with [wr_bcu_server]/bcu_driver_param.h
        bool enb_pid;
        float kp_acc;
        float kp_dec;
        float ki_acc;
        float ki_dec;
        float kd_acc;
        float kd_dec;
    };
    //// footprint
    enum ShapeType {ROUND, RECTANGLE, POLYGON};
    struct FootprintParam
    {
        /// common
        ShapeType shape_type;
        float rot_center_offset_m;
        float height_m;
        /// round
        float round_radius_m;
        /// rectangle
        float rectangle_width_m;
        float rectangle_length_m;
        /// polygon
        int polygon_vertex_num;
        Point3DList polygon_vertices;
    };
    //// chassis
    enum ChassisModelType {CARLIKE, DIFFDRV, UNIVWHEEL, OMNIWHEEL};
    enum SteerEncLocation {CENTER, LEFT, RIGHT};
    struct ChassisParam
    {
        /// sync with [wr_chassis_server]/chassis_model_param.h
        /// common
        ChassisModelType model_type;
        float wheel_rdc_ratio;
        float wheel_radius_m;
        float wheel_span_m;
        float max_lin_acc_time_s;
        float max_ang_acc_time_s;
        float autoset_max_lin_spd_mps;
        float autoset_max_ang_spd_rps;
        /// carlike
        SteerEncLocation carlike_steer_enc_location;
        float carlike_axle_dist_m;
    };

    //// base
    struct BaseParam
    {
        string base_id;
        bool enb_slave_mode;
        MotorParam motor_param;
        PidParam pid_param;
        ChassisParam chassis_param;
        FootprintParam footprint_param;
    };

    //// sensor
    enum InterfaceType {ETHERNET, SERIAL, USB, CAN, ETHERCAT, BLUETOOTH};
    /// sensor/lidar
    struct LidarFilter
    {
        bool enb_itl_flt;
        bool enb_beam_skip;
        bool enb_fov_mastk;
        float itl_flt_dist_fac;
        float itl_flt_ori_fac;
        int beam_skip_step;
        FloatMatrix fov_mask_deg;
    };
    struct LidarParam
    {
        string type;
        InterfaceType itf_type;
        string itf_id;
        LidarFilter filter;
        Pose3D install_pose;
        double max_angle;
        double min_angle;
    };
    sequence<LidarParam> LidarParamList;
    /// sensor/imu
    struct ImuParam
    {
        string type;  //{URANUS_IMU, JY61_IMU, SCC2000_IMU, SIMIMU}
        InterfaceType itf_type;
        string itf_id;
        Pose3D install_pose;
    };
    sequence<ImuParam> ImuParamList;
    /// sensor>camera
    struct CameraParam
    {
        InterfaceType itf_type;
        string itf_id;
        string type;  //{PS3EYE_BIG_FOV, PS3EYE_SMALL_FOV, SIMCAMERA}
        int width_pix;
        int height_pix;
        int fps_hz;
        Pose3D install_pose;
    };
    sequence<CameraParam> CameraParamList;
    /// sensor>gps
    struct GpsParam
    {
        string type;  //{SIMGPS};
        InterfaceType itf_type;
        string itf_id;
        Pose3D install_pose;
    };
    sequence<GpsParam> GpsParamList;
    /// sensor>sonar
    struct SonarParam
    {
        float min_range_m;
        float max_range_m;
        float scan_freq_hz;
        float fov_deg;
        Pose3D install_pose;
    };
    sequence<SonarParam> SonarParamList;
    /// sensor>infrd
    struct InfrdParam
    {
        float min_range_m;
        float max_range_m;
        float fov_deg;
        Pose3D install_pose;
    };
    sequence<InfrdParam> InfrdParamList;
    /// sensor>bumper
    enum BumperLocation
    {
        FRONT_BUMPER, FRONT_LEFT_BUMPER, FRONT_RIGHT_BUMPER,
        BACK_BUMPER, BACK_LEFT_BUMPER, BACK_RIGHT_BUMPER,
        LEFT_BUMPER, RIGHT_BUMPER,
    };
    struct BumperParam
    {
        BumperLocation location;
    };
    sequence<BumperParam> BumperParamList;
    /// sensor>battery
    struct BatteryParam
    {
        float max_voltage_v;
        float capacity_ah;
    };
    /// sensor>all
    struct SensorParam
    {
        bool play_bag;
        bool record_bag;

        /// localization
        // lidar
        int lidar_num;
        LidarParamList lidar_params;
        // imu
        int imu_num;
        ImuParamList imu_params;
        // camera
        int camera_num;
        CameraParamList camera_params;
        // gps
        int gps_num;
        GpsParamList gps_params;

        /// obstacle detection
        // sonar
        int sonar_num;
        SonarParamList sonar_params;
        // infrd
        int infrd_num;
        InfrdParamList infrd_params;
        // bumper
        int bumper_num;
        BumperParamList bumper_params;

        /// misc
        BatteryParam battery_param;
    };

    //// teleop
    struct TeleopParam
    {
        float lin_spd_lmt_ratio;
        float ang_spd_lmt_ratio;
    };

    //// slam
    struct MapOptimizer
    {
        bool enb_map_opt;
        float map_res_mpp;
        float update_dist_thrs_m;
        float update_ori_thrs_rad;
        bool enb_trace_clearing;
        bool enb_filtering;
        int filter_size;
    };
    struct SlamParam
    {
        /// common
        float map_res_mpp;// [m/pixel]
        float update_dist_thrs_m;
        float update_ori_thrs_rad;
        float max_lin_spd_mps;
        float max_ang_spd_rps;
        /// optimization
        MapOptimizer optimizer;
    };

    //// navi
    struct P2pPlannerParam
    {
        float acc_lim_x_mps2;// m/s
        float acc_lim_theta_rps2;// rad/s
        float max_vel_x_mps;
        float min_vel_x_mps;
        float max_rot_vel_rps;
        float min_rot_vel_rps;
        float plan_strictness;
        float path_approach_avenue_factor;
        float safety_inflation_factor;
    };
    struct PfPlannerParam
    {
        bool waypoint_mode;
//        bool enable_acute_turn;
        float look_ahead_dist_m;
        float position_control_tolerance_m;
        float heading_control_tolerance_rad;
        float max_lin_vel_mps;//[m/s]
        float max_ang_vel_rps;
        float heading_kp;
    };
    struct CcpPlannerParam
    {
        float coverage_spacing_m;
    };
    struct NaviParam
    {
        /// common
        float lin_spd_lmt_ratio;
        float ang_spd_lmt_ratio;
        float x_err_tolr_m;
        float y_err_tolr_m;
        float yaw_err_tolr_rad;

        /// planners
        P2pPlannerParam p2p_planner_param;
        PfPlannerParam pf_planner_param;
        CcpPlannerParam ccp_planner_param;
    };
    //// record & play
    struct RecordParam
    {
        bool enb_record;
        bool record_tf_static;
    };
    struct PlayParam
    {
        bool enb_play;
        bool play_tf_static;
        float play_rate;
    };
};// module wizrobo

#endif// WIZROBO_NPU_PARAM_ICE
