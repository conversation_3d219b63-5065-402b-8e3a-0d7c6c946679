// -------------------------------------
// Version: 1.0
// Creation: 2017-05-19 by lhan
// Maintainer: <EMAIL>,
//             <EMAIL>,
//             <EMAIL>,
//             <EMAIL>
// -------------------------------------
#ifndef WIZROBO_NPU_DATA_ICE
#define WIZROBO_NPU_DATA_ICE
#include <npuice_geometry.ice>

module wizrobo_npu {

    //// array
    sequence<bool> BoolArray;
    sequence<int> IntArray;
    sequence<float> FloatArray;
    sequence<FloatArray> FloatMatrix;
    sequence<string> StringArray;
    sequence<byte> ByteArray;

    //// file
    sequence<byte> ZipFile;

    /// sensor_status
    enum DiagnosticsStatus{DISCONNECT, CONNECT, DATA_RECEIVED , DATA_ERROR , PROGRAM_NORMAL , PROGRAM_ERROR , IGNORE};


    struct SensorState
    {
         string sensor_id;
         DiagnosticsStatus hardware_status;
         DiagnosticsStatus topic_status;
         DiagnosticsStatus node_status;
    };
    sequence<SensorState> SensorStatus;

    //// system diagnostic
    struct SystemDiagInfo
    {
        string todo;
    };
    
    ////fileInfo
    struct FileInfo
    {
        string file_name;
        int chunk_num;
        int chunk_size_Bytes;
    };
    
    struct FileData
    {
        string file_name;
        int chunk_num;
        ZipFile data;
        int chunk_length;
        int chunk_index;
        int offset;
    };

    //// motor
    struct MotorEnc
    {
        int motor_num;
        IntArray ticks;
        float steer_angle_deg;
    };
    struct MotorSpd
    {
        int motor_num;
        FloatArray rpms;
        float steer_angle_deg;
    };

    //// lidar
    struct LidarScan
    {
        Point3DList points;
        FloatArray intensities;
    };
    struct ImgLidarScan
    {
        ImgPointList points;
        FloatArray intensities;
    };

    //// sonar
    struct SonarScan
    {
        Point3DList points;
    };
    struct ImgSonarScan
    {
        ImgPointList points;
    };

    //// infrd
    struct InfrdScan
    {
        Point3DList points;
    };
    struct ImgInfrdScan
    {
        ImgPointList points;
    };

    //// bumper
    struct BumperData
    {
        bool state;// [on/off]
    };
    sequence<BumperData> BumperDataList;
    struct BumperArray
    {
        BumperDataList states;
    };

    //// imu
    struct ImuData
    {
        float roll_deg;// [deg]
        float pitch_deg;// [deg]
        float yaw_deg;// [deg]
    };
    //// gps [TODO]
    struct GpsData// sync with [wr_bcu_server]
    {
        float latitude_deg;// [deg]
        float longitude_deg;// [deg]
        float altitude_m;// [deg]
    };
    //// battery [TODO]
    struct BatteryStatus
    {
        float current_a;// [A]
        float voltage_v;// [V]
        float temperature_deg;// [deg]
        float capacity_level;// [0~1]
    };
};// module wizrobo

#endif// WIZROBO_NPU_BASE_ICE
