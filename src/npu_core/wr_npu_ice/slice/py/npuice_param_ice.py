# **********************************************************************
#
# Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
#
# This copy of Ice is licensed to you under the terms described in the
# ICE_LICENSE file included in this distribution.
#
# **********************************************************************
#
# Ice version 3.5.1
#
# <auto-generated>
#
# Generated from file `npuice_param.ice'
#
# Warning: do not edit this file.
#
# </auto-generated>
#

import Ice, IcePy
import npuice_geometry_ice

# Included module wizrobo_npu
_M_wizrobo_npu = Ice.openModule('wizrobo_npu')

# Start of module wizrobo_npu
__name__ = 'wizrobo_npu'

if 'NpuMode' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.NpuMode = Ice.createTempClass()
    class NpuMode(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    NpuMode.MASTER = NpuMode("MASTER", 0)
    NpuMode.SLAVE = NpuMode("SLAVE", 1)
    NpuMode._enumerators = { 0:NpuMode.MASTER, 1:NpuMode.SLAVE }

    _M_wizrobo_npu._t_NpuMode = IcePy.defineEnum('::wizrobo_npu::NpuMode', NpuMode, (), NpuMode._enumerators)

    _M_wizrobo_npu.NpuMode = NpuMode
    del NpuMode

if 'CoreParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.CoreParam = Ice.createTempClass()
    class CoreParam(object):
        def __init__(self, npu_mode=_M_wizrobo_npu.NpuMode.MASTER, config_id='', map_id='', record_id=''):
            self.npu_mode = npu_mode
            self.config_id = config_id
            self.map_id = map_id
            self.record_id = record_id

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.npu_mode)
            _h = 5 * _h + Ice.getHash(self.config_id)
            _h = 5 * _h + Ice.getHash(self.map_id)
            _h = 5 * _h + Ice.getHash(self.record_id)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.CoreParam):
                return NotImplemented
            else:
                if self.npu_mode is None or other.npu_mode is None:
                    if self.npu_mode != other.npu_mode:
                        return (-1 if self.npu_mode is None else 1)
                else:
                    if self.npu_mode < other.npu_mode:
                        return -1
                    elif self.npu_mode > other.npu_mode:
                        return 1
                if self.config_id is None or other.config_id is None:
                    if self.config_id != other.config_id:
                        return (-1 if self.config_id is None else 1)
                else:
                    if self.config_id < other.config_id:
                        return -1
                    elif self.config_id > other.config_id:
                        return 1
                if self.map_id is None or other.map_id is None:
                    if self.map_id != other.map_id:
                        return (-1 if self.map_id is None else 1)
                else:
                    if self.map_id < other.map_id:
                        return -1
                    elif self.map_id > other.map_id:
                        return 1
                if self.record_id is None or other.record_id is None:
                    if self.record_id != other.record_id:
                        return (-1 if self.record_id is None else 1)
                else:
                    if self.record_id < other.record_id:
                        return -1
                    elif self.record_id > other.record_id:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_CoreParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_CoreParam = IcePy.defineStruct('::wizrobo_npu::CoreParam', CoreParam, (), (
        ('npu_mode', (), _M_wizrobo_npu._t_NpuMode),
        ('config_id', (), IcePy._t_string),
        ('map_id', (), IcePy._t_string),
        ('record_id', (), IcePy._t_string)
    ))

    _M_wizrobo_npu.CoreParam = CoreParam
    del CoreParam

if 'BrakeType' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.BrakeType = Ice.createTempClass()
    class BrakeType(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    BrakeType.SOFT_BRK = BrakeType("SOFT_BRK", 0)
    BrakeType.HARD_BRK = BrakeType("HARD_BRK", 1)
    BrakeType._enumerators = { 0:BrakeType.SOFT_BRK, 1:BrakeType.HARD_BRK }

    _M_wizrobo_npu._t_BrakeType = IcePy.defineEnum('::wizrobo_npu::BrakeType', BrakeType, (), BrakeType._enumerators)

    _M_wizrobo_npu.BrakeType = BrakeType
    del BrakeType

if 'ValidOutputLevel' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ValidOutputLevel = Ice.createTempClass()
    class ValidOutputLevel(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    ValidOutputLevel.LOW_VALID = ValidOutputLevel("LOW_VALID", 0)
    ValidOutputLevel.HIGH_VALID = ValidOutputLevel("HIGH_VALID", 1)
    ValidOutputLevel._enumerators = { 0:ValidOutputLevel.LOW_VALID, 1:ValidOutputLevel.HIGH_VALID }

    _M_wizrobo_npu._t_ValidOutputLevel = IcePy.defineEnum('::wizrobo_npu::ValidOutputLevel', ValidOutputLevel, (), ValidOutputLevel._enumerators)

    _M_wizrobo_npu.ValidOutputLevel = ValidOutputLevel
    del ValidOutputLevel

if 'MotorParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.MotorParam = Ice.createTempClass()
    class MotorParam(object):
        def __init__(self, motor_num=0, motor_dir_str='', motor_max_spd_rpm=0, motor_rdc_ratio=0.0, motor_enc_res_ppr=0, motor_pwm_frq_hz=0, motor_brk_type=_M_wizrobo_npu.BrakeType.SOFT_BRK, enb_vol=_M_wizrobo_npu.ValidOutputLevel.LOW_VALID, dir_vol=_M_wizrobo_npu.ValidOutputLevel.LOW_VALID, pwm_vol=_M_wizrobo_npu.ValidOutputLevel.LOW_VALID, brk_vol=_M_wizrobo_npu.ValidOutputLevel.LOW_VALID, enb_auxdir_mode=False, enb_throttle_mode=False, throttle_zero_pos_fac=0.0, throttle_zero_neg_fac=0.0, end_left_right_switch=False):
            self.motor_num = motor_num
            self.motor_dir_str = motor_dir_str
            self.motor_max_spd_rpm = motor_max_spd_rpm
            self.motor_rdc_ratio = motor_rdc_ratio
            self.motor_enc_res_ppr = motor_enc_res_ppr
            self.motor_pwm_frq_hz = motor_pwm_frq_hz
            self.motor_brk_type = motor_brk_type
            self.enb_vol = enb_vol
            self.dir_vol = dir_vol
            self.pwm_vol = pwm_vol
            self.brk_vol = brk_vol
            self.enb_auxdir_mode = enb_auxdir_mode
            self.enb_throttle_mode = enb_throttle_mode
            self.throttle_zero_pos_fac = throttle_zero_pos_fac
            self.throttle_zero_neg_fac = throttle_zero_neg_fac
            self.end_left_right_switch = end_left_right_switch

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.MotorParam):
                return NotImplemented
            else:
                if self.motor_num != other.motor_num:
                    return False
                if self.motor_dir_str != other.motor_dir_str:
                    return False
                if self.motor_max_spd_rpm != other.motor_max_spd_rpm:
                    return False
                if self.motor_rdc_ratio != other.motor_rdc_ratio:
                    return False
                if self.motor_enc_res_ppr != other.motor_enc_res_ppr:
                    return False
                if self.motor_pwm_frq_hz != other.motor_pwm_frq_hz:
                    return False
                if self.motor_brk_type != other.motor_brk_type:
                    return False
                if self.enb_vol != other.enb_vol:
                    return False
                if self.dir_vol != other.dir_vol:
                    return False
                if self.pwm_vol != other.pwm_vol:
                    return False
                if self.brk_vol != other.brk_vol:
                    return False
                if self.enb_auxdir_mode != other.enb_auxdir_mode:
                    return False
                if self.enb_throttle_mode != other.enb_throttle_mode:
                    return False
                if self.throttle_zero_pos_fac != other.throttle_zero_pos_fac:
                    return False
                if self.throttle_zero_neg_fac != other.throttle_zero_neg_fac:
                    return False
                if self.end_left_right_switch != other.end_left_right_switch:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_MotorParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_MotorParam = IcePy.defineStruct('::wizrobo_npu::MotorParam', MotorParam, (), (
        ('motor_num', (), IcePy._t_int),
        ('motor_dir_str', (), IcePy._t_string),
        ('motor_max_spd_rpm', (), IcePy._t_int),
        ('motor_rdc_ratio', (), IcePy._t_float),
        ('motor_enc_res_ppr', (), IcePy._t_int),
        ('motor_pwm_frq_hz', (), IcePy._t_int),
        ('motor_brk_type', (), _M_wizrobo_npu._t_BrakeType),
        ('enb_vol', (), _M_wizrobo_npu._t_ValidOutputLevel),
        ('dir_vol', (), _M_wizrobo_npu._t_ValidOutputLevel),
        ('pwm_vol', (), _M_wizrobo_npu._t_ValidOutputLevel),
        ('brk_vol', (), _M_wizrobo_npu._t_ValidOutputLevel),
        ('enb_auxdir_mode', (), IcePy._t_bool),
        ('enb_throttle_mode', (), IcePy._t_bool),
        ('throttle_zero_pos_fac', (), IcePy._t_float),
        ('throttle_zero_neg_fac', (), IcePy._t_float),
        ('end_left_right_switch', (), IcePy._t_bool)
    ))

    _M_wizrobo_npu.MotorParam = MotorParam
    del MotorParam

if 'PidParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.PidParam = Ice.createTempClass()
    class PidParam(object):
        def __init__(self, enb_pid=False, kp_acc=0.0, kp_dec=0.0, ki_acc=0.0, ki_dec=0.0, kd_acc=0.0, kd_dec=0.0):
            self.enb_pid = enb_pid
            self.kp_acc = kp_acc
            self.kp_dec = kp_dec
            self.ki_acc = ki_acc
            self.ki_dec = ki_dec
            self.kd_acc = kd_acc
            self.kd_dec = kd_dec

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.PidParam):
                return NotImplemented
            else:
                if self.enb_pid != other.enb_pid:
                    return False
                if self.kp_acc != other.kp_acc:
                    return False
                if self.kp_dec != other.kp_dec:
                    return False
                if self.ki_acc != other.ki_acc:
                    return False
                if self.ki_dec != other.ki_dec:
                    return False
                if self.kd_acc != other.kd_acc:
                    return False
                if self.kd_dec != other.kd_dec:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_PidParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_PidParam = IcePy.defineStruct('::wizrobo_npu::PidParam', PidParam, (), (
        ('enb_pid', (), IcePy._t_bool),
        ('kp_acc', (), IcePy._t_float),
        ('kp_dec', (), IcePy._t_float),
        ('ki_acc', (), IcePy._t_float),
        ('ki_dec', (), IcePy._t_float),
        ('kd_acc', (), IcePy._t_float),
        ('kd_dec', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.PidParam = PidParam
    del PidParam

if 'ShapeType' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ShapeType = Ice.createTempClass()
    class ShapeType(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    ShapeType.ROUND = ShapeType("ROUND", 0)
    ShapeType.RECTANGLE = ShapeType("RECTANGLE", 1)
    ShapeType.POLYGON = ShapeType("POLYGON", 2)
    ShapeType._enumerators = { 0:ShapeType.ROUND, 1:ShapeType.RECTANGLE, 2:ShapeType.POLYGON }

    _M_wizrobo_npu._t_ShapeType = IcePy.defineEnum('::wizrobo_npu::ShapeType', ShapeType, (), ShapeType._enumerators)

    _M_wizrobo_npu.ShapeType = ShapeType
    del ShapeType

if 'FootprintParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.FootprintParam = Ice.createTempClass()
    class FootprintParam(object):
        def __init__(self, shape_type=_M_wizrobo_npu.ShapeType.ROUND, rot_center_offset_m=0.0, round_radius_m=0.0, rectangle_width_m=0.0, rectangle_length_m=0.0, polygon_vertex_num=0, polygon_vertices=None):
            self.shape_type = shape_type
            self.rot_center_offset_m = rot_center_offset_m
            self.round_radius_m = round_radius_m
            self.rectangle_width_m = rectangle_width_m
            self.rectangle_length_m = rectangle_length_m
            self.polygon_vertex_num = polygon_vertex_num
            self.polygon_vertices = polygon_vertices

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.FootprintParam):
                return NotImplemented
            else:
                if self.shape_type != other.shape_type:
                    return False
                if self.rot_center_offset_m != other.rot_center_offset_m:
                    return False
                if self.round_radius_m != other.round_radius_m:
                    return False
                if self.rectangle_width_m != other.rectangle_width_m:
                    return False
                if self.rectangle_length_m != other.rectangle_length_m:
                    return False
                if self.polygon_vertex_num != other.polygon_vertex_num:
                    return False
                if self.polygon_vertices != other.polygon_vertices:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_FootprintParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_FootprintParam = IcePy.defineStruct('::wizrobo_npu::FootprintParam', FootprintParam, (), (
        ('shape_type', (), _M_wizrobo_npu._t_ShapeType),
        ('rot_center_offset_m', (), IcePy._t_float),
        ('round_radius_m', (), IcePy._t_float),
        ('rectangle_width_m', (), IcePy._t_float),
        ('rectangle_length_m', (), IcePy._t_float),
        ('polygon_vertex_num', (), IcePy._t_int),
        ('polygon_vertices', (), _M_wizrobo_npu._t_Point3DList)
    ))

    _M_wizrobo_npu.FootprintParam = FootprintParam
    del FootprintParam

if 'ChassisModelType' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ChassisModelType = Ice.createTempClass()
    class ChassisModelType(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    ChassisModelType.CARLIKE = ChassisModelType("CARLIKE", 0)
    ChassisModelType.DIFFDRV = ChassisModelType("DIFFDRV", 1)
    ChassisModelType.UNIVWHEEL = ChassisModelType("UNIVWHEEL", 2)
    ChassisModelType.OMNIWHEEL = ChassisModelType("OMNIWHEEL", 3)
    ChassisModelType._enumerators = { 0:ChassisModelType.CARLIKE, 1:ChassisModelType.DIFFDRV, 2:ChassisModelType.UNIVWHEEL, 3:ChassisModelType.OMNIWHEEL }

    _M_wizrobo_npu._t_ChassisModelType = IcePy.defineEnum('::wizrobo_npu::ChassisModelType', ChassisModelType, (), ChassisModelType._enumerators)

    _M_wizrobo_npu.ChassisModelType = ChassisModelType
    del ChassisModelType

if 'SteerEncLocation' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.SteerEncLocation = Ice.createTempClass()
    class SteerEncLocation(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    SteerEncLocation.CENTER = SteerEncLocation("CENTER", 0)
    SteerEncLocation.LEFT = SteerEncLocation("LEFT", 1)
    SteerEncLocation.RIGHT = SteerEncLocation("RIGHT", 2)
    SteerEncLocation._enumerators = { 0:SteerEncLocation.CENTER, 1:SteerEncLocation.LEFT, 2:SteerEncLocation.RIGHT }

    _M_wizrobo_npu._t_SteerEncLocation = IcePy.defineEnum('::wizrobo_npu::SteerEncLocation', SteerEncLocation, (), SteerEncLocation._enumerators)

    _M_wizrobo_npu.SteerEncLocation = SteerEncLocation
    del SteerEncLocation

if 'ChassisParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ChassisParam = Ice.createTempClass()
    class ChassisParam(object):
        def __init__(self, model_type=_M_wizrobo_npu.ChassisModelType.CARLIKE, wheel_rdc_ratio=0.0, wheel_radius_m=0.0, wheel_span_m=0.0, max_lin_acc_time_s=0.0, max_ang_acc_time_s=0.0, autoset_max_lin_spd_mps=0.0, autoset_max_ang_spd_rps=0.0, carlike_steer_enc_location=_M_wizrobo_npu.SteerEncLocation.CENTER, carlike_axle_dist_m=0.0):
            self.model_type = model_type
            self.wheel_rdc_ratio = wheel_rdc_ratio
            self.wheel_radius_m = wheel_radius_m
            self.wheel_span_m = wheel_span_m
            self.max_lin_acc_time_s = max_lin_acc_time_s
            self.max_ang_acc_time_s = max_ang_acc_time_s
            self.autoset_max_lin_spd_mps = autoset_max_lin_spd_mps
            self.autoset_max_ang_spd_rps = autoset_max_ang_spd_rps
            self.carlike_steer_enc_location = carlike_steer_enc_location
            self.carlike_axle_dist_m = carlike_axle_dist_m

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.ChassisParam):
                return NotImplemented
            else:
                if self.model_type != other.model_type:
                    return False
                if self.wheel_rdc_ratio != other.wheel_rdc_ratio:
                    return False
                if self.wheel_radius_m != other.wheel_radius_m:
                    return False
                if self.wheel_span_m != other.wheel_span_m:
                    return False
                if self.max_lin_acc_time_s != other.max_lin_acc_time_s:
                    return False
                if self.max_ang_acc_time_s != other.max_ang_acc_time_s:
                    return False
                if self.autoset_max_lin_spd_mps != other.autoset_max_lin_spd_mps:
                    return False
                if self.autoset_max_ang_spd_rps != other.autoset_max_ang_spd_rps:
                    return False
                if self.carlike_steer_enc_location != other.carlike_steer_enc_location:
                    return False
                if self.carlike_axle_dist_m != other.carlike_axle_dist_m:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ChassisParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_ChassisParam = IcePy.defineStruct('::wizrobo_npu::ChassisParam', ChassisParam, (), (
        ('model_type', (), _M_wizrobo_npu._t_ChassisModelType),
        ('wheel_rdc_ratio', (), IcePy._t_float),
        ('wheel_radius_m', (), IcePy._t_float),
        ('wheel_span_m', (), IcePy._t_float),
        ('max_lin_acc_time_s', (), IcePy._t_float),
        ('max_ang_acc_time_s', (), IcePy._t_float),
        ('autoset_max_lin_spd_mps', (), IcePy._t_float),
        ('autoset_max_ang_spd_rps', (), IcePy._t_float),
        ('carlike_steer_enc_location', (), _M_wizrobo_npu._t_SteerEncLocation),
        ('carlike_axle_dist_m', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.ChassisParam = ChassisParam
    del ChassisParam

if 'BaseParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.BaseParam = Ice.createTempClass()
    class BaseParam(object):
        def __init__(self, base_id='', enb_slave_mode=False, motor_param=Ice._struct_marker, pid_param=Ice._struct_marker, chassis_param=Ice._struct_marker, footprint_param=Ice._struct_marker):
            self.base_id = base_id
            self.enb_slave_mode = enb_slave_mode
            if motor_param is Ice._struct_marker:
                self.motor_param = _M_wizrobo_npu.MotorParam()
            else:
                self.motor_param = motor_param
            if pid_param is Ice._struct_marker:
                self.pid_param = _M_wizrobo_npu.PidParam()
            else:
                self.pid_param = pid_param
            if chassis_param is Ice._struct_marker:
                self.chassis_param = _M_wizrobo_npu.ChassisParam()
            else:
                self.chassis_param = chassis_param
            if footprint_param is Ice._struct_marker:
                self.footprint_param = _M_wizrobo_npu.FootprintParam()
            else:
                self.footprint_param = footprint_param

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.BaseParam):
                return NotImplemented
            else:
                if self.base_id != other.base_id:
                    return False
                if self.enb_slave_mode != other.enb_slave_mode:
                    return False
                if self.motor_param != other.motor_param:
                    return False
                if self.pid_param != other.pid_param:
                    return False
                if self.chassis_param != other.chassis_param:
                    return False
                if self.footprint_param != other.footprint_param:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_BaseParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_BaseParam = IcePy.defineStruct('::wizrobo_npu::BaseParam', BaseParam, (), (
        ('base_id', (), IcePy._t_string),
        ('enb_slave_mode', (), IcePy._t_bool),
        ('motor_param', (), _M_wizrobo_npu._t_MotorParam),
        ('pid_param', (), _M_wizrobo_npu._t_PidParam),
        ('chassis_param', (), _M_wizrobo_npu._t_ChassisParam),
        ('footprint_param', (), _M_wizrobo_npu._t_FootprintParam)
    ))

    _M_wizrobo_npu.BaseParam = BaseParam
    del BaseParam

if 'LidarType' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.LidarType = Ice.createTempClass()
    class LidarType(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    LidarType.RPLIDAR = LidarType("RPLIDAR", 0)
    LidarType.RSLIDAR = LidarType("RSLIDAR", 1)
    LidarType.LSLIDAR = LidarType("LSLIDAR", 2)
    LidarType.FSLIDAR = LidarType("FSLIDAR", 3)
    LidarType.SICK_TIM551 = LidarType("SICK_TIM551", 4)
    LidarType.SICK_TIM561 = LidarType("SICK_TIM561", 5)
    LidarType.SICK_TIM571 = LidarType("SICK_TIM571", 6)
    LidarType.SICK_LMS111 = LidarType("SICK_LMS111", 7)
    LidarType.SICK_LMS141 = LidarType("SICK_LMS141", 8)
    LidarType.SICK_LMS151 = LidarType("SICK_LMS151", 9)
    LidarType.SICK_LMS511 = LidarType("SICK_LMS511", 10)
    LidarType.SICK_S300 = LidarType("SICK_S300", 11)
    LidarType.HOKUYO_UST10LX = LidarType("HOKUYO_UST10LX", 12)
    LidarType.HOKUYO_UTM30LX = LidarType("HOKUYO_UTM30LX", 13)
    LidarType.PF_R2000_20HZ = LidarType("PF_R2000_20HZ", 14)
    LidarType.PF_R2000_50HZ = LidarType("PF_R2000_50HZ", 15)
    LidarType.XTION_LIDAR = LidarType("XTION_LIDAR", 16)
    LidarType.SIMLIDAR = LidarType("SIMLIDAR", 17)
    LidarType._enumerators = { 0:LidarType.RPLIDAR, 1:LidarType.RSLIDAR, 2:LidarType.LSLIDAR, 3:LidarType.FSLIDAR, 4:LidarType.SICK_TIM551, 5:LidarType.SICK_TIM561, 6:LidarType.SICK_TIM571, 7:LidarType.SICK_LMS111, 8:LidarType.SICK_LMS141, 9:LidarType.SICK_LMS151, 10:LidarType.SICK_LMS511, 11:LidarType.SICK_S300, 12:LidarType.HOKUYO_UST10LX, 13:LidarType.HOKUYO_UTM30LX, 14:LidarType.PF_R2000_20HZ, 15:LidarType.PF_R2000_50HZ, 16:LidarType.XTION_LIDAR, 17:LidarType.SIMLIDAR }

    _M_wizrobo_npu._t_LidarType = IcePy.defineEnum('::wizrobo_npu::LidarType', LidarType, (), LidarType._enumerators)

    _M_wizrobo_npu.LidarType = LidarType
    del LidarType

if 'InterfaceType' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.InterfaceType = Ice.createTempClass()
    class InterfaceType(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    InterfaceType.ETHERNET = InterfaceType("ETHERNET", 0)
    InterfaceType.SERIAL = InterfaceType("SERIAL", 1)
    InterfaceType.RECORD = InterfaceType("RECORD", 2)
    InterfaceType._enumerators = { 0:InterfaceType.ETHERNET, 1:InterfaceType.SERIAL, 2:InterfaceType.RECORD }

    _M_wizrobo_npu._t_InterfaceType = IcePy.defineEnum('::wizrobo_npu::InterfaceType', InterfaceType, (), InterfaceType._enumerators)

    _M_wizrobo_npu.InterfaceType = InterfaceType
    del InterfaceType

if 'LidarParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.LidarParam = Ice.createTempClass()
    class LidarParam(object):
        def __init__(self, type=_M_wizrobo_npu.LidarType.RPLIDAR, itf_type=_M_wizrobo_npu.InterfaceType.ETHERNET, serial_port_id='', ethernet_ip='', enb_itl_filter=False, install_pose=Ice._struct_marker, record_file_id='', max_angle=0.0, min_angle=0.0):
            self.type = type
            self.itf_type = itf_type
            self.serial_port_id = serial_port_id
            self.ethernet_ip = ethernet_ip
            self.enb_itl_filter = enb_itl_filter
            if install_pose is Ice._struct_marker:
                self.install_pose = _M_wizrobo_npu.Pose3D()
            else:
                self.install_pose = install_pose
            self.record_file_id = record_file_id
            self.max_angle = max_angle
            self.min_angle = min_angle

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.LidarParam):
                return NotImplemented
            else:
                if self.type != other.type:
                    return False
                if self.itf_type != other.itf_type:
                    return False
                if self.serial_port_id != other.serial_port_id:
                    return False
                if self.ethernet_ip != other.ethernet_ip:
                    return False
                if self.enb_itl_filter != other.enb_itl_filter:
                    return False
                if self.install_pose != other.install_pose:
                    return False
                if self.record_file_id != other.record_file_id:
                    return False
                if self.max_angle != other.max_angle:
                    return False
                if self.min_angle != other.min_angle:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_LidarParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_LidarParam = IcePy.defineStruct('::wizrobo_npu::LidarParam', LidarParam, (), (
        ('type', (), _M_wizrobo_npu._t_LidarType),
        ('itf_type', (), _M_wizrobo_npu._t_InterfaceType),
        ('serial_port_id', (), IcePy._t_string),
        ('ethernet_ip', (), IcePy._t_string),
        ('enb_itl_filter', (), IcePy._t_bool),
        ('install_pose', (), _M_wizrobo_npu._t_Pose3D),
        ('record_file_id', (), IcePy._t_string),
        ('max_angle', (), IcePy._t_double),
        ('min_angle', (), IcePy._t_double)
    ))

    _M_wizrobo_npu.LidarParam = LidarParam
    del LidarParam

if '_t_LidarParamList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_LidarParamList = IcePy.defineSequence('::wizrobo_npu::LidarParamList', (), _M_wizrobo_npu._t_LidarParam)

if 'ImuParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImuParam = Ice.createTempClass()
    class ImuParam(object):
        def __init__(self, install_pose=Ice._struct_marker):
            if install_pose is Ice._struct_marker:
                self.install_pose = _M_wizrobo_npu.Pose3D()
            else:
                self.install_pose = install_pose

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.ImuParam):
                return NotImplemented
            else:
                if self.install_pose != other.install_pose:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImuParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImuParam = IcePy.defineStruct('::wizrobo_npu::ImuParam', ImuParam, (), (('install_pose', (), _M_wizrobo_npu._t_Pose3D),))

    _M_wizrobo_npu.ImuParam = ImuParam
    del ImuParam

if '_t_ImuParamList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_ImuParamList = IcePy.defineSequence('::wizrobo_npu::ImuParamList', (), _M_wizrobo_npu._t_ImuParam)

if 'SonarParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.SonarParam = Ice.createTempClass()
    class SonarParam(object):
        def __init__(self, min_range_m=0.0, max_range_m=0.0, scan_freq_hz=0.0, fov_deg=0.0, install_pose=Ice._struct_marker):
            self.min_range_m = min_range_m
            self.max_range_m = max_range_m
            self.scan_freq_hz = scan_freq_hz
            self.fov_deg = fov_deg
            if install_pose is Ice._struct_marker:
                self.install_pose = _M_wizrobo_npu.Pose3D()
            else:
                self.install_pose = install_pose

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.SonarParam):
                return NotImplemented
            else:
                if self.min_range_m != other.min_range_m:
                    return False
                if self.max_range_m != other.max_range_m:
                    return False
                if self.scan_freq_hz != other.scan_freq_hz:
                    return False
                if self.fov_deg != other.fov_deg:
                    return False
                if self.install_pose != other.install_pose:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_SonarParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_SonarParam = IcePy.defineStruct('::wizrobo_npu::SonarParam', SonarParam, (), (
        ('min_range_m', (), IcePy._t_float),
        ('max_range_m', (), IcePy._t_float),
        ('scan_freq_hz', (), IcePy._t_float),
        ('fov_deg', (), IcePy._t_float),
        ('install_pose', (), _M_wizrobo_npu._t_Pose3D)
    ))

    _M_wizrobo_npu.SonarParam = SonarParam
    del SonarParam

if '_t_SonarParamList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_SonarParamList = IcePy.defineSequence('::wizrobo_npu::SonarParamList', (), _M_wizrobo_npu._t_SonarParam)

if 'InfrdParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.InfrdParam = Ice.createTempClass()
    class InfrdParam(object):
        def __init__(self, min_range_m=0.0, max_range_m=0.0, fov_deg=0.0, install_pose=Ice._struct_marker):
            self.min_range_m = min_range_m
            self.max_range_m = max_range_m
            self.fov_deg = fov_deg
            if install_pose is Ice._struct_marker:
                self.install_pose = _M_wizrobo_npu.Pose3D()
            else:
                self.install_pose = install_pose

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.InfrdParam):
                return NotImplemented
            else:
                if self.min_range_m != other.min_range_m:
                    return False
                if self.max_range_m != other.max_range_m:
                    return False
                if self.fov_deg != other.fov_deg:
                    return False
                if self.install_pose != other.install_pose:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_InfrdParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_InfrdParam = IcePy.defineStruct('::wizrobo_npu::InfrdParam', InfrdParam, (), (
        ('min_range_m', (), IcePy._t_float),
        ('max_range_m', (), IcePy._t_float),
        ('fov_deg', (), IcePy._t_float),
        ('install_pose', (), _M_wizrobo_npu._t_Pose3D)
    ))

    _M_wizrobo_npu.InfrdParam = InfrdParam
    del InfrdParam

if '_t_InfrdParamList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_InfrdParamList = IcePy.defineSequence('::wizrobo_npu::InfrdParamList', (), _M_wizrobo_npu._t_InfrdParam)

if 'BumperLocation' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.BumperLocation = Ice.createTempClass()
    class BumperLocation(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    BumperLocation.FRONT_BUMPER = BumperLocation("FRONT_BUMPER", 0)
    BumperLocation.FRONT_LEFT_BUMPER = BumperLocation("FRONT_LEFT_BUMPER", 1)
    BumperLocation.FRONT_RIGHT_BUMPER = BumperLocation("FRONT_RIGHT_BUMPER", 2)
    BumperLocation.BACK_BUMPER = BumperLocation("BACK_BUMPER", 3)
    BumperLocation.BACK_LEFT_BUMPER = BumperLocation("BACK_LEFT_BUMPER", 4)
    BumperLocation.BACK_RIGHT_BUMPER = BumperLocation("BACK_RIGHT_BUMPER", 5)
    BumperLocation.LEFT_BUMPER = BumperLocation("LEFT_BUMPER", 6)
    BumperLocation.RIGHT_BUMPER = BumperLocation("RIGHT_BUMPER", 7)
    BumperLocation._enumerators = { 0:BumperLocation.FRONT_BUMPER, 1:BumperLocation.FRONT_LEFT_BUMPER, 2:BumperLocation.FRONT_RIGHT_BUMPER, 3:BumperLocation.BACK_BUMPER, 4:BumperLocation.BACK_LEFT_BUMPER, 5:BumperLocation.BACK_RIGHT_BUMPER, 6:BumperLocation.LEFT_BUMPER, 7:BumperLocation.RIGHT_BUMPER }

    _M_wizrobo_npu._t_BumperLocation = IcePy.defineEnum('::wizrobo_npu::BumperLocation', BumperLocation, (), BumperLocation._enumerators)

    _M_wizrobo_npu.BumperLocation = BumperLocation
    del BumperLocation

if 'BumperParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.BumperParam = Ice.createTempClass()
    class BumperParam(object):
        def __init__(self, location=_M_wizrobo_npu.BumperLocation.FRONT_BUMPER):
            self.location = location

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.location)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.BumperParam):
                return NotImplemented
            else:
                if self.location is None or other.location is None:
                    if self.location != other.location:
                        return (-1 if self.location is None else 1)
                else:
                    if self.location < other.location:
                        return -1
                    elif self.location > other.location:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_BumperParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_BumperParam = IcePy.defineStruct('::wizrobo_npu::BumperParam', BumperParam, (), (('location', (), _M_wizrobo_npu._t_BumperLocation),))

    _M_wizrobo_npu.BumperParam = BumperParam
    del BumperParam

if '_t_BumperParamList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_BumperParamList = IcePy.defineSequence('::wizrobo_npu::BumperParamList', (), _M_wizrobo_npu._t_BumperParam)

if 'GpsParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.GpsParam = Ice.createTempClass()
    class GpsParam(object):
        def __init__(self, install_pose=Ice._struct_marker):
            if install_pose is Ice._struct_marker:
                self.install_pose = _M_wizrobo_npu.Pose3D()
            else:
                self.install_pose = install_pose

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.GpsParam):
                return NotImplemented
            else:
                if self.install_pose != other.install_pose:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_GpsParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_GpsParam = IcePy.defineStruct('::wizrobo_npu::GpsParam', GpsParam, (), (('install_pose', (), _M_wizrobo_npu._t_Pose3D),))

    _M_wizrobo_npu.GpsParam = GpsParam
    del GpsParam

if '_t_GpsParamList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_GpsParamList = IcePy.defineSequence('::wizrobo_npu::GpsParamList', (), _M_wizrobo_npu._t_GpsParam)

if 'CameraParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.CameraParam = Ice.createTempClass()
    class CameraParam(object):
        def __init__(self, install_pose=Ice._struct_marker):
            if install_pose is Ice._struct_marker:
                self.install_pose = _M_wizrobo_npu.Pose3D()
            else:
                self.install_pose = install_pose

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.CameraParam):
                return NotImplemented
            else:
                if self.install_pose != other.install_pose:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_CameraParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_CameraParam = IcePy.defineStruct('::wizrobo_npu::CameraParam', CameraParam, (), (('install_pose', (), _M_wizrobo_npu._t_Pose3D),))

    _M_wizrobo_npu.CameraParam = CameraParam
    del CameraParam

if '_t_CameraParamList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_CameraParamList = IcePy.defineSequence('::wizrobo_npu::CameraParamList', (), _M_wizrobo_npu._t_CameraParam)

if 'BatteryParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.BatteryParam = Ice.createTempClass()
    class BatteryParam(object):
        def __init__(self, max_voltage_v=0.0, capacity_ah=0.0):
            self.max_voltage_v = max_voltage_v
            self.capacity_ah = capacity_ah

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.BatteryParam):
                return NotImplemented
            else:
                if self.max_voltage_v != other.max_voltage_v:
                    return False
                if self.capacity_ah != other.capacity_ah:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_BatteryParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_BatteryParam = IcePy.defineStruct('::wizrobo_npu::BatteryParam', BatteryParam, (), (
        ('max_voltage_v', (), IcePy._t_float),
        ('capacity_ah', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.BatteryParam = BatteryParam
    del BatteryParam

if 'SensorParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.SensorParam = Ice.createTempClass()
    class SensorParam(object):
        def __init__(self, lidar_num=0, lidar_params=None, sonar_num=0, sonar_params=None, infrd_num=0, infrd_params=None, bumper_num=0, bumper_params=None, imu_num=0, imu_params=None, gps_num=0, gps_params=None, camera_num=0, camera_params=None):
            self.lidar_num = lidar_num
            self.lidar_params = lidar_params
            self.sonar_num = sonar_num
            self.sonar_params = sonar_params
            self.infrd_num = infrd_num
            self.infrd_params = infrd_params
            self.bumper_num = bumper_num
            self.bumper_params = bumper_params
            self.imu_num = imu_num
            self.imu_params = imu_params
            self.gps_num = gps_num
            self.gps_params = gps_params
            self.camera_num = camera_num
            self.camera_params = camera_params

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.SensorParam):
                return NotImplemented
            else:
                if self.lidar_num != other.lidar_num:
                    return False
                if self.lidar_params != other.lidar_params:
                    return False
                if self.sonar_num != other.sonar_num:
                    return False
                if self.sonar_params != other.sonar_params:
                    return False
                if self.infrd_num != other.infrd_num:
                    return False
                if self.infrd_params != other.infrd_params:
                    return False
                if self.bumper_num != other.bumper_num:
                    return False
                if self.bumper_params != other.bumper_params:
                    return False
                if self.imu_num != other.imu_num:
                    return False
                if self.imu_params != other.imu_params:
                    return False
                if self.gps_num != other.gps_num:
                    return False
                if self.gps_params != other.gps_params:
                    return False
                if self.camera_num != other.camera_num:
                    return False
                if self.camera_params != other.camera_params:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_SensorParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_SensorParam = IcePy.defineStruct('::wizrobo_npu::SensorParam', SensorParam, (), (
        ('lidar_num', (), IcePy._t_int),
        ('lidar_params', (), _M_wizrobo_npu._t_LidarParamList),
        ('sonar_num', (), IcePy._t_int),
        ('sonar_params', (), _M_wizrobo_npu._t_SonarParamList),
        ('infrd_num', (), IcePy._t_int),
        ('infrd_params', (), _M_wizrobo_npu._t_InfrdParamList),
        ('bumper_num', (), IcePy._t_int),
        ('bumper_params', (), _M_wizrobo_npu._t_BumperParamList),
        ('imu_num', (), IcePy._t_int),
        ('imu_params', (), _M_wizrobo_npu._t_ImuParamList),
        ('gps_num', (), IcePy._t_int),
        ('gps_params', (), _M_wizrobo_npu._t_GpsParamList),
        ('camera_num', (), IcePy._t_int),
        ('camera_params', (), _M_wizrobo_npu._t_CameraParamList)
    ))

    _M_wizrobo_npu.SensorParam = SensorParam
    del SensorParam

if 'TeleopParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.TeleopParam = Ice.createTempClass()
    class TeleopParam(object):
        def __init__(self, lin_spd_lmt_ratio=0.0, ang_spd_lmt_ratio=0.0):
            self.lin_spd_lmt_ratio = lin_spd_lmt_ratio
            self.ang_spd_lmt_ratio = ang_spd_lmt_ratio

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.TeleopParam):
                return NotImplemented
            else:
                if self.lin_spd_lmt_ratio != other.lin_spd_lmt_ratio:
                    return False
                if self.ang_spd_lmt_ratio != other.ang_spd_lmt_ratio:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_TeleopParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_TeleopParam = IcePy.defineStruct('::wizrobo_npu::TeleopParam', TeleopParam, (), (
        ('lin_spd_lmt_ratio', (), IcePy._t_float),
        ('ang_spd_lmt_ratio', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.TeleopParam = TeleopParam
    del TeleopParam

if 'MapOptimizer' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.MapOptimizer = Ice.createTempClass()
    class MapOptimizer(object):
        def __init__(self, enb_map_opt=False, map_res_mpp=0.0, update_dist_thrs_m=0.0, update_ori_thrs_rad=0.0, enb_trace_clearing=False, enb_filtering=False, filter_size=0):
            self.enb_map_opt = enb_map_opt
            self.map_res_mpp = map_res_mpp
            self.update_dist_thrs_m = update_dist_thrs_m
            self.update_ori_thrs_rad = update_ori_thrs_rad
            self.enb_trace_clearing = enb_trace_clearing
            self.enb_filtering = enb_filtering
            self.filter_size = filter_size

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.MapOptimizer):
                return NotImplemented
            else:
                if self.enb_map_opt != other.enb_map_opt:
                    return False
                if self.map_res_mpp != other.map_res_mpp:
                    return False
                if self.update_dist_thrs_m != other.update_dist_thrs_m:
                    return False
                if self.update_ori_thrs_rad != other.update_ori_thrs_rad:
                    return False
                if self.enb_trace_clearing != other.enb_trace_clearing:
                    return False
                if self.enb_filtering != other.enb_filtering:
                    return False
                if self.filter_size != other.filter_size:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_MapOptimizer)

        __repr__ = __str__

    _M_wizrobo_npu._t_MapOptimizer = IcePy.defineStruct('::wizrobo_npu::MapOptimizer', MapOptimizer, (), (
        ('enb_map_opt', (), IcePy._t_bool),
        ('map_res_mpp', (), IcePy._t_float),
        ('update_dist_thrs_m', (), IcePy._t_float),
        ('update_ori_thrs_rad', (), IcePy._t_float),
        ('enb_trace_clearing', (), IcePy._t_bool),
        ('enb_filtering', (), IcePy._t_bool),
        ('filter_size', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.MapOptimizer = MapOptimizer
    del MapOptimizer

if 'SlamParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.SlamParam = Ice.createTempClass()
    class SlamParam(object):
        def __init__(self, map_res_mpp=0.0, map_size_m=0, update_dist_thrs_m=0.0, update_ori_thrs_rad=0.0, max_lin_spd_mps=0.0, max_ang_spd_rps=0.0, optimizer_param=Ice._struct_marker):
            self.map_res_mpp = map_res_mpp
            self.map_size_m = map_size_m
            self.update_dist_thrs_m = update_dist_thrs_m
            self.update_ori_thrs_rad = update_ori_thrs_rad
            self.max_lin_spd_mps = max_lin_spd_mps
            self.max_ang_spd_rps = max_ang_spd_rps
            if optimizer_param is Ice._struct_marker:
                self.optimizer_param = _M_wizrobo_npu.MapOptimizer()
            else:
                self.optimizer_param = optimizer_param

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.SlamParam):
                return NotImplemented
            else:
                if self.map_res_mpp != other.map_res_mpp:
                    return False
                if self.map_size_m != other.map_size_m:
                    return False
                if self.update_dist_thrs_m != other.update_dist_thrs_m:
                    return False
                if self.update_ori_thrs_rad != other.update_ori_thrs_rad:
                    return False
                if self.max_lin_spd_mps != other.max_lin_spd_mps:
                    return False
                if self.max_ang_spd_rps != other.max_ang_spd_rps:
                    return False
                if self.optimizer_param != other.optimizer_param:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_SlamParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_SlamParam = IcePy.defineStruct('::wizrobo_npu::SlamParam', SlamParam, (), (
        ('map_res_mpp', (), IcePy._t_float),
        ('map_size_m', (), IcePy._t_int),
        ('update_dist_thrs_m', (), IcePy._t_float),
        ('update_ori_thrs_rad', (), IcePy._t_float),
        ('max_lin_spd_mps', (), IcePy._t_float),
        ('max_ang_spd_rps', (), IcePy._t_float),
        ('optimizer_param', (), _M_wizrobo_npu._t_MapOptimizer)
    ))

    _M_wizrobo_npu.SlamParam = SlamParam
    del SlamParam

if 'P2pPlannerParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.P2pPlannerParam = Ice.createTempClass()
    class P2pPlannerParam(object):
        def __init__(self, acc_lim_x_mps2=0.0, acc_lim_theta_rps2=0.0, max_vel_x_mps=0.0, min_vel_x_mps=0.0, max_rot_vel_rps=0.0, min_rot_vel_rps=0.0, plan_strictness=0.0, path_approach_avenue_factor=0.0, safety_inflation_factor=0.0):
            self.acc_lim_x_mps2 = acc_lim_x_mps2
            self.acc_lim_theta_rps2 = acc_lim_theta_rps2
            self.max_vel_x_mps = max_vel_x_mps
            self.min_vel_x_mps = min_vel_x_mps
            self.max_rot_vel_rps = max_rot_vel_rps
            self.min_rot_vel_rps = min_rot_vel_rps
            self.plan_strictness = plan_strictness
            self.path_approach_avenue_factor = path_approach_avenue_factor
            self.safety_inflation_factor = safety_inflation_factor

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.P2pPlannerParam):
                return NotImplemented
            else:
                if self.acc_lim_x_mps2 != other.acc_lim_x_mps2:
                    return False
                if self.acc_lim_theta_rps2 != other.acc_lim_theta_rps2:
                    return False
                if self.max_vel_x_mps != other.max_vel_x_mps:
                    return False
                if self.min_vel_x_mps != other.min_vel_x_mps:
                    return False
                if self.max_rot_vel_rps != other.max_rot_vel_rps:
                    return False
                if self.min_rot_vel_rps != other.min_rot_vel_rps:
                    return False
                if self.plan_strictness != other.plan_strictness:
                    return False
                if self.path_approach_avenue_factor != other.path_approach_avenue_factor:
                    return False
                if self.safety_inflation_factor != other.safety_inflation_factor:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_P2pPlannerParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_P2pPlannerParam = IcePy.defineStruct('::wizrobo_npu::P2pPlannerParam', P2pPlannerParam, (), (
        ('acc_lim_x_mps2', (), IcePy._t_float),
        ('acc_lim_theta_rps2', (), IcePy._t_float),
        ('max_vel_x_mps', (), IcePy._t_float),
        ('min_vel_x_mps', (), IcePy._t_float),
        ('max_rot_vel_rps', (), IcePy._t_float),
        ('min_rot_vel_rps', (), IcePy._t_float),
        ('plan_strictness', (), IcePy._t_float),
        ('path_approach_avenue_factor', (), IcePy._t_float),
        ('safety_inflation_factor', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.P2pPlannerParam = P2pPlannerParam
    del P2pPlannerParam

if 'PfPlannerParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.PfPlannerParam = Ice.createTempClass()
    class PfPlannerParam(object):
        def __init__(self, waypoint_mode=False, look_ahead_dist_m=0.0, position_control_tolerance_m=0.0, heading_control_tolerance_rad=0.0, max_lin_vel_mps=0.0, max_ang_vel_rps=0.0, heading_kp=0.0):
            self.waypoint_mode = waypoint_mode
            self.look_ahead_dist_m = look_ahead_dist_m
            self.position_control_tolerance_m = position_control_tolerance_m
            self.heading_control_tolerance_rad = heading_control_tolerance_rad
            self.max_lin_vel_mps = max_lin_vel_mps
            self.max_ang_vel_rps = max_ang_vel_rps
            self.heading_kp = heading_kp

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.PfPlannerParam):
                return NotImplemented
            else:
                if self.waypoint_mode != other.waypoint_mode:
                    return False
                if self.look_ahead_dist_m != other.look_ahead_dist_m:
                    return False
                if self.position_control_tolerance_m != other.position_control_tolerance_m:
                    return False
                if self.heading_control_tolerance_rad != other.heading_control_tolerance_rad:
                    return False
                if self.max_lin_vel_mps != other.max_lin_vel_mps:
                    return False
                if self.max_ang_vel_rps != other.max_ang_vel_rps:
                    return False
                if self.heading_kp != other.heading_kp:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_PfPlannerParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_PfPlannerParam = IcePy.defineStruct('::wizrobo_npu::PfPlannerParam', PfPlannerParam, (), (
        ('waypoint_mode', (), IcePy._t_bool),
        ('look_ahead_dist_m', (), IcePy._t_float),
        ('position_control_tolerance_m', (), IcePy._t_float),
        ('heading_control_tolerance_rad', (), IcePy._t_float),
        ('max_lin_vel_mps', (), IcePy._t_float),
        ('max_ang_vel_rps', (), IcePy._t_float),
        ('heading_kp', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.PfPlannerParam = PfPlannerParam
    del PfPlannerParam

if 'CcpPlannerParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.CcpPlannerParam = Ice.createTempClass()
    class CcpPlannerParam(object):
        def __init__(self, coverage_spacing_m=0.0):
            self.coverage_spacing_m = coverage_spacing_m

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.CcpPlannerParam):
                return NotImplemented
            else:
                if self.coverage_spacing_m != other.coverage_spacing_m:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_CcpPlannerParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_CcpPlannerParam = IcePy.defineStruct('::wizrobo_npu::CcpPlannerParam', CcpPlannerParam, (), (('coverage_spacing_m', (), IcePy._t_float),))

    _M_wizrobo_npu.CcpPlannerParam = CcpPlannerParam
    del CcpPlannerParam

if 'NaviParam' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.NaviParam = Ice.createTempClass()
    class NaviParam(object):
        def __init__(self, lin_spd_lmt_ratio=0.0, ang_spd_lmt_ratio=0.0, x_err_tolr_m=0.0, y_err_tolr_m=0.0, yaw_err_tolr_rad=0.0, p2p_planner_param=Ice._struct_marker, pf_planner_param=Ice._struct_marker, ccp_planner_param=Ice._struct_marker):
            self.lin_spd_lmt_ratio = lin_spd_lmt_ratio
            self.ang_spd_lmt_ratio = ang_spd_lmt_ratio
            self.x_err_tolr_m = x_err_tolr_m
            self.y_err_tolr_m = y_err_tolr_m
            self.yaw_err_tolr_rad = yaw_err_tolr_rad
            if p2p_planner_param is Ice._struct_marker:
                self.p2p_planner_param = _M_wizrobo_npu.P2pPlannerParam()
            else:
                self.p2p_planner_param = p2p_planner_param
            if pf_planner_param is Ice._struct_marker:
                self.pf_planner_param = _M_wizrobo_npu.PfPlannerParam()
            else:
                self.pf_planner_param = pf_planner_param
            if ccp_planner_param is Ice._struct_marker:
                self.ccp_planner_param = _M_wizrobo_npu.CcpPlannerParam()
            else:
                self.ccp_planner_param = ccp_planner_param

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.NaviParam):
                return NotImplemented
            else:
                if self.lin_spd_lmt_ratio != other.lin_spd_lmt_ratio:
                    return False
                if self.ang_spd_lmt_ratio != other.ang_spd_lmt_ratio:
                    return False
                if self.x_err_tolr_m != other.x_err_tolr_m:
                    return False
                if self.y_err_tolr_m != other.y_err_tolr_m:
                    return False
                if self.yaw_err_tolr_rad != other.yaw_err_tolr_rad:
                    return False
                if self.p2p_planner_param != other.p2p_planner_param:
                    return False
                if self.pf_planner_param != other.pf_planner_param:
                    return False
                if self.ccp_planner_param != other.ccp_planner_param:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_NaviParam)

        __repr__ = __str__

    _M_wizrobo_npu._t_NaviParam = IcePy.defineStruct('::wizrobo_npu::NaviParam', NaviParam, (), (
        ('lin_spd_lmt_ratio', (), IcePy._t_float),
        ('ang_spd_lmt_ratio', (), IcePy._t_float),
        ('x_err_tolr_m', (), IcePy._t_float),
        ('y_err_tolr_m', (), IcePy._t_float),
        ('yaw_err_tolr_rad', (), IcePy._t_float),
        ('p2p_planner_param', (), _M_wizrobo_npu._t_P2pPlannerParam),
        ('pf_planner_param', (), _M_wizrobo_npu._t_PfPlannerParam),
        ('ccp_planner_param', (), _M_wizrobo_npu._t_CcpPlannerParam)
    ))

    _M_wizrobo_npu.NaviParam = NaviParam
    del NaviParam

_M_wizrobo_npu.RT0_CONECT_LIDAR = 1

_M_wizrobo_npu.RT0_CONECT_IMU = 2

_M_wizrobo_npu.RT0_CONECT_BCU_MOTOR = 4

_M_wizrobo_npu.RT0_CONECT_BCU_SENSOR = 8

_M_wizrobo_npu.RT0_CONECT_GPS = 16

_M_wizrobo_npu.RT0_DATA_LIDAR = 65536

_M_wizrobo_npu.RT0_DATA_IMU = 131072

_M_wizrobo_npu.RT0_DATA_ENCODER = 262144

_M_wizrobo_npu.RT0_DATA_GPS = 524288

_M_wizrobo_npu.RT0_DATA_MAP = 1048576

_M_wizrobo_npu.RT1_INIT_POSE_NOT_SET = 1

_M_wizrobo_npu.RT1_LOCALIZATION_EXCEPTION = 2

_M_wizrobo_npu.RT1_SOFTWARE_NODE_CRASH = 4

_M_wizrobo_npu.RT1_NAVI_OBSTACLE_BLOCK = 65536

_M_wizrobo_npu.RT1_NAVI_PLANNING_FAILED = 131072

_M_wizrobo_npu.RT1_NAVI_EMB_TRIGGERED = 262144

_M_wizrobo_npu.RT1_NAVI_BUMPER_TRIGGERED = 524288

_M_wizrobo_npu.RT1_NAVI_ENTRP_DETECTED = 1048576

if '_t_RuntimeStatusList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_RuntimeStatusList = IcePy.defineSequence('::wizrobo_npu::RuntimeStatusList', (), IcePy._t_int)

# End of module wizrobo_npu
