# **********************************************************************
#
# Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
#
# This copy of Ice is licensed to you under the terms described in the
# ICE_LICENSE file included in this distribution.
#
# **********************************************************************
#
# Ice version 3.5.1
#
# <auto-generated>
#
# Generated from file `npuice_map.ice'
#
# Warning: do not edit this file.
#
# </auto-generated>
#

import Ice, IcePy
import npuice_geometry_ice
import npuice_data_ice

# Included module wizrobo_npu
_M_wizrobo_npu = Ice.openModule('wizrobo_npu')

# Start of module wizrobo_npu
__name__ = 'wizrobo_npu'

if 'StationType' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.StationType = Ice.createTempClass()
    class StationType(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    StationType.START = StationType("START", 0)
    StationType.CHARGER = StationType("CHARGER", 1)
    StationType.USER_DEFINED = StationType("USER_DEFINED", 2)
    StationType._enumerators = { 0:StationType.START, 1:StationType.CHARGER, 2:StationType.USER_DEFINED }

    _M_wizrobo_npu._t_StationType = IcePy.defineEnum('::wizrobo_npu::StationType', StationType, (), StationType._enumerators)

    _M_wizrobo_npu.StationType = StationType
    del StationType

if 'StationInfo' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.StationInfo = Ice.createTempClass()
    class StationInfo(object):
        def __init__(self, map_id='', id='', type=_M_wizrobo_npu.StationType.START, artag_id=0):
            self.map_id = map_id
            self.id = id
            self.type = type
            self.artag_id = artag_id

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.map_id)
            _h = 5 * _h + Ice.getHash(self.id)
            _h = 5 * _h + Ice.getHash(self.type)
            _h = 5 * _h + Ice.getHash(self.artag_id)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.StationInfo):
                return NotImplemented
            else:
                if self.map_id is None or other.map_id is None:
                    if self.map_id != other.map_id:
                        return (-1 if self.map_id is None else 1)
                else:
                    if self.map_id < other.map_id:
                        return -1
                    elif self.map_id > other.map_id:
                        return 1
                if self.id is None or other.id is None:
                    if self.id != other.id:
                        return (-1 if self.id is None else 1)
                else:
                    if self.id < other.id:
                        return -1
                    elif self.id > other.id:
                        return 1
                if self.type is None or other.type is None:
                    if self.type != other.type:
                        return (-1 if self.type is None else 1)
                else:
                    if self.type < other.type:
                        return -1
                    elif self.type > other.type:
                        return 1
                if self.artag_id is None or other.artag_id is None:
                    if self.artag_id != other.artag_id:
                        return (-1 if self.artag_id is None else 1)
                else:
                    if self.artag_id < other.artag_id:
                        return -1
                    elif self.artag_id > other.artag_id:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_StationInfo)

        __repr__ = __str__

    _M_wizrobo_npu._t_StationInfo = IcePy.defineStruct('::wizrobo_npu::StationInfo', StationInfo, (), (
        ('map_id', (), IcePy._t_string),
        ('id', (), IcePy._t_string),
        ('type', (), _M_wizrobo_npu._t_StationType),
        ('artag_id', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.StationInfo = StationInfo
    del StationInfo

if 'Station' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Station = Ice.createTempClass()
    class Station(object):
        def __init__(self, info=Ice._struct_marker, pose=Ice._struct_marker):
            if info is Ice._struct_marker:
                self.info = _M_wizrobo_npu.StationInfo()
            else:
                self.info = info
            if pose is Ice._struct_marker:
                self.pose = _M_wizrobo_npu.Pose3D()
            else:
                self.pose = pose

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Station):
                return NotImplemented
            else:
                if self.info != other.info:
                    return False
                if self.pose != other.pose:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Station)

        __repr__ = __str__

    _M_wizrobo_npu._t_Station = IcePy.defineStruct('::wizrobo_npu::Station', Station, (), (
        ('info', (), _M_wizrobo_npu._t_StationInfo),
        ('pose', (), _M_wizrobo_npu._t_Pose3D)
    ))

    _M_wizrobo_npu.Station = Station
    del Station

if '_t_StationList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_StationList = IcePy.defineSequence('::wizrobo_npu::StationList', (), _M_wizrobo_npu._t_Station)

if 'ImgStation' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImgStation = Ice.createTempClass()
    class ImgStation(object):
        def __init__(self, info=Ice._struct_marker, pose=Ice._struct_marker):
            if info is Ice._struct_marker:
                self.info = _M_wizrobo_npu.StationInfo()
            else:
                self.info = info
            if pose is Ice._struct_marker:
                self.pose = _M_wizrobo_npu.ImgPose()
            else:
                self.pose = pose

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.ImgStation):
                return NotImplemented
            else:
                if self.info != other.info:
                    return False
                if self.pose != other.pose:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImgStation)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImgStation = IcePy.defineStruct('::wizrobo_npu::ImgStation', ImgStation, (), (
        ('info', (), _M_wizrobo_npu._t_StationInfo),
        ('pose', (), _M_wizrobo_npu._t_ImgPose)
    ))

    _M_wizrobo_npu.ImgStation = ImgStation
    del ImgStation

if '_t_ImgStationList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_ImgStationList = IcePy.defineSequence('::wizrobo_npu::ImgStationList', (), _M_wizrobo_npu._t_ImgStation)

if 'PathInfo' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.PathInfo = Ice.createTempClass()
    class PathInfo(object):
        def __init__(self, map_id='', id='', length=0.0, pose_num=0):
            self.map_id = map_id
            self.id = id
            self.length = length
            self.pose_num = pose_num

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.PathInfo):
                return NotImplemented
            else:
                if self.map_id != other.map_id:
                    return False
                if self.id != other.id:
                    return False
                if self.length != other.length:
                    return False
                if self.pose_num != other.pose_num:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_PathInfo)

        __repr__ = __str__

    _M_wizrobo_npu._t_PathInfo = IcePy.defineStruct('::wizrobo_npu::PathInfo', PathInfo, (), (
        ('map_id', (), IcePy._t_string),
        ('id', (), IcePy._t_string),
        ('length', (), IcePy._t_float),
        ('pose_num', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.PathInfo = PathInfo
    del PathInfo

if 'Path' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Path = Ice.createTempClass()
    class Path(object):
        def __init__(self, info=Ice._struct_marker, poses=None):
            if info is Ice._struct_marker:
                self.info = _M_wizrobo_npu.PathInfo()
            else:
                self.info = info
            self.poses = poses

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Path):
                return NotImplemented
            else:
                if self.info != other.info:
                    return False
                if self.poses != other.poses:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Path)

        __repr__ = __str__

    _M_wizrobo_npu._t_Path = IcePy.defineStruct('::wizrobo_npu::Path', Path, (), (
        ('info', (), _M_wizrobo_npu._t_PathInfo),
        ('poses', (), _M_wizrobo_npu._t_Pose3DList)
    ))

    _M_wizrobo_npu.Path = Path
    del Path

if '_t_PathList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_PathList = IcePy.defineSequence('::wizrobo_npu::PathList', (), _M_wizrobo_npu._t_Path)

if 'ImgPath' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImgPath = Ice.createTempClass()
    class ImgPath(object):
        def __init__(self, info=Ice._struct_marker, poses=None):
            if info is Ice._struct_marker:
                self.info = _M_wizrobo_npu.PathInfo()
            else:
                self.info = info
            self.poses = poses

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.ImgPath):
                return NotImplemented
            else:
                if self.info != other.info:
                    return False
                if self.poses != other.poses:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImgPath)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImgPath = IcePy.defineStruct('::wizrobo_npu::ImgPath', ImgPath, (), (
        ('info', (), _M_wizrobo_npu._t_PathInfo),
        ('poses', (), _M_wizrobo_npu._t_ImgPoseList)
    ))

    _M_wizrobo_npu.ImgPath = ImgPath
    del ImgPath

if '_t_ImgPathList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_ImgPathList = IcePy.defineSequence('::wizrobo_npu::ImgPathList', (), _M_wizrobo_npu._t_ImgPath)

if 'actionname' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.actionname = Ice.createTempClass()
    class actionname(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    actionname.navi = actionname("navi", 0)
    actionname.follow = actionname("follow", 1)
    actionname.forward = actionname("forward", 2)
    actionname.backward = actionname("backward", 3)
    actionname.turnleft = actionname("turnleft", 4)
    actionname.turnright = actionname("turnright", 5)
    actionname._enumerators = { 0:actionname.navi, 1:actionname.follow, 2:actionname.forward, 3:actionname.backward, 4:actionname.turnleft, 5:actionname.turnright }

    _M_wizrobo_npu._t_actionname = IcePy.defineEnum('::wizrobo_npu::actionname', actionname, (), actionname._enumerators)

    _M_wizrobo_npu.actionname = actionname
    del actionname

if 'TaskAction' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.TaskAction = Ice.createTempClass()
    class TaskAction(object):
        def __init__(self, action_name=_M_wizrobo_npu.actionname.navi, action_args='', duration=0):
            self.action_name = action_name
            self.action_args = action_args
            self.duration = duration

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.action_name)
            _h = 5 * _h + Ice.getHash(self.action_args)
            _h = 5 * _h + Ice.getHash(self.duration)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.TaskAction):
                return NotImplemented
            else:
                if self.action_name is None or other.action_name is None:
                    if self.action_name != other.action_name:
                        return (-1 if self.action_name is None else 1)
                else:
                    if self.action_name < other.action_name:
                        return -1
                    elif self.action_name > other.action_name:
                        return 1
                if self.action_args is None or other.action_args is None:
                    if self.action_args != other.action_args:
                        return (-1 if self.action_args is None else 1)
                else:
                    if self.action_args < other.action_args:
                        return -1
                    elif self.action_args > other.action_args:
                        return 1
                if self.duration is None or other.duration is None:
                    if self.duration != other.duration:
                        return (-1 if self.duration is None else 1)
                else:
                    if self.duration < other.duration:
                        return -1
                    elif self.duration > other.duration:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_TaskAction)

        __repr__ = __str__

    _M_wizrobo_npu._t_TaskAction = IcePy.defineStruct('::wizrobo_npu::TaskAction', TaskAction, (), (
        ('action_name', (), _M_wizrobo_npu._t_actionname),
        ('action_args', (), IcePy._t_string),
        ('duration', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.TaskAction = TaskAction
    del TaskAction

if '_t_TaskActionList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_TaskActionList = IcePy.defineSequence('::wizrobo_npu::TaskActionList', (), _M_wizrobo_npu._t_TaskAction)

if 'TaskInfo' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.TaskInfo = Ice.createTempClass()
    class TaskInfo(object):
        def __init__(self, map_id='', task_id='', action_list=None):
            self.map_id = map_id
            self.task_id = task_id
            self.action_list = action_list

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.map_id)
            _h = 5 * _h + Ice.getHash(self.task_id)
            if self.action_list:
                for _i0 in self.action_list:
                    _h = 5 * _h + Ice.getHash(_i0)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.TaskInfo):
                return NotImplemented
            else:
                if self.map_id is None or other.map_id is None:
                    if self.map_id != other.map_id:
                        return (-1 if self.map_id is None else 1)
                else:
                    if self.map_id < other.map_id:
                        return -1
                    elif self.map_id > other.map_id:
                        return 1
                if self.task_id is None or other.task_id is None:
                    if self.task_id != other.task_id:
                        return (-1 if self.task_id is None else 1)
                else:
                    if self.task_id < other.task_id:
                        return -1
                    elif self.task_id > other.task_id:
                        return 1
                if self.action_list is None or other.action_list is None:
                    if self.action_list != other.action_list:
                        return (-1 if self.action_list is None else 1)
                else:
                    if self.action_list < other.action_list:
                        return -1
                    elif self.action_list > other.action_list:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_TaskInfo)

        __repr__ = __str__

    _M_wizrobo_npu._t_TaskInfo = IcePy.defineStruct('::wizrobo_npu::TaskInfo', TaskInfo, (), (
        ('map_id', (), IcePy._t_string),
        ('task_id', (), IcePy._t_string),
        ('action_list', (), _M_wizrobo_npu._t_TaskActionList)
    ))

    _M_wizrobo_npu.TaskInfo = TaskInfo
    del TaskInfo

if 'Task' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Task = Ice.createTempClass()
    class Task(object):
        def __init__(self, info=Ice._struct_marker, enb_taskloop=False, task_loop_times=0):
            if info is Ice._struct_marker:
                self.info = _M_wizrobo_npu.TaskInfo()
            else:
                self.info = info
            self.enb_taskloop = enb_taskloop
            self.task_loop_times = task_loop_times

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.info)
            _h = 5 * _h + Ice.getHash(self.enb_taskloop)
            _h = 5 * _h + Ice.getHash(self.task_loop_times)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.Task):
                return NotImplemented
            else:
                if self.info is None or other.info is None:
                    if self.info != other.info:
                        return (-1 if self.info is None else 1)
                else:
                    if self.info < other.info:
                        return -1
                    elif self.info > other.info:
                        return 1
                if self.enb_taskloop is None or other.enb_taskloop is None:
                    if self.enb_taskloop != other.enb_taskloop:
                        return (-1 if self.enb_taskloop is None else 1)
                else:
                    if self.enb_taskloop < other.enb_taskloop:
                        return -1
                    elif self.enb_taskloop > other.enb_taskloop:
                        return 1
                if self.task_loop_times is None or other.task_loop_times is None:
                    if self.task_loop_times != other.task_loop_times:
                        return (-1 if self.task_loop_times is None else 1)
                else:
                    if self.task_loop_times < other.task_loop_times:
                        return -1
                    elif self.task_loop_times > other.task_loop_times:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Task)

        __repr__ = __str__

    _M_wizrobo_npu._t_Task = IcePy.defineStruct('::wizrobo_npu::Task', Task, (), (
        ('info', (), _M_wizrobo_npu._t_TaskInfo),
        ('enb_taskloop', (), IcePy._t_bool),
        ('task_loop_times', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.Task = Task
    del Task

if '_t_TaskList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_TaskList = IcePy.defineSequence('::wizrobo_npu::TaskList', (), _M_wizrobo_npu._t_Task)

if 'VirtualWallInfo' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.VirtualWallInfo = Ice.createTempClass()
    class VirtualWallInfo(object):
        def __init__(self, map_id='', id='', length=0.0, closed=False):
            self.map_id = map_id
            self.id = id
            self.length = length
            self.closed = closed

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.VirtualWallInfo):
                return NotImplemented
            else:
                if self.map_id != other.map_id:
                    return False
                if self.id != other.id:
                    return False
                if self.length != other.length:
                    return False
                if self.closed != other.closed:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_VirtualWallInfo)

        __repr__ = __str__

    _M_wizrobo_npu._t_VirtualWallInfo = IcePy.defineStruct('::wizrobo_npu::VirtualWallInfo', VirtualWallInfo, (), (
        ('map_id', (), IcePy._t_string),
        ('id', (), IcePy._t_string),
        ('length', (), IcePy._t_float),
        ('closed', (), IcePy._t_bool)
    ))

    _M_wizrobo_npu.VirtualWallInfo = VirtualWallInfo
    del VirtualWallInfo

if 'VirtualWall' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.VirtualWall = Ice.createTempClass()
    class VirtualWall(object):
        def __init__(self, info=Ice._struct_marker, points=None):
            if info is Ice._struct_marker:
                self.info = _M_wizrobo_npu.VirtualWallInfo()
            else:
                self.info = info
            self.points = points

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.VirtualWall):
                return NotImplemented
            else:
                if self.info != other.info:
                    return False
                if self.points != other.points:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_VirtualWall)

        __repr__ = __str__

    _M_wizrobo_npu._t_VirtualWall = IcePy.defineStruct('::wizrobo_npu::VirtualWall', VirtualWall, (), (
        ('info', (), _M_wizrobo_npu._t_VirtualWallInfo),
        ('points', (), _M_wizrobo_npu._t_Point3DList)
    ))

    _M_wizrobo_npu.VirtualWall = VirtualWall
    del VirtualWall

if '_t_VirtualWallList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_VirtualWallList = IcePy.defineSequence('::wizrobo_npu::VirtualWallList', (), _M_wizrobo_npu._t_VirtualWall)

if 'ImgVirtualWall' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImgVirtualWall = Ice.createTempClass()
    class ImgVirtualWall(object):
        def __init__(self, info=Ice._struct_marker, points=None):
            if info is Ice._struct_marker:
                self.info = _M_wizrobo_npu.VirtualWallInfo()
            else:
                self.info = info
            self.points = points

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.ImgVirtualWall):
                return NotImplemented
            else:
                if self.info != other.info:
                    return False
                if self.points != other.points:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImgVirtualWall)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImgVirtualWall = IcePy.defineStruct('::wizrobo_npu::ImgVirtualWall', ImgVirtualWall, (), (
        ('info', (), _M_wizrobo_npu._t_VirtualWallInfo),
        ('points', (), _M_wizrobo_npu._t_ImgPointList)
    ))

    _M_wizrobo_npu.ImgVirtualWall = ImgVirtualWall
    del ImgVirtualWall

if '_t_ImgVirtualWallList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_ImgVirtualWallList = IcePy.defineSequence('::wizrobo_npu::ImgVirtualWallList', (), _M_wizrobo_npu._t_ImgVirtualWall)

if 'CellType' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.CellType = Ice.createTempClass()
    class CellType(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    CellType.OCCUPIED_CELL = CellType("OCCUPIED_CELL", 0)
    CellType.FREE_CELL = CellType("FREE_CELL", 1)
    CellType.UNKNOWN_CELL = CellType("UNKNOWN_CELL", 2)
    CellType._enumerators = { 0:CellType.OCCUPIED_CELL, 1:CellType.FREE_CELL, 2:CellType.UNKNOWN_CELL }

    _M_wizrobo_npu._t_CellType = IcePy.defineEnum('::wizrobo_npu::CellType', CellType, (), CellType._enumerators)

    _M_wizrobo_npu.CellType = CellType
    del CellType

if '_t_CellArray' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_CellArray = IcePy.defineSequence('::wizrobo_npu::CellArray', (), _M_wizrobo_npu._t_CellType)

if 'CellMat' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.CellMat = Ice.createTempClass()
    class CellMat(object):
        def __init__(self, width=0, height=0, data=None):
            self.width = width
            self.height = height
            self.data = data

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.width)
            _h = 5 * _h + Ice.getHash(self.height)
            if self.data:
                for _i0 in self.data:
                    _h = 5 * _h + Ice.getHash(_i0)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.CellMat):
                return NotImplemented
            else:
                if self.width is None or other.width is None:
                    if self.width != other.width:
                        return (-1 if self.width is None else 1)
                else:
                    if self.width < other.width:
                        return -1
                    elif self.width > other.width:
                        return 1
                if self.height is None or other.height is None:
                    if self.height != other.height:
                        return (-1 if self.height is None else 1)
                else:
                    if self.height < other.height:
                        return -1
                    elif self.height > other.height:
                        return 1
                if self.data is None or other.data is None:
                    if self.data != other.data:
                        return (-1 if self.data is None else 1)
                else:
                    if self.data < other.data:
                        return -1
                    elif self.data > other.data:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_CellMat)

        __repr__ = __str__

    _M_wizrobo_npu._t_CellMat = IcePy.defineStruct('::wizrobo_npu::CellMat', CellMat, (), (
        ('width', (), IcePy._t_int),
        ('height', (), IcePy._t_int),
        ('data', (), _M_wizrobo_npu._t_CellArray)
    ))

    _M_wizrobo_npu.CellMat = CellMat
    del CellMat

if '_t_PixelMap' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_PixelMap = IcePy.defineSequence('::wizrobo_npu::PixelMap', (), IcePy._t_byte)

if 'PixelMat' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.PixelMat = Ice.createTempClass()
    class PixelMat(object):
        def __init__(self, width=0, height=0, ratio=0.0, data=None):
            self.width = width
            self.height = height
            self.ratio = ratio
            self.data = data

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.PixelMat):
                return NotImplemented
            else:
                if self.width != other.width:
                    return False
                if self.height != other.height:
                    return False
                if self.ratio != other.ratio:
                    return False
                if self.data != other.data:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_PixelMat)

        __repr__ = __str__

    _M_wizrobo_npu._t_PixelMat = IcePy.defineStruct('::wizrobo_npu::PixelMat', PixelMat, (), (
        ('width', (), IcePy._t_int),
        ('height', (), IcePy._t_int),
        ('ratio', (), IcePy._t_float),
        ('data', (), _M_wizrobo_npu._t_ByteArray)
    ))

    _M_wizrobo_npu.PixelMat = PixelMat
    del PixelMat

if 'MapInfo' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.MapInfo = Ice.createTempClass()
    class MapInfo(object):
        def __init__(self, index=0, id='', creation_time='', resolution=0.0, dimension=Ice._struct_marker, offset=Ice._struct_marker, thumbnail=Ice._struct_marker, station_num=0, path_num=0, task_num=0):
            self.index = index
            self.id = id
            self.creation_time = creation_time
            self.resolution = resolution
            if dimension is Ice._struct_marker:
                self.dimension = _M_wizrobo_npu.Vector3D()
            else:
                self.dimension = dimension
            if offset is Ice._struct_marker:
                self.offset = _M_wizrobo_npu.Vector3D()
            else:
                self.offset = offset
            if thumbnail is Ice._struct_marker:
                self.thumbnail = _M_wizrobo_npu.PixelMat()
            else:
                self.thumbnail = thumbnail
            self.station_num = station_num
            self.path_num = path_num
            self.task_num = task_num

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.MapInfo):
                return NotImplemented
            else:
                if self.index != other.index:
                    return False
                if self.id != other.id:
                    return False
                if self.creation_time != other.creation_time:
                    return False
                if self.resolution != other.resolution:
                    return False
                if self.dimension != other.dimension:
                    return False
                if self.offset != other.offset:
                    return False
                if self.thumbnail != other.thumbnail:
                    return False
                if self.station_num != other.station_num:
                    return False
                if self.path_num != other.path_num:
                    return False
                if self.task_num != other.task_num:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_MapInfo)

        __repr__ = __str__

    _M_wizrobo_npu._t_MapInfo = IcePy.defineStruct('::wizrobo_npu::MapInfo', MapInfo, (), (
        ('index', (), IcePy._t_int),
        ('id', (), IcePy._t_string),
        ('creation_time', (), IcePy._t_string),
        ('resolution', (), IcePy._t_float),
        ('dimension', (), _M_wizrobo_npu._t_Vector3D),
        ('offset', (), _M_wizrobo_npu._t_Vector3D),
        ('thumbnail', (), _M_wizrobo_npu._t_PixelMat),
        ('station_num', (), IcePy._t_int),
        ('path_num', (), IcePy._t_int),
        ('task_num', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.MapInfo = MapInfo
    del MapInfo

if '_t_MapInfoList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_MapInfoList = IcePy.defineSequence('::wizrobo_npu::MapInfoList', (), _M_wizrobo_npu._t_MapInfo)

if 'Map2D' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Map2D = Ice.createTempClass()
    class Map2D(object):
        def __init__(self, info=Ice._struct_marker, mat=Ice._struct_marker, stations=None, tasks=None, paths=None, virtual_walls=None):
            if info is Ice._struct_marker:
                self.info = _M_wizrobo_npu.MapInfo()
            else:
                self.info = info
            if mat is Ice._struct_marker:
                self.mat = _M_wizrobo_npu.CellMat()
            else:
                self.mat = mat
            self.stations = stations
            self.tasks = tasks
            self.paths = paths
            self.virtual_walls = virtual_walls

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Map2D):
                return NotImplemented
            else:
                if self.info != other.info:
                    return False
                if self.mat != other.mat:
                    return False
                if self.stations != other.stations:
                    return False
                if self.tasks != other.tasks:
                    return False
                if self.paths != other.paths:
                    return False
                if self.virtual_walls != other.virtual_walls:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Map2D)

        __repr__ = __str__

    _M_wizrobo_npu._t_Map2D = IcePy.defineStruct('::wizrobo_npu::Map2D', Map2D, (), (
        ('info', (), _M_wizrobo_npu._t_MapInfo),
        ('mat', (), _M_wizrobo_npu._t_CellMat),
        ('stations', (), _M_wizrobo_npu._t_StationList),
        ('tasks', (), _M_wizrobo_npu._t_TaskList),
        ('paths', (), _M_wizrobo_npu._t_PathList),
        ('virtual_walls', (), _M_wizrobo_npu._t_VirtualWallList)
    ))

    _M_wizrobo_npu.Map2D = Map2D
    del Map2D

if '_t_MapList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_MapList = IcePy.defineSequence('::wizrobo_npu::MapList', (), _M_wizrobo_npu._t_Map2D)

if 'ImgMap' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImgMap = Ice.createTempClass()
    class ImgMap(object):
        def __init__(self, info=Ice._struct_marker, mat=Ice._struct_marker, stations=None, paths=None, virtual_walls=None):
            if info is Ice._struct_marker:
                self.info = _M_wizrobo_npu.MapInfo()
            else:
                self.info = info
            if mat is Ice._struct_marker:
                self.mat = _M_wizrobo_npu.PixelMat()
            else:
                self.mat = mat
            self.stations = stations
            self.paths = paths
            self.virtual_walls = virtual_walls

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.ImgMap):
                return NotImplemented
            else:
                if self.info != other.info:
                    return False
                if self.mat != other.mat:
                    return False
                if self.stations != other.stations:
                    return False
                if self.paths != other.paths:
                    return False
                if self.virtual_walls != other.virtual_walls:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImgMap)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImgMap = IcePy.defineStruct('::wizrobo_npu::ImgMap', ImgMap, (), (
        ('info', (), _M_wizrobo_npu._t_MapInfo),
        ('mat', (), _M_wizrobo_npu._t_PixelMat),
        ('stations', (), _M_wizrobo_npu._t_ImgStationList),
        ('paths', (), _M_wizrobo_npu._t_ImgPathList),
        ('virtual_walls', (), _M_wizrobo_npu._t_ImgVirtualWallList)
    ))

    _M_wizrobo_npu.ImgMap = ImgMap
    del ImgMap

# End of module wizrobo_npu
