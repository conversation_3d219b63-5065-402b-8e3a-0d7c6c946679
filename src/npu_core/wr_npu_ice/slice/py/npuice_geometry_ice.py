# **********************************************************************
#
# Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
#
# This copy of Ice is licensed to you under the terms described in the
# ICE_LICENSE file included in this distribution.
#
# **********************************************************************
#
# Ice version 3.5.1
#
# <auto-generated>
#
# Generated from file `npuice_geometry.ice'
#
# Warning: do not edit this file.
#
# </auto-generated>
#

import Ice, IcePy

# Start of module wizrobo_npu
_M_wizrobo_npu = Ice.openModule('wizrobo_npu')
__name__ = 'wizrobo_npu'

_M_wizrobo_npu.INVALID_POSE3D_VALUE = -1000

if 'Point3D' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Point3D = Ice.createTempClass()
    class Point3D(object):
        def __init__(self, x=0.0, y=0.0, z=0.0):
            self.x = x
            self.y = y
            self.z = z

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Point3D):
                return NotImplemented
            else:
                if self.x != other.x:
                    return False
                if self.y != other.y:
                    return False
                if self.z != other.z:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Point3D)

        __repr__ = __str__

    _M_wizrobo_npu._t_Point3D = IcePy.defineStruct('::wizrobo_npu::Point3D', Point3D, (), (
        ('x', (), IcePy._t_float),
        ('y', (), IcePy._t_float),
        ('z', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.Point3D = Point3D
    del Point3D

if '_t_Point3DList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_Point3DList = IcePy.defineSequence('::wizrobo_npu::Point3DList', (), _M_wizrobo_npu._t_Point3D)

if 'ImgPoint' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImgPoint = Ice.createTempClass()
    class ImgPoint(object):
        def __init__(self, u=0, v=0):
            self.u = u
            self.v = v

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.u)
            _h = 5 * _h + Ice.getHash(self.v)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.ImgPoint):
                return NotImplemented
            else:
                if self.u is None or other.u is None:
                    if self.u != other.u:
                        return (-1 if self.u is None else 1)
                else:
                    if self.u < other.u:
                        return -1
                    elif self.u > other.u:
                        return 1
                if self.v is None or other.v is None:
                    if self.v != other.v:
                        return (-1 if self.v is None else 1)
                else:
                    if self.v < other.v:
                        return -1
                    elif self.v > other.v:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImgPoint)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImgPoint = IcePy.defineStruct('::wizrobo_npu::ImgPoint', ImgPoint, (), (
        ('u', (), IcePy._t_int),
        ('v', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.ImgPoint = ImgPoint
    del ImgPoint

if '_t_ImgPointList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_ImgPointList = IcePy.defineSequence('::wizrobo_npu::ImgPointList', (), _M_wizrobo_npu._t_ImgPoint)

if 'Vector3D' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Vector3D = Ice.createTempClass()
    class Vector3D(object):
        def __init__(self, x=0.0, y=0.0, z=0.0):
            self.x = x
            self.y = y
            self.z = z

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Vector3D):
                return NotImplemented
            else:
                if self.x != other.x:
                    return False
                if self.y != other.y:
                    return False
                if self.z != other.z:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Vector3D)

        __repr__ = __str__

    _M_wizrobo_npu._t_Vector3D = IcePy.defineStruct('::wizrobo_npu::Vector3D', Vector3D, (), (
        ('x', (), IcePy._t_double),
        ('y', (), IcePy._t_double),
        ('z', (), IcePy._t_double)
    ))

    _M_wizrobo_npu.Vector3D = Vector3D
    del Vector3D

if 'Quaternion' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Quaternion = Ice.createTempClass()
    class Quaternion(object):
        def __init__(self, x=0.0, y=0.0, z=0.0, w=0.0):
            self.x = x
            self.y = y
            self.z = z
            self.w = w

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Quaternion):
                return NotImplemented
            else:
                if self.x != other.x:
                    return False
                if self.y != other.y:
                    return False
                if self.z != other.z:
                    return False
                if self.w != other.w:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Quaternion)

        __repr__ = __str__

    _M_wizrobo_npu._t_Quaternion = IcePy.defineStruct('::wizrobo_npu::Quaternion', Quaternion, (), (
        ('x', (), IcePy._t_double),
        ('y', (), IcePy._t_double),
        ('z', (), IcePy._t_double),
        ('w', (), IcePy._t_double)
    ))

    _M_wizrobo_npu.Quaternion = Quaternion
    del Quaternion

if '_t_DoubleArray' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_DoubleArray = IcePy.defineSequence('::wizrobo_npu::DoubleArray', (), IcePy._t_double)

if 'Pose3D' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Pose3D = Ice.createTempClass()
    class Pose3D(object):
        def __init__(self, x=0.0, y=0.0, z=0.0, roll=0.0, pitch=0.0, yaw=0.0):
            self.x = x
            self.y = y
            self.z = z
            self.roll = roll
            self.pitch = pitch
            self.yaw = yaw

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Pose3D):
                return NotImplemented
            else:
                if self.x != other.x:
                    return False
                if self.y != other.y:
                    return False
                if self.z != other.z:
                    return False
                if self.roll != other.roll:
                    return False
                if self.pitch != other.pitch:
                    return False
                if self.yaw != other.yaw:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Pose3D)

        __repr__ = __str__

    _M_wizrobo_npu._t_Pose3D = IcePy.defineStruct('::wizrobo_npu::Pose3D', Pose3D, (), (
        ('x', (), IcePy._t_float),
        ('y', (), IcePy._t_float),
        ('z', (), IcePy._t_float),
        ('roll', (), IcePy._t_float),
        ('pitch', (), IcePy._t_float),
        ('yaw', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.Pose3D = Pose3D
    del Pose3D

if '_t_Pose3DList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_Pose3DList = IcePy.defineSequence('::wizrobo_npu::Pose3DList', (), _M_wizrobo_npu._t_Pose3D)

if 'ImgPose' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImgPose = Ice.createTempClass()
    class ImgPose(object):
        def __init__(self, u=0, v=0, theta=0.0):
            self.u = u
            self.v = v
            self.theta = theta

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.ImgPose):
                return NotImplemented
            else:
                if self.u != other.u:
                    return False
                if self.v != other.v:
                    return False
                if self.theta != other.theta:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImgPose)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImgPose = IcePy.defineStruct('::wizrobo_npu::ImgPose', ImgPose, (), (
        ('u', (), IcePy._t_int),
        ('v', (), IcePy._t_int),
        ('theta', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.ImgPose = ImgPose
    del ImgPose

if '_t_ImgPoseList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_ImgPoseList = IcePy.defineSequence('::wizrobo_npu::ImgPoseList', (), _M_wizrobo_npu._t_ImgPose)

if 'Vel2D' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Vel2D = Ice.createTempClass()
    class Vel2D(object):
        def __init__(self, v_x=0.0, v_y=0.0, v_yaw=0.0):
            self.v_x = v_x
            self.v_y = v_y
            self.v_yaw = v_yaw

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Vel2D):
                return NotImplemented
            else:
                if self.v_x != other.v_x:
                    return False
                if self.v_y != other.v_y:
                    return False
                if self.v_yaw != other.v_yaw:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Vel2D)

        __repr__ = __str__

    _M_wizrobo_npu._t_Vel2D = IcePy.defineStruct('::wizrobo_npu::Vel2D', Vel2D, (), (
        ('v_x', (), IcePy._t_float),
        ('v_y', (), IcePy._t_float),
        ('v_yaw', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.Vel2D = Vel2D
    del Vel2D

if 'Vel3D' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Vel3D = Ice.createTempClass()
    class Vel3D(object):
        def __init__(self, v_x=0.0, v_y=0.0, v_z=0.0, v_roll=0.0, v_pitch=0.0, v_yaw=0.0):
            self.v_x = v_x
            self.v_y = v_y
            self.v_z = v_z
            self.v_roll = v_roll
            self.v_pitch = v_pitch
            self.v_yaw = v_yaw

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Vel3D):
                return NotImplemented
            else:
                if self.v_x != other.v_x:
                    return False
                if self.v_y != other.v_y:
                    return False
                if self.v_z != other.v_z:
                    return False
                if self.v_roll != other.v_roll:
                    return False
                if self.v_pitch != other.v_pitch:
                    return False
                if self.v_yaw != other.v_yaw:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Vel3D)

        __repr__ = __str__

    _M_wizrobo_npu._t_Vel3D = IcePy.defineStruct('::wizrobo_npu::Vel3D', Vel3D, (), (
        ('v_x', (), IcePy._t_float),
        ('v_y', (), IcePy._t_float),
        ('v_z', (), IcePy._t_float),
        ('v_roll', (), IcePy._t_float),
        ('v_pitch', (), IcePy._t_float),
        ('v_yaw', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.Vel3D = Vel3D
    del Vel3D

if 'Acc2D' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Acc2D = Ice.createTempClass()
    class Acc2D(object):
        def __init__(self, a_x=0.0, a_y=0.0, a_yaw=0.0):
            self.a_x = a_x
            self.a_y = a_y
            self.a_yaw = a_yaw

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Acc2D):
                return NotImplemented
            else:
                if self.a_x != other.a_x:
                    return False
                if self.a_y != other.a_y:
                    return False
                if self.a_yaw != other.a_yaw:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Acc2D)

        __repr__ = __str__

    _M_wizrobo_npu._t_Acc2D = IcePy.defineStruct('::wizrobo_npu::Acc2D', Acc2D, (), (
        ('a_x', (), IcePy._t_float),
        ('a_y', (), IcePy._t_float),
        ('a_yaw', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.Acc2D = Acc2D
    del Acc2D

if 'Acc3D' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.Acc3D = Ice.createTempClass()
    class Acc3D(object):
        def __init__(self, a_x=0.0, a_y=0.0, a_z=0.0, a_roll=0.0, a_pitch=0.0, a_yaw=0.0):
            self.a_x = a_x
            self.a_y = a_y
            self.a_z = a_z
            self.a_roll = a_roll
            self.a_pitch = a_pitch
            self.a_yaw = a_yaw

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.Acc3D):
                return NotImplemented
            else:
                if self.a_x != other.a_x:
                    return False
                if self.a_y != other.a_y:
                    return False
                if self.a_z != other.a_z:
                    return False
                if self.a_roll != other.a_roll:
                    return False
                if self.a_pitch != other.a_pitch:
                    return False
                if self.a_yaw != other.a_yaw:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_Acc3D)

        __repr__ = __str__

    _M_wizrobo_npu._t_Acc3D = IcePy.defineStruct('::wizrobo_npu::Acc3D', Acc3D, (), (
        ('a_x', (), IcePy._t_float),
        ('a_y', (), IcePy._t_float),
        ('a_z', (), IcePy._t_float),
        ('a_roll', (), IcePy._t_float),
        ('a_pitch', (), IcePy._t_float),
        ('a_yaw', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.Acc3D = Acc3D
    del Acc3D

if 'InitPoseArea' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.InitPoseArea = Ice.createTempClass()
    class InitPoseArea(object):
        def __init__(self, pose=Ice._struct_marker, width=0.0, height=0.0):
            if pose is Ice._struct_marker:
                self.pose = _M_wizrobo_npu.Pose3D()
            else:
                self.pose = pose
            self.width = width
            self.height = height

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.InitPoseArea):
                return NotImplemented
            else:
                if self.pose != other.pose:
                    return False
                if self.width != other.width:
                    return False
                if self.height != other.height:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_InitPoseArea)

        __repr__ = __str__

    _M_wizrobo_npu._t_InitPoseArea = IcePy.defineStruct('::wizrobo_npu::InitPoseArea', InitPoseArea, (), (
        ('pose', (), _M_wizrobo_npu._t_Pose3D),
        ('width', (), IcePy._t_float),
        ('height', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.InitPoseArea = InitPoseArea
    del InitPoseArea

if 'InitImgPoseArea' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.InitImgPoseArea = Ice.createTempClass()
    class InitImgPoseArea(object):
        def __init__(self, pose=Ice._struct_marker, width=0, height=0):
            if pose is Ice._struct_marker:
                self.pose = _M_wizrobo_npu.ImgPose()
            else:
                self.pose = pose
            self.width = width
            self.height = height

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.InitImgPoseArea):
                return NotImplemented
            else:
                if self.pose != other.pose:
                    return False
                if self.width != other.width:
                    return False
                if self.height != other.height:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_InitImgPoseArea)

        __repr__ = __str__

    _M_wizrobo_npu._t_InitImgPoseArea = IcePy.defineStruct('::wizrobo_npu::InitImgPoseArea', InitImgPoseArea, (), (
        ('pose', (), _M_wizrobo_npu._t_ImgPose),
        ('width', (), IcePy._t_int),
        ('height', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.InitImgPoseArea = InitImgPoseArea
    del InitImgPoseArea

# End of module wizrobo_npu
