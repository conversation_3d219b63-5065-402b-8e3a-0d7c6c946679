# **********************************************************************
#
# Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
#
# This copy of Ice is licensed to you under the terms described in the
# ICE_LICENSE file included in this distribution.
#
# **********************************************************************
#
# Ice version 3.5.1
#
# <auto-generated>
#
# Generated from file `npuice_data.ice'
#
# Warning: do not edit this file.
#
# </auto-generated>
#

import Ice, IcePy
import npuice_geometry_ice
import npuice_param_ice

# Included module wizrobo_npu
_M_wizrobo_npu = Ice.openModule('wizrobo_npu')

# Start of module wizrobo_npu
__name__ = 'wizrobo_npu'

if '_t_BoolArray' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_BoolArray = IcePy.defineSequence('::wizrobo_npu::BoolArray', (), IcePy._t_bool)

if '_t_IntArray' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_IntArray = IcePy.defineSequence('::wizrobo_npu::IntArray', (), IcePy._t_int)

if '_t_FloatArray' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_FloatArray = IcePy.defineSequence('::wizrobo_npu::FloatArray', (), IcePy._t_float)

if '_t_StringArray' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_StringArray = IcePy.defineSequence('::wizrobo_npu::StringArray', (), IcePy._t_string)

if '_t_ByteArray' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_ByteArray = IcePy.defineSequence('::wizrobo_npu::ByteArray', (), IcePy._t_byte)

if '_t_ZipFile' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_ZipFile = IcePy.defineSequence('::wizrobo_npu::ZipFile', (), IcePy._t_byte)

if 'DiagnosticsStatus' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.DiagnosticsStatus = Ice.createTempClass()
    class DiagnosticsStatus(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    DiagnosticsStatus.DISCONNECT = DiagnosticsStatus("DISCONNECT", 0)
    DiagnosticsStatus.CONNECT = DiagnosticsStatus("CONNECT", 1)
    DiagnosticsStatus.DATA_RECEIVED = DiagnosticsStatus("DATA_RECEIVED", 2)
    DiagnosticsStatus.DATA_ERROR = DiagnosticsStatus("DATA_ERROR", 3)
    DiagnosticsStatus.PROGRAM_NORMAL = DiagnosticsStatus("PROGRAM_NORMAL", 4)
    DiagnosticsStatus.PROGRAM_ERROR = DiagnosticsStatus("PROGRAM_ERROR", 5)
    DiagnosticsStatus.IGNORE = DiagnosticsStatus("IGNORE", 6)
    DiagnosticsStatus._enumerators = { 0:DiagnosticsStatus.DISCONNECT, 1:DiagnosticsStatus.CONNECT, 2:DiagnosticsStatus.DATA_RECEIVED, 3:DiagnosticsStatus.DATA_ERROR, 4:DiagnosticsStatus.PROGRAM_NORMAL, 5:DiagnosticsStatus.PROGRAM_ERROR, 6:DiagnosticsStatus.IGNORE }

    _M_wizrobo_npu._t_DiagnosticsStatus = IcePy.defineEnum('::wizrobo_npu::DiagnosticsStatus', DiagnosticsStatus, (), DiagnosticsStatus._enumerators)

    _M_wizrobo_npu.DiagnosticsStatus = DiagnosticsStatus
    del DiagnosticsStatus

if 'SensorState' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.SensorState = Ice.createTempClass()
    class SensorState(object):
        def __init__(self, sensor_id='', hardware_status=_M_wizrobo_npu.DiagnosticsStatus.DISCONNECT, topic_status=_M_wizrobo_npu.DiagnosticsStatus.DISCONNECT, node_status=_M_wizrobo_npu.DiagnosticsStatus.DISCONNECT):
            self.sensor_id = sensor_id
            self.hardware_status = hardware_status
            self.topic_status = topic_status
            self.node_status = node_status

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.sensor_id)
            _h = 5 * _h + Ice.getHash(self.hardware_status)
            _h = 5 * _h + Ice.getHash(self.topic_status)
            _h = 5 * _h + Ice.getHash(self.node_status)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.SensorState):
                return NotImplemented
            else:
                if self.sensor_id is None or other.sensor_id is None:
                    if self.sensor_id != other.sensor_id:
                        return (-1 if self.sensor_id is None else 1)
                else:
                    if self.sensor_id < other.sensor_id:
                        return -1
                    elif self.sensor_id > other.sensor_id:
                        return 1
                if self.hardware_status is None or other.hardware_status is None:
                    if self.hardware_status != other.hardware_status:
                        return (-1 if self.hardware_status is None else 1)
                else:
                    if self.hardware_status < other.hardware_status:
                        return -1
                    elif self.hardware_status > other.hardware_status:
                        return 1
                if self.topic_status is None or other.topic_status is None:
                    if self.topic_status != other.topic_status:
                        return (-1 if self.topic_status is None else 1)
                else:
                    if self.topic_status < other.topic_status:
                        return -1
                    elif self.topic_status > other.topic_status:
                        return 1
                if self.node_status is None or other.node_status is None:
                    if self.node_status != other.node_status:
                        return (-1 if self.node_status is None else 1)
                else:
                    if self.node_status < other.node_status:
                        return -1
                    elif self.node_status > other.node_status:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_SensorState)

        __repr__ = __str__

    _M_wizrobo_npu._t_SensorState = IcePy.defineStruct('::wizrobo_npu::SensorState', SensorState, (), (
        ('sensor_id', (), IcePy._t_string),
        ('hardware_status', (), _M_wizrobo_npu._t_DiagnosticsStatus),
        ('topic_status', (), _M_wizrobo_npu._t_DiagnosticsStatus),
        ('node_status', (), _M_wizrobo_npu._t_DiagnosticsStatus)
    ))

    _M_wizrobo_npu.SensorState = SensorState
    del SensorState

if '_t_SensorStatus' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_SensorStatus = IcePy.defineSequence('::wizrobo_npu::SensorStatus', (), _M_wizrobo_npu._t_SensorState)

if 'SystemDiagInfo' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.SystemDiagInfo = Ice.createTempClass()
    class SystemDiagInfo(object):
        def __init__(self, todo=''):
            self.todo = todo

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.todo)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.SystemDiagInfo):
                return NotImplemented
            else:
                if self.todo is None or other.todo is None:
                    if self.todo != other.todo:
                        return (-1 if self.todo is None else 1)
                else:
                    if self.todo < other.todo:
                        return -1
                    elif self.todo > other.todo:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_SystemDiagInfo)

        __repr__ = __str__

    _M_wizrobo_npu._t_SystemDiagInfo = IcePy.defineStruct('::wizrobo_npu::SystemDiagInfo', SystemDiagInfo, (), (('todo', (), IcePy._t_string),))

    _M_wizrobo_npu.SystemDiagInfo = SystemDiagInfo
    del SystemDiagInfo

if 'FileInfo' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.FileInfo = Ice.createTempClass()
    class FileInfo(object):
        def __init__(self, file_name='', chunk_num=0, chunk_size_Bytes=0):
            self.file_name = file_name
            self.chunk_num = chunk_num
            self.chunk_size_Bytes = chunk_size_Bytes

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.file_name)
            _h = 5 * _h + Ice.getHash(self.chunk_num)
            _h = 5 * _h + Ice.getHash(self.chunk_size_Bytes)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.FileInfo):
                return NotImplemented
            else:
                if self.file_name is None or other.file_name is None:
                    if self.file_name != other.file_name:
                        return (-1 if self.file_name is None else 1)
                else:
                    if self.file_name < other.file_name:
                        return -1
                    elif self.file_name > other.file_name:
                        return 1
                if self.chunk_num is None or other.chunk_num is None:
                    if self.chunk_num != other.chunk_num:
                        return (-1 if self.chunk_num is None else 1)
                else:
                    if self.chunk_num < other.chunk_num:
                        return -1
                    elif self.chunk_num > other.chunk_num:
                        return 1
                if self.chunk_size_Bytes is None or other.chunk_size_Bytes is None:
                    if self.chunk_size_Bytes != other.chunk_size_Bytes:
                        return (-1 if self.chunk_size_Bytes is None else 1)
                else:
                    if self.chunk_size_Bytes < other.chunk_size_Bytes:
                        return -1
                    elif self.chunk_size_Bytes > other.chunk_size_Bytes:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_FileInfo)

        __repr__ = __str__

    _M_wizrobo_npu._t_FileInfo = IcePy.defineStruct('::wizrobo_npu::FileInfo', FileInfo, (), (
        ('file_name', (), IcePy._t_string),
        ('chunk_num', (), IcePy._t_int),
        ('chunk_size_Bytes', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.FileInfo = FileInfo
    del FileInfo

if 'FileData' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.FileData = Ice.createTempClass()
    class FileData(object):
        def __init__(self, file_name='', chunk_num=0, data=None, chunk_length=0, chunk_index=0, offset=0):
            self.file_name = file_name
            self.chunk_num = chunk_num
            self.data = data
            self.chunk_length = chunk_length
            self.chunk_index = chunk_index
            self.offset = offset

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.file_name)
            _h = 5 * _h + Ice.getHash(self.chunk_num)
            if self.data:
                for _i0 in self.data:
                    _h = 5 * _h + Ice.getHash(_i0)
            _h = 5 * _h + Ice.getHash(self.chunk_length)
            _h = 5 * _h + Ice.getHash(self.chunk_index)
            _h = 5 * _h + Ice.getHash(self.offset)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.FileData):
                return NotImplemented
            else:
                if self.file_name is None or other.file_name is None:
                    if self.file_name != other.file_name:
                        return (-1 if self.file_name is None else 1)
                else:
                    if self.file_name < other.file_name:
                        return -1
                    elif self.file_name > other.file_name:
                        return 1
                if self.chunk_num is None or other.chunk_num is None:
                    if self.chunk_num != other.chunk_num:
                        return (-1 if self.chunk_num is None else 1)
                else:
                    if self.chunk_num < other.chunk_num:
                        return -1
                    elif self.chunk_num > other.chunk_num:
                        return 1
                if self.data is None or other.data is None:
                    if self.data != other.data:
                        return (-1 if self.data is None else 1)
                else:
                    if self.data < other.data:
                        return -1
                    elif self.data > other.data:
                        return 1
                if self.chunk_length is None or other.chunk_length is None:
                    if self.chunk_length != other.chunk_length:
                        return (-1 if self.chunk_length is None else 1)
                else:
                    if self.chunk_length < other.chunk_length:
                        return -1
                    elif self.chunk_length > other.chunk_length:
                        return 1
                if self.chunk_index is None or other.chunk_index is None:
                    if self.chunk_index != other.chunk_index:
                        return (-1 if self.chunk_index is None else 1)
                else:
                    if self.chunk_index < other.chunk_index:
                        return -1
                    elif self.chunk_index > other.chunk_index:
                        return 1
                if self.offset is None or other.offset is None:
                    if self.offset != other.offset:
                        return (-1 if self.offset is None else 1)
                else:
                    if self.offset < other.offset:
                        return -1
                    elif self.offset > other.offset:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_FileData)

        __repr__ = __str__

    _M_wizrobo_npu._t_FileData = IcePy.defineStruct('::wizrobo_npu::FileData', FileData, (), (
        ('file_name', (), IcePy._t_string),
        ('chunk_num', (), IcePy._t_int),
        ('data', (), _M_wizrobo_npu._t_ZipFile),
        ('chunk_length', (), IcePy._t_int),
        ('chunk_index', (), IcePy._t_int),
        ('offset', (), IcePy._t_int)
    ))

    _M_wizrobo_npu.FileData = FileData
    del FileData

if 'MotorEnc' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.MotorEnc = Ice.createTempClass()
    class MotorEnc(object):
        def __init__(self, motor_num=0, ticks=None, steer_angle_deg=0.0):
            self.motor_num = motor_num
            self.ticks = ticks
            self.steer_angle_deg = steer_angle_deg

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.MotorEnc):
                return NotImplemented
            else:
                if self.motor_num != other.motor_num:
                    return False
                if self.ticks != other.ticks:
                    return False
                if self.steer_angle_deg != other.steer_angle_deg:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_MotorEnc)

        __repr__ = __str__

    _M_wizrobo_npu._t_MotorEnc = IcePy.defineStruct('::wizrobo_npu::MotorEnc', MotorEnc, (), (
        ('motor_num', (), IcePy._t_int),
        ('ticks', (), _M_wizrobo_npu._t_IntArray),
        ('steer_angle_deg', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.MotorEnc = MotorEnc
    del MotorEnc

if 'MotorSpd' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.MotorSpd = Ice.createTempClass()
    class MotorSpd(object):
        def __init__(self, motor_num=0, rpms=None, steer_angle_deg=0.0):
            self.motor_num = motor_num
            self.rpms = rpms
            self.steer_angle_deg = steer_angle_deg

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.MotorSpd):
                return NotImplemented
            else:
                if self.motor_num != other.motor_num:
                    return False
                if self.rpms != other.rpms:
                    return False
                if self.steer_angle_deg != other.steer_angle_deg:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_MotorSpd)

        __repr__ = __str__

    _M_wizrobo_npu._t_MotorSpd = IcePy.defineStruct('::wizrobo_npu::MotorSpd', MotorSpd, (), (
        ('motor_num', (), IcePy._t_int),
        ('rpms', (), _M_wizrobo_npu._t_FloatArray),
        ('steer_angle_deg', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.MotorSpd = MotorSpd
    del MotorSpd

if 'LidarScan' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.LidarScan = Ice.createTempClass()
    class LidarScan(object):
        def __init__(self, points=None, intensities=None):
            self.points = points
            self.intensities = intensities

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.LidarScan):
                return NotImplemented
            else:
                if self.points != other.points:
                    return False
                if self.intensities != other.intensities:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_LidarScan)

        __repr__ = __str__

    _M_wizrobo_npu._t_LidarScan = IcePy.defineStruct('::wizrobo_npu::LidarScan', LidarScan, (), (
        ('points', (), _M_wizrobo_npu._t_Point3DList),
        ('intensities', (), _M_wizrobo_npu._t_FloatArray)
    ))

    _M_wizrobo_npu.LidarScan = LidarScan
    del LidarScan

if 'ImgLidarScan' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImgLidarScan = Ice.createTempClass()
    class ImgLidarScan(object):
        def __init__(self, points=None, intensities=None):
            self.points = points
            self.intensities = intensities

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.ImgLidarScan):
                return NotImplemented
            else:
                if self.points != other.points:
                    return False
                if self.intensities != other.intensities:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImgLidarScan)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImgLidarScan = IcePy.defineStruct('::wizrobo_npu::ImgLidarScan', ImgLidarScan, (), (
        ('points', (), _M_wizrobo_npu._t_ImgPointList),
        ('intensities', (), _M_wizrobo_npu._t_FloatArray)
    ))

    _M_wizrobo_npu.ImgLidarScan = ImgLidarScan
    del ImgLidarScan

if 'SonarScan' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.SonarScan = Ice.createTempClass()
    class SonarScan(object):
        def __init__(self, points=None):
            self.points = points

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.SonarScan):
                return NotImplemented
            else:
                if self.points != other.points:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_SonarScan)

        __repr__ = __str__

    _M_wizrobo_npu._t_SonarScan = IcePy.defineStruct('::wizrobo_npu::SonarScan', SonarScan, (), (('points', (), _M_wizrobo_npu._t_Point3DList),))

    _M_wizrobo_npu.SonarScan = SonarScan
    del SonarScan

if 'ImgSonarScan' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImgSonarScan = Ice.createTempClass()
    class ImgSonarScan(object):
        def __init__(self, points=None):
            self.points = points

        def __hash__(self):
            _h = 0
            if self.points:
                for _i0 in self.points:
                    _h = 5 * _h + Ice.getHash(_i0)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.ImgSonarScan):
                return NotImplemented
            else:
                if self.points is None or other.points is None:
                    if self.points != other.points:
                        return (-1 if self.points is None else 1)
                else:
                    if self.points < other.points:
                        return -1
                    elif self.points > other.points:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImgSonarScan)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImgSonarScan = IcePy.defineStruct('::wizrobo_npu::ImgSonarScan', ImgSonarScan, (), (('points', (), _M_wizrobo_npu._t_ImgPointList),))

    _M_wizrobo_npu.ImgSonarScan = ImgSonarScan
    del ImgSonarScan

if 'InfrdScan' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.InfrdScan = Ice.createTempClass()
    class InfrdScan(object):
        def __init__(self, points=None):
            self.points = points

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.InfrdScan):
                return NotImplemented
            else:
                if self.points != other.points:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_InfrdScan)

        __repr__ = __str__

    _M_wizrobo_npu._t_InfrdScan = IcePy.defineStruct('::wizrobo_npu::InfrdScan', InfrdScan, (), (('points', (), _M_wizrobo_npu._t_Point3DList),))

    _M_wizrobo_npu.InfrdScan = InfrdScan
    del InfrdScan

if 'ImgInfrdScan' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImgInfrdScan = Ice.createTempClass()
    class ImgInfrdScan(object):
        def __init__(self, points=None):
            self.points = points

        def __hash__(self):
            _h = 0
            if self.points:
                for _i0 in self.points:
                    _h = 5 * _h + Ice.getHash(_i0)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.ImgInfrdScan):
                return NotImplemented
            else:
                if self.points is None or other.points is None:
                    if self.points != other.points:
                        return (-1 if self.points is None else 1)
                else:
                    if self.points < other.points:
                        return -1
                    elif self.points > other.points:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImgInfrdScan)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImgInfrdScan = IcePy.defineStruct('::wizrobo_npu::ImgInfrdScan', ImgInfrdScan, (), (('points', (), _M_wizrobo_npu._t_ImgPointList),))

    _M_wizrobo_npu.ImgInfrdScan = ImgInfrdScan
    del ImgInfrdScan

if 'BumperData' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.BumperData = Ice.createTempClass()
    class BumperData(object):
        def __init__(self, location=_M_wizrobo_npu.BumperLocation.FRONT_BUMPER, state=False):
            self.location = location
            self.state = state

        def __hash__(self):
            _h = 0
            _h = 5 * _h + Ice.getHash(self.location)
            _h = 5 * _h + Ice.getHash(self.state)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.BumperData):
                return NotImplemented
            else:
                if self.location is None or other.location is None:
                    if self.location != other.location:
                        return (-1 if self.location is None else 1)
                else:
                    if self.location < other.location:
                        return -1
                    elif self.location > other.location:
                        return 1
                if self.state is None or other.state is None:
                    if self.state != other.state:
                        return (-1 if self.state is None else 1)
                else:
                    if self.state < other.state:
                        return -1
                    elif self.state > other.state:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_BumperData)

        __repr__ = __str__

    _M_wizrobo_npu._t_BumperData = IcePy.defineStruct('::wizrobo_npu::BumperData', BumperData, (), (
        ('location', (), _M_wizrobo_npu._t_BumperLocation),
        ('state', (), IcePy._t_bool)
    ))

    _M_wizrobo_npu.BumperData = BumperData
    del BumperData

if '_t_BumperDataList' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu._t_BumperDataList = IcePy.defineSequence('::wizrobo_npu::BumperDataList', (), _M_wizrobo_npu._t_BumperData)

if 'BumperArray' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.BumperArray = Ice.createTempClass()
    class BumperArray(object):
        def __init__(self, states=None):
            self.states = states

        def __hash__(self):
            _h = 0
            if self.states:
                for _i0 in self.states:
                    _h = 5 * _h + Ice.getHash(_i0)
            return _h % 0x7fffffff

        def __compare(self, other):
            if other is None:
                return 1
            elif not isinstance(other, _M_wizrobo_npu.BumperArray):
                return NotImplemented
            else:
                if self.states is None or other.states is None:
                    if self.states != other.states:
                        return (-1 if self.states is None else 1)
                else:
                    if self.states < other.states:
                        return -1
                    elif self.states > other.states:
                        return 1
                return 0

        def __lt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r < 0

        def __le__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r <= 0

        def __gt__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r > 0

        def __ge__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r >= 0

        def __eq__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r == 0

        def __ne__(self, other):
            r = self.__compare(other)
            if r is NotImplemented:
                return r
            else:
                return r != 0

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_BumperArray)

        __repr__ = __str__

    _M_wizrobo_npu._t_BumperArray = IcePy.defineStruct('::wizrobo_npu::BumperArray', BumperArray, (), (('states', (), _M_wizrobo_npu._t_BumperDataList),))

    _M_wizrobo_npu.BumperArray = BumperArray
    del BumperArray

if 'ImuData' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ImuData = Ice.createTempClass()
    class ImuData(object):
        def __init__(self, roll_deg=0.0, pitch_deg=0.0, yaw_deg=0.0):
            self.roll_deg = roll_deg
            self.pitch_deg = pitch_deg
            self.yaw_deg = yaw_deg

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.ImuData):
                return NotImplemented
            else:
                if self.roll_deg != other.roll_deg:
                    return False
                if self.pitch_deg != other.pitch_deg:
                    return False
                if self.yaw_deg != other.yaw_deg:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_ImuData)

        __repr__ = __str__

    _M_wizrobo_npu._t_ImuData = IcePy.defineStruct('::wizrobo_npu::ImuData', ImuData, (), (
        ('roll_deg', (), IcePy._t_float),
        ('pitch_deg', (), IcePy._t_float),
        ('yaw_deg', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.ImuData = ImuData
    del ImuData

if 'GpsData' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.GpsData = Ice.createTempClass()
    class GpsData(object):
        def __init__(self, latitude_deg=0.0, longitude_deg=0.0, altitude_m=0.0):
            self.latitude_deg = latitude_deg
            self.longitude_deg = longitude_deg
            self.altitude_m = altitude_m

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.GpsData):
                return NotImplemented
            else:
                if self.latitude_deg != other.latitude_deg:
                    return False
                if self.longitude_deg != other.longitude_deg:
                    return False
                if self.altitude_m != other.altitude_m:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_GpsData)

        __repr__ = __str__

    _M_wizrobo_npu._t_GpsData = IcePy.defineStruct('::wizrobo_npu::GpsData', GpsData, (), (
        ('latitude_deg', (), IcePy._t_float),
        ('longitude_deg', (), IcePy._t_float),
        ('altitude_m', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.GpsData = GpsData
    del GpsData

if 'BatteryStatus' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.BatteryStatus = Ice.createTempClass()
    class BatteryStatus(object):
        def __init__(self, current_a=0.0, voltage_v=0.0, temperature_deg=0.0, capacity_level=0.0):
            self.current_a = current_a
            self.voltage_v = voltage_v
            self.temperature_deg = temperature_deg
            self.capacity_level = capacity_level

        def __eq__(self, other):
            if other is None:
                return False
            elif not isinstance(other, _M_wizrobo_npu.BatteryStatus):
                return NotImplemented
            else:
                if self.current_a != other.current_a:
                    return False
                if self.voltage_v != other.voltage_v:
                    return False
                if self.temperature_deg != other.temperature_deg:
                    return False
                if self.capacity_level != other.capacity_level:
                    return False
                return True

        def __ne__(self, other):
            return not self.__eq__(other)

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_BatteryStatus)

        __repr__ = __str__

    _M_wizrobo_npu._t_BatteryStatus = IcePy.defineStruct('::wizrobo_npu::BatteryStatus', BatteryStatus, (), (
        ('current_a', (), IcePy._t_float),
        ('voltage_v', (), IcePy._t_float),
        ('temperature_deg', (), IcePy._t_float),
        ('capacity_level', (), IcePy._t_float)
    ))

    _M_wizrobo_npu.BatteryStatus = BatteryStatus
    del BatteryStatus

# End of module wizrobo_npu
