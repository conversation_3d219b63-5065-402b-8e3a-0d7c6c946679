# **********************************************************************
#
# Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
#
# This copy of Ice is licensed to you under the terms described in the
# ICE_LICENSE file included in this distribution.
#
# **********************************************************************
#
# Ice version 3.5.1
#
# <auto-generated>
#
# Generated from file `npuice_api.ice'
#
# Warning: do not edit this file.
#
# </auto-generated>
#

import Ice, IcePy
import npuice_exception_ice
import npuice_geometry_ice
import npuice_data_ice
import npuice_param_ice
import npuice_map_ice

# Included module wizrobo_npu
_M_wizrobo_npu = Ice.openModule('wizrobo_npu')

# Start of module wizrobo_npu
__name__ = 'wizrobo_npu'

_M_wizrobo_npu.NPU_API_VERSION = "0.9.13"

_M_wizrobo_npu.NPU_ICE_VERSION = "3.5.0"

if 'ServerState' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ServerState = Ice.createTempClass()
    class ServerState(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    ServerState.UNKNOWN = ServerState("UNKNOWN", 0)
    ServerState.CONNECTED = ServerState("CONNECTED", 1)
    ServerState.TIMEOUT = ServerState("TIMEOUT", 2)
    ServerState.DISCONNECTED = ServerState("DISCONNECTED", 3)
    ServerState.MEM_EXCEED = ServerState("MEM_EXCEED", 4)
    ServerState._enumerators = { 0:ServerState.UNKNOWN, 1:ServerState.CONNECTED, 2:ServerState.TIMEOUT, 3:ServerState.DISCONNECTED, 4:ServerState.MEM_EXCEED }

    _M_wizrobo_npu._t_ServerState = IcePy.defineEnum('::wizrobo_npu::ServerState', ServerState, (), ServerState._enumerators)

    _M_wizrobo_npu.ServerState = ServerState
    del ServerState

if 'ActionState' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ActionState = Ice.createTempClass()
    class ActionState(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    ActionState.IDLE_ACTION = ActionState("IDLE_ACTION", 0)
    ActionState.SLAM_ACTION = ActionState("SLAM_ACTION", 1)
    ActionState.NAVI_ACTION = ActionState("NAVI_ACTION", 2)
    ActionState.TELEOP_ACTION = ActionState("TELEOP_ACTION", 3)
    ActionState.SWITCH_ACTION = ActionState("SWITCH_ACTION", 4)
    ActionState._enumerators = { 0:ActionState.IDLE_ACTION, 1:ActionState.SLAM_ACTION, 2:ActionState.NAVI_ACTION, 3:ActionState.TELEOP_ACTION, 4:ActionState.SWITCH_ACTION }

    _M_wizrobo_npu._t_ActionState = IcePy.defineEnum('::wizrobo_npu::ActionState', ActionState, (), ActionState._enumerators)

    _M_wizrobo_npu.ActionState = ActionState
    del ActionState

if 'NpuState' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.NpuState = Ice.createTempClass()
    class NpuState(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    NpuState.IDLE_STATE = NpuState("IDLE_STATE", 0)
    NpuState.SLAM_STATE = NpuState("SLAM_STATE", 1)
    NpuState.NAVI_STATE = NpuState("NAVI_STATE", 2)
    NpuState.TELEOP_STATE = NpuState("TELEOP_STATE", 3)
    NpuState.SWITCH_STATE = NpuState("SWITCH_STATE", 4)
    NpuState._enumerators = { 0:NpuState.IDLE_STATE, 1:NpuState.SLAM_STATE, 2:NpuState.NAVI_STATE, 3:NpuState.TELEOP_STATE, 4:NpuState.SWITCH_STATE }

    _M_wizrobo_npu._t_NpuState = IcePy.defineEnum('::wizrobo_npu::NpuState', NpuState, (), NpuState._enumerators)

    _M_wizrobo_npu.NpuState = NpuState
    del NpuState

if 'ManualCmdType' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ManualCmdType = Ice.createTempClass()
    class ManualCmdType(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    ManualCmdType.MOVE_FWD = ManualCmdType("MOVE_FWD", 0)
    ManualCmdType.MOVE_BCK = ManualCmdType("MOVE_BCK", 1)
    ManualCmdType.TURN_LFT = ManualCmdType("TURN_LFT", 2)
    ManualCmdType.TURN_RGT = ManualCmdType("TURN_RGT", 3)
    ManualCmdType.MOVE_FWD_LFT = ManualCmdType("MOVE_FWD_LFT", 4)
    ManualCmdType.MOVE_FWD_RGT = ManualCmdType("MOVE_FWD_RGT", 5)
    ManualCmdType.STOP_MOVE = ManualCmdType("STOP_MOVE", 6)
    ManualCmdType._enumerators = { 0:ManualCmdType.MOVE_FWD, 1:ManualCmdType.MOVE_BCK, 2:ManualCmdType.TURN_LFT, 3:ManualCmdType.TURN_RGT, 4:ManualCmdType.MOVE_FWD_LFT, 5:ManualCmdType.MOVE_FWD_RGT, 6:ManualCmdType.STOP_MOVE }

    _M_wizrobo_npu._t_ManualCmdType = IcePy.defineEnum('::wizrobo_npu::ManualCmdType', ManualCmdType, (), ManualCmdType._enumerators)

    _M_wizrobo_npu.ManualCmdType = ManualCmdType
    del ManualCmdType

if 'NaviMode' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.NaviMode = Ice.createTempClass()
    class NaviMode(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    NaviMode.P2P_NAVI = NaviMode("P2P_NAVI", 0)
    NaviMode.PF_NAVI = NaviMode("PF_NAVI", 1)
    NaviMode.HYBRID_NAVI = NaviMode("HYBRID_NAVI", 2)
    NaviMode._enumerators = { 0:NaviMode.P2P_NAVI, 1:NaviMode.PF_NAVI, 2:NaviMode.HYBRID_NAVI }

    _M_wizrobo_npu._t_NaviMode = IcePy.defineEnum('::wizrobo_npu::NaviMode', NaviMode, (), NaviMode._enumerators)

    _M_wizrobo_npu.NaviMode = NaviMode
    del NaviMode

if 'NaviState' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.NaviState = Ice.createTempClass()
    class NaviState(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    NaviState.PENDING = NaviState("PENDING", 0)
    NaviState.ACTIVE = NaviState("ACTIVE", 1)
    NaviState.PREEMPTED = NaviState("PREEMPTED", 2)
    NaviState.SUCCEEDED = NaviState("SUCCEEDED", 3)
    NaviState.ABORTED = NaviState("ABORTED", 4)
    NaviState.REJECTED = NaviState("REJECTED", 5)
    NaviState.PREEMPTING = NaviState("PREEMPTING", 6)
    NaviState.RECALLING = NaviState("RECALLING", 7)
    NaviState.RECALLED = NaviState("RECALLED", 8)
    NaviState.LOST = NaviState("LOST", 9)
    NaviState.IDLE = NaviState("IDLE", 10)
    NaviState._enumerators = { 0:NaviState.PENDING, 1:NaviState.ACTIVE, 2:NaviState.PREEMPTED, 3:NaviState.SUCCEEDED, 4:NaviState.ABORTED, 5:NaviState.REJECTED, 6:NaviState.PREEMPTING, 7:NaviState.RECALLING, 8:NaviState.RECALLED, 9:NaviState.LOST, 10:NaviState.IDLE }

    _M_wizrobo_npu._t_NaviState = IcePy.defineEnum('::wizrobo_npu::NaviState', NaviState, (), NaviState._enumerators)

    _M_wizrobo_npu.NaviState = NaviState
    del NaviState

if 'CcpMode' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.CcpMode = Ice.createTempClass()
    class CcpMode(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    CcpMode.ZIGZAG_CCP = CcpMode("ZIGZAG_CCP", 0)
    CcpMode.SPIRAL_CCP = CcpMode("SPIRAL_CCP", 1)
    CcpMode._enumerators = { 0:CcpMode.ZIGZAG_CCP, 1:CcpMode.SPIRAL_CCP }

    _M_wizrobo_npu._t_CcpMode = IcePy.defineEnum('::wizrobo_npu::CcpMode', CcpMode, (), CcpMode._enumerators)

    _M_wizrobo_npu.CcpMode = CcpMode
    del CcpMode

if 'SlamMode' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.SlamMode = Ice.createTempClass()
    class SlamMode(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    SlamMode.PF_SLAM = SlamMode("PF_SLAM", 0)
    SlamMode.ICP_SLAM = SlamMode("ICP_SLAM", 1)
    SlamMode.WARE_SLAM = SlamMode("WARE_SLAM", 2)
    SlamMode.GH_SLAM = SlamMode("GH_SLAM", 3)
    SlamMode._enumerators = { 0:SlamMode.PF_SLAM, 1:SlamMode.ICP_SLAM, 2:SlamMode.WARE_SLAM, 3:SlamMode.GH_SLAM }

    _M_wizrobo_npu._t_SlamMode = IcePy.defineEnum('::wizrobo_npu::SlamMode', SlamMode, (), SlamMode._enumerators)

    _M_wizrobo_npu.SlamMode = SlamMode
    del SlamMode

if 'CheckMode' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.CheckMode = Ice.createTempClass()
    class CheckMode(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    CheckMode.ALL = CheckMode("ALL", 0)
    CheckMode.BCU = CheckMode("BCU", 1)
    CheckMode.IMU = CheckMode("IMU", 2)
    CheckMode.LIDAR = CheckMode("LIDAR", 3)
    CheckMode._enumerators = { 0:CheckMode.ALL, 1:CheckMode.BCU, 2:CheckMode.IMU, 3:CheckMode.LIDAR }

    _M_wizrobo_npu._t_CheckMode = IcePy.defineEnum('::wizrobo_npu::CheckMode', CheckMode, (), CheckMode._enumerators)

    _M_wizrobo_npu.CheckMode = CheckMode
    del CheckMode

if 'NpuIce' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.NpuIce = Ice.createTempClass()
    class NpuIce(Ice.Object):
        def __init__(self):
            if Ice.getType(self) == _M_wizrobo_npu.NpuIce:
                raise RuntimeError('wizrobo_npu.NpuIce is an abstract class')

        def ice_ids(self, current=None):
            return ('::Ice::Object', '::wizrobo_npu::NpuIce')

        def ice_id(self, current=None):
            return '::wizrobo_npu::NpuIce'

        def ice_staticId():
            return '::wizrobo_npu::NpuIce'
        ice_staticId = staticmethod(ice_staticId)

        def GetServerVersion(self, current=None):
            pass

        def Connect(self, version, current=None):
            pass

        def GetServerState(self, current=None):
            pass

        def GetSystemDiagInfo(self, current=None):
            pass

        def GetActionState(self, current=None):
            pass

        def GetNpuState(self, current=None):
            pass

        def Shutdown(self, current=None):
            pass

        def Reboot(self, current=None):
            pass

        def GetConfigIdList(self, current=None):
            pass

        def SelectConfig(self, id, current=None):
            pass

        def DeleteConfig(self, id, current=None):
            pass

        def AddConfig(self, id, current=None):
            pass

        def GetCoreParam(self, current=None):
            pass

        def SetCoreParam(self, param, current=None):
            pass

        def GetMotorParam(self, current=None):
            pass

        def SetMotorParam(self, param, current=None):
            pass

        def GetPidParam(self, current=None):
            pass

        def SetPidParam(self, param, current=None):
            pass

        def GetChassisParam(self, current=None):
            pass

        def SetChassisParam(self, param, current=None):
            pass

        def GetFootprintParam(self, current=None):
            pass

        def SetFootprintParam(self, param, current=None):
            pass

        def GetBaseParam(self, current=None):
            pass

        def SetBaseParam(self, param, current=None):
            pass

        def GetSensorParam(self, current=None):
            pass

        def SetSensorParam(self, param, current=None):
            pass

        def GetTeleopParam(self, current=None):
            pass

        def SetTeleopParam(self, param, current=None):
            pass

        def GetNaviParam(self, current=None):
            pass

        def SetNaviParam(self, param, current=None):
            pass

        def GetSlamParam(self, current=None):
            pass

        def SetSlamParam(self, param, current=None):
            pass

        def FeedMotorEnc(self, enc, current=None):
            pass

        def FeedActMotorSpd(self, spd, current=None):
            pass

        def GetMotorEnc(self, current=None):
            pass

        def ClearMotorEnc(self, current=None):
            pass

        def GetCmdMotorSpd(self, current=None):
            pass

        def GetActMotorSpd(self, current=None):
            pass

        def GetLidarScan(self, current=None):
            pass

        def GetImgLidarScan(self, current=None):
            pass

        def GetImuData(self, current=None):
            pass

        def GetSonarScan(self, current=None):
            pass

        def GetImgSonarScan(self, current=None):
            pass

        def GetInfrdScan(self, current=None):
            pass

        def GetImgInfrdScan(self, current=None):
            pass

        def GetBumperArray(self, current=None):
            pass

        def GetBatteryStatus(self, current=None):
            pass

        def SetManualCmd(self, cmd, current=None):
            pass

        def SetManualVel(self, lin_scale, ang_scale, current=None):
            pass

        def GetMapInfos(self, current=None):
            pass

        def SetMapInfos(self, list, current=None):
            pass

        def SelectMap(self, id, current=None):
            pass

        def GetStations(self, map_id, current=None):
            pass

        def SetStations(self, map_id, list, current=None):
            pass

        def GetImgStations(self, map_id, current=None):
            pass

        def SetImgStations(self, map_id, list, current=None):
            pass

        def GetTaskList(self, map_id, current=None):
            pass

        def ExecuteTask(self, list, current=None):
            pass

        def SetTasks(self, map_id, list, current=None):
            pass

        def GetPaths(self, map_id, current=None):
            pass

        def SetPaths(self, map_id, list, current=None):
            pass

        def GetImgPaths(self, map_id, current=None):
            pass

        def SetImgPaths(self, map_id, list, current=None):
            pass

        def GetVirtualWalls(self, map_id, current=None):
            pass

        def SetVirtualWalls(self, map_id, virtual_walls, current=None):
            pass

        def GetImgVirtualWalls(self, map_id, current=None):
            pass

        def SetImgVirtualWalls(self, map_id, virtual_walls, current=None):
            pass

        def GetCmdVel(self, current=None):
            pass

        def GetActVel(self, current=None):
            pass

        def GetCurrentVel(self, current=None):
            pass

        def GetAcc(self, current=None):
            pass

        def GetCurrentAcc(self, current=None):
            pass

        def GetCmdPose(self, current=None):
            pass

        def GetCmdImgPose(self, current=None):
            pass

        def GetActPose(self, current=None):
            pass

        def GetActImgPose(self, current=None):
            pass

        def GetCurrentPose(self, current=None):
            pass

        def GetCurrentImgPose(self, current=None):
            pass

        def GetCmdPath(self, current=None):
            pass

        def GetCmdImgPath(self, current=None):
            pass

        def GetActPath(self, current=None):
            pass

        def GetActImgPath(self, current=None):
            pass

        def GetCurrentPath(self, current=None):
            pass

        def GetCurrentImgPath(self, current=None):
            pass

        def GetMap(self, current=None):
            pass

        def GetImgMap(self, current=None):
            pass

        def GetCurrentMap(self, current=None):
            pass

        def GetCurrentImgMap(self, current=None):
            pass

        def GetFootprintVertices(self, current=None):
            pass

        def GetFootprintImgVertices(self, current=None):
            pass

        def StartTelop(self, current=None):
            pass

        def StopTelop(self, current=None):
            pass

        def SetInitPose(self, pose, current=None):
            pass

        def SetInitImgPose(self, pose, current=None):
            pass

        def SetInitPoseArea(self, pose, current=None):
            pass

        def SetInitImgPoseArea(self, pose, current=None):
            pass

        def GetNaviMode(self, current=None):
            pass

        def StartNavi(self, mode, current=None):
            pass

        def StopNavi(self, current=None):
            pass

        def PauseTask(self, current=None):
            pass

        def ContinueTask(self, current=None):
            pass

        def CancelTask(self, current=None):
            pass

        def GetTaskProgress(self, current=None):
            pass

        def GetNaviState(self, current=None):
            pass

        def GotoPose(self, pose, current=None):
            pass

        def GotoImgPose(self, pose, current=None):
            pass

        def GotoGoal(self, goal, current=None):
            pass

        def GotoImgGoal(self, goal, current=None):
            pass

        def GotoStation(self, map_id, station_id, current=None):
            pass

        def FollowTempPath(self, poses, current=None):
            pass

        def FollowTempImgPath(self, poses, current=None):
            pass

        def FollowPath(self, map_id, path_id, current=None):
            pass

        def PlanCoveragePath(self, vertices, current=None):
            pass

        def PlanCoverageImgPath(self, vertices, current=None):
            pass

        def GetSlamMode(self, current=None):
            pass

        def StartSlam(self, mode, current=None):
            pass

        def StopSlam(self, map_id, current=None):
            pass

        def SaveMapImg(self, map_id, current=None):
            pass

        def ExportConfigFile(self, id, current=None):
            pass

        def ImportConfigFile(self, file, current=None):
            pass

        def ExportMapFile(self, id, current=None):
            pass

        def ImportMapFile(self, file, current=None):
            pass

        def GetExportFileInfo_async(self, _cb, fileName, current=None):
            pass

        def GetExportFiledata(self, fileName, chunk_index, current=None):
            pass

        def SendImportFileData(self, data, current=None):
            pass

        def CheckSensorStatus_async(self, _cb, mode, current=None):
            pass

        def GetSensorStatus(self, current=None):
            pass

        def CheckAbnormalInfo(self, current=None):
            pass

        def GetRuntimeStatus(self, current=None):
            pass

        def __str__(self):
            return IcePy.stringify(self, _M_wizrobo_npu._t_NpuIce)

        __repr__ = __str__

    _M_wizrobo_npu.NpuIcePrx = Ice.createTempClass()
    class NpuIcePrx(Ice.ObjectPrx):

        def GetServerVersion(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetServerVersion.invoke(self, ((), _ctx))

        def begin_GetServerVersion(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetServerVersion.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetServerVersion(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetServerVersion.end(self, _r)

        def Connect(self, version, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_Connect.invoke(self, ((version, ), _ctx))

        def begin_Connect(self, version, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_Connect.begin(self, ((version, ), _response, _ex, _sent, _ctx))

        def end_Connect(self, _r):
            return _M_wizrobo_npu.NpuIce._op_Connect.end(self, _r)

        def GetServerState(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetServerState.invoke(self, ((), _ctx))

        def begin_GetServerState(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetServerState.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetServerState(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetServerState.end(self, _r)

        def GetSystemDiagInfo(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSystemDiagInfo.invoke(self, ((), _ctx))

        def begin_GetSystemDiagInfo(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSystemDiagInfo.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetSystemDiagInfo(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetSystemDiagInfo.end(self, _r)

        def GetActionState(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActionState.invoke(self, ((), _ctx))

        def begin_GetActionState(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActionState.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetActionState(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetActionState.end(self, _r)

        def GetNpuState(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetNpuState.invoke(self, ((), _ctx))

        def begin_GetNpuState(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetNpuState.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetNpuState(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetNpuState.end(self, _r)

        def Shutdown(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_Shutdown.invoke(self, ((), _ctx))

        def begin_Shutdown(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_Shutdown.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_Shutdown(self, _r):
            return _M_wizrobo_npu.NpuIce._op_Shutdown.end(self, _r)

        def Reboot(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_Reboot.invoke(self, ((), _ctx))

        def begin_Reboot(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_Reboot.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_Reboot(self, _r):
            return _M_wizrobo_npu.NpuIce._op_Reboot.end(self, _r)

        def GetConfigIdList(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetConfigIdList.invoke(self, ((), _ctx))

        def begin_GetConfigIdList(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetConfigIdList.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetConfigIdList(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetConfigIdList.end(self, _r)

        def SelectConfig(self, id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SelectConfig.invoke(self, ((id, ), _ctx))

        def begin_SelectConfig(self, id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SelectConfig.begin(self, ((id, ), _response, _ex, _sent, _ctx))

        def end_SelectConfig(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SelectConfig.end(self, _r)

        def DeleteConfig(self, id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_DeleteConfig.invoke(self, ((id, ), _ctx))

        def begin_DeleteConfig(self, id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_DeleteConfig.begin(self, ((id, ), _response, _ex, _sent, _ctx))

        def end_DeleteConfig(self, _r):
            return _M_wizrobo_npu.NpuIce._op_DeleteConfig.end(self, _r)

        def AddConfig(self, id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_AddConfig.invoke(self, ((id, ), _ctx))

        def begin_AddConfig(self, id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_AddConfig.begin(self, ((id, ), _response, _ex, _sent, _ctx))

        def end_AddConfig(self, _r):
            return _M_wizrobo_npu.NpuIce._op_AddConfig.end(self, _r)

        def GetCoreParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCoreParam.invoke(self, ((), _ctx))

        def begin_GetCoreParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCoreParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCoreParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCoreParam.end(self, _r)

        def SetCoreParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetCoreParam.invoke(self, ((param, ), _ctx))

        def begin_SetCoreParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetCoreParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetCoreParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetCoreParam.end(self, _r)

        def GetMotorParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetMotorParam.invoke(self, ((), _ctx))

        def begin_GetMotorParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetMotorParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetMotorParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetMotorParam.end(self, _r)

        def SetMotorParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetMotorParam.invoke(self, ((param, ), _ctx))

        def begin_SetMotorParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetMotorParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetMotorParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetMotorParam.end(self, _r)

        def GetPidParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetPidParam.invoke(self, ((), _ctx))

        def begin_GetPidParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetPidParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetPidParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetPidParam.end(self, _r)

        def SetPidParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetPidParam.invoke(self, ((param, ), _ctx))

        def begin_SetPidParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetPidParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetPidParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetPidParam.end(self, _r)

        def GetChassisParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetChassisParam.invoke(self, ((), _ctx))

        def begin_GetChassisParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetChassisParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetChassisParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetChassisParam.end(self, _r)

        def SetChassisParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetChassisParam.invoke(self, ((param, ), _ctx))

        def begin_SetChassisParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetChassisParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetChassisParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetChassisParam.end(self, _r)

        def GetFootprintParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetFootprintParam.invoke(self, ((), _ctx))

        def begin_GetFootprintParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetFootprintParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetFootprintParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetFootprintParam.end(self, _r)

        def SetFootprintParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetFootprintParam.invoke(self, ((param, ), _ctx))

        def begin_SetFootprintParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetFootprintParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetFootprintParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetFootprintParam.end(self, _r)

        def GetBaseParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetBaseParam.invoke(self, ((), _ctx))

        def begin_GetBaseParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetBaseParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetBaseParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetBaseParam.end(self, _r)

        def SetBaseParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetBaseParam.invoke(self, ((param, ), _ctx))

        def begin_SetBaseParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetBaseParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetBaseParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetBaseParam.end(self, _r)

        def GetSensorParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSensorParam.invoke(self, ((), _ctx))

        def begin_GetSensorParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSensorParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetSensorParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetSensorParam.end(self, _r)

        def SetSensorParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetSensorParam.invoke(self, ((param, ), _ctx))

        def begin_SetSensorParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetSensorParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetSensorParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetSensorParam.end(self, _r)

        def GetTeleopParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetTeleopParam.invoke(self, ((), _ctx))

        def begin_GetTeleopParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetTeleopParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetTeleopParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetTeleopParam.end(self, _r)

        def SetTeleopParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetTeleopParam.invoke(self, ((param, ), _ctx))

        def begin_SetTeleopParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetTeleopParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetTeleopParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetTeleopParam.end(self, _r)

        def GetNaviParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetNaviParam.invoke(self, ((), _ctx))

        def begin_GetNaviParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetNaviParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetNaviParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetNaviParam.end(self, _r)

        def SetNaviParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetNaviParam.invoke(self, ((param, ), _ctx))

        def begin_SetNaviParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetNaviParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetNaviParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetNaviParam.end(self, _r)

        def GetSlamParam(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSlamParam.invoke(self, ((), _ctx))

        def begin_GetSlamParam(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSlamParam.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetSlamParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetSlamParam.end(self, _r)

        def SetSlamParam(self, param, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetSlamParam.invoke(self, ((param, ), _ctx))

        def begin_SetSlamParam(self, param, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetSlamParam.begin(self, ((param, ), _response, _ex, _sent, _ctx))

        def end_SetSlamParam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetSlamParam.end(self, _r)

        def FeedMotorEnc(self, enc, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FeedMotorEnc.invoke(self, ((enc, ), _ctx))

        def begin_FeedMotorEnc(self, enc, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FeedMotorEnc.begin(self, ((enc, ), _response, _ex, _sent, _ctx))

        def end_FeedMotorEnc(self, _r):
            return _M_wizrobo_npu.NpuIce._op_FeedMotorEnc.end(self, _r)

        def FeedActMotorSpd(self, spd, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FeedActMotorSpd.invoke(self, ((spd, ), _ctx))

        def begin_FeedActMotorSpd(self, spd, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FeedActMotorSpd.begin(self, ((spd, ), _response, _ex, _sent, _ctx))

        def end_FeedActMotorSpd(self, _r):
            return _M_wizrobo_npu.NpuIce._op_FeedActMotorSpd.end(self, _r)

        def GetMotorEnc(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetMotorEnc.invoke(self, ((), _ctx))

        def begin_GetMotorEnc(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetMotorEnc.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetMotorEnc(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetMotorEnc.end(self, _r)

        def ClearMotorEnc(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ClearMotorEnc.invoke(self, ((), _ctx))

        def begin_ClearMotorEnc(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ClearMotorEnc.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_ClearMotorEnc(self, _r):
            return _M_wizrobo_npu.NpuIce._op_ClearMotorEnc.end(self, _r)

        def GetCmdMotorSpd(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdMotorSpd.invoke(self, ((), _ctx))

        def begin_GetCmdMotorSpd(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdMotorSpd.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCmdMotorSpd(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCmdMotorSpd.end(self, _r)

        def GetActMotorSpd(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActMotorSpd.invoke(self, ((), _ctx))

        def begin_GetActMotorSpd(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActMotorSpd.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetActMotorSpd(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetActMotorSpd.end(self, _r)

        def GetLidarScan(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetLidarScan.invoke(self, ((), _ctx))

        def begin_GetLidarScan(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetLidarScan.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetLidarScan(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetLidarScan.end(self, _r)

        def GetImgLidarScan(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgLidarScan.invoke(self, ((), _ctx))

        def begin_GetImgLidarScan(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgLidarScan.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetImgLidarScan(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetImgLidarScan.end(self, _r)

        def GetImuData(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImuData.invoke(self, ((), _ctx))

        def begin_GetImuData(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImuData.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetImuData(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetImuData.end(self, _r)

        def GetSonarScan(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSonarScan.invoke(self, ((), _ctx))

        def begin_GetSonarScan(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSonarScan.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetSonarScan(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetSonarScan.end(self, _r)

        def GetImgSonarScan(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgSonarScan.invoke(self, ((), _ctx))

        def begin_GetImgSonarScan(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgSonarScan.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetImgSonarScan(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetImgSonarScan.end(self, _r)

        def GetInfrdScan(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetInfrdScan.invoke(self, ((), _ctx))

        def begin_GetInfrdScan(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetInfrdScan.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetInfrdScan(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetInfrdScan.end(self, _r)

        def GetImgInfrdScan(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgInfrdScan.invoke(self, ((), _ctx))

        def begin_GetImgInfrdScan(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgInfrdScan.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetImgInfrdScan(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetImgInfrdScan.end(self, _r)

        def GetBumperArray(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetBumperArray.invoke(self, ((), _ctx))

        def begin_GetBumperArray(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetBumperArray.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetBumperArray(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetBumperArray.end(self, _r)

        def GetBatteryStatus(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetBatteryStatus.invoke(self, ((), _ctx))

        def begin_GetBatteryStatus(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetBatteryStatus.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetBatteryStatus(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetBatteryStatus.end(self, _r)

        def SetManualCmd(self, cmd, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetManualCmd.invoke(self, ((cmd, ), _ctx))

        def begin_SetManualCmd(self, cmd, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetManualCmd.begin(self, ((cmd, ), _response, _ex, _sent, _ctx))

        def end_SetManualCmd(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetManualCmd.end(self, _r)

        def SetManualVel(self, lin_scale, ang_scale, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetManualVel.invoke(self, ((lin_scale, ang_scale), _ctx))

        def begin_SetManualVel(self, lin_scale, ang_scale, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetManualVel.begin(self, ((lin_scale, ang_scale), _response, _ex, _sent, _ctx))

        def end_SetManualVel(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetManualVel.end(self, _r)

        def GetMapInfos(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetMapInfos.invoke(self, ((), _ctx))

        def begin_GetMapInfos(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetMapInfos.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetMapInfos(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetMapInfos.end(self, _r)

        def SetMapInfos(self, list, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetMapInfos.invoke(self, ((list, ), _ctx))

        def begin_SetMapInfos(self, list, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetMapInfos.begin(self, ((list, ), _response, _ex, _sent, _ctx))

        def end_SetMapInfos(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetMapInfos.end(self, _r)

        def SelectMap(self, id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SelectMap.invoke(self, ((id, ), _ctx))

        def begin_SelectMap(self, id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SelectMap.begin(self, ((id, ), _response, _ex, _sent, _ctx))

        def end_SelectMap(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SelectMap.end(self, _r)

        def GetStations(self, map_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetStations.invoke(self, ((map_id, ), _ctx))

        def begin_GetStations(self, map_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetStations.begin(self, ((map_id, ), _response, _ex, _sent, _ctx))

        def end_GetStations(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetStations.end(self, _r)

        def SetStations(self, map_id, list, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetStations.invoke(self, ((map_id, list), _ctx))

        def begin_SetStations(self, map_id, list, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetStations.begin(self, ((map_id, list), _response, _ex, _sent, _ctx))

        def end_SetStations(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetStations.end(self, _r)

        def GetImgStations(self, map_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgStations.invoke(self, ((map_id, ), _ctx))

        def begin_GetImgStations(self, map_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgStations.begin(self, ((map_id, ), _response, _ex, _sent, _ctx))

        def end_GetImgStations(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetImgStations.end(self, _r)

        def SetImgStations(self, map_id, list, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetImgStations.invoke(self, ((map_id, list), _ctx))

        def begin_SetImgStations(self, map_id, list, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetImgStations.begin(self, ((map_id, list), _response, _ex, _sent, _ctx))

        def end_SetImgStations(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetImgStations.end(self, _r)

        def GetTaskList(self, map_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetTaskList.invoke(self, ((map_id, ), _ctx))

        def begin_GetTaskList(self, map_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetTaskList.begin(self, ((map_id, ), _response, _ex, _sent, _ctx))

        def end_GetTaskList(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetTaskList.end(self, _r)

        def ExecuteTask(self, list, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ExecuteTask.invoke(self, ((list, ), _ctx))

        def begin_ExecuteTask(self, list, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ExecuteTask.begin(self, ((list, ), _response, _ex, _sent, _ctx))

        def end_ExecuteTask(self, _r):
            return _M_wizrobo_npu.NpuIce._op_ExecuteTask.end(self, _r)

        def SetTasks(self, map_id, list, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetTasks.invoke(self, ((map_id, list), _ctx))

        def begin_SetTasks(self, map_id, list, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetTasks.begin(self, ((map_id, list), _response, _ex, _sent, _ctx))

        def end_SetTasks(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetTasks.end(self, _r)

        def GetPaths(self, map_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetPaths.invoke(self, ((map_id, ), _ctx))

        def begin_GetPaths(self, map_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetPaths.begin(self, ((map_id, ), _response, _ex, _sent, _ctx))

        def end_GetPaths(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetPaths.end(self, _r)

        def SetPaths(self, map_id, list, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetPaths.invoke(self, ((map_id, list), _ctx))

        def begin_SetPaths(self, map_id, list, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetPaths.begin(self, ((map_id, list), _response, _ex, _sent, _ctx))

        def end_SetPaths(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetPaths.end(self, _r)

        def GetImgPaths(self, map_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgPaths.invoke(self, ((map_id, ), _ctx))

        def begin_GetImgPaths(self, map_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgPaths.begin(self, ((map_id, ), _response, _ex, _sent, _ctx))

        def end_GetImgPaths(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetImgPaths.end(self, _r)

        def SetImgPaths(self, map_id, list, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetImgPaths.invoke(self, ((map_id, list), _ctx))

        def begin_SetImgPaths(self, map_id, list, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetImgPaths.begin(self, ((map_id, list), _response, _ex, _sent, _ctx))

        def end_SetImgPaths(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetImgPaths.end(self, _r)

        def GetVirtualWalls(self, map_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetVirtualWalls.invoke(self, ((map_id, ), _ctx))

        def begin_GetVirtualWalls(self, map_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetVirtualWalls.begin(self, ((map_id, ), _response, _ex, _sent, _ctx))

        def end_GetVirtualWalls(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetVirtualWalls.end(self, _r)

        def SetVirtualWalls(self, map_id, virtual_walls, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetVirtualWalls.invoke(self, ((map_id, virtual_walls), _ctx))

        def begin_SetVirtualWalls(self, map_id, virtual_walls, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetVirtualWalls.begin(self, ((map_id, virtual_walls), _response, _ex, _sent, _ctx))

        def end_SetVirtualWalls(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetVirtualWalls.end(self, _r)

        def GetImgVirtualWalls(self, map_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgVirtualWalls.invoke(self, ((map_id, ), _ctx))

        def begin_GetImgVirtualWalls(self, map_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgVirtualWalls.begin(self, ((map_id, ), _response, _ex, _sent, _ctx))

        def end_GetImgVirtualWalls(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetImgVirtualWalls.end(self, _r)

        def SetImgVirtualWalls(self, map_id, virtual_walls, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetImgVirtualWalls.invoke(self, ((map_id, virtual_walls), _ctx))

        def begin_SetImgVirtualWalls(self, map_id, virtual_walls, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetImgVirtualWalls.begin(self, ((map_id, virtual_walls), _response, _ex, _sent, _ctx))

        def end_SetImgVirtualWalls(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetImgVirtualWalls.end(self, _r)

        def GetCmdVel(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdVel.invoke(self, ((), _ctx))

        def begin_GetCmdVel(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdVel.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCmdVel(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCmdVel.end(self, _r)

        def GetActVel(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActVel.invoke(self, ((), _ctx))

        def begin_GetActVel(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActVel.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetActVel(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetActVel.end(self, _r)

        def GetCurrentVel(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentVel.invoke(self, ((), _ctx))

        def begin_GetCurrentVel(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentVel.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCurrentVel(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentVel.end(self, _r)

        def GetAcc(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetAcc.invoke(self, ((), _ctx))

        def begin_GetAcc(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetAcc.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetAcc(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetAcc.end(self, _r)

        def GetCurrentAcc(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentAcc.invoke(self, ((), _ctx))

        def begin_GetCurrentAcc(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentAcc.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCurrentAcc(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentAcc.end(self, _r)

        def GetCmdPose(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdPose.invoke(self, ((), _ctx))

        def begin_GetCmdPose(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdPose.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCmdPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCmdPose.end(self, _r)

        def GetCmdImgPose(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdImgPose.invoke(self, ((), _ctx))

        def begin_GetCmdImgPose(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdImgPose.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCmdImgPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCmdImgPose.end(self, _r)

        def GetActPose(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActPose.invoke(self, ((), _ctx))

        def begin_GetActPose(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActPose.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetActPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetActPose.end(self, _r)

        def GetActImgPose(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActImgPose.invoke(self, ((), _ctx))

        def begin_GetActImgPose(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActImgPose.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetActImgPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetActImgPose.end(self, _r)

        def GetCurrentPose(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentPose.invoke(self, ((), _ctx))

        def begin_GetCurrentPose(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentPose.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCurrentPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentPose.end(self, _r)

        def GetCurrentImgPose(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentImgPose.invoke(self, ((), _ctx))

        def begin_GetCurrentImgPose(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentImgPose.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCurrentImgPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentImgPose.end(self, _r)

        def GetCmdPath(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdPath.invoke(self, ((), _ctx))

        def begin_GetCmdPath(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdPath.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCmdPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCmdPath.end(self, _r)

        def GetCmdImgPath(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdImgPath.invoke(self, ((), _ctx))

        def begin_GetCmdImgPath(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCmdImgPath.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCmdImgPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCmdImgPath.end(self, _r)

        def GetActPath(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActPath.invoke(self, ((), _ctx))

        def begin_GetActPath(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActPath.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetActPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetActPath.end(self, _r)

        def GetActImgPath(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActImgPath.invoke(self, ((), _ctx))

        def begin_GetActImgPath(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetActImgPath.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetActImgPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetActImgPath.end(self, _r)

        def GetCurrentPath(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentPath.invoke(self, ((), _ctx))

        def begin_GetCurrentPath(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentPath.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCurrentPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentPath.end(self, _r)

        def GetCurrentImgPath(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentImgPath.invoke(self, ((), _ctx))

        def begin_GetCurrentImgPath(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentImgPath.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCurrentImgPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentImgPath.end(self, _r)

        def GetMap(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetMap.invoke(self, ((), _ctx))

        def begin_GetMap(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetMap.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetMap(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetMap.end(self, _r)

        def GetImgMap(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgMap.invoke(self, ((), _ctx))

        def begin_GetImgMap(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetImgMap.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetImgMap(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetImgMap.end(self, _r)

        def GetCurrentMap(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentMap.invoke(self, ((), _ctx))

        def begin_GetCurrentMap(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentMap.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCurrentMap(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentMap.end(self, _r)

        def GetCurrentImgMap(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentImgMap.invoke(self, ((), _ctx))

        def begin_GetCurrentImgMap(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentImgMap.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetCurrentImgMap(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetCurrentImgMap.end(self, _r)

        def GetFootprintVertices(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetFootprintVertices.invoke(self, ((), _ctx))

        def begin_GetFootprintVertices(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetFootprintVertices.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetFootprintVertices(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetFootprintVertices.end(self, _r)

        def GetFootprintImgVertices(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetFootprintImgVertices.invoke(self, ((), _ctx))

        def begin_GetFootprintImgVertices(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetFootprintImgVertices.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetFootprintImgVertices(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetFootprintImgVertices.end(self, _r)

        def StartTelop(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StartTelop.invoke(self, ((), _ctx))

        def begin_StartTelop(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StartTelop.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_StartTelop(self, _r):
            return _M_wizrobo_npu.NpuIce._op_StartTelop.end(self, _r)

        def StopTelop(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StopTelop.invoke(self, ((), _ctx))

        def begin_StopTelop(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StopTelop.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_StopTelop(self, _r):
            return _M_wizrobo_npu.NpuIce._op_StopTelop.end(self, _r)

        def SetInitPose(self, pose, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetInitPose.invoke(self, ((pose, ), _ctx))

        def begin_SetInitPose(self, pose, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetInitPose.begin(self, ((pose, ), _response, _ex, _sent, _ctx))

        def end_SetInitPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetInitPose.end(self, _r)

        def SetInitImgPose(self, pose, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetInitImgPose.invoke(self, ((pose, ), _ctx))

        def begin_SetInitImgPose(self, pose, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetInitImgPose.begin(self, ((pose, ), _response, _ex, _sent, _ctx))

        def end_SetInitImgPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetInitImgPose.end(self, _r)

        def SetInitPoseArea(self, pose, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetInitPoseArea.invoke(self, ((pose, ), _ctx))

        def begin_SetInitPoseArea(self, pose, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetInitPoseArea.begin(self, ((pose, ), _response, _ex, _sent, _ctx))

        def end_SetInitPoseArea(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetInitPoseArea.end(self, _r)

        def SetInitImgPoseArea(self, pose, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetInitImgPoseArea.invoke(self, ((pose, ), _ctx))

        def begin_SetInitImgPoseArea(self, pose, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SetInitImgPoseArea.begin(self, ((pose, ), _response, _ex, _sent, _ctx))

        def end_SetInitImgPoseArea(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SetInitImgPoseArea.end(self, _r)

        def GetNaviMode(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetNaviMode.invoke(self, ((), _ctx))

        def begin_GetNaviMode(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetNaviMode.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetNaviMode(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetNaviMode.end(self, _r)

        def StartNavi(self, mode, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StartNavi.invoke(self, ((mode, ), _ctx))

        def begin_StartNavi(self, mode, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StartNavi.begin(self, ((mode, ), _response, _ex, _sent, _ctx))

        def end_StartNavi(self, _r):
            return _M_wizrobo_npu.NpuIce._op_StartNavi.end(self, _r)

        def StopNavi(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StopNavi.invoke(self, ((), _ctx))

        def begin_StopNavi(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StopNavi.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_StopNavi(self, _r):
            return _M_wizrobo_npu.NpuIce._op_StopNavi.end(self, _r)

        def PauseTask(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_PauseTask.invoke(self, ((), _ctx))

        def begin_PauseTask(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_PauseTask.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_PauseTask(self, _r):
            return _M_wizrobo_npu.NpuIce._op_PauseTask.end(self, _r)

        def ContinueTask(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ContinueTask.invoke(self, ((), _ctx))

        def begin_ContinueTask(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ContinueTask.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_ContinueTask(self, _r):
            return _M_wizrobo_npu.NpuIce._op_ContinueTask.end(self, _r)

        def CancelTask(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_CancelTask.invoke(self, ((), _ctx))

        def begin_CancelTask(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_CancelTask.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_CancelTask(self, _r):
            return _M_wizrobo_npu.NpuIce._op_CancelTask.end(self, _r)

        def GetTaskProgress(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetTaskProgress.invoke(self, ((), _ctx))

        def begin_GetTaskProgress(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetTaskProgress.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetTaskProgress(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetTaskProgress.end(self, _r)

        def GetNaviState(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetNaviState.invoke(self, ((), _ctx))

        def begin_GetNaviState(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetNaviState.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetNaviState(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetNaviState.end(self, _r)

        def GotoPose(self, pose, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoPose.invoke(self, ((pose, ), _ctx))

        def begin_GotoPose(self, pose, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoPose.begin(self, ((pose, ), _response, _ex, _sent, _ctx))

        def end_GotoPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GotoPose.end(self, _r)

        def GotoImgPose(self, pose, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoImgPose.invoke(self, ((pose, ), _ctx))

        def begin_GotoImgPose(self, pose, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoImgPose.begin(self, ((pose, ), _response, _ex, _sent, _ctx))

        def end_GotoImgPose(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GotoImgPose.end(self, _r)

        def GotoGoal(self, goal, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoGoal.invoke(self, ((goal, ), _ctx))

        def begin_GotoGoal(self, goal, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoGoal.begin(self, ((goal, ), _response, _ex, _sent, _ctx))

        def end_GotoGoal(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GotoGoal.end(self, _r)

        def GotoImgGoal(self, goal, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoImgGoal.invoke(self, ((goal, ), _ctx))

        def begin_GotoImgGoal(self, goal, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoImgGoal.begin(self, ((goal, ), _response, _ex, _sent, _ctx))

        def end_GotoImgGoal(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GotoImgGoal.end(self, _r)

        def GotoStation(self, map_id, station_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoStation.invoke(self, ((map_id, station_id), _ctx))

        def begin_GotoStation(self, map_id, station_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GotoStation.begin(self, ((map_id, station_id), _response, _ex, _sent, _ctx))

        def end_GotoStation(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GotoStation.end(self, _r)

        def FollowTempPath(self, poses, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FollowTempPath.invoke(self, ((poses, ), _ctx))

        def begin_FollowTempPath(self, poses, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FollowTempPath.begin(self, ((poses, ), _response, _ex, _sent, _ctx))

        def end_FollowTempPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_FollowTempPath.end(self, _r)

        def FollowTempImgPath(self, poses, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FollowTempImgPath.invoke(self, ((poses, ), _ctx))

        def begin_FollowTempImgPath(self, poses, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FollowTempImgPath.begin(self, ((poses, ), _response, _ex, _sent, _ctx))

        def end_FollowTempImgPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_FollowTempImgPath.end(self, _r)

        def FollowPath(self, map_id, path_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FollowPath.invoke(self, ((map_id, path_id), _ctx))

        def begin_FollowPath(self, map_id, path_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_FollowPath.begin(self, ((map_id, path_id), _response, _ex, _sent, _ctx))

        def end_FollowPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_FollowPath.end(self, _r)

        def PlanCoveragePath(self, vertices, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_PlanCoveragePath.invoke(self, ((vertices, ), _ctx))

        def begin_PlanCoveragePath(self, vertices, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_PlanCoveragePath.begin(self, ((vertices, ), _response, _ex, _sent, _ctx))

        def end_PlanCoveragePath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_PlanCoveragePath.end(self, _r)

        def PlanCoverageImgPath(self, vertices, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_PlanCoverageImgPath.invoke(self, ((vertices, ), _ctx))

        def begin_PlanCoverageImgPath(self, vertices, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_PlanCoverageImgPath.begin(self, ((vertices, ), _response, _ex, _sent, _ctx))

        def end_PlanCoverageImgPath(self, _r):
            return _M_wizrobo_npu.NpuIce._op_PlanCoverageImgPath.end(self, _r)

        def GetSlamMode(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSlamMode.invoke(self, ((), _ctx))

        def begin_GetSlamMode(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSlamMode.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetSlamMode(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetSlamMode.end(self, _r)

        def StartSlam(self, mode, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StartSlam.invoke(self, ((mode, ), _ctx))

        def begin_StartSlam(self, mode, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StartSlam.begin(self, ((mode, ), _response, _ex, _sent, _ctx))

        def end_StartSlam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_StartSlam.end(self, _r)

        def StopSlam(self, map_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StopSlam.invoke(self, ((map_id, ), _ctx))

        def begin_StopSlam(self, map_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_StopSlam.begin(self, ((map_id, ), _response, _ex, _sent, _ctx))

        def end_StopSlam(self, _r):
            return _M_wizrobo_npu.NpuIce._op_StopSlam.end(self, _r)

        def SaveMapImg(self, map_id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SaveMapImg.invoke(self, ((map_id, ), _ctx))

        def begin_SaveMapImg(self, map_id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SaveMapImg.begin(self, ((map_id, ), _response, _ex, _sent, _ctx))

        def end_SaveMapImg(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SaveMapImg.end(self, _r)

        def ExportConfigFile(self, id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ExportConfigFile.invoke(self, ((id, ), _ctx))

        def begin_ExportConfigFile(self, id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ExportConfigFile.begin(self, ((id, ), _response, _ex, _sent, _ctx))

        def end_ExportConfigFile(self, _r):
            return _M_wizrobo_npu.NpuIce._op_ExportConfigFile.end(self, _r)

        def ImportConfigFile(self, file, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ImportConfigFile.invoke(self, ((file, ), _ctx))

        def begin_ImportConfigFile(self, file, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ImportConfigFile.begin(self, ((file, ), _response, _ex, _sent, _ctx))

        def end_ImportConfigFile(self, _r):
            return _M_wizrobo_npu.NpuIce._op_ImportConfigFile.end(self, _r)

        def ExportMapFile(self, id, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ExportMapFile.invoke(self, ((id, ), _ctx))

        def begin_ExportMapFile(self, id, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ExportMapFile.begin(self, ((id, ), _response, _ex, _sent, _ctx))

        def end_ExportMapFile(self, _r):
            return _M_wizrobo_npu.NpuIce._op_ExportMapFile.end(self, _r)

        def ImportMapFile(self, file, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ImportMapFile.invoke(self, ((file, ), _ctx))

        def begin_ImportMapFile(self, file, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_ImportMapFile.begin(self, ((file, ), _response, _ex, _sent, _ctx))

        def end_ImportMapFile(self, _r):
            return _M_wizrobo_npu.NpuIce._op_ImportMapFile.end(self, _r)

        def GetExportFileInfo(self, fileName, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetExportFileInfo.invoke(self, ((fileName, ), _ctx))

        def begin_GetExportFileInfo(self, fileName, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetExportFileInfo.begin(self, ((fileName, ), _response, _ex, _sent, _ctx))

        def end_GetExportFileInfo(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetExportFileInfo.end(self, _r)

        def GetExportFiledata(self, fileName, chunk_index, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetExportFiledata.invoke(self, ((fileName, chunk_index), _ctx))

        def begin_GetExportFiledata(self, fileName, chunk_index, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetExportFiledata.begin(self, ((fileName, chunk_index), _response, _ex, _sent, _ctx))

        def end_GetExportFiledata(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetExportFiledata.end(self, _r)

        def SendImportFileData(self, data, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SendImportFileData.invoke(self, ((data, ), _ctx))

        def begin_SendImportFileData(self, data, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_SendImportFileData.begin(self, ((data, ), _response, _ex, _sent, _ctx))

        def end_SendImportFileData(self, _r):
            return _M_wizrobo_npu.NpuIce._op_SendImportFileData.end(self, _r)

        def CheckSensorStatus(self, mode, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_CheckSensorStatus.invoke(self, ((mode, ), _ctx))

        def begin_CheckSensorStatus(self, mode, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_CheckSensorStatus.begin(self, ((mode, ), _response, _ex, _sent, _ctx))

        def end_CheckSensorStatus(self, _r):
            return _M_wizrobo_npu.NpuIce._op_CheckSensorStatus.end(self, _r)

        def GetSensorStatus(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSensorStatus.invoke(self, ((), _ctx))

        def begin_GetSensorStatus(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetSensorStatus.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetSensorStatus(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetSensorStatus.end(self, _r)

        def CheckAbnormalInfo(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_CheckAbnormalInfo.invoke(self, ((), _ctx))

        def begin_CheckAbnormalInfo(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_CheckAbnormalInfo.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_CheckAbnormalInfo(self, _r):
            return _M_wizrobo_npu.NpuIce._op_CheckAbnormalInfo.end(self, _r)

        def GetRuntimeStatus(self, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetRuntimeStatus.invoke(self, ((), _ctx))

        def begin_GetRuntimeStatus(self, _response=None, _ex=None, _sent=None, _ctx=None):
            return _M_wizrobo_npu.NpuIce._op_GetRuntimeStatus.begin(self, ((), _response, _ex, _sent, _ctx))

        def end_GetRuntimeStatus(self, _r):
            return _M_wizrobo_npu.NpuIce._op_GetRuntimeStatus.end(self, _r)

        def checkedCast(proxy, facetOrCtx=None, _ctx=None):
            return _M_wizrobo_npu.NpuIcePrx.ice_checkedCast(proxy, '::wizrobo_npu::NpuIce', facetOrCtx, _ctx)
        checkedCast = staticmethod(checkedCast)

        def uncheckedCast(proxy, facet=None):
            return _M_wizrobo_npu.NpuIcePrx.ice_uncheckedCast(proxy, facet)
        uncheckedCast = staticmethod(uncheckedCast)

    _M_wizrobo_npu._t_NpuIcePrx = IcePy.defineProxy('::wizrobo_npu::NpuIce', NpuIcePrx)

    _M_wizrobo_npu._t_NpuIce = IcePy.defineClass('::wizrobo_npu::NpuIce', NpuIce, -1, (), True, False, None, (), ())
    NpuIce._ice_type = _M_wizrobo_npu._t_NpuIce

    NpuIce._op_GetServerVersion = IcePy.Operation('GetServerVersion', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), IcePy._t_string, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_Connect = IcePy.Operation('Connect', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetServerState = IcePy.Operation('GetServerState', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ServerState, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetSystemDiagInfo = IcePy.Operation('GetSystemDiagInfo', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_SystemDiagInfo, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetActionState = IcePy.Operation('GetActionState', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ActionState, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetNpuState = IcePy.Operation('GetNpuState', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_NpuState, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_Shutdown = IcePy.Operation('Shutdown', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_Reboot = IcePy.Operation('Reboot', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetConfigIdList = IcePy.Operation('GetConfigIdList', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_StringArray, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SelectConfig = IcePy.Operation('SelectConfig', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_DeleteConfig = IcePy.Operation('DeleteConfig', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_AddConfig = IcePy.Operation('AddConfig', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCoreParam = IcePy.Operation('GetCoreParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_CoreParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetCoreParam = IcePy.Operation('SetCoreParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_CoreParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetMotorParam = IcePy.Operation('GetMotorParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_MotorParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetMotorParam = IcePy.Operation('SetMotorParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_MotorParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetPidParam = IcePy.Operation('GetPidParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_PidParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetPidParam = IcePy.Operation('SetPidParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_PidParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetChassisParam = IcePy.Operation('GetChassisParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ChassisParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetChassisParam = IcePy.Operation('SetChassisParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_ChassisParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetFootprintParam = IcePy.Operation('GetFootprintParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_FootprintParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetFootprintParam = IcePy.Operation('SetFootprintParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_FootprintParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetBaseParam = IcePy.Operation('GetBaseParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_BaseParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetBaseParam = IcePy.Operation('SetBaseParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_BaseParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetSensorParam = IcePy.Operation('GetSensorParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_SensorParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetSensorParam = IcePy.Operation('SetSensorParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_SensorParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetTeleopParam = IcePy.Operation('GetTeleopParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_TeleopParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetTeleopParam = IcePy.Operation('SetTeleopParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_TeleopParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetNaviParam = IcePy.Operation('GetNaviParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_NaviParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetNaviParam = IcePy.Operation('SetNaviParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_NaviParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetSlamParam = IcePy.Operation('GetSlamParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_SlamParam, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetSlamParam = IcePy.Operation('SetSlamParam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_SlamParam, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_FeedMotorEnc = IcePy.Operation('FeedMotorEnc', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_MotorEnc, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_FeedActMotorSpd = IcePy.Operation('FeedActMotorSpd', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_MotorSpd, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetMotorEnc = IcePy.Operation('GetMotorEnc', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_MotorEnc, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_ClearMotorEnc = IcePy.Operation('ClearMotorEnc', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCmdMotorSpd = IcePy.Operation('GetCmdMotorSpd', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_MotorSpd, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetActMotorSpd = IcePy.Operation('GetActMotorSpd', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_MotorSpd, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetLidarScan = IcePy.Operation('GetLidarScan', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_LidarScan, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetImgLidarScan = IcePy.Operation('GetImgLidarScan', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgLidarScan, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetImuData = IcePy.Operation('GetImuData', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImuData, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetSonarScan = IcePy.Operation('GetSonarScan', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_SonarScan, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetImgSonarScan = IcePy.Operation('GetImgSonarScan', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgSonarScan, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetInfrdScan = IcePy.Operation('GetInfrdScan', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_InfrdScan, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetImgInfrdScan = IcePy.Operation('GetImgInfrdScan', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgInfrdScan, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetBumperArray = IcePy.Operation('GetBumperArray', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_BumperArray, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetBatteryStatus = IcePy.Operation('GetBatteryStatus', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_BatteryStatus, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetManualCmd = IcePy.Operation('SetManualCmd', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_ManualCmdType, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetManualVel = IcePy.Operation('SetManualVel', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_float, False, 0), ((), IcePy._t_float, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetMapInfos = IcePy.Operation('GetMapInfos', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_MapInfoList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetMapInfos = IcePy.Operation('SetMapInfos', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_MapInfoList, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SelectMap = IcePy.Operation('SelectMap', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetStations = IcePy.Operation('GetStations', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_StationList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetStations = IcePy.Operation('SetStations', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), _M_wizrobo_npu._t_StationList, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetImgStations = IcePy.Operation('GetImgStations', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_ImgStationList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetImgStations = IcePy.Operation('SetImgStations', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), _M_wizrobo_npu._t_ImgStationList, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetTaskList = IcePy.Operation('GetTaskList', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_TaskList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_ExecuteTask = IcePy.Operation('ExecuteTask', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_TaskList, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetTasks = IcePy.Operation('SetTasks', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), _M_wizrobo_npu._t_TaskList, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetPaths = IcePy.Operation('GetPaths', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_PathList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetPaths = IcePy.Operation('SetPaths', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), _M_wizrobo_npu._t_PathList, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetImgPaths = IcePy.Operation('GetImgPaths', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_ImgPathList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetImgPaths = IcePy.Operation('SetImgPaths', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), _M_wizrobo_npu._t_ImgPathList, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetVirtualWalls = IcePy.Operation('GetVirtualWalls', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_VirtualWallList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetVirtualWalls = IcePy.Operation('SetVirtualWalls', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), _M_wizrobo_npu._t_VirtualWallList, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetImgVirtualWalls = IcePy.Operation('GetImgVirtualWalls', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_ImgVirtualWallList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetImgVirtualWalls = IcePy.Operation('SetImgVirtualWalls', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), _M_wizrobo_npu._t_ImgVirtualWallList, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCmdVel = IcePy.Operation('GetCmdVel', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Vel3D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetActVel = IcePy.Operation('GetActVel', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Vel3D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCurrentVel = IcePy.Operation('GetCurrentVel', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Vel3D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetAcc = IcePy.Operation('GetAcc', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Acc3D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCurrentAcc = IcePy.Operation('GetCurrentAcc', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Acc3D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCmdPose = IcePy.Operation('GetCmdPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Pose3D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCmdImgPose = IcePy.Operation('GetCmdImgPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgPose, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetActPose = IcePy.Operation('GetActPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Pose3D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetActImgPose = IcePy.Operation('GetActImgPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgPose, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCurrentPose = IcePy.Operation('GetCurrentPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Pose3D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCurrentImgPose = IcePy.Operation('GetCurrentImgPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgPose, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCmdPath = IcePy.Operation('GetCmdPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Path, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCmdImgPath = IcePy.Operation('GetCmdImgPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgPath, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetActPath = IcePy.Operation('GetActPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Path, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetActImgPath = IcePy.Operation('GetActImgPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgPath, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCurrentPath = IcePy.Operation('GetCurrentPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Path, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCurrentImgPath = IcePy.Operation('GetCurrentImgPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgPath, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetMap = IcePy.Operation('GetMap', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Map2D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetImgMap = IcePy.Operation('GetImgMap', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgMap, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCurrentMap = IcePy.Operation('GetCurrentMap', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Map2D, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetCurrentImgMap = IcePy.Operation('GetCurrentImgMap', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgMap, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetFootprintVertices = IcePy.Operation('GetFootprintVertices', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_Point3DList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetFootprintImgVertices = IcePy.Operation('GetFootprintImgVertices', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_ImgPointList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_StartTelop = IcePy.Operation('StartTelop', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_StopTelop = IcePy.Operation('StopTelop', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetInitPose = IcePy.Operation('SetInitPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_Pose3D, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetInitImgPose = IcePy.Operation('SetInitImgPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_ImgPose, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetInitPoseArea = IcePy.Operation('SetInitPoseArea', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_InitPoseArea, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SetInitImgPoseArea = IcePy.Operation('SetInitImgPoseArea', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_InitImgPoseArea, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetNaviMode = IcePy.Operation('GetNaviMode', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_NaviMode, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_StartNavi = IcePy.Operation('StartNavi', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_NaviMode, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_StopNavi = IcePy.Operation('StopNavi', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_PauseTask = IcePy.Operation('PauseTask', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_ContinueTask = IcePy.Operation('ContinueTask', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_CancelTask = IcePy.Operation('CancelTask', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetTaskProgress = IcePy.Operation('GetTaskProgress', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), IcePy._t_float, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetNaviState = IcePy.Operation('GetNaviState', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_NaviState, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GotoPose = IcePy.Operation('GotoPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_Pose3D, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GotoImgPose = IcePy.Operation('GotoImgPose', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_ImgPose, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GotoGoal = IcePy.Operation('GotoGoal', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_Pose3D, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GotoImgGoal = IcePy.Operation('GotoImgGoal', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_ImgPose, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GotoStation = IcePy.Operation('GotoStation', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), IcePy._t_string, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_FollowTempPath = IcePy.Operation('FollowTempPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_Pose3DList, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_FollowTempImgPath = IcePy.Operation('FollowTempImgPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_ImgPoseList, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_FollowPath = IcePy.Operation('FollowPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), IcePy._t_string, False, 0)), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_PlanCoveragePath = IcePy.Operation('PlanCoveragePath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_Point3DList, False, 0),), (), ((), _M_wizrobo_npu._t_Pose3DList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_PlanCoverageImgPath = IcePy.Operation('PlanCoverageImgPath', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_ImgPointList, False, 0),), (), ((), _M_wizrobo_npu._t_ImgPoseList, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetSlamMode = IcePy.Operation('GetSlamMode', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_SlamMode, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_StartSlam = IcePy.Operation('StartSlam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_SlamMode, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_StopSlam = IcePy.Operation('StopSlam', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SaveMapImg = IcePy.Operation('SaveMapImg', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_ExportConfigFile = IcePy.Operation('ExportConfigFile', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_ZipFile, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_ImportConfigFile = IcePy.Operation('ImportConfigFile', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_ZipFile, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_ExportMapFile = IcePy.Operation('ExportMapFile', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_ZipFile, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_ImportMapFile = IcePy.Operation('ImportMapFile', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_ZipFile, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetExportFileInfo = IcePy.Operation('GetExportFileInfo', Ice.OperationMode.Normal, Ice.OperationMode.Normal, True, None, (), (((), IcePy._t_string, False, 0),), (), ((), _M_wizrobo_npu._t_FileInfo, False, 0), ())
    NpuIce._op_GetExportFiledata = IcePy.Operation('GetExportFiledata', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), IcePy._t_string, False, 0), ((), IcePy._t_int, False, 0)), (), ((), _M_wizrobo_npu._t_FileData, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_SendImportFileData = IcePy.Operation('SendImportFileData', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (((), _M_wizrobo_npu._t_FileData, False, 0),), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_CheckSensorStatus = IcePy.Operation('CheckSensorStatus', Ice.OperationMode.Normal, Ice.OperationMode.Normal, True, None, (), (((), _M_wizrobo_npu._t_CheckMode, False, 0),), (), ((), _M_wizrobo_npu._t_SensorStatus, False, 0), ())
    NpuIce._op_GetSensorStatus = IcePy.Operation('GetSensorStatus', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_SensorStatus, False, 0), (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_CheckAbnormalInfo = IcePy.Operation('CheckAbnormalInfo', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), None, (_M_wizrobo_npu._t_NpuException,))
    NpuIce._op_GetRuntimeStatus = IcePy.Operation('GetRuntimeStatus', Ice.OperationMode.Normal, Ice.OperationMode.Normal, False, None, (), (), (), ((), _M_wizrobo_npu._t_RuntimeStatusList, False, 0), (_M_wizrobo_npu._t_NpuException,))

    _M_wizrobo_npu.NpuIce = NpuIce
    del NpuIce

    _M_wizrobo_npu.NpuIcePrx = NpuIcePrx
    del NpuIcePrx

# End of module wizrobo_npu
