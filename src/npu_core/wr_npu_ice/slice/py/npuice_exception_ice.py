# **********************************************************************
#
# Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
#
# This copy of Ice is licensed to you under the terms described in the
# ICE_LICENSE file included in this distribution.
#
# **********************************************************************
#
# Ice version 3.5.1
#
# <auto-generated>
#
# Generated from file `npuice_exception.ice'
#
# Warning: do not edit this file.
#
# </auto-generated>
#

import Ice, IcePy

# Start of module wizrobo_npu
_M_wizrobo_npu = Ice.openModule('wizrobo_npu')
__name__ = 'wizrobo_npu'

if 'ErrorType' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.ErrorType = Ice.createTempClass()
    class ErrorType(Ice.EnumBase):

        def __init__(self, _n, _v):
            Ice.EnumBase.__init__(self, _n, _v)

        def valueOf(self, _n):
            if _n in self._enumerators:
                return self._enumerators[_n]
            return None
        valueOf = classmethod(valueOf)

    ErrorType.ERR_UNMATCHED_VERSION = ErrorType("ERR_UNMATCHED_VERSION", 0)
    ErrorType.ERR_NO_KEY = ErrorType("ERR_NO_KEY", 1)
    ErrorType.ERR_NOT_CONNECTED = ErrorType("ERR_NOT_CONNECTED", 2)
    ErrorType.ERR_BASE_UNINITED = ErrorType("ERR_BASE_UNINITED", 3)
    ErrorType.ERR_ILLEGAL_PARA = ErrorType("ERR_ILLEGAL_PARA", 4)
    ErrorType.ERR_UNKNOWN = ErrorType("ERR_UNKNOWN", 5)
    ErrorType._enumerators = { 0:ErrorType.ERR_UNMATCHED_VERSION, 1:ErrorType.ERR_NO_KEY, 2:ErrorType.ERR_NOT_CONNECTED, 3:ErrorType.ERR_BASE_UNINITED, 4:ErrorType.ERR_ILLEGAL_PARA, 5:ErrorType.ERR_UNKNOWN }

    _M_wizrobo_npu._t_ErrorType = IcePy.defineEnum('::wizrobo_npu::ErrorType', ErrorType, (), ErrorType._enumerators)

    _M_wizrobo_npu.ErrorType = ErrorType
    del ErrorType

if 'NpuException' not in _M_wizrobo_npu.__dict__:
    _M_wizrobo_npu.NpuException = Ice.createTempClass()
    class NpuException(Ice.UserException):
        def __init__(self, date='', time='', type=_M_wizrobo_npu.ErrorType.ERR_UNMATCHED_VERSION, msg=''):
            self.date = date
            self.time = time
            self.type = type
            self.msg = msg

        def __str__(self):
            return IcePy.stringifyException(self)

        __repr__ = __str__

        _ice_name = 'wizrobo_npu::NpuException'

    _M_wizrobo_npu._t_NpuException = IcePy.defineException('::wizrobo_npu::NpuException', NpuException, (), False, None, (
        ('date', (), IcePy._t_string, False, 0),
        ('time', (), IcePy._t_string, False, 0),
        ('type', (), _M_wizrobo_npu._t_ErrorType, False, 0),
        ('msg', (), IcePy._t_string, False, 0)
    ))
    NpuException._ice_type = _M_wizrobo_npu._t_NpuException

    _M_wizrobo_npu.NpuException = NpuException
    del NpuException

# End of module wizrobo_npu
