#! /bin/bash
## version 1.0
## <NAME_EMAIL> @2017-04-05
## 1. add auto-compilation of ice files
## --------------------------

echo "$(tput setaf 2)>> Generate npuice*.h and npuice*.cpp...$(tput sgr 0)"
if [ "$1"x == "--path"x ]; then
    cd "$2"
fi

cd ..
echo "$(tput setaf 2)>> slice2cpp...$(tput sgr 0)"
slice2cpp --underscore -I. *.ice || exit 0
echo "$(tput setaf 2)>> Done$(tput sgr 0)"

mv *.h ../include/npuice
mv *.cpp ../src/npuice

cd utils
echo "$(tput setaf 2)>> Generate npuicei...$(tput sgr 0)"
./generate_npuicei_class.py  || exit 0
echo "$(tput setaf 2)>> Done$(tput sgr 0)"

mv npu_icei.h ../../../wr_npu_server/include
mv npu_icei.cpp ../../../wr_npu_server/src
echo "$(tput setaf 2)>> All Done$(tput sgr 0)"
exit 0
