#! /usr/bin/python

import re

icei_cpp_temp = \
"""/****************************************
*Maintainer status: developed
*Maintainer: Tom (<EMAIL>)
*Date: 2016-10-11
*Copyright(c) 2016-2017 LinkMiao CO., Ltd.
*All rights reserved
****************************************/

#include <iostream>
#include <time.h>

#include "npu_icei.h"

namespace wizrobo_npu {

typedef struct _NpuClient
{
    std::string ip;
    int         port;
    time_t      active_time;
}NpuClient;

const int CLIENT_ACT = 1;
const int TIME_OUT = 10;

static NpuClient npu_client = {"", -1, 0};
#define ICEI_COUT 0

static bool CheckNpuClient(const ::Ice::Current& cur_client)
{
    const Ice::ConnectionInfo *p_conn_info = cur_client.con->getInfo()._ptr;
    const Ice::IPConnectionInfo *p_ip_conn_info = reinterpret_cast<const Ice::IPConnectionInfo *>(p_conn_info);

    const std::string &ip = p_ip_conn_info->remoteAddress;
    int port = p_ip_conn_info->remotePort;
    time_t now = time(0);
    int interval = now - npu_client.active_time;

#if ICEI_COUT
    std::cout << "CheckNpuClient: client("
              << ip << ":" << port << "/"
              << npu_client.ip << ":"<<  npu_client.port << ") "
              << interval ;
#endif
    if (ip == npu_client.ip && port == npu_client.port)
    {
        npu_client.active_time = now;
#if ICEI_COUT
        std::cout << " accept" << std::endl;
#endif
        return true;
    }
#if ICEI_COUT
    std::cout << " reject" << std::endl;
#endif
    cur_client.con->close(false);
    return false;
}

static bool AddNpuClient(const ::Ice::Current& cur_client)
{
    const Ice::ConnectionInfo *p_conn_info = cur_client.con->getInfo()._ptr;
    const Ice::IPConnectionInfo *p_ip_conn_info = reinterpret_cast<const Ice::IPConnectionInfo *>(p_conn_info);
    const std::string &ip = p_ip_conn_info->remoteAddress;
    int port = p_ip_conn_info->remotePort;
    time_t now = time(0);
    int interval = now - npu_client.active_time;

#if ICEI_COUT
    std::cout << "AddNpuClient: client("
              << ip << ":" << port << "/"
              << npu_client.ip << ":"<<  npu_client.port << ") "
              << interval;
#endif
    if (npu_client.ip == "" || ip == npu_client.ip
            || interval > TIME_OUT)
    {
#if ICEI_COUT
        std::cout << " accept" << std::endl;
#endif
        npu_client.ip = ip;
        npu_client.port = port;
        npu_client.active_time = now;
        return true;
    }
#if ICEI_COUT
    std::cout << " reject" << std::endl;
#endif
    cur_client.con->close(false);
    return false;
}


NpuIceI::NpuIceI(int argc, char **argv, NpuServer* ptr)
{
    //p_npu_server = new NpuServer(argc, argv);
    p_npu_server = ptr;
}

NpuIceI::~NpuIceI()
{
}

//// connection
string NpuIceI::GetServerVersion(const ::Ice::Current&)
{
    return p_npu_server->GetServerVersion();
}

void NpuIceI::Connect(const string& version, const ::Ice::Current& cur_client)
{
    if (AddNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::Connect() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call Connect() failed! There is another client connected!");
        return;
    }
    return p_npu_server->Connect(version);
}

////exce_info
void NpuIceI::CheckAbnormalInfo(const ::Ice::Current& )
{
    return p_npu_server->CheckAbnormalInfo();
}
%(cpp_content)s

}
"""

icei_h_temp = \
"""/****************************************
*Maintainer status: developed
*Maintainer: Tom (<EMAIL>)
*Title: WR C++ API
*Version: 0.9.9
*Copyright(c) 2016-2017 LinkMiao CO., Ltd.
*All rights reserved
****************************************/
#ifndef WIZROBO_NPU_NPU_ICEI_H
#define WIZROBO_NPU_NPU_ICEI_H
#include "ros/ros.h"
#include "std_msgs/String.h"
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <geometry_msgs/Twist.h>
#include <nav_msgs/OccupancyGrid.h>
#include <tf/transform_listener.h>

#include <IceUtil/IceUtil.h>
#include <Ice/Ice.h>

#include "npuice_api.h"
#include "npu_server.h"

namespace wizrobo_npu {
using namespace std;

class NpuIceI : public NpuIce
{
private:
    NpuServer *p_npu_server;

public:
    NpuIceI(int argc, char** agrv, NpuServer* ptr);
    virtual ~NpuIceI();

%(h_content)s

};
}
#endif// WIZROBO_NPU_NPU_ICEI_H
"""

function_def_temp = \
"""%(return_type)s NpuIceI::%(function_name)s(%(func_argument_line)s)
{
    %(return_define_line)s
    if (CheckNpuClient(cur_client) == false)
    {
        ROS_ERROR("Call NpuIceI::%(function_name)s() failed!");
        throw NpuException("", "", ERR_UNKNOWN, "Call NpuIceI::%(function_name)s() failed! There is another client connected");
        %(return_value_line)s
    }
    return p_npu_server->%(function_name)s(%(variable_line)s);
}
"""

function_ref_temp = """    virtual %(return_type)s %(function_name)s(%(h_argument_line)s);"""

function_ignore_list = ["GetServerVersion", "Connect", "CheckAbnormalInfo"]

def generate_function_definition(line):
    pattern = r"^ +(\[\"amd\"\])? +([^ ]+) ([^ ]+)\((.*)\)"
    rtn = re.findall(pattern, line)
    amd_flag = rtn[0][0]
    return_type = rtn[0][1]
    function_name = rtn[0][2]
    variable_list = []
    func_argument_list = []
    h_argument_list = []
    for e in rtn[0][3].split(","):
        rtn = re.findall(r"([^ ]+) ([^ ]+)", e)
        if rtn == []:
            continue
        arg_type, arg_name = rtn[0]
        type_list = ("bool", "int", "float",
            ##api
            "ServerState", "ManualCmdType","NaviMode",
            "NaviState", "CcpMode", "SlamMode",
            "NpuState", "TaskState","CheckMode",
            ##exception
            "ErrorType",
            ##map
            "StationType", "CellType",
            ##param
            "NpuMode", "BrakeType", "ValidOutputLevel",
            "LidarType", "InterfaceType", "BumperLocation",
            "ModelType", "SteerSensorLocation", "ShapeType",
            "ImuType", "GpsType", "CameraType")
        if arg_type not in type_list:
            func_argument_list.append("const %s& %s" %(arg_type, arg_name))
            h_argument_list.append("const %s& %s" %(arg_type, arg_name))
        else:            
            func_argument_list.append("const %s %s" %(arg_type, arg_name))
            h_argument_list.append("const %s %s" %(arg_type, arg_name))
        variable_list.append(rtn[0][1])
    if amd_flag != "":
        return_type = "void"
        variable_list = ["ptr"] + variable_list
        func_argument_list = ["const ::wizrobo_npu::AMD_NpuIce_%sPtr& ptr" % function_name] + func_argument_list
        h_argument_list = ["const ::wizrobo_npu::AMD_NpuIce_%sPtr& ptr" % function_name] + h_argument_list
        function_name = function_name + "_async"
    func_argument_list.append("const ::Ice::Current& cur_client")
    h_argument_list.append("const ::Ice::Current& = ::Ice::Current()")
    func_argument_line = ", ".join(func_argument_list)
    h_argument_line = ", ".join(h_argument_list)
    variable_line = ", ".join(variable_list)   
    return_value = "rtn;" if return_type != "void" else ";"
    if return_type == "void":
        return_define_line = ""
        return_value_line = "return;"
    else:
        return_define_line = return_type + " rtn;"
        return_value_line = "return rtn;"


    _map = { "amd_flag" : amd_flag, 
            "return_type" : return_type, "return_define_line" : return_define_line,
            "return_value_line" : return_value_line,
            "function_name" : function_name, "func_argument_line" : func_argument_line, 
            "h_argument_line" : h_argument_line, "variable_line" : variable_line}
    return _map

def main():
    ice_file = open("../npuice_api.ice", "r")
    ice_content = ice_file.read()
    ice_file.close()

    pattern = r"interface NpuIce[ \n]*{ *\n(.*)\n *};// interface NpuIce"
    ice_content = re.findall(pattern, ice_content, re.S)[0]
    
    lines = ice_content.split("\n")
    cpp_content = ""
    h_content = ""
    for line in lines:
        if re.match(r"^ *$", line) != None :
            pass
        elif re.match(r"^ *///*", line) != None or re.match(r"^ *#", line) != None:
            cpp_content = cpp_content + "\n" + line.lstrip() + "\n"
            h_content = h_content + "\n" + "    " + line.lstrip() + "\n"
        else:
            _d = generate_function_definition(line)
            h_content = h_content + function_ref_temp%_d + "\n" 
            if _d["function_name"] in function_ignore_list:
                continue
            #print _d
            cpp_content = cpp_content + function_def_temp%_d + "\n"
    fd = open("./npu_icei.cpp", "w")
    fd.write( icei_cpp_temp % { "cpp_content" : cpp_content } )
    fd.close()
    fd = open("./npu_icei.h", "w")
    fd.write( icei_h_temp % { "h_content" : h_content } )
    fd.close()

if __name__ == '__main__':
    main()
