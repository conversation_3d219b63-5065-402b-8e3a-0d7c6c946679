#! /usr/bin/python

import re

function_def_none_void_temp = \
"""%(return_type)s NpuServer::%(function_name)s(%(argument_line)s)
{
    ROS_INFO("%(function_name)s()");
    %(return_type)s return_value;
    _TEST_CONNECTTED(return_value);
    return return_value;
}
"""

function_def_void_temp = \
"""void NpuServer::%(function_name)s(%(argument_line)s)
{
    ROS_INFO("%(function_name)s()");
    _TEST_CONNECTTED(void());
    return;
}
"""

function_ref_temp = """    virtual %(return_type)s %(function_name)s(%(argument_line)s);"""

def generate_function_definition(line):
    pattern = r" *([^ ]+) ([^ ]+)\((.*)\)"
    rtn = re.findall(pattern, line)
    return_type = rtn[0][0]
    function_name = rtn[0][1]
    argument_list = []
    variable_list = []
    for e in rtn[0][2].split(","):
        rtn = re.findall(r"([^ ]+) ([^ ]+)", e)
        if rtn == []:
            continue
        argument_list.append("%s %s" %( rtn[0][0], rtn[0][1]))
        variable_list.append(rtn[0][1])
    argument_line = ", ".join(argument_list)
    variable_line = ", ".join(variable_list)

    return { "return_type" : return_type, "function_name" : function_name,
        "argument_line" : argument_line, "variable_line" : variable_line}

def main():
    ice_file = open("./npuice_api.ice", "r")
    ice_content = ice_file.read()
    ice_file.close()

    pattern = r"interface NpuIce[ \n]*{ *\n(.*)\n *};// interface NpuIce"
    ice_content = re.findall(pattern, ice_content, re.S)[0]
    
    lines = ice_content.split("\n")
    cpp_content = ""
    h_content = ""
    for line in lines:
        if re.match(r"^ *$", line) != None :
            pass
        elif re.match(r"^ *///*", line) != None or re.match(r"^ *#", line) != None:
            cpp_content = cpp_content + "\n" + line.lstrip() + "\n"
        else:
            _d = generate_function_definition(line)
            #print _d
            if _d["return_type"] == "void":
                cpp_content = cpp_content + function_def_void_temp%_d + "\n"   
            else:
                cpp_content = cpp_content + function_def_none_void_temp%_d + "\n"   
            pass
    print cpp_content

if __name__ == '__main__':
    main()