// -------------------------------------
// Version: 1.0.1
// Create Date: 2017-04-11
// Maintainer: <EMAIL>,
//             <EMAIL>,
//             <EMAIL>,
//             <EMAIL>
// -------------------------------------
// Change log: 2017-05-19 #lhan
//      1. rm npuice_base.ice & npuice_motor.ice & npuice_sensor.ice
//      2. add npuice_data.ice && npuice_param.ice
// -------------------------------------
// Change log: 2017-04-07 #lhan
//      1. Add imagery geometry
//      2. Change definition files into "geometry, motor, sensor and base"
//      3. Remove all "2D" subfix, all default pose is 3D
//      4. Add exception mechanism
// -------------------------------------
// Change log: 2018-09-05 #lhan
//      1. Add geographic coordinates, path, area for GPS applications
#ifndef WIZROBO_NPUICE_API_ICE
#define WIZROBO_NPUICE_API_ICE

//// compilation setting
#define NPU_PRO 1

#include <npuice_exception.ice>
#include <npuice_geometry.ice>
#include <npuice_data.ice>
#include <npuice_param.ice>
#include <npuice_map.ice>

module wizrobo_npu {

    //// version
    const string NPU_API_VERSION = "0.9.14"; // lhan@2018-09-05
    const string NPU_ICE_VERSION = "3.5.0";

    //// enum
    // connection
    enum ServerState {UNKNOWN, CONNECTED, TIMEOUT, DISCONNECTED, MEM_EXCEED};
    //enum ConnectionState {UNKNOWN, CONNECTED, TIMEOUT, DISCONNECTED, MEM_EXCEED};
    // action state
    enum ActionState {IDLE_ACTION, SLAM_ACTION, NAVI_ACTION, TELEOP_ACTION, SWITCH_ACTION};
    enum NpuState {IDLE_STATE, SLAM_STATE, NAVI_STATE, TELEOP_STATE, SWITCH_STATE, SLAM_NAVI_STATE};
    // manual control
    enum ManualCmdType {MOVE_FWD,MOVE_BCK,TURN_LFT,TURN_RGT,MOVE_FWD_LFT,MOVE_FWD_RGT,STOP_MOVE};
    // navi
    enum NaviMode {P2P_NAVI, PF_NAVI, HYBRID_NAVI};// p2p, path_following, traj_tracking]
    enum NaviState {PENDING, ACTIVE, PREEMPTED, SUCCEEDED, ABORTED, REJECTED, PREEMPTING, RECALLING, RECALLED, LOST, IDLE};
    enum CcpMode {ZIGZAG_CCP, SPIRAL_CCP};
    // slam
    enum SlamMode {PF_SLAM, ICP_SLAM, WARE_SLAM, GH_SLAM};

    enum CheckMode {ALL , BCU , IMU , LIDAR};

    interface NpuIce
    {
        //// connection
        string GetServerVersion() throws NpuException;
        void Connect(string version) throws NpuException;
        ServerState GetServerState() throws NpuException;
        SystemDiagInfo GetSystemDiagInfo() throws NpuException;
        ActionState GetActionState() throws NpuException;
        NpuState GetNpuState() throws NpuException;

        //// power
        void Shutdown() throws NpuException;
        void Reboot() throws NpuException;

        //// config management
        StringArray GetConfigIdList() throws NpuException;
        void SelectConfig(string id) throws NpuException;//重启后生效
        void DeleteConfig(string id) throws NpuException;//TODO
        void AddConfig(string id) throws NpuException;//TODO
        // npu
        CoreParam GetCoreParam() throws NpuException;
        void SetCoreParam(CoreParam param) throws NpuException;
        // motor
        MotorParam GetMotorParam() throws NpuException;
        void SetMotorParam(MotorParam param) throws NpuException;
        // pid
        PidParam GetPidParam() throws NpuException;
        void SetPidParam(PidParam param) throws NpuException;
        // chassis
        ChassisParam GetChassisParam() throws NpuException;
        void SetChassisParam(ChassisParam param) throws NpuException;
        // footprint
        FootprintParam GetFootprintParam() throws NpuException;
        void SetFootprintParam(FootprintParam param) throws NpuException;
        // base
        BaseParam GetBaseParam() throws NpuException;
        void SetBaseParam(BaseParam param) throws NpuException;
        // sensor
        SensorParam GetSensorParam() throws NpuException;
        void SetSensorParam(SensorParam param) throws NpuException;
        // teleop
        TeleopParam GetTeleopParam() throws NpuException;
        void SetTeleopParam(TeleopParam param) throws NpuException;
        // navi
        NaviParam GetNaviParam() throws NpuException;
        void SetNaviParam(NaviParam param) throws NpuException;
        // slam
        SlamParam GetSlamParam() throws NpuException;
        void SetSlamParam(SlamParam param) throws NpuException;

        //// slave mode
        void FeedMotorEnc(MotorEnc enc) throws NpuException;
        void FeedActMotorSpd(MotorSpd spd) throws NpuException;

        //// motor data
        // enc
        MotorEnc GetMotorEnc() throws NpuException;
        void ClearMotorEnc() throws NpuException;
        // spd
        MotorSpd GetCmdMotorSpd() throws NpuException;
        MotorSpd GetActMotorSpd() throws NpuException;

        //// sensor data
        // lidar
        LidarScan GetLidarScan() throws NpuException;
        ImgLidarScan GetImgLidarScan() throws NpuException;
        // imu
        ImuData GetImuData() throws NpuException;
        // sonar
        SonarScan GetSonarScan() throws NpuException;
        ImgSonarScan GetImgSonarScan() throws NpuException;
        // infrd
        InfrdScan GetInfrdScan() throws NpuException;
        ImgInfrdScan GetImgInfrdScan() throws NpuException;
        // bumper
        BumperArray GetBumperArray() throws NpuException;
        // battery
        BatteryStatus GetBatteryStatus() throws NpuException;

        //// manul control
        void SetManualCmd(ManualCmdType cmd) throws NpuException;
        void SetManualVel(float lin_scale, float ang_scale) throws NpuException;//scale = [0,1]

        //// map management
        // map
        MapInfoList GetMapInfos() throws NpuException;
        void SetMapInfos(MapInfoList list) throws NpuException;
        void SelectMap(string id) throws NpuException;
        // station
        StationList GetStations(string map_id) throws NpuException;
        void SetStations(string map_id, StationList list) throws NpuException;
        ImgStationList GetImgStations(string map_id) throws NpuException;
        void SetImgStations(string map_id, ImgStationList list) throws NpuException;
        GeoStationList GetGeoStations(string map_id) throws NpuException;
        void SetGeoStations(string map_id, GeoStationList list) throws NpuException;
        // task
        TaskList GetTaskList(string map_id) throws NpuException;
        void ExecuteTask(TaskList list) throws NpuException;
        void SetTasks(string map_id, TaskList list) throws NpuException;
        // path
        PathList GetPaths(string map_id) throws NpuException;
        void SetPaths(string map_id, PathList list) throws NpuException;
        ImgPathList GetImgPaths(string map_id) throws NpuException;
        void SetImgPaths(string map_id, ImgPathList list) throws NpuException;
        GeoPathList GetGeoPaths(string map_id) throws NpuException;
        GeoPoseList GetGeoPath() throws NpuException;
        void SetGeoPaths(string map_id, GeoPathList list) throws NpuException;
        void SetGeoPath(GeoPath geo_path) throws NpuException;
        // virtual wall
        VirtualWallList GetVirtualWalls(string map_id) throws NpuException;
        void SetVirtualWalls(string map_id, VirtualWallList virtual_walls) throws NpuException;
        ImgVirtualWallList GetImgVirtualWalls(string map_id) throws NpuException;
        void SetImgVirtualWalls(string map_id, ImgVirtualWallList virtual_walls) throws NpuException;
        GeoVirtualWallList GetGeoVirtualWalls(string map_id) throws NpuException;
        void SetGeoVirtualWalls(string map_id, GeoVirtualWallList virtual_walls) throws NpuException;

        //// common runtime data
        // vel
        Vel3D GetCmdVel() throws NpuException;
        Vel3D GetActVel() throws NpuException;
        // TODEL: be replaced by GetActVel()
        Vel3D GetCurrentVel() throws NpuException;
        // acc
        Acc3D GetAcc() throws NpuException;
        // TODEL: be replaced by GetAcc
        Acc3D GetCurrentAcc() throws NpuException;
        // pose
        Pose3D GetCmdPose() throws NpuException;
        Pose3D GetActPose() throws NpuException;
        ImgPose GetCmdImgPose() throws NpuException;
        ImgPose GetActImgPose() throws NpuException;
        GeoPose GetCmdGeoPose() throws NpuException;
        GeoPose GetActGeoPose() throws NpuException;
        // TODEL: be replaced by GetActPose()
        Pose3D GetCurrentPose() throws NpuException;
        // TODEL: be replaced by GetActImgPose()
        ImgPose GetCurrentImgPose() throws NpuException;
        // path
        Path GetGlobalPath() throws NpuException;
        Path GetLocalPath() throws NpuException;
        Path GetActPath() throws NpuException;
        ImgPath GetGlobalImgPath() throws NpuException;
        ImgPath GetLocalImgPath() throws NpuException;
        ImgPath GetActImgPath() throws NpuException;
        GeoPath GetGlobalGeoPath() throws NpuException;
        GeoPath GetLocalGeoPath() throws NpuException;
        GeoPath GetActGeoPath() throws NpuException;
        // TODEL: replaced by GetGlobalPath()
        Path GetCurrentPath() throws NpuException;
        // TODEL: replaced by GetGlobalImgPath()
        ImgPath GetCurrentImgPath() throws NpuException;
        // map
        Map2D GetMap() throws NpuException;
        ImgMap GetImgMap() throws NpuException;
        GeoMap GetGeoMap() throws NpuException;
        // TODEL: be replaced by GetMap()
        Map2D GetCurrentMap() throws NpuException;
        // TODEL: be replaced by GetImgMap()
        ImgMap GetCurrentImgMap() throws NpuException;
        // footprint
        Point3DList GetFootprintVertices() throws NpuException;
        ImgPointList GetFootprintImgVertices() throws NpuException;
        GeoPointList GetFootprintGeoVertices() throws NpuException;

        //// telop
        void StartTelop() throws NpuException;
        void StopTelop() throws NpuException;
        //// navi
        /// navi.common
        // initial pose
        void SetInitPose(Pose3D pose) throws NpuException;
        void SetInitPoseArea(InitPoseArea pose) throws NpuException;
        void SetInitImgPose(ImgPose pose) throws NpuException;
        void SetInitImgPoseArea(InitImgPoseArea pose) throws NpuException;
        void SetInitGeoPose(GeoPose pose) throws NpuException;
        void SetInitGeoPoseArea(InitGeoPoseArea pose) throws NpuException;
        float GetMatchingScore() throws NpuException;
        // start & stop
        NaviMode GetNaviMode() throws NpuException;// NEW
        void StartNavi(NaviMode mode) throws NpuException;
        void StopNavi() throws NpuException;
        // task control
        void PauseTask() throws NpuException;
        void ContinueTask() throws NpuException;
        void CancelTask() throws NpuException;
        float GetTaskProgress() throws NpuException;// [0, 1]
        // status
        NaviState GetNaviState() throws NpuException;

        /// navi.p2p
        void GotoPose(Pose3D pose) throws NpuException;
        void GotoImgPose(ImgPose pose) throws NpuException;
        void GotoGeoPose(GeoPose pose) throws NpuException;
        //TODEL: replaced by GotoPose(Pose3D pose);
        void GotoGoal(Pose3D goal) throws NpuException;
        // TODEL: replaced by GotoImgPose(Pose3D pose);
        void GotoImgGoal(ImgPose goal) throws NpuException;
        void GotoStation(string map_id, string station_id) throws NpuException;
        /// navi.pf
        void FollowTempPath(Pose3DList poses) throws NpuException;
        void FollowTempImgPath(ImgPoseList poses) throws NpuException;
        void FollowTempGeoPath(GeoPoseList poses) throws NpuException;
        void FollowPath(string map_id, string path_id) throws NpuException;
        //// navi.ccp
        Pose3DList PlanCoveragePath(Point3DList vertices) throws NpuException;
        ImgPoseList PlanCoverageImgPath(ImgPointList vertices) throws NpuException;
        GeoPoseList PlanCoverageGeoPath(GeoPointList vertices) throws NpuException;

        //// slam
        SlamMode GetSlamMode() throws NpuException;// NEW
        void StartSlam(SlamMode mode) throws NpuException;
        void StopSlam(string map_id) throws NpuException;// empty string means give up

        /// save map
        void SaveMapImg(string map_id) throws NpuException;

        //// file
        ZipFile ExportConfigFile(string file_name) throws NpuException;
        void ImportConfigFile(ZipFile file, string file_name) throws NpuException;
        ZipFile ExportMapFile(string file_name) throws NpuException;
        void ImportMapFile(ZipFile file, string file_name) throws NpuException;
        
        ["amd"] FileInfo GetExportFileInfo(string fileName);
        FileData GetExportFiledata(string fileName,int chunk_index) throws NpuException;
        void SendImportFileData(FileData data) throws NpuException;

        ////sensorstatus
        ["amd"] SensorStatus CheckSensorStatus(CheckMode mode);
        SensorStatus GetSensorStatus() throws NpuException;

        ////exce_info
        void CheckAbnormalInfo() throws NpuException;

        ////task_system
        //void StopTaskSystem() throws NpuException;

        RuntimeStatusList GetRuntimeStatus() throws NpuException;

    };// interface NpuIce
};
#endif// WIZROBO_NPUICE_API_ICE
