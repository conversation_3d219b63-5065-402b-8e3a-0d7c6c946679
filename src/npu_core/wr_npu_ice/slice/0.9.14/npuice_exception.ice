// -------------------------------------
// Version: 1.0.0
// Date: 2017-04-04
// Maintainer: <EMAIL>,
//             <EMAIL>,
//             <EMAIL>,
//             <EMAIL>
// -------------------------------------
// Change log: 2017-04-07 #lhan
//      1. Add imagery geometry
//      2. Change definition files into "geometry, motor, sensor and base"
//      3. Remove all "2D" subfix, all default pose is 3D
//      4. Before the base is inited, only enc can be received and only pwd can be set
#ifndef WIZROBO_NPUICE_EXCEPTION_ICE
#define WIZROBO_NPUICE_EXCEPTION_ICE

module wizrobo_npu {

    //// exception
    enum ErrorType
    {
        ERR_UNMATCHED_VERSION,
        ERR_NO_KEY,
        ERR_NOT_CONNECTED,
        ERR_BASE_UNINITED,
        ERR_ILLEGAL_PARA,
        ERR_UNKNOWN
    };
    exception NpuException
    {
          string date;
          string time;
          ErrorType type;
          string msg;
    };

};// module wizrobo

#endif// WIZROBO_NPUICE_EXCEPTION_ICE
