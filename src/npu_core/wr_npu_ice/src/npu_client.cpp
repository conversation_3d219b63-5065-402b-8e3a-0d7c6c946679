#include "npu_client.h"

namespace wizrobo_npu {
NpuConnector::NpuConnector()
{
}

bool NpuConnector::GetServerIp(std::string& ip_str)
{
    setvbuf(stdout, NULL, _IONBF, 0);
    fflush(stdout);

    struct sockaddr_in addr;
    bzero(&addr, sizeof(struct sockaddr_in));
    addr.sin_family =AF_INET;
    addr.sin_addr.s_addr = htonl(INADDR_ANY);
    addr.sin_port = htons(5188);

    int sock = -1;
    if ((sock = socket(AF_INET, SOCK_DGRAM, 0)) == -1)
    {
        std::cout<<"socket error\n";
        return false;
    }

    if(bind(sock,(struct sockaddr *)&(addr), sizeof(struct sockaddr_in)) == -1)
    {
        std::cout<<"bind error...\n";
        return false;
    }

    int len = sizeof(sockaddr_in);
    char smsg[200] = {0};

    while(1)
    {
        int ret=recvfrom(sock, smsg, 100, 0, (struct sockaddr*)&addr,(socklen_t*)&len);
        std::cout << smsg << std::endl;

        if(ret<=0)
        {
            std::cout<< "read error...." << sock << std::endl;
            return false;
        }
        break;
    }

    std::string str(smsg);
    std::cout << "server msg = " << str << std::endl;

    int start_idx = str.find("[");
    int end_idx = str.find("]");
    ip_str = str.substr(start_idx + 1, end_idx - start_idx - 1);
    std::cout << "-- ip = " << ip_str << std::endl;

    start_idx = str.find("<");
    end_idx = str.find(">");
    std::string api_ver_str = str.substr(start_idx + 1, end_idx - start_idx - 1);
    std::cout << " -- api version = " << api_ver_str << std::endl;

    start_idx = str.find("{");
    end_idx = str.find("}");
    std::string sys_ver_str = str.substr(start_idx + 1, end_idx - start_idx - 1);
    std::cout << " -- system version = " << sys_ver_str << std::endl;
    return true;
}

bool NpuConnector::Connect(const std::string &ip_str, NpuClient& npu_client)
{
    /***********ICE***********/
    std::stringstream ss;
    const char *c;
    try
    {
        Ice::PropertiesPtr props = Ice::createProperties();
        props->setProperty("Ice.MessageSizeMax",  "51200");
        Ice::InitializationData initicedata;
        initicedata.properties = props;
        ic = Ice::initialize(initicedata);
        ss << "Npu:tcp -h ";
        ss << ip_str;
        ss << " -p 10190";
        ss << " -t 5000";
        std::cout << "server ip = " << ip_str << std::endl;
        c = ss.str().c_str();
        base = ic -> stringToProxy(c);
        npu_client = ::wizrobo_npu::NpuIcePrx::checkedCast(base);
        std::cout << "Check case success." << std::endl;
        if(!npu_client)
        {
            throw "/Invalid proxy";
        }
    }
    catch(const Ice::Exception&ex)
    {
        std::cout << "1 error" << std::endl;
        std::cerr << ex << std::endl;
        return false;
    }
    catch (const char* msg)
    {
        std::cout << "2 error" << std::endl;
        std::cerr << msg << std::endl;
        return false;
    }

   // Init();
    std::cout << "IceInit is ok" << std::endl;
    std::string v = npu_client->GetServerVersion();
    npu_client->Connect(v);
    return true;
}

bool NpuConnector::Disconnect(NpuClient& npu_client)
{
    ic -> destroy();
    npu_client = NULL;
    return true;
}
}// namespace
