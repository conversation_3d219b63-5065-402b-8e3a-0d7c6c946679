// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_scrubber_interaction.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <npuice_scrubber_interaction.h>
#include <Ice/LocalException.h>
#include <Ice/ObjectFactory.h>
#include <Ice/BasicStream.h>
#include <Ice/Object.h>
#include <IceUtil/Iterator.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace
{

const ::std::string __wizrobo_scrubber__ScrubberIce__GetClearControlData_name = "GetClearControlData";

const ::std::string __wizrobo_scrubber__ScrubberIce__SetClearControlData_name = "SetClearControlData";

const ::std::string __wizrobo_scrubber__ScrubberIce__GetSensorState_name = "GetSensorState";

}

namespace Ice
{
}
::IceProxy::Ice::Object* ::IceProxy::wizrobo_scrubber::upCast(::IceProxy::wizrobo_scrubber::ScrubberIce* p) { return p; }

void
::IceProxy::wizrobo_scrubber::__read(::IceInternal::BasicStream* __is, ::IceInternal::ProxyHandle< ::IceProxy::wizrobo_scrubber::ScrubberIce>& v)
{
    ::Ice::ObjectPrx proxy;
    __is->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ::IceProxy::wizrobo_scrubber::ScrubberIce;
        v->__copyFrom(proxy);
    }
}

::wizrobo_scrubber::ScrClearControlUnit
IceProxy::wizrobo_scrubber::ScrubberIce::GetClearControlData(const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_scrubber__ScrubberIce__GetClearControlData_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_scrubber__ScrubberIce__GetClearControlData_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_scrubber::ScrubberIce*>(__delBase.get());
            return __del->GetClearControlData(__ctx, __observer);
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_scrubber::ScrubberIce::begin_GetClearControlData(const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_scrubber__ScrubberIce__GetClearControlData_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_scrubber__ScrubberIce__GetClearControlData_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_scrubber__ScrubberIce__GetClearControlData_name, ::Ice::Normal, __ctx);
        __result->__writeEmptyParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

::wizrobo_scrubber::ScrClearControlUnit
IceProxy::wizrobo_scrubber::ScrubberIce::end_GetClearControlData(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_scrubber__ScrubberIce__GetClearControlData_name);
    ::wizrobo_scrubber::ScrClearControlUnit __ret;
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        ::IceInternal::BasicStream* __is = __result->__startReadParams();
        __is->read(__ret);
        __result->__endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

void
IceProxy::wizrobo_scrubber::ScrubberIce::SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_scrubber__ScrubberIce__SetClearControlData_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_scrubber__ScrubberIce__SetClearControlData_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_scrubber::ScrubberIce*>(__delBase.get());
            __del->SetClearControlData(clearcontrol, __ctx, __observer);
            return;
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_scrubber::ScrubberIce::begin_SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_scrubber__ScrubberIce__SetClearControlData_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_scrubber__ScrubberIce__SetClearControlData_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_scrubber__ScrubberIce__SetClearControlData_name, ::Ice::Normal, __ctx);
        ::IceInternal::BasicStream* __os = __result->__startWriteParams(::Ice::DefaultFormat);
        __os->write(clearcontrol);
        __result->__endWriteParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

void
IceProxy::wizrobo_scrubber::ScrubberIce::end_SetClearControlData(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_scrubber__ScrubberIce__SetClearControlData_name);
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        __result->__readEmptyParams();
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

::wizrobo_scrubber::ScrSensorStatusUnit
IceProxy::wizrobo_scrubber::ScrubberIce::GetSensorState(const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_scrubber__ScrubberIce__GetSensorState_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_scrubber__ScrubberIce__GetSensorState_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_scrubber::ScrubberIce*>(__delBase.get());
            return __del->GetSensorState(__ctx, __observer);
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_scrubber::ScrubberIce::begin_GetSensorState(const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_scrubber__ScrubberIce__GetSensorState_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_scrubber__ScrubberIce__GetSensorState_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_scrubber__ScrubberIce__GetSensorState_name, ::Ice::Normal, __ctx);
        __result->__writeEmptyParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

::wizrobo_scrubber::ScrSensorStatusUnit
IceProxy::wizrobo_scrubber::ScrubberIce::end_GetSensorState(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_scrubber__ScrubberIce__GetSensorState_name);
    ::wizrobo_scrubber::ScrSensorStatusUnit __ret;
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        ::IceInternal::BasicStream* __is = __result->__startReadParams();
        __is->read(__ret);
        __result->__endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

const ::std::string&
IceProxy::wizrobo_scrubber::ScrubberIce::ice_staticId()
{
    return ::wizrobo_scrubber::ScrubberIce::ice_staticId();
}

::IceInternal::Handle< ::IceDelegateM::Ice::Object>
IceProxy::wizrobo_scrubber::ScrubberIce::__createDelegateM()
{
    return ::IceInternal::Handle< ::IceDelegateM::Ice::Object>(new ::IceDelegateM::wizrobo_scrubber::ScrubberIce);
}

::IceInternal::Handle< ::IceDelegateD::Ice::Object>
IceProxy::wizrobo_scrubber::ScrubberIce::__createDelegateD()
{
    return ::IceInternal::Handle< ::IceDelegateD::Ice::Object>(new ::IceDelegateD::wizrobo_scrubber::ScrubberIce);
}

::IceProxy::Ice::Object*
IceProxy::wizrobo_scrubber::ScrubberIce::__newInstance() const
{
    return new ScrubberIce;
}

::wizrobo_scrubber::ScrClearControlUnit
IceDelegateM::wizrobo_scrubber::ScrubberIce::GetClearControlData(const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_scrubber__ScrubberIce__GetClearControlData_name, ::Ice::Normal, __context, __observer);
    __og.writeEmptyParams();
    bool __ok = __og.invoke();
    ::wizrobo_scrubber::ScrClearControlUnit __ret;
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        ::IceInternal::BasicStream* __is = __og.startReadParams();
        __is->read(__ret);
        __og.endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

void
IceDelegateM::wizrobo_scrubber::ScrubberIce::SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_scrubber__ScrubberIce__SetClearControlData_name, ::Ice::Normal, __context, __observer);
    try
    {
        ::IceInternal::BasicStream* __os = __og.startWriteParams(::Ice::DefaultFormat);
        __os->write(clearcontrol);
        __og.endWriteParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __og.abort(__ex);
    }
    bool __ok = __og.invoke();
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        __og.readEmptyParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

::wizrobo_scrubber::ScrSensorStatusUnit
IceDelegateM::wizrobo_scrubber::ScrubberIce::GetSensorState(const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_scrubber__ScrubberIce__GetSensorState_name, ::Ice::Normal, __context, __observer);
    __og.writeEmptyParams();
    bool __ok = __og.invoke();
    ::wizrobo_scrubber::ScrSensorStatusUnit __ret;
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        ::IceInternal::BasicStream* __is = __og.startReadParams();
        __is->read(__ret);
        __og.endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

::wizrobo_scrubber::ScrClearControlUnit
IceDelegateD::wizrobo_scrubber::ScrubberIce::GetClearControlData(const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(::wizrobo_scrubber::ScrClearControlUnit& __result, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _result(__result)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                _result = servant->GetClearControlData(_current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        ::wizrobo_scrubber::ScrClearControlUnit& _result;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_scrubber__ScrubberIce__GetClearControlData_name, ::Ice::Normal, __context);
    ::wizrobo_scrubber::ScrClearControlUnit __result;
    try
    {
        _DirectI __direct(__result, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
    return __result;
}

void
IceDelegateD::wizrobo_scrubber::ScrubberIce::SetClearControlData(const ::wizrobo_scrubber::ScrClearControlUnit& clearcontrol, const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(const ::wizrobo_scrubber::ScrClearControlUnit& __p_clearcontrol, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _m_clearcontrol(__p_clearcontrol)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                servant->SetClearControlData(_m_clearcontrol, _current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        const ::wizrobo_scrubber::ScrClearControlUnit& _m_clearcontrol;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_scrubber__ScrubberIce__SetClearControlData_name, ::Ice::Normal, __context);
    try
    {
        _DirectI __direct(clearcontrol, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
}

::wizrobo_scrubber::ScrSensorStatusUnit
IceDelegateD::wizrobo_scrubber::ScrubberIce::GetSensorState(const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(::wizrobo_scrubber::ScrSensorStatusUnit& __result, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _result(__result)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                _result = servant->GetSensorState(_current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        ::wizrobo_scrubber::ScrSensorStatusUnit& _result;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_scrubber__ScrubberIce__GetSensorState_name, ::Ice::Normal, __context);
    ::wizrobo_scrubber::ScrSensorStatusUnit __result;
    try
    {
        _DirectI __direct(__result, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
    return __result;
}

::Ice::Object* wizrobo_scrubber::upCast(::wizrobo_scrubber::ScrubberIce* p) { return p; }

namespace
{
const ::std::string __wizrobo_scrubber__ScrubberIce_ids[2] =
{
    "::Ice::Object",
    "::wizrobo_scrubber::ScrubberIce"
};

}

bool
wizrobo_scrubber::ScrubberIce::ice_isA(const ::std::string& _s, const ::Ice::Current&) const
{
    return ::std::binary_search(__wizrobo_scrubber__ScrubberIce_ids, __wizrobo_scrubber__ScrubberIce_ids + 2, _s);
}

::std::vector< ::std::string>
wizrobo_scrubber::ScrubberIce::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&__wizrobo_scrubber__ScrubberIce_ids[0], &__wizrobo_scrubber__ScrubberIce_ids[2]);
}

const ::std::string&
wizrobo_scrubber::ScrubberIce::ice_id(const ::Ice::Current&) const
{
    return __wizrobo_scrubber__ScrubberIce_ids[1];
}

const ::std::string&
wizrobo_scrubber::ScrubberIce::ice_staticId()
{
    return __wizrobo_scrubber__ScrubberIce_ids[1];
}

::Ice::DispatchStatus
wizrobo_scrubber::ScrubberIce::___GetClearControlData(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    __inS.readEmptyParams();
    try
    {
        ::wizrobo_scrubber::ScrClearControlUnit __ret = GetClearControlData(__current);
        ::IceInternal::BasicStream* __os = __inS.__startWriteParams(::Ice::DefaultFormat);
        __os->write(__ret);
        __inS.__endWriteParams(true);
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_scrubber::ScrubberIce::___SetClearControlData(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    ::IceInternal::BasicStream* __is = __inS.startReadParams();
    ::wizrobo_scrubber::ScrClearControlUnit clearcontrol;
    __is->read(clearcontrol);
    __inS.endReadParams();
    try
    {
        SetClearControlData(clearcontrol, __current);
        __inS.__writeEmptyParams();
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_scrubber::ScrubberIce::___GetSensorState(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    __inS.readEmptyParams();
    try
    {
        ::wizrobo_scrubber::ScrSensorStatusUnit __ret = GetSensorState(__current);
        ::IceInternal::BasicStream* __os = __inS.__startWriteParams(::Ice::DefaultFormat);
        __os->write(__ret);
        __inS.__endWriteParams(true);
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

namespace
{
const ::std::string __wizrobo_scrubber__ScrubberIce_all[] =
{
    "GetClearControlData",
    "GetSensorState",
    "SetClearControlData",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping"
};

}

::Ice::DispatchStatus
wizrobo_scrubber::ScrubberIce::__dispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair< const ::std::string*, const ::std::string*> r = ::std::equal_range(__wizrobo_scrubber__ScrubberIce_all, __wizrobo_scrubber__ScrubberIce_all + 7, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - __wizrobo_scrubber__ScrubberIce_all)
    {
        case 0:
        {
            return ___GetClearControlData(in, current);
        }
        case 1:
        {
            return ___GetSensorState(in, current);
        }
        case 2:
        {
            return ___SetClearControlData(in, current);
        }
        case 3:
        {
            return ___ice_id(in, current);
        }
        case 4:
        {
            return ___ice_ids(in, current);
        }
        case 5:
        {
            return ___ice_isA(in, current);
        }
        case 6:
        {
            return ___ice_ping(in, current);
        }
    }

    assert(false);
    throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
}

void
wizrobo_scrubber::ScrubberIce::__writeImpl(::IceInternal::BasicStream* __os) const
{
    __os->startWriteSlice(ice_staticId(), -1, true);
    __os->endWriteSlice();
}

void
wizrobo_scrubber::ScrubberIce::__readImpl(::IceInternal::BasicStream* __is)
{
    __is->startReadSlice();
    __is->endReadSlice();
}

void 
wizrobo_scrubber::__patch(ScrubberIcePtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ::wizrobo_scrubber::ScrubberIcePtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(::wizrobo_scrubber::ScrubberIce::ice_staticId(), v);
    }
}
