// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_scrubber.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <npuice_scrubber.h>
#include <Ice/LocalException.h>
#include <Ice/ObjectFactory.h>
#include <Ice/BasicStream.h>
#include <Ice/Object.h>
#include <IceUtil/Iterator.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace
{

const ::std::string __wizrobo_npu_scrubber__ScrubberIce__SetScrbCtrlPanel_name = "SetScrbCtrlPanel";

const ::std::string __wizrobo_npu_scrubber__ScrubberIce__GetScrbCtrlPanel_name = "GetScrbCtrlPanel";

const ::std::string __wizrobo_npu_scrubber__ScrubberIce__SetTrioooCmd_name = "SetTrioooCmd";

const ::std::string __wizrobo_npu_scrubber__ScrubberIce__GetBatteryStatus_name = "GetBatteryStatus";

const ::std::string __wizrobo_npu_scrubber__ScrubberIce__SetBatteryParam_name = "SetBatteryParam";

const ::std::string __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsStatus_name = "GetCleanerPartsStatus";

const ::std::string __wizrobo_npu_scrubber__ScrubberIce__GetCleanerSensorData_name = "GetCleanerSensorData";

const ::std::string __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsParam_name = "GetCleanerPartsParam";

const ::std::string __wizrobo_npu_scrubber__ScrubberIce__GetSystemStatus_name = "GetSystemStatus";

}

namespace Ice
{
}
::IceProxy::Ice::Object* ::IceProxy::wizrobo_npu_scrubber::upCast(::IceProxy::wizrobo_npu_scrubber::ScrubberIce* p) { return p; }

void
::IceProxy::wizrobo_npu_scrubber::__read(::IceInternal::BasicStream* __is, ::IceInternal::ProxyHandle< ::IceProxy::wizrobo_npu_scrubber::ScrubberIce>& v)
{
    ::Ice::ObjectPrx proxy;
    __is->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ::IceProxy::wizrobo_npu_scrubber::ScrubberIce;
        v->__copyFrom(proxy);
    }
}

void
IceProxy::wizrobo_npu_scrubber::ScrubberIce::SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_npu_scrubber__ScrubberIce__SetScrbCtrlPanel_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__SetScrbCtrlPanel_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce*>(__delBase.get());
            __del->SetScrbCtrlPanel(panel, mask, __ctx, __observer);
            return;
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_npu_scrubber::ScrubberIce::begin_SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__SetScrbCtrlPanel_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_npu_scrubber__ScrubberIce__SetScrbCtrlPanel_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_npu_scrubber__ScrubberIce__SetScrbCtrlPanel_name, ::Ice::Normal, __ctx);
        ::IceInternal::BasicStream* __os = __result->__startWriteParams(::Ice::DefaultFormat);
        __os->write(panel);
        __os->write(mask);
        __result->__endWriteParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

void
IceProxy::wizrobo_npu_scrubber::ScrubberIce::end_SetScrbCtrlPanel(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_npu_scrubber__ScrubberIce__SetScrbCtrlPanel_name);
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        __result->__readEmptyParams();
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

void
IceProxy::wizrobo_npu_scrubber::ScrubberIce::GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_npu_scrubber__ScrubberIce__GetScrbCtrlPanel_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetScrbCtrlPanel_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce*>(__delBase.get());
            __del->GetScrbCtrlPanel(panel, mask, __ctx, __observer);
            return;
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_npu_scrubber::ScrubberIce::begin_GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetScrbCtrlPanel_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_npu_scrubber__ScrubberIce__GetScrbCtrlPanel_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_npu_scrubber__ScrubberIce__GetScrbCtrlPanel_name, ::Ice::Normal, __ctx);
        ::IceInternal::BasicStream* __os = __result->__startWriteParams(::Ice::DefaultFormat);
        __os->write(panel);
        __os->write(mask);
        __result->__endWriteParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

void
IceProxy::wizrobo_npu_scrubber::ScrubberIce::end_GetScrbCtrlPanel(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_npu_scrubber__ScrubberIce__GetScrbCtrlPanel_name);
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        __result->__readEmptyParams();
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

void
IceProxy::wizrobo_npu_scrubber::ScrubberIce::SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_npu_scrubber__ScrubberIce__SetTrioooCmd_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__SetTrioooCmd_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce*>(__delBase.get());
            __del->SetTrioooCmd(cmd, __ctx, __observer);
            return;
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_npu_scrubber::ScrubberIce::begin_SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__SetTrioooCmd_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_npu_scrubber__ScrubberIce__SetTrioooCmd_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_npu_scrubber__ScrubberIce__SetTrioooCmd_name, ::Ice::Normal, __ctx);
        ::IceInternal::BasicStream* __os = __result->__startWriteParams(::Ice::DefaultFormat);
        __os->write(cmd);
        __result->__endWriteParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

void
IceProxy::wizrobo_npu_scrubber::ScrubberIce::end_SetTrioooCmd(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_npu_scrubber__ScrubberIce__SetTrioooCmd_name);
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        __result->__readEmptyParams();
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

::wizrobo_npu_scrubber::BatteryStatus
IceProxy::wizrobo_npu_scrubber::ScrubberIce::GetBatteryStatus(const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_npu_scrubber__ScrubberIce__GetBatteryStatus_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetBatteryStatus_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce*>(__delBase.get());
            return __del->GetBatteryStatus(__ctx, __observer);
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_npu_scrubber::ScrubberIce::begin_GetBatteryStatus(const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetBatteryStatus_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_npu_scrubber__ScrubberIce__GetBatteryStatus_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_npu_scrubber__ScrubberIce__GetBatteryStatus_name, ::Ice::Normal, __ctx);
        __result->__writeEmptyParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

::wizrobo_npu_scrubber::BatteryStatus
IceProxy::wizrobo_npu_scrubber::ScrubberIce::end_GetBatteryStatus(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_npu_scrubber__ScrubberIce__GetBatteryStatus_name);
    ::wizrobo_npu_scrubber::BatteryStatus __ret;
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        ::IceInternal::BasicStream* __is = __result->__startReadParams();
        __is->read(__ret);
        __result->__endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

void
IceProxy::wizrobo_npu_scrubber::ScrubberIce::SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_npu_scrubber__ScrubberIce__SetBatteryParam_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__SetBatteryParam_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce*>(__delBase.get());
            __del->SetBatteryParam(param, __ctx, __observer);
            return;
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_npu_scrubber::ScrubberIce::begin_SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__SetBatteryParam_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_npu_scrubber__ScrubberIce__SetBatteryParam_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_npu_scrubber__ScrubberIce__SetBatteryParam_name, ::Ice::Normal, __ctx);
        ::IceInternal::BasicStream* __os = __result->__startWriteParams(::Ice::DefaultFormat);
        __os->write(param);
        __result->__endWriteParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

void
IceProxy::wizrobo_npu_scrubber::ScrubberIce::end_SetBatteryParam(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_npu_scrubber__ScrubberIce__SetBatteryParam_name);
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        __result->__readEmptyParams();
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

::wizrobo_npu_scrubber::CleanerPartsStatus
IceProxy::wizrobo_npu_scrubber::ScrubberIce::GetCleanerPartsStatus(const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsStatus_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsStatus_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce*>(__delBase.get());
            return __del->GetCleanerPartsStatus(__ctx, __observer);
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_npu_scrubber::ScrubberIce::begin_GetCleanerPartsStatus(const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsStatus_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsStatus_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsStatus_name, ::Ice::Normal, __ctx);
        __result->__writeEmptyParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

::wizrobo_npu_scrubber::CleanerPartsStatus
IceProxy::wizrobo_npu_scrubber::ScrubberIce::end_GetCleanerPartsStatus(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsStatus_name);
    ::wizrobo_npu_scrubber::CleanerPartsStatus __ret;
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        ::IceInternal::BasicStream* __is = __result->__startReadParams();
        __is->read(__ret);
        __result->__endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

::wizrobo_npu_scrubber::CleanerSensorData
IceProxy::wizrobo_npu_scrubber::ScrubberIce::GetCleanerSensorData(const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerSensorData_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetCleanerSensorData_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce*>(__delBase.get());
            return __del->GetCleanerSensorData(__ctx, __observer);
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_npu_scrubber::ScrubberIce::begin_GetCleanerSensorData(const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetCleanerSensorData_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerSensorData_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_npu_scrubber__ScrubberIce__GetCleanerSensorData_name, ::Ice::Normal, __ctx);
        __result->__writeEmptyParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

::wizrobo_npu_scrubber::CleanerSensorData
IceProxy::wizrobo_npu_scrubber::ScrubberIce::end_GetCleanerSensorData(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerSensorData_name);
    ::wizrobo_npu_scrubber::CleanerSensorData __ret;
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        ::IceInternal::BasicStream* __is = __result->__startReadParams();
        __is->read(__ret);
        __result->__endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

::wizrobo_npu_scrubber::CleanerPartsParam
IceProxy::wizrobo_npu_scrubber::ScrubberIce::GetCleanerPartsParam(const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsParam_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsParam_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce*>(__delBase.get());
            return __del->GetCleanerPartsParam(__ctx, __observer);
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_npu_scrubber::ScrubberIce::begin_GetCleanerPartsParam(const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsParam_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsParam_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsParam_name, ::Ice::Normal, __ctx);
        __result->__writeEmptyParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

::wizrobo_npu_scrubber::CleanerPartsParam
IceProxy::wizrobo_npu_scrubber::ScrubberIce::end_GetCleanerPartsParam(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsParam_name);
    ::wizrobo_npu_scrubber::CleanerPartsParam __ret;
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        ::IceInternal::BasicStream* __is = __result->__startReadParams();
        __is->read(__ret);
        __result->__endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

::wizrobo_npu_scrubber::SystemStatus
IceProxy::wizrobo_npu_scrubber::ScrubberIce::GetSystemStatus(const ::Ice::Context* __ctx)
{
    ::IceInternal::InvocationObserver __observer(this, __wizrobo_npu_scrubber__ScrubberIce__GetSystemStatus_name, __ctx);
    int __cnt = 0;
    while(true)
    {
        ::IceInternal::Handle< ::IceDelegate::Ice::Object> __delBase;
        try
        {
            __checkTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetSystemStatus_name);
            __delBase = __getDelegate(false);
            ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce* __del = dynamic_cast< ::IceDelegate::wizrobo_npu_scrubber::ScrubberIce*>(__delBase.get());
            return __del->GetSystemStatus(__ctx, __observer);
        }
        catch(const ::IceInternal::LocalExceptionWrapper& __ex)
        {
            __handleExceptionWrapper(__delBase, __ex, __observer);
        }
        catch(const ::Ice::LocalException& __ex)
        {
            __handleException(__delBase, __ex, true, __cnt, __observer);
        }
    }
}

::Ice::AsyncResultPtr
IceProxy::wizrobo_npu_scrubber::ScrubberIce::begin_GetSystemStatus(const ::Ice::Context* __ctx, const ::IceInternal::CallbackBasePtr& __del, const ::Ice::LocalObjectPtr& __cookie)
{
    __checkAsyncTwowayOnly(__wizrobo_npu_scrubber__ScrubberIce__GetSystemStatus_name);
    ::IceInternal::OutgoingAsyncPtr __result = new ::IceInternal::OutgoingAsync(this, __wizrobo_npu_scrubber__ScrubberIce__GetSystemStatus_name, __del, __cookie);
    try
    {
        __result->__prepare(__wizrobo_npu_scrubber__ScrubberIce__GetSystemStatus_name, ::Ice::Normal, __ctx);
        __result->__writeEmptyParams();
        __result->__send(true);
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __result->__exceptionAsync(__ex);
    }
    return __result;
}

::wizrobo_npu_scrubber::SystemStatus
IceProxy::wizrobo_npu_scrubber::ScrubberIce::end_GetSystemStatus(const ::Ice::AsyncResultPtr& __result)
{
    ::Ice::AsyncResult::__check(__result, this, __wizrobo_npu_scrubber__ScrubberIce__GetSystemStatus_name);
    ::wizrobo_npu_scrubber::SystemStatus __ret;
    bool __ok = __result->__wait();
    try
    {
        if(!__ok)
        {
            try
            {
                __result->__throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                throw ::Ice::UnknownUserException(__FILE__, __LINE__, __ex.ice_name());
            }
        }
        ::IceInternal::BasicStream* __is = __result->__startReadParams();
        __is->read(__ret);
        __result->__endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& ex)
    {
        __result->__getObserver().failed(ex.ice_name());
        throw;
    }
}

const ::std::string&
IceProxy::wizrobo_npu_scrubber::ScrubberIce::ice_staticId()
{
    return ::wizrobo_npu_scrubber::ScrubberIce::ice_staticId();
}

::IceInternal::Handle< ::IceDelegateM::Ice::Object>
IceProxy::wizrobo_npu_scrubber::ScrubberIce::__createDelegateM()
{
    return ::IceInternal::Handle< ::IceDelegateM::Ice::Object>(new ::IceDelegateM::wizrobo_npu_scrubber::ScrubberIce);
}

::IceInternal::Handle< ::IceDelegateD::Ice::Object>
IceProxy::wizrobo_npu_scrubber::ScrubberIce::__createDelegateD()
{
    return ::IceInternal::Handle< ::IceDelegateD::Ice::Object>(new ::IceDelegateD::wizrobo_npu_scrubber::ScrubberIce);
}

::IceProxy::Ice::Object*
IceProxy::wizrobo_npu_scrubber::ScrubberIce::__newInstance() const
{
    return new ScrubberIce;
}

void
IceDelegateM::wizrobo_npu_scrubber::ScrubberIce::SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_npu_scrubber__ScrubberIce__SetScrbCtrlPanel_name, ::Ice::Normal, __context, __observer);
    try
    {
        ::IceInternal::BasicStream* __os = __og.startWriteParams(::Ice::DefaultFormat);
        __os->write(panel);
        __os->write(mask);
        __og.endWriteParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __og.abort(__ex);
    }
    bool __ok = __og.invoke();
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        __og.readEmptyParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

void
IceDelegateM::wizrobo_npu_scrubber::ScrubberIce::GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_npu_scrubber__ScrubberIce__GetScrbCtrlPanel_name, ::Ice::Normal, __context, __observer);
    try
    {
        ::IceInternal::BasicStream* __os = __og.startWriteParams(::Ice::DefaultFormat);
        __os->write(panel);
        __os->write(mask);
        __og.endWriteParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __og.abort(__ex);
    }
    bool __ok = __og.invoke();
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        __og.readEmptyParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

void
IceDelegateM::wizrobo_npu_scrubber::ScrubberIce::SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_npu_scrubber__ScrubberIce__SetTrioooCmd_name, ::Ice::Normal, __context, __observer);
    try
    {
        ::IceInternal::BasicStream* __os = __og.startWriteParams(::Ice::DefaultFormat);
        __os->write(cmd);
        __og.endWriteParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __og.abort(__ex);
    }
    bool __ok = __og.invoke();
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        __og.readEmptyParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

::wizrobo_npu_scrubber::BatteryStatus
IceDelegateM::wizrobo_npu_scrubber::ScrubberIce::GetBatteryStatus(const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_npu_scrubber__ScrubberIce__GetBatteryStatus_name, ::Ice::Normal, __context, __observer);
    __og.writeEmptyParams();
    bool __ok = __og.invoke();
    ::wizrobo_npu_scrubber::BatteryStatus __ret;
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        ::IceInternal::BasicStream* __is = __og.startReadParams();
        __is->read(__ret);
        __og.endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

void
IceDelegateM::wizrobo_npu_scrubber::ScrubberIce::SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_npu_scrubber__ScrubberIce__SetBatteryParam_name, ::Ice::Normal, __context, __observer);
    try
    {
        ::IceInternal::BasicStream* __os = __og.startWriteParams(::Ice::DefaultFormat);
        __os->write(param);
        __og.endWriteParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        __og.abort(__ex);
    }
    bool __ok = __og.invoke();
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        __og.readEmptyParams();
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

::wizrobo_npu_scrubber::CleanerPartsStatus
IceDelegateM::wizrobo_npu_scrubber::ScrubberIce::GetCleanerPartsStatus(const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsStatus_name, ::Ice::Normal, __context, __observer);
    __og.writeEmptyParams();
    bool __ok = __og.invoke();
    ::wizrobo_npu_scrubber::CleanerPartsStatus __ret;
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        ::IceInternal::BasicStream* __is = __og.startReadParams();
        __is->read(__ret);
        __og.endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

::wizrobo_npu_scrubber::CleanerSensorData
IceDelegateM::wizrobo_npu_scrubber::ScrubberIce::GetCleanerSensorData(const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_npu_scrubber__ScrubberIce__GetCleanerSensorData_name, ::Ice::Normal, __context, __observer);
    __og.writeEmptyParams();
    bool __ok = __og.invoke();
    ::wizrobo_npu_scrubber::CleanerSensorData __ret;
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        ::IceInternal::BasicStream* __is = __og.startReadParams();
        __is->read(__ret);
        __og.endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

::wizrobo_npu_scrubber::CleanerPartsParam
IceDelegateM::wizrobo_npu_scrubber::ScrubberIce::GetCleanerPartsParam(const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsParam_name, ::Ice::Normal, __context, __observer);
    __og.writeEmptyParams();
    bool __ok = __og.invoke();
    ::wizrobo_npu_scrubber::CleanerPartsParam __ret;
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        ::IceInternal::BasicStream* __is = __og.startReadParams();
        __is->read(__ret);
        __og.endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

::wizrobo_npu_scrubber::SystemStatus
IceDelegateM::wizrobo_npu_scrubber::ScrubberIce::GetSystemStatus(const ::Ice::Context* __context, ::IceInternal::InvocationObserver& __observer)
{
    ::IceInternal::Outgoing __og(__handler.get(), __wizrobo_npu_scrubber__ScrubberIce__GetSystemStatus_name, ::Ice::Normal, __context, __observer);
    __og.writeEmptyParams();
    bool __ok = __og.invoke();
    ::wizrobo_npu_scrubber::SystemStatus __ret;
    try
    {
        if(!__ok)
        {
            try
            {
                __og.throwUserException();
            }
            catch(const ::wizrobo_npu::NpuException&)
            {
                throw;
            }
            catch(const ::Ice::UserException& __ex)
            {
                ::Ice::UnknownUserException __uue(__FILE__, __LINE__, __ex.ice_name());
                throw __uue;
            }
        }
        ::IceInternal::BasicStream* __is = __og.startReadParams();
        __is->read(__ret);
        __og.endReadParams();
        return __ret;
    }
    catch(const ::Ice::LocalException& __ex)
    {
        throw ::IceInternal::LocalExceptionWrapper(__ex, false);
    }
}

void
IceDelegateD::wizrobo_npu_scrubber::ScrubberIce::SetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& __p_panel, ::Ice::Int __p_mask, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _m_panel(__p_panel),
            _m_mask(__p_mask)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_npu_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_npu_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                servant->SetScrbCtrlPanel(_m_panel, _m_mask, _current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        const ::wizrobo_npu_scrubber::ScrbCtrlPanel& _m_panel;
        ::Ice::Int _m_mask;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_npu_scrubber__ScrubberIce__SetScrbCtrlPanel_name, ::Ice::Normal, __context);
    try
    {
        _DirectI __direct(panel, mask, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
}

void
IceDelegateD::wizrobo_npu_scrubber::ScrubberIce::GetScrbCtrlPanel(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& panel, ::Ice::Int mask, const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(const ::wizrobo_npu_scrubber::ScrbCtrlPanel& __p_panel, ::Ice::Int __p_mask, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _m_panel(__p_panel),
            _m_mask(__p_mask)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_npu_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_npu_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                servant->GetScrbCtrlPanel(_m_panel, _m_mask, _current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        const ::wizrobo_npu_scrubber::ScrbCtrlPanel& _m_panel;
        ::Ice::Int _m_mask;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_npu_scrubber__ScrubberIce__GetScrbCtrlPanel_name, ::Ice::Normal, __context);
    try
    {
        _DirectI __direct(panel, mask, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
}

void
IceDelegateD::wizrobo_npu_scrubber::ScrubberIce::SetTrioooCmd(::Ice::Int cmd, const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(::Ice::Int __p_cmd, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _m_cmd(__p_cmd)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_npu_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_npu_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                servant->SetTrioooCmd(_m_cmd, _current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        ::Ice::Int _m_cmd;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_npu_scrubber__ScrubberIce__SetTrioooCmd_name, ::Ice::Normal, __context);
    try
    {
        _DirectI __direct(cmd, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
}

::wizrobo_npu_scrubber::BatteryStatus
IceDelegateD::wizrobo_npu_scrubber::ScrubberIce::GetBatteryStatus(const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(::wizrobo_npu_scrubber::BatteryStatus& __result, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _result(__result)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_npu_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_npu_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                _result = servant->GetBatteryStatus(_current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        ::wizrobo_npu_scrubber::BatteryStatus& _result;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_npu_scrubber__ScrubberIce__GetBatteryStatus_name, ::Ice::Normal, __context);
    ::wizrobo_npu_scrubber::BatteryStatus __result;
    try
    {
        _DirectI __direct(__result, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
    return __result;
}

void
IceDelegateD::wizrobo_npu_scrubber::ScrubberIce::SetBatteryParam(const ::wizrobo_npu_scrubber::BatteryStatus& param, const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(const ::wizrobo_npu_scrubber::BatteryStatus& __p_param, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _m_param(__p_param)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_npu_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_npu_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                servant->SetBatteryParam(_m_param, _current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        const ::wizrobo_npu_scrubber::BatteryStatus& _m_param;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_npu_scrubber__ScrubberIce__SetBatteryParam_name, ::Ice::Normal, __context);
    try
    {
        _DirectI __direct(param, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
}

::wizrobo_npu_scrubber::CleanerPartsStatus
IceDelegateD::wizrobo_npu_scrubber::ScrubberIce::GetCleanerPartsStatus(const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(::wizrobo_npu_scrubber::CleanerPartsStatus& __result, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _result(__result)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_npu_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_npu_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                _result = servant->GetCleanerPartsStatus(_current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        ::wizrobo_npu_scrubber::CleanerPartsStatus& _result;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsStatus_name, ::Ice::Normal, __context);
    ::wizrobo_npu_scrubber::CleanerPartsStatus __result;
    try
    {
        _DirectI __direct(__result, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
    return __result;
}

::wizrobo_npu_scrubber::CleanerSensorData
IceDelegateD::wizrobo_npu_scrubber::ScrubberIce::GetCleanerSensorData(const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(::wizrobo_npu_scrubber::CleanerSensorData& __result, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _result(__result)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_npu_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_npu_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                _result = servant->GetCleanerSensorData(_current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        ::wizrobo_npu_scrubber::CleanerSensorData& _result;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerSensorData_name, ::Ice::Normal, __context);
    ::wizrobo_npu_scrubber::CleanerSensorData __result;
    try
    {
        _DirectI __direct(__result, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
    return __result;
}

::wizrobo_npu_scrubber::CleanerPartsParam
IceDelegateD::wizrobo_npu_scrubber::ScrubberIce::GetCleanerPartsParam(const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(::wizrobo_npu_scrubber::CleanerPartsParam& __result, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _result(__result)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_npu_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_npu_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                _result = servant->GetCleanerPartsParam(_current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        ::wizrobo_npu_scrubber::CleanerPartsParam& _result;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_npu_scrubber__ScrubberIce__GetCleanerPartsParam_name, ::Ice::Normal, __context);
    ::wizrobo_npu_scrubber::CleanerPartsParam __result;
    try
    {
        _DirectI __direct(__result, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
    return __result;
}

::wizrobo_npu_scrubber::SystemStatus
IceDelegateD::wizrobo_npu_scrubber::ScrubberIce::GetSystemStatus(const ::Ice::Context* __context, ::IceInternal::InvocationObserver&)
{
    class _DirectI : public ::IceInternal::Direct
    {
    public:

        _DirectI(::wizrobo_npu_scrubber::SystemStatus& __result, const ::Ice::Current& __current) : 
            ::IceInternal::Direct(__current),
            _result(__result)
        {
        }
        
        virtual ::Ice::DispatchStatus
        run(::Ice::Object* object)
        {
            ::wizrobo_npu_scrubber::ScrubberIce* servant = dynamic_cast< ::wizrobo_npu_scrubber::ScrubberIce*>(object);
            if(!servant)
            {
                throw ::Ice::OperationNotExistException(__FILE__, __LINE__, _current.id, _current.facet, _current.operation);
            }
            try
            {
                _result = servant->GetSystemStatus(_current);
                return ::Ice::DispatchOK;
            }
            catch(const ::Ice::UserException& __ex)
            {
                setUserException(__ex);
                return ::Ice::DispatchUserException;
            }
        }
        
    private:
        
        ::wizrobo_npu_scrubber::SystemStatus& _result;
    };
    
    ::Ice::Current __current;
    __initCurrent(__current, __wizrobo_npu_scrubber__ScrubberIce__GetSystemStatus_name, ::Ice::Normal, __context);
    ::wizrobo_npu_scrubber::SystemStatus __result;
    try
    {
        _DirectI __direct(__result, __current);
        try
        {
            __direct.getServant()->__collocDispatch(__direct);
        }
        catch(...)
        {
            __direct.destroy();
            throw;
        }
        __direct.destroy();
    }
    catch(const ::wizrobo_npu::NpuException&)
    {
        throw;
    }
    catch(const ::Ice::SystemException&)
    {
        throw;
    }
    catch(const ::IceInternal::LocalExceptionWrapper&)
    {
        throw;
    }
    catch(const ::std::exception& __ex)
    {
        ::IceInternal::LocalExceptionWrapper::throwWrapper(__ex);
    }
    catch(...)
    {
        throw ::IceInternal::LocalExceptionWrapper(::Ice::UnknownException(__FILE__, __LINE__, "unknown c++ exception"), false);
    }
    return __result;
}

::Ice::Object* wizrobo_npu_scrubber::upCast(::wizrobo_npu_scrubber::ScrubberIce* p) { return p; }

namespace
{
const ::std::string __wizrobo_npu_scrubber__ScrubberIce_ids[2] =
{
    "::Ice::Object",
    "::wizrobo_npu_scrubber::ScrubberIce"
};

}

bool
wizrobo_npu_scrubber::ScrubberIce::ice_isA(const ::std::string& _s, const ::Ice::Current&) const
{
    return ::std::binary_search(__wizrobo_npu_scrubber__ScrubberIce_ids, __wizrobo_npu_scrubber__ScrubberIce_ids + 2, _s);
}

::std::vector< ::std::string>
wizrobo_npu_scrubber::ScrubberIce::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&__wizrobo_npu_scrubber__ScrubberIce_ids[0], &__wizrobo_npu_scrubber__ScrubberIce_ids[2]);
}

const ::std::string&
wizrobo_npu_scrubber::ScrubberIce::ice_id(const ::Ice::Current&) const
{
    return __wizrobo_npu_scrubber__ScrubberIce_ids[1];
}

const ::std::string&
wizrobo_npu_scrubber::ScrubberIce::ice_staticId()
{
    return __wizrobo_npu_scrubber__ScrubberIce_ids[1];
}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::___SetScrbCtrlPanel(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    ::IceInternal::BasicStream* __is = __inS.startReadParams();
    ::wizrobo_npu_scrubber::ScrbCtrlPanel panel;
    ::Ice::Int mask;
    __is->read(panel);
    __is->read(mask);
    __inS.endReadParams();
    try
    {
        SetScrbCtrlPanel(panel, mask, __current);
        __inS.__writeEmptyParams();
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::___GetScrbCtrlPanel(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    ::IceInternal::BasicStream* __is = __inS.startReadParams();
    ::wizrobo_npu_scrubber::ScrbCtrlPanel panel;
    ::Ice::Int mask;
    __is->read(panel);
    __is->read(mask);
    __inS.endReadParams();
    try
    {
        GetScrbCtrlPanel(panel, mask, __current);
        __inS.__writeEmptyParams();
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::___SetTrioooCmd(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    ::IceInternal::BasicStream* __is = __inS.startReadParams();
    ::Ice::Int cmd;
    __is->read(cmd);
    __inS.endReadParams();
    try
    {
        SetTrioooCmd(cmd, __current);
        __inS.__writeEmptyParams();
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::___GetBatteryStatus(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    __inS.readEmptyParams();
    try
    {
        ::wizrobo_npu_scrubber::BatteryStatus __ret = GetBatteryStatus(__current);
        ::IceInternal::BasicStream* __os = __inS.__startWriteParams(::Ice::DefaultFormat);
        __os->write(__ret);
        __inS.__endWriteParams(true);
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::___SetBatteryParam(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    ::IceInternal::BasicStream* __is = __inS.startReadParams();
    ::wizrobo_npu_scrubber::BatteryStatus param;
    __is->read(param);
    __inS.endReadParams();
    try
    {
        SetBatteryParam(param, __current);
        __inS.__writeEmptyParams();
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::___GetCleanerPartsStatus(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    __inS.readEmptyParams();
    try
    {
        ::wizrobo_npu_scrubber::CleanerPartsStatus __ret = GetCleanerPartsStatus(__current);
        ::IceInternal::BasicStream* __os = __inS.__startWriteParams(::Ice::DefaultFormat);
        __os->write(__ret);
        __inS.__endWriteParams(true);
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::___GetCleanerSensorData(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    __inS.readEmptyParams();
    try
    {
        ::wizrobo_npu_scrubber::CleanerSensorData __ret = GetCleanerSensorData(__current);
        ::IceInternal::BasicStream* __os = __inS.__startWriteParams(::Ice::DefaultFormat);
        __os->write(__ret);
        __inS.__endWriteParams(true);
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::___GetCleanerPartsParam(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    __inS.readEmptyParams();
    try
    {
        ::wizrobo_npu_scrubber::CleanerPartsParam __ret = GetCleanerPartsParam(__current);
        ::IceInternal::BasicStream* __os = __inS.__startWriteParams(::Ice::DefaultFormat);
        __os->write(__ret);
        __inS.__endWriteParams(true);
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::___GetSystemStatus(::IceInternal::Incoming& __inS, const ::Ice::Current& __current)
{
    __checkMode(::Ice::Normal, __current.mode);
    __inS.readEmptyParams();
    try
    {
        ::wizrobo_npu_scrubber::SystemStatus __ret = GetSystemStatus(__current);
        ::IceInternal::BasicStream* __os = __inS.__startWriteParams(::Ice::DefaultFormat);
        __os->write(__ret);
        __inS.__endWriteParams(true);
        return ::Ice::DispatchOK;
    }
    catch(const ::wizrobo_npu::NpuException& __ex)
    {
        __inS.__writeUserException(__ex, ::Ice::DefaultFormat);
    }
    return ::Ice::DispatchUserException;
}

namespace
{
const ::std::string __wizrobo_npu_scrubber__ScrubberIce_all[] =
{
    "GetBatteryStatus",
    "GetCleanerPartsParam",
    "GetCleanerPartsStatus",
    "GetCleanerSensorData",
    "GetScrbCtrlPanel",
    "GetSystemStatus",
    "SetBatteryParam",
    "SetScrbCtrlPanel",
    "SetTrioooCmd",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping"
};

}

::Ice::DispatchStatus
wizrobo_npu_scrubber::ScrubberIce::__dispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair< const ::std::string*, const ::std::string*> r = ::std::equal_range(__wizrobo_npu_scrubber__ScrubberIce_all, __wizrobo_npu_scrubber__ScrubberIce_all + 13, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - __wizrobo_npu_scrubber__ScrubberIce_all)
    {
        case 0:
        {
            return ___GetBatteryStatus(in, current);
        }
        case 1:
        {
            return ___GetCleanerPartsParam(in, current);
        }
        case 2:
        {
            return ___GetCleanerPartsStatus(in, current);
        }
        case 3:
        {
            return ___GetCleanerSensorData(in, current);
        }
        case 4:
        {
            return ___GetScrbCtrlPanel(in, current);
        }
        case 5:
        {
            return ___GetSystemStatus(in, current);
        }
        case 6:
        {
            return ___SetBatteryParam(in, current);
        }
        case 7:
        {
            return ___SetScrbCtrlPanel(in, current);
        }
        case 8:
        {
            return ___SetTrioooCmd(in, current);
        }
        case 9:
        {
            return ___ice_id(in, current);
        }
        case 10:
        {
            return ___ice_ids(in, current);
        }
        case 11:
        {
            return ___ice_isA(in, current);
        }
        case 12:
        {
            return ___ice_ping(in, current);
        }
    }

    assert(false);
    throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
}

void
wizrobo_npu_scrubber::ScrubberIce::__writeImpl(::IceInternal::BasicStream* __os) const
{
    __os->startWriteSlice(ice_staticId(), -1, true);
    __os->endWriteSlice();
}

void
wizrobo_npu_scrubber::ScrubberIce::__readImpl(::IceInternal::BasicStream* __is)
{
    __is->startReadSlice();
    __is->endReadSlice();
}

void 
wizrobo_npu_scrubber::__patch(ScrubberIcePtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ::wizrobo_npu_scrubber::ScrubberIcePtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(::wizrobo_npu_scrubber::ScrubberIce::ice_staticId(), v);
    }
}
