// **********************************************************************
//
// Copyright (c) 2003-2013 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.5.1
//
// <auto-generated>
//
// Generated from file `npuice_exception.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <npuice_exception.h>
#include <Ice/BasicStream.h>
#include <Ice/Object.h>
#include <Ice/LocalException.h>
#include <IceUtil/Iterator.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 305
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

namespace
{

const char* __wizrobo_npu__NpuException_name = "wizrobo_npu::NpuException";

struct __F__wizrobo_npu__NpuException : public ::IceInternal::UserExceptionFactory
{
    virtual void
    createAndThrow(const ::std::string&)
    {
        throw ::wizrobo_npu::NpuException();
    }
};

class __F__wizrobo_npu__NpuException__Init
{
public:

    __F__wizrobo_npu__NpuException__Init()
    {
        ::IceInternal::factoryTable->addExceptionFactory("::wizrobo_npu::NpuException", new __F__wizrobo_npu__NpuException);
    }

    ~__F__wizrobo_npu__NpuException__Init()
    {
        ::IceInternal::factoryTable->removeExceptionFactory("::wizrobo_npu::NpuException");
    }
};

const __F__wizrobo_npu__NpuException__Init __F__wizrobo_npu__NpuException__i;

}

wizrobo_npu::NpuException::NpuException(const ::std::string& __ice_date, const ::std::string& __ice_time, ::wizrobo_npu::ErrorType __ice_type, const ::std::string& __ice_msg) :
    ::Ice::UserException(),
    date(__ice_date),
    time(__ice_time),
    type(__ice_type),
    msg(__ice_msg)
{
}

wizrobo_npu::NpuException::~NpuException() throw()
{
}

::std::string
wizrobo_npu::NpuException::ice_name() const
{
    return __wizrobo_npu__NpuException_name;
}

wizrobo_npu::NpuException*
wizrobo_npu::NpuException::ice_clone() const
{
    return new NpuException(*this);
}

void
wizrobo_npu::NpuException::ice_throw() const
{
    throw *this;
}

void
wizrobo_npu::NpuException::__writeImpl(::IceInternal::BasicStream* __os) const
{
    __os->startWriteSlice("::wizrobo_npu::NpuException", -1, true);
    __os->write(date);
    __os->write(time);
    __os->write(type);
    __os->write(msg);
    __os->endWriteSlice();
}

void
wizrobo_npu::NpuException::__readImpl(::IceInternal::BasicStream* __is)
{
    __is->startReadSlice();
    __is->read(date);
    __is->read(time);
    __is->read(type);
    __is->read(msg);
    __is->endReadSlice();
}

namespace Ice
{
}
