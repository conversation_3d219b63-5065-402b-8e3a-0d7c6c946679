#include "station_graph.h"

#include <stdlib.h>
#include <iostream>

#define SG_DEBUG(fmt, arg...) printf("SGRAPH::%s() " fmt "\n", __FUNCTION__, ##arg)

//#define SG_DEBUG(fmt, arg...) printf(fmt, arg)
//#define SG_DEBUG printf
namespace station_graph {

using namespace std;

typedef struct _PointOnLineScore {
    bool isInRange;
    double dist;
    double deflection;
}PointOnLineScore;

static double normRad(double rad)
{
    while (rad >= (M_PI)) {
        rad -= 2.0 * M_PI;
    }
    while (rad <= (-M_PI)) {
        rad += 2.0 * M_PI;
    }
    return rad;
}

static bool CheckIsInRange(Station* ptr_s, Station* ptr_e, double px, double py)
{
    if (ptr_s == NULL || ptr_e == NULL) {
        return false;
    }
    if ((ptr_s->x_<=px && px<=ptr_e->x_ && ptr_s->y_<=py && py<=ptr_e->y_)
            || (ptr_s->x_<=px && px<=ptr_e->x_ && ptr_e->y_<=py && py<=ptr_s->y_)
            || (ptr_e->x_<=px && px<=ptr_s->x_ && ptr_s->y_<=py && py<=ptr_e->y_)
            || (ptr_e->x_<=px && px<=ptr_s->x_ && ptr_e->y_<=py && py<=ptr_s->y_)) {
        return true;
    } else {
        return false;
    }
}

static PointOnLineScore CheckPointOnLine(Station* ptr_s, Station* ptr_e, double px, double py, double ptheta)
{
    PointOnLineScore score;
    score.isInRange = CheckIsInRange(ptr_s, ptr_e, px, py);
    SG_DEBUG("ptr_e_y is:%f, ptr_s_y is:%f, ptr_e_x is:%f, ptr_s_x is: %f", ptr_e->y_, ptr_s->y_, ptr_e->x_, ptr_s->x_);
    double theta_line = normRad(atan2((ptr_e->y_ - ptr_s->y_), (ptr_e->x_ - ptr_s->x_)));
    SG_DEBUG("theta_line is:%f", theta_line);
    SG_DEBUG("robot theta is:%f", ptheta);
    score.deflection = ABS(normRad(theta_line - ptheta));
    SG_DEBUG("deflection is:%f", score.deflection);
    double theta_point = normRad(atan2((ptr_e->y_ - py), (ptr_e->x_ - px)));
    double theta_dt = ABS(normRad(theta_line - theta_point));
    double _dist = sqrt(SQU(ptr_e->x_ - px) + SQU(ptr_e->y_ - py));
    score.dist = _dist * sin(theta_dt);
}

Station::Station(boost::property_tree::ptree& pt)
{
    index_ = pt.get<int>("index", -1);
    ptr_from_ = NULL;
    d2s_ = DBL_MAX;
    x_ = pt.get<float>("x", 0.0);
    y_ = pt.get<float>("y", 0.0);
    theta_ = pt.get<float>("theta", 0.0);
    enb_navi_arrived_ = pt.get<bool>("enb_navi_arrived", false);
    csg_station_ = pt.get<bool>("csg_station", false);
    inflection_point_  = pt.get<bool>("inflection_point", true);
    is_charger_ = pt.get<bool>("is_charger", false);
    is_charge_station_ = pt.get<bool>("is_charge_station", false);
}

Station::Station(int index)
{
    index_ = index;
    ptr_from_ = NULL;
    d2s_ = DBL_MAX;
    x_ = 0.0;
    y_ = 0.0;
    theta_ = 0.0;
    enb_navi_arrived_ = false;
    csg_station_ = false;
    inflection_point_  = true;
    is_charger_ = false;
    is_charge_station_ = false;
}

void Station::DeleteAdjacentStation(int index)
{
    std::cout<<"DeleteAdjacentStation() index: " << index <<std::endl;
    auto iter = adjacent_stations_.begin();
    int cnt_ = adjacent_stations_.size();
    std::cout<< "DeleteAdjacentStation() adjacent_stations_.size is: "<< cnt_ << std::endl;
    /*while (iter != adjacent_stations_.end())*/
    for(int i=0; i<cnt_; i++) {
        int adj_id = (*iter)->GetIndex();
        if ( adj_id == index ) {
            std::cout << "DeleteAdjacentStation() find adj_id: " << (*iter)->GetIndex() << std::endl;
            adjacent_stations_.erase(iter);
            return;
//            continue;
        }
        ++iter;
    }
    return;
}

bool Station::IsAdjacent(int index)
{
    for ( auto ptr : adjacent_stations_ ) {
        if (ptr->GetIndex() == index) {
            return true;
        }
    }
    return false;
}

void Station::ToPtree(boost::property_tree::ptree& pt)
{
    pt.add<int>("index", index_);
    pt.add<float>("x", x_);
    pt.add<float>("y", y_);
    pt.add<float>("theta", theta_);
    pt.add<bool>("enb_navi_arrived", enb_navi_arrived_);
    pt.add<bool>("csg_station", csg_station_);
    pt.add<bool>("inflection_point", inflection_point_);
    pt.add<bool>("is_charger", is_charger_);
    pt.add<bool>("is_charge_station", is_charge_station_);
    boost::property_tree::ptree adjacent_table;
    for (auto ptr_adjacent : adjacent_stations_) {
        boost::property_tree::ptree e;
        e.put_value<int>(ptr_adjacent->GetIndex());
        adjacent_table.push_back(std::make_pair("", e));
    }
    pt.add_child("adjacent", adjacent_table);
}


/* StationGraph */

StationGraph::StationGraph()
    : station_index_(-1)
    , ptr_current_station_(NULL)
    , ptr_pre_station_(NULL)
    , station_cnt_(0)
    , path_cnt_(0)
{
    LoadFromFile();
    SG_DEBUG("Station_graph() station_ size is: %ld", stations_.size());
}


bool StationGraph::AddPath(Station *ptr_1, Station *ptr_2)
{
    if (HasStation(ptr_1) == NULL || HasStation(ptr_2) == NULL) {
        return false;
    }
    ptr_1->AddAdjacentStation(ptr_2);
    return true;
}

void StationGraph::DeleteStation(Station* ptr_station)
{
    std::cout<<"DeleteStation() delete station: " << ptr_station->GetIndex() <<std::endl;
    auto iter = stations_.begin();
    int cnt_ = stations_.size();
    /*while (iter != stations_.end())*/
    for(int i=0; i<cnt_; i++) {
        std::cout<<"cnt_ is:"<<stations_.size()<<std::endl;
        if ((*iter)->GetIndex() == ptr_station->GetIndex()) {
            for (auto ptr_adj_station : (*iter)->GetAdjacentStation()) {
                std::cout<<"DeleteStation() find adjacent station: " << ptr_adj_station->GetIndex() <<std::endl;
                ptr_adj_station->DeleteAdjacentStation(*iter);
            }
//            std::cout<<"cnt_ is:"<<stations_.size()<<std::endl;
            stations_.erase(iter);
//            std::cout<<"cnt_ is:"<<stations_.size()<<std::endl;
            return;
        }
        ++iter;
    }
}

bool StationGraph::DeletePath(int index_1, int index_2)
{
    std::cout<<"DeletePath"<<std::endl;
    int cnt_ = stations_.size();
    SG_DEBUG("station list size is: %d", cnt_);
    auto iter = stations_.begin();
    for(int i=0; i<cnt_; i++) {
        std::cout << "station index is: " << (*iter)->GetIndex() << std::endl;
        std::cout << "index_1 id is: " << index_1 << std::endl;
        if((*iter)->GetIndex() == index_1) {
            std::cout<<"Delete01"<<std::endl;
            for(auto ptr_adj_station : (*iter)->GetAdjacentStation()) {
                std::cout<<"Delete02"<<std::endl;
//                ptr_station->DeleteAdjacentStation(index_1);
                if(ptr_adj_station->GetIndex() == index_2) {
                    ptr_adj_station->DeleteAdjacentStation(index_1);
//                    (*iter)->GetAdjacentStation().erase(ptr_adj_station);
                }

            }
            (*iter)->DeleteAdjacentStation(index_2);
            return true;
        }
        ++iter;
    }
    return true;
}

list<Station*> StationGraph::FindPath(Station* ptr_target, Station* ptr_current, bool is_block)
{
    //SG_DEBUG("is_block is: %d", is_block);
    MakeDistenceField(ptr_current, is_block);
    list<Station*> path;
    Station* ptr = ptr_target;
    while (ptr->ptr_from_ != NULL) {
        path.push_front(ptr);
        ptr = ptr->ptr_from_;
    }
    path.push_front(ptr);
    return path;
}

#if 1 /* Greed */
void StationGraph::MakePlan(list<Station *>& station_list, bool is_block)
{
    SG_DEBUG("MakePlan...");
    list<Station *> station_list_backup = station_list;
    station_list.clear();
    /* Make a plan form current station */
    Station* ptr_current_station = ptr_current_station_;
    if (ptr_current_station == NULL) {
        SG_DEBUG("current_station is null!!!");
        return;
    }
    int i = 0;
    list<list<Station *>> paths;
    list<list<Station *>> paths_up;
    paths.resize(0);
    paths_up.resize(0);

    SG_DEBUG("station_list len is %ld", station_list_backup.size());
    while (!station_list_backup.empty()) {
        double dist;
        double min_dist = DBL_MAX;
        list<Station *>::iterator iter_closest = station_list_backup.begin();
        list<Station *>::iterator iter_current = station_list_backup.begin();
        for (list<Station *>::iterator iter_station = station_list_backup.begin();iter_station!=station_list_backup.end();iter_station++) {
            if ((*ptr_current_station) == (*(*iter_station))) {
                SG_DEBUG("find iter_current!");
                iter_current = iter_station;
            }
            /* Find a path from *ptr_current_station to *(*iter_station) */
            list<Station *> path = FindPath((*iter_station), ptr_current_station, is_block);
            dist = path.back()->d2s_;
            if (dist > DBL_MAX / 2) {
                /* Means (*iter_station) is a standalone station */
                SG_DEBUG("Can NOT find a path from station(%d) to station(%d)", ptr_current_station->GetIndex(), (*iter_station)->GetIndex());
                continue;
            }
            paths.push_back(path);
            if (dist < min_dist) {
                min_dist = dist;
                iter_closest = iter_station;
            }
#if 0
            SG_DEBUG("++ path(%02d->%02d) d2s: %.2f seq: ",
                   ptr_current_station->GetIndex(), (*iter_station)->GetIndex(),
                   dist);
            for(auto iter=path.begin();iter!=path.end();iter++) {
                printf("%02d ", (*iter)->GetIndex());
            }
            printf("\n");
#endif
        }
        if (paths.size() <= 0) {
            SG_DEBUG("station(%d) does NOT have a adjacent node, it is NOT reachable!", ptr_current_station->GetIndex());
            paths.clear();
            station_list_backup.erase(iter_current);
            ptr_current_station = station_list_backup.front();
            continue;
        }
#if 0
        SG_DEBUG("-- closest(%02d) d2s: %.2f", (*iter_closest)->GetIndex(), min_dist);
#endif
        ptr_current_station = *iter_closest;
        station_list.push_back(*iter_closest);

        for(auto p : paths){
            if(p.back()->id_ == (*iter_closest)->id_){

//                /*** Debug ***/
//                for(auto iter=p.begin();iter!=p.end();iter++) {
//                    SG_DEBUG("The path's station is:%02d ", (*iter)->GetIndex());
//                }
//                /*** Debug ***/

                if(i == 0) {
                    paths_up.push_back(p);
                    for(auto iter=p.begin();iter!=p.end();iter++) {
                    SG_DEBUG("The path's station is:%02d ", (*iter)->GetIndex());
                }
                    i++;
                } else {
                    auto iter_station = p.begin();
                    p.erase(iter_station);
                    paths_up.push_back(p);
                    SG_DEBUG("Else~The path's station is:%02d ", (*iter_station)->GetIndex());
                    i++;
                }
            }
        }
        paths.clear();
        station_list_backup.erase(iter_closest);
    }
    SG_DEBUG("make plan over!");
    paths_ = paths_up;
    return;
}
#endif

#if 0
void StationGraph::MakePlan(list<Station *>& station_list)
{
    list<Station *> station_list_backup = station_list;
    station_list.clear();

    Station* ptr_current_station = ptr_current_station_;
    if (ptr_current_station == NULL) {
        return;
    }

    while (!station_list_backup.empty()) {
        double dist;
        double min_dist = DBL_MAX;
        list<Station *>::iterator iter_closest;
        for (list<Station *>::iterator iter_station = station_list_backup.begin();
             iter_station!=station_list_backup.end();iter_station++) {
            list<Station *> path = FindPath((*iter_station), ptr_current_station);
            dist = path.back()->d2s_;
#if 0
            SG_DEBUG("++ path(%02d->%02d) d2s: %.2f seq: ",
                   ptr_current_station->GetIndex(), (*iter_station)->GetIndex(),
                   dist);
            for(auto iter=path.begin();iter!=path.end();iter++) {
                SG_DEBUG("%02d ", (*iter)->GetIndex());
            }
            SG_DEBUG("");
#endif
            if (dist < min_dist) {
                min_dist = dist;
                iter_closest = iter_station;
            }
        }
#if 0
        SG_DEBUG("-- closest(%02d) d2s: %.2f", (*iter_closest)->GetIndex(), min_dist);
#endif
        ptr_current_station = *iter_closest;
        station_list.push_back(*iter_closest);
        station_list_backup.erase(iter_closest);
    }
    return;
}
#endif

Station* StationGraph::GetStationPtr(int index)
{
    for (auto& ptr_station : stations_) {
        if (ptr_station->GetIndex() == index) {
            return ptr_station;
        }
    }
    return NULL;
}

Station* StationGraph::GetStationPtr(double x, double y)
{
    for (auto ptr : stations_) {
        if (ABS(x - ptr->x_) <= _esp_ && ABS(y - ptr->y_) <= _esp_) {
            return ptr;
        }
    }
    return NULL;
}

Station* StationGraph::GetClosestStation(double x, double y)
{
    double min_dist = DBL_MAX;
    double _dist;
    Station* ptr_closest_station;
    for (auto ptr : stations_) {
        _dist = DIST(x, y, ptr->x_, ptr->y_);
        if ( _dist < min_dist) {
            min_dist = _dist;
            ptr_closest_station = ptr;
            SG_DEBUG("+[%d](%.2f, %.2f)/(%.2f, %.2f)/(%.2f, %.2f)", ptr->GetIndex(), ptr->x_, ptr->y_, x, y, _dist, min_dist);
        } else {
            SG_DEBUG("-[%d](%.2f, %.2f)/(%.2f, %.2f)/(%.2f, %.2f)", ptr->GetIndex(), ptr->x_, ptr->y_, x, y, _dist, min_dist);
        }
    }
    return ptr_closest_station;
}

void StationGraph::PrintStationGraph()
{
    for ( auto& station : stations_) {
        cout << station->GetIndex() << " : ";
        for ( auto& adjacent : station->GetAdjacentStation() ) {
            cout << adjacent->GetIndex() << ", ";
        }
        cout << "X" << endl;
    }
}

void StationGraph::LoadFromFile()
{
    std::string ss;
    ss.append("/npu.x86_64/").append(map_name_).append("_stations.json");
    SG_DEBUG("Map name is \"%s\", load \"%s\"!", map_name_.c_str(), ss.c_str());

    boost::property_tree::ptree pt;
    try {
        boost::property_tree::json_parser::read_json(ss, pt);
    } catch(boost::property_tree::json_parser::json_parser_error e) {
        SG_DEBUG("\n\n|!!!--> %s\n", e.what());
        return;
    }

    boost::property_tree::ptree& pt_root = pt.get_child("stations");
    auto pt_iter = pt_root.begin();
    while (pt_iter != pt_root.end()) {
        boost::property_tree::ptree::value_type& element = (*pt_iter);
        boost::property_tree::ptree& pt_station = element.second;
        int station_index = pt_station.get<int>("index", -1);
        //double _x = pt_station.get<float>("x", 0.0);
        //double _y = pt_station.get<float>("y", 0.0);
        //SG_DEBUG("|!!!station_index: %d, _x: %.2f, _y: %.2f", station_index, _x, _y);
        if (station_index_ < station_index) {
            station_index_ = station_index;
        }
        Station* ptr = new Station(pt_station);
        stations_.push_back(ptr);
        pt_iter++;
    }
    pt_iter = pt_root.begin();
    while ( pt_iter != pt_root.end() ) {
        boost::property_tree::ptree::value_type& element = (*pt_iter);
        boost::property_tree::ptree& pt_station = element.second;
        boost::property_tree::ptree& adjacent_array = pt_station.get_child("adjacent");
        boost::property_tree::ptree::iterator adjacent_iter = adjacent_array.begin();
        int station_index = pt_station.get<int>("index");
        Station* ptr_station = GetStationPtr(station_index);
        while (adjacent_iter != adjacent_array.end()) {
            boost::property_tree::ptree::value_type& e = (*adjacent_iter);
            int adjacent_index = atoi(e.second.data().c_str());
            Station* ptr_adjacent = GetStationPtr(adjacent_index);
            if (ptr_adjacent != NULL) {
                AddPath(ptr_station, ptr_adjacent);
            }
            adjacent_iter++;
        }
        pt_iter++;
    }
    return;
}

void StationGraph::DumpToFile()
{
    std::string ss;
    ss.append("/npu.x86_64/").append(map_name_).append("_stations.json");
    SG_DEBUG("Map name is \"%s\", dump to \"%s\"", map_name_.c_str(), ss.c_str());

    boost::property_tree::ptree pt_root;
    boost::property_tree::ptree pt_stations;
    for(auto ptr_station : stations_) {
        boost::property_tree::ptree pt_station;
        ptr_station->ToPtree(pt_station);
        pt_stations.push_back(std::make_pair("", pt_station));
    }
    pt_root.add_child("stations", pt_stations);
    boost::property_tree::json_parser::write_json(ss, pt_root);
}

void StationGraph::MakeDistenceField(Station* ptr_current_station, bool is_block)
{
    for (auto ptr_i : stations_) {
        ptr_i->ptr_from_ = NULL;
        ptr_i->d2s_ = DBL_MAX;
    }
    list<Station*> q;
    ptr_current_station->ptr_from_ = NULL;
    ptr_current_station->d2s_ = 0;
    int start_index_ = ptr_current_station->GetIndex();
    //SG_DEBUG("start index is: %d", start_index_);
    if (is_block == true) {
        //SG_DEBUG("Have obstacle!");
        double theta_point2robot = CheckCurStation(ptr_current_station, current_x_, current_y_, current_theta_);
        //SG_DEBUG("obstacle in forward or backward (0.523): %.2f", theta_point2robot);
        if(theta_point2robot < 0.523) {
            for(auto ptr_j : ptr_current_station->GetAdjacentStation()) {
                double theta_point2robot2 = CheckThetaDiffer(ptr_current_station, ptr_j, current_x_, current_y_, current_theta_);
                if(theta_point2robot2 > 2.8) {
                    ptr_current_station = ptr_j;
                    ptr_current_station->ptr_from_ = NULL;
                    ptr_current_station->d2s_ = 0;
                    //SG_DEBUG("##0 current station index is: %d", ptr_j->GetIndex());
                }
            }
        }
        q.push_back(ptr_current_station);
        int j_index = ptr_current_station->GetIndex();
        while (!q.empty()) {
            Station* ptr_station;
            double i_min_dist = DBL_MAX;
            list<Station*>::iterator iter_min;
            for(list<Station*>::iterator iter_q = q.begin(); iter_q!=q.end(); ++iter_q) {
                //SG_DEBUG("iter_q index is: %d, iter_q->d2s_ is: %.2f, i_min_dist is: %.2f", (*iter_q)->GetIndex(), (*iter_q)->d2s_, i_min_dist);
                if ((*iter_q)->d2s_ <= i_min_dist) {
                    i_min_dist = (*iter_q)->d2s_;
                    iter_min = iter_q;
                }
            }
            ptr_station = (*iter_min);
            q.erase(iter_min);
            for(auto ptr_j : ptr_station->GetAdjacentStation()) {
                double dist;
                dist = DIST(ptr_j->x_, ptr_j->y_, ptr_station->x_, ptr_station->y_);
                double theta_point2robot3 = CheckThetaDiffer(ptr_station, ptr_j, current_x_, current_y_, current_theta_);
                int i_index = ptr_station->GetIndex();
                if (theta_point2robot3 < 0.523 && i_index == j_index) {
                    dist = dist + 1000;
                } else {
                    dist = dist;
                }
                //SG_DEBUG("ptr_j is: %d, ptr_station is: %d", ptr_j->GetIndex(), ptr_station->GetIndex());
                if(start_index_ == ptr_j->GetIndex() && ptr_station->GetIndex() == j_index) {
                    ptr_j->d2s_ = DBL_MAX;
                }
                //SG_DEBUG("dist is: %.2f, ptr_station->d2s_ is: %.2f, ptr_j->d2s_ is: %.2f", dist, ptr_station->d2s_, ptr_j->d2s_);
                if (dist + ptr_station->d2s_ < ptr_j->d2s_) {
                    ptr_j->d2s_ = dist + ptr_station->d2s_;
                    ptr_j->ptr_from_ = ptr_station;
                    q.push_back(ptr_j);
                    //SG_DEBUG("##3 ptr_j->d2s_: %.2f, ptr_j->from: %d", ptr_j->d2s_, ptr_j->ptr_from_->GetIndex());
                }
            }
        }
    } else {
        //SG_DEBUG("Have no obstacle!");
        q.push_back(ptr_current_station);
        while (!q.empty()) {
            Station* ptr_station;
            double i_min_dist = DBL_MAX;
            list<Station*>::iterator iter_min;
            for(list<Station*>::iterator iter_q = q.begin(); iter_q!=q.end(); ++iter_q) {
                //SG_DEBUG("* iter_q index is: %d, iter_q->d2s_ is: %.2f, i_min_dist is: %.2f", (*iter_q)->GetIndex(), (*iter_q)->d2s_, i_min_dist);
                if ((*iter_q)->d2s_ <= i_min_dist) {
                    i_min_dist = (*iter_q)->d2s_;
                    iter_min = iter_q;
                    //SG_DEBUG("# iter_q index is: %d, iter_q->d2s_ is: %.2f, i_min_dist is: %.2f", (*iter_q)->GetIndex(), (*iter_q)->d2s_, i_min_dist);
                }
            }
            ptr_station = (*iter_min);
            q.erase(iter_min);
            for(auto ptr_j : ptr_station->GetAdjacentStation()) {
                double dist;
                dist = DIST(ptr_j->x_, ptr_j->y_, ptr_station->x_, ptr_station->y_);
                //SG_DEBUG("ptr_j is: %d, ptr_station is: %d", ptr_j->GetIndex(), ptr_station->GetIndex());
                //SG_DEBUG("dist is: %.2f, ptr_station->d2s_ is: %.2f, ptr_j->d2s_ is: %.2f", dist, ptr_station->d2s_, ptr_j->d2s_);
                if (dist + ptr_station->d2s_ < ptr_j->d2s_) {
                    ptr_j->d2s_ = dist + ptr_station->d2s_;
                    ptr_j->ptr_from_ = ptr_station;
                    q.push_back(ptr_j);
                    //SG_DEBUG("##3 ptr_j->d2s_: %.2f, ptr_j->from: %d", ptr_j->d2s_, ptr_j->ptr_from_->GetIndex());
                }
            }
        }
    }
    //SG_DEBUG("list_q is empty!");
}

double StationGraph::CheckThetaDiffer(Station* ptr_s, Station* ptr_e, double px, double py, double ptheta)
{
    double theta_line = normRad(atan2((ptr_e->y_ - ptr_s->y_), (ptr_e->x_ - ptr_s->x_)));
    double deflection = ABS(normRad(theta_line - ptheta));
#if 0
    SG_DEBUG("ptr_e_y: %.2f, ptr_s_y: %.2f, ptr_e_x: %.2f, ptr_s_x: %.2f",
             ptr_e->y_, ptr_s->y_, ptr_e->x_, ptr_s->x_);
    SG_DEBUG("theta_line: %.2f, robot theta: %.2f, deflection: %.2f", theta_line, ptheta, deflection);
#endif
    return deflection;
}

double StationGraph::CheckCurStation(Station *ptr_s, double px, double py, double ptheta)
{
    SG_DEBUG("CheckCurStation()! ");
    double theta_line = normRad(atan2((ptr_s->y_ - py), (ptr_s->x_ - px)));
    double deflection = ABS(normRad(theta_line - ptheta));
    SG_DEBUG("deflection is:%f", deflection);
    return deflection;
}

Station* StationGraph::HasStation(Station* ptr_target)
{
    for (auto& ptr : stations_) {
        if ((*ptr).GetIndex() == (*ptr_target).GetIndex()) {
            return ptr;
        }
    }
    return NULL;
}
} // namespace station_graph
