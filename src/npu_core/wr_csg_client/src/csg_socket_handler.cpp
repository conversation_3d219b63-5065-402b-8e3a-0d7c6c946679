#include "csg_socket_handler.h"

#include <stdio.h>
#include <stdlib.h>

#include <iostream>
#include <iomanip>
#include <string>
#include <string.h>
#include <algorithm>

#include <sys/time.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <unistd.h>
#include <fcntl.h>
#include <arpa/inet.h>

#include <Ice/Ice.h>
#include <ros/ros.h>

#include <errno.h>
#include <error.h>

#include <npu_log.h>

#define ENDIAN_EXCHANGE_HALFWORD(x) ((((x)&0xFF00)>>8)+(((x)&0x00FF)<<8))
#define ENDIAN_EXCHANGE_WORD(x) ((((x)&0xFF000000)>>24)+(((x)&0x00FF0000)>>8)+(((x)&0x0000FF00)<<8)+(((x)&0x000000FF)<<24))

namespace  wizrobo {

static double normRad(double rad)
{
    while (rad >= (M_PI)) {
        rad -= 2.0 * M_PI;
    }
    while (rad <= (-M_PI)) {
        rad += 2.0 * M_PI;
    }
    return rad;
}

CsgSocketHandler::CsgSocketHandler(CsgCache* ptr_csg_cache)
    : is_inited_(false)
    , is_registed_(false)
    , ptr_csg_cache_(ptr_csg_cache)
    , socket_(-1)
#if 1
    , server_ip_("************")
#else
    , server_ip_("*************") // "*************", "**************"
#endif
    , server_port_(25556)
    , ptr_rb_free_(recv_buffer_)
    , rb_avaliable_length_(0)
    , rb_free_length_(BUFFER_LENGTH)
    , patrol_arrive_node_serial_("")
    , patrol_arrive_node_mode_(WAIT_FOR_MOVING)
{
    action_func_map_["regist"] = std::bind(&CsgSocketHandler::ActionRegist, this);
    action_func_map_["move"] = std::bind(&CsgSocketHandler::ActionMove, this);
    action_func_map_["gspostionparamadd"] = std::bind(&CsgSocketHandler::ActionGsPostionParamAdd, this);
    action_func_map_["gspostionparammodify"] = std::bind(&CsgSocketHandler::ActionGsPostionParamModify, this);
    action_func_map_["gspostionparamdelete"] = std::bind(&CsgSocketHandler::ActionGsPostionParamDelete, this);
    action_func_map_["routeline"] = std::bind(&CsgSocketHandler::ActionPatrolStartPathPlan, this);
    action_func_map_["arrivenode"] = std::bind(&CsgSocketHandler::ActionPatrolArriveNode, this);
    action_func_map_["stop"] = std::bind(&CsgSocketHandler::ActionStop, this);
    action_func_map_["charge"] = std::bind(&CsgSocketHandler::Gotocharge, this);
    action_func_map_["gsshutdown"] = std::bind(&CsgSocketHandler::ShutDown, this);
    action_func_map_["gsstart"] = std::bind(&CsgSocketHandler::StartNavi, this);
    action_func_map_["gsstop"] = std::bind(&CsgSocketHandler::StopNavi, this);
    action_func_map_["gsmapswitch"] = std::bind(&CsgSocketHandler::SwitchMap, this);
    action_func_map_["heartbeat"] = std::bind(&CsgSocketHandler::ActionHeartBeat, this);
    task_q_.clear();

    ros::NodeHandle prv_nh("~");
    api_test_ = prv_nh.advertiseService("api_test", &CsgSocketHandler::ApiTestCallback, this);
}

bool CsgSocketHandler::Init()
{
    LM_DEBUG("Init()!");
    return true;
}

#if 1
void CsgSocketHandler::Run()
{
    ros::Time heart_beats_stamp = ros::Time::now();
    while (ros::ok()) {
        usleep(10000);
        if (!socket_connected_) {
            DoConnect();
            continue;
        }
        RecvOnce();
        ProcessRecvBuffer();
        if (!is_registed_) {
            DoRegist();
            continue;
        }
        float heart_beats_interval = (ros::Time::now() - heart_beats_stamp).toSec();
        if ( heart_beats_interval > 30) {
            if (heart_beating_ == true) {
                heart_beating_ = false;
                ReportHeartBeat();
            } else {
                is_registed_ = false;
                socket_connected_ = false;
                close(socket_);
                socket_ = -1;
                LM_ERROR("heart_beating failed! Reset socket");
                continue;
            }
            heart_beats_stamp = ros::Time::now();
        }
        ReportArriveCrossing();
        ReportRosExceptionState();
        ReportRosRealState();
        ReportChargeHome();
        ReportPatrolArriveNode();
        CompletedStartNavi();
        CompletedStopNavi();
        CompletedSwitchMap();
    }
}
#else
void CsgSocketHandler::Run()
{
    while (ros::ok()) {
        //LM_ERROR("<=");
        usleep(10*1000);
        RecvOnce();
        ProcessRecvBuffer();
    }
}
#endif
void CsgSocketHandler::HeartBeat()
{
}

void CsgSocketHandler::ActionRegist() //Tcp Connect
{
    string result = GetValueFromPtree("result");
    is_registed_ = (result == "0000")?true:false;
    if(is_registed_) {
        counter_ = 666;
    }
    return;
}

void CsgSocketHandler::ActionMove() //Manual remote control
{
    int angle = GetIntValueFromPtree("angle");
    int mode = GetIntValueFromPtree("mode");
    double speed = GetFloatValueFromPtree("speed");
    string serial = GetValueFromPtree("serial");

    if(mode != ptr_csg_cache_->action_move_.mode) {
        ptr_csg_cache_->stop_mode_ = mode;
        ptr_csg_cache_->action_move_.mode = mode;
        ptr_csg_cache_->enb_action_move_ = false;
        return;
    }

    ptr_csg_cache_->enb_action_move_ = true;
    ptr_csg_cache_->action_move_.angle = angle;
    ptr_csg_cache_->action_move_.spd = speed;

    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "Move");
    pt_data.add<string>("result", "0000");
    pt_data.add<string>("msg", "");
    pt_data.add<string>("serial", serial);
    SendDataFrame(pt_data);
}

void CsgSocketHandler::Gotocharge()  //Get charge instruction
{
    string action = GetValueFromPtree("action");
    chargeSerial_ = GetValueFromPtree("serial");
    LM_DEBUG("action: %s serial: %s", action.c_str(), chargeSerial_.c_str());

    ptr_csg_cache_->arrived_charge_point_ = true;
}

void CsgSocketHandler::ShutDown()
{
    string action = GetValueFromPtree("action");
    string serial = GetValueFromPtree("serial");
    LM_DEBUG("action: %s serial: %s", action.c_str(), serial.c_str());

    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", action);
    pt_data.add<string>("result", "0000");
    pt_data.add<string>("serial", serial);
    boost::property_tree::ptree pt_content;
    pt_content.add<string>("msg", "GSShutDown");
    pt_data.add_child("content", pt_content);
    SendDataFrame(pt_data);

    ptr_csg_cache_->shut_down_ = true;
}

void CsgSocketHandler::StartNavi()
{
    string action = GetValueFromPtree("action");
    string serial = GetValueFromPtree("serial");
    LM_DEBUG("action: %s serial: %s", action.c_str(), serial.c_str());
    startNavi_serial_ = serial;

    ptr_csg_cache_->stop_navi_ = false;
    ptr_csg_cache_->start_navi_ = true;
}

void CsgSocketHandler::StopNavi()
{
    string action = GetValueFromPtree("action");
    string serial = GetValueFromPtree("serial");
    LM_DEBUG("action: %s serial: %s", action.c_str(), serial.c_str());

    stopNavi_serial_ = serial;

    ptr_csg_cache_->start_navi_ = false;
    ptr_csg_cache_->stop_navi_ = true;
}

void CsgSocketHandler::SwitchMap()
{
    string action = GetValueFromPtree("action");
    string serial = GetValueFromPtree("serial");
    string map_name = GetValueFromPtree("mapCode");
    LM_DEBUG("action: %s serial: %s", action.c_str(), serial.c_str());

    switchMap_serial_ = serial;

    ptr_csg_cache_->switched_map = map_name;
    ptr_csg_cache_->enb_switch_map_ = true;
}

void CsgSocketHandler::ActionGsPostionParamAdd() //Upload robot pose
{
    string serial = GetValueFromPtree("serial");
    boost::property_tree::ptree pt_node_type = ptree_cmd_.get_child("nodeType");
    string site = pt_node_type.get<string>("site", "n");
    string corner = pt_node_type.get<string>("corner", "n");
    string chargeRoom = pt_node_type.get<string>("chargeRoom", "n");
    string chargePile = pt_node_type.get<string>("chargePile", "n");
    string freePoint = pt_node_type.get<string>("freePoint", "n");
    LM_DEBUG("chargePile is: %s", chargePile.c_str());
    LM_DEBUG("chargeRoom is: %s", chargeRoom.c_str());
    LM_DEBUG("corner is: %s", corner.c_str());
    LM_DEBUG("site is: %s", site.c_str());
    LM_DEBUG("freePoint is: %s", freePoint.c_str());

    ptr_csg_cache_->station_graph_.station_index_ += 1;
    station_graph::Station* ptr_station = new station_graph::Station(ptr_csg_cache_->station_graph_.station_index_);
    ptr_station->x_ = GetFloatValueFromPtree("x");;
    ptr_station->y_ = GetFloatValueFromPtree("y");;
    ptr_station->theta_ = GetFloatValueFromPtree("orientation");
    if(site.compare("Y") == 0) {
        ptr_station->csg_station_ = true;
    } else {
        ptr_station->csg_station_ = false;
    }
    if(corner.compare("Y") == 0) {
        ptr_station->inflection_point_ = true;
    } else {
        ptr_station->inflection_point_ = false;
    }
    if(chargeRoom.compare("Y") == 0) {
        int cnt_ = ptr_csg_cache_->station_graph_.stations_.size();
        list<station_graph::Station *>::iterator iter_station = ptr_csg_cache_->station_graph_.stations_.begin();
        for(int i=0; i<cnt_; i++) {
            if((*iter_station)->is_charge_station_) {
                (*iter_station)->is_charge_station_ = false;
            }
            ++iter_station;
        }
        ptr_station->is_charge_station_ = true;
        ptr_csg_cache_->charge_station_x_ = ptr_station->x_;
        ptr_csg_cache_->charge_station_y_ = ptr_station->y_;
    } else {
        ptr_station->is_charge_station_ = false;
    }
    if(chargePile.compare("Y") == 0) {
        int cnt_ = ptr_csg_cache_->station_graph_.stations_.size();
        list<station_graph::Station *>::iterator iter_station = ptr_csg_cache_->station_graph_.stations_.begin();
        for(int i=0; i<cnt_; i++) {
            if((*iter_station)->is_charger_) {
                (*iter_station)->is_charger_ = false;
            }
            ++iter_station;
        }
        ptr_station->is_charger_ = true;
        ptr_csg_cache_->charger_x_ = ptr_station->x_;
        ptr_csg_cache_->charger_y_ = ptr_station->y_;
    } else {
        ptr_station->is_charger_ = false;
    }
    if(freePoint.compare("Y") == 0) {
        ptr_station->enb_navi_arrived_ = true;
    } else {
        ptr_station->enb_navi_arrived_ = false;
    }
    ptr_csg_cache_->station_graph_.InsertStation(ptr_station);
    ptr_csg_cache_->station_graph_.DumpToFile();

    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "GSPostionParamAdd");
    pt_data.add<string>("serial", serial);
    pt_data.add<string>("result", "0000");
    pt_data.add<int>("msg", ptr_station->GetIndex());
    pt_data.add<float>("x", ptr_station->x_);
    pt_data.add<float>("y", ptr_station->y_);
    pt_data.add<float>("orientation", ptr_station->theta_);
    LM_DEBUG("AddNewPoint!!!!!!!\n");
    SendDataFrame(pt_data);
    return;
}

void CsgSocketHandler::ActionGsPostionParamModify()
{
    string serial = GetValueFromPtree("serial");
    double x = GetFloatValueFromPtree("x");
    double y = GetFloatValueFromPtree("y");
    string id = GetValueFromPtree("id");
    boost::property_tree::ptree pt_node_type = ptree_cmd_.get_child("nodeType");
    string site = pt_node_type.get<string>("site", "n");
    string corner = pt_node_type.get<string>("corner", "n");
    string chargeRoom = pt_node_type.get<string>("chargeRoom", "n");
    string chargePile = pt_node_type.get<string>("chargePile", "n");
    string freePoint = pt_node_type.get<string>("freePoint", "n");

    if(chargeRoom.compare("Y") == 0) {
        int cnt_ = ptr_csg_cache_->station_graph_.stations_.size();
        list<station_graph::Station *>::iterator iter_station_ = ptr_csg_cache_->station_graph_.stations_.begin();
        for(int i=0; i<cnt_; i++) {
            if((*iter_station_)->is_charge_station_) {
                (*iter_station_)->is_charge_station_ = false;
            }
            ++iter_station_;
        }
        ptr_csg_cache_->station_graph_.DumpToFile();
    }

    if(chargePile.compare("Y") == 0) {
        int cnt_ = ptr_csg_cache_->station_graph_.stations_.size();
        list<station_graph::Station *>::iterator iter_station_ = ptr_csg_cache_->station_graph_.stations_.begin();
        for(int i=0; i<cnt_; i++) {
            if((*iter_station_)->is_charger_) {
                (*iter_station_)->is_charger_ = false;
            }
            ++iter_station_;
        }
        ptr_csg_cache_->station_graph_.DumpToFile();
    }

    int cnt_ = ptr_csg_cache_->station_graph_.stations_.size();
    list<station_graph::Station *>::iterator iter_station = ptr_csg_cache_->station_graph_.stations_.begin();
    for(int i=0; i<cnt_; i++) {
        if(x == (*iter_station)->x_ && y == (*iter_station)->y_) {
            if(site.compare("Y") == 0) {
                (*iter_station)->csg_station_ = true;
            } else {
                (*iter_station)->csg_station_ = false;
            }
            if(corner.compare("Y") == 0) {
                (*iter_station)->inflection_point_ = true;
            } else {
                (*iter_station)->inflection_point_ = false;
            }
            if(freePoint.compare("Y") == 0) {
                (*iter_station)->enb_navi_arrived_ = true;
            } else {
                (*iter_station)->enb_navi_arrived_ = false;
            }
            if(chargeRoom.compare("Y") == 0) {
                (*iter_station)->is_charge_station_ = true;
                ptr_csg_cache_->charge_station_x_ = (*iter_station)->x_;
                ptr_csg_cache_->charge_station_y_ = (*iter_station)->y_;
            } else {
                (*iter_station)->is_charge_station_ = false;
            }
            if(chargePile.compare("Y") == 0) {
                (*iter_station)->is_charger_ = true;
                ptr_csg_cache_->charger_x_ = x;
                ptr_csg_cache_->charger_y_ = y;
            } else {
                (*iter_station)->is_charger_ = false;
            }
            ptr_csg_cache_->station_graph_.DumpToFile();
            break;
        }
        ++iter_station;
    }

    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "GSPostionParamModify");
    pt_data.add<string>("serial", serial);
    pt_data.add<string>("result", "0000");
    pt_data.add<int>("msg", (*iter_station)->GetIndex());
    pt_data.add<float>("x", (*iter_station)->x_);
    pt_data.add<float>("y", (*iter_station)->y_);
    pt_data.add<string>("id", id);
    LM_DEBUG("ModifyPoint!!!!!!!\n");
    SendDataFrame(pt_data);
    return;
}

void CsgSocketHandler::ActionGsPostionParamDelete()
{
    string serial = GetValueFromPtree("serial");
    double x = GetFloatValueFromPtree("x");
    double y = GetFloatValueFromPtree("y");

    int cnt_ = ptr_csg_cache_->station_graph_.stations_.size();
    list<station_graph::Station *>::iterator iter_station = ptr_csg_cache_->station_graph_.stations_.begin();
    for(int i=0; i<cnt_; i++) {
        if(x == (*iter_station)->x_ && y == (*iter_station)->y_) {
            ptr_csg_cache_->station_graph_.DeleteStation((*iter_station));
            ptr_csg_cache_->station_graph_.DumpToFile();
            LM_DEBUG("DumpToFile done!");
            break;
        }
        ++iter_station;
    }
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "GSPostionParamDelete");
    pt_data.add<string>("serial", serial);
    pt_data.add<string>("result", "0000");
    pt_data.add<string>("msg", "");
    pt_data.add<float>("x", x);
    pt_data.add<float>("y", y);
    LM_DEBUG("DeletePoint!!!!!!!\n");
    SendDataFrame(pt_data);
    return;
}

void CsgSocketHandler::ActionPatrolStartPathPlan()  //Planning path according to task
{
    string serial = GetValueFromPtree("serial");
    boost::property_tree::ptree pt_map_node_list = ptree_cmd_.get_child("mapNodeList");
    boost::property_tree::ptree pt_data;
    list<station_graph::Station*> station_list;
    list<list<station_graph::Station*>> path_;

    auto iter_map_node = pt_map_node_list.begin();
    while (iter_map_node != pt_map_node_list.end()) {
        boost::property_tree::ptree::value_type& map_node = (*iter_map_node);
        string id = map_node.second.get<string>("id", "");
        double x = map_node.second.get<float>("x", 0.0);
        double y = map_node.second.get<float>("y", 0.0);
        station_graph::Station* ptr = ptr_csg_cache_->station_graph_.GetStationPtr(x, y);
        if (ptr == NULL) {
            ptr = ptr_csg_cache_->station_graph_.stations_.back();
            LM_DEBUG("Can NOT find station[%s](%.2f, %.2f)", id.c_str(), x, y);
            pt_data.add<string>("action", "RouteLine");
            pt_data.add<string>("result", "0001");
            pt_data.add<string>("msg", "Can NOT find station.");
            pt_data.add<string>("serial", serial);
            SendDataFrame(pt_data);
            return;
        }
        LM_DEBUG("Find station[%02d](%.2f, %.2f)", ptr->GetIndex(), ptr->x_, ptr->y_);
        ptr->id_ = id;
        station_list.push_back(ptr);
        iter_map_node++;
    }
    LM_DEBUG("Get %ld stations, %ld is avaliable.", pt_map_node_list.size(), station_list.size());

    if (station_list.empty()) {
        pt_data.add<string>("action", "RouteLine");
        pt_data.add<string>("result", "0001");
        pt_data.add<string>("msg", "station_list is empty.");
        pt_data.add<string>("serial", serial);
        SendDataFrame(pt_data);
        return;
    }
#if 1
    bool is_block = false;
#else
    bool is_block = ptr_csg_cache_->ros_status_.have_obstacle_;
#endif

    ptr_csg_cache_->goal_path_ = station_list;
    ptr_csg_cache_->station_graph_.MakePlan(station_list, is_block);
    path_ = ptr_csg_cache_->station_graph_.paths_;

    if (path_.empty()) {
        pt_data.add<string>("action", "RouteLine");
        pt_data.add<string>("result", "0001");
        pt_data.add<string>("msg", "Make plan failed.");
        pt_data.add<string>("serial", serial);
        SendDataFrame(pt_data);
        return;
    }
    pt_data.add<string>("action", "RouteLine");
    pt_data.add<string>("result", "0000");
    pt_data.add<string>("msg", "");
    pt_data.add<string>("serial", serial);

    int i = 0;
    pt_map_node_list.clear();
    LM_DEBUG("all path_.size(): %d", static_cast<int>(path_.size()));
    for (auto ptr_path : path_) {
        LM_DEBUG("==> all node of path: %d", static_cast<int>(ptr_path.size()));
        for(auto ptr_station : ptr_path) {
            LM_DEBUG("[%02d](%.2f, %.2f, %d, %d)", ptr_station->GetIndex(),
                      ptr_station->x_, ptr_station->y_,
                      ptr_station->inflection_point_, ptr_station->csg_station_);
            boost::property_tree::ptree pt_map_node;
            pt_map_node.add<int>("no", i);
            if(!ptr_station->inflection_point_) {
                pt_map_node.add<string>("id", ptr_station->id_);
            }else if(ptr_station->csg_station_) {
                pt_map_node.add<string>("id", ptr_station->id_);
            }else {
                pt_map_node.add<string>("id", "");
            }
            pt_map_node.add<float>("x", ptr_station->x_);
            pt_map_node.add<float>("y", ptr_station->y_);
            pt_map_node_list.push_back(std::make_pair("", pt_map_node));
            i++;
        }
    }
    pt_data.add_child("mapNodeList", pt_map_node_list);
    SendDataFrame(pt_data);
    LM_DEBUG("Done");
}


void CsgSocketHandler::FiltPath(list<station_graph::Station *> &path, list<station_graph::Station *> &filted_path)
{
    LM_DEBUG("FiltPath!");
    const float angle = 30.0 / 180.0 * 3.1415926;
    int path_length = path.size();
    LM_DEBUG("path length is: %d", path_length);
    if(path_length <= 0) {
        LM_DEBUG("There is no pose in path.");
        return;
    } else if(path_length >= 2) {
        filted_path.clear();
        list<station_graph::Station *>::iterator iter_temp = path.begin();
        list<station_graph::Station *>::iterator iter_s_station = iter_temp;
        iter_temp++;
        list<station_graph::Station *>::iterator iter_e_station = iter_temp;
        float x_s = (*iter_s_station)->x_;
        float y_s = (*iter_s_station)->y_;
        LM_DEBUG("The first point x is: %f", x_s);
        LM_DEBUG("The first point y is: %f", y_s);
        float x_m = ptr_csg_cache_->ros_status_.act_pose_x_;
        float y_m = ptr_csg_cache_->ros_status_.act_pose_y_;
        LM_DEBUG("The second point x is: %f", x_m);
        LM_DEBUG("The second point y is: %f", y_m);
        float x_e = (*iter_e_station)->x_;
        float y_e = (*iter_e_station)->y_;
        LM_DEBUG("The third point x is: %f", x_e);
        LM_DEBUG("The third point y is: %f", y_e);

        float dx_m2s = x_m - x_s;
        float dy_m2s = y_m - y_s;
        float theta_m2s = normRad(atan2(dy_m2s, dx_m2s));

        float dx_e2m = x_e - x_m;
        float dy_e2m = y_e - y_m;
        float theta_e2m = normRad(atan2(dy_e2m, dx_e2m));

        float angle_differ = ABS(normRad(theta_e2m - theta_m2s));
        LM_DEBUG("angle_differ is: %f/%f", angle_differ, angle);
        if(angle_differ > angle) {
            filted_path = path;
            return;
        }
        float dist_f2s = DIST(x_s, y_s, x_e, y_e);
        float dist_c2s = DIST(x_m, y_m, x_e, y_e);
        LM_DEBUG("The first distance is: %f", dist_f2s);
        LM_DEBUG("The second distance is: %f", dist_c2s);
        if(dist_f2s > dist_c2s) {
            path.erase(iter_s_station);
            filted_path = path;
            LM_DEBUG("fileted_path1 length is: %ld", filted_path.size());
            return;
        }
    }
    filted_path = path;
    LM_DEBUG("fileted_path2 length is: %ld", filted_path.size());
}

void CsgSocketHandler::ActionPatrolArriveNode() //Perform current task
{
    patrol_arrive_node_serial_ = GetValueFromPtree("serial");
    boost::property_tree::ptree pt_map_node = ptree_cmd_.get_child("mapNode");
    double x = pt_map_node.get<float>("x", 0.0);
    double y = pt_map_node.get<float>("y", 0.0);

    LM_DEBUG("Goal(%.2f, %.2f), Current(%.2f, %.2f), differ(%.2f, %.2f)",
             x, y, ptr_csg_cache_->ros_status_.act_pose_x_, ptr_csg_cache_->ros_status_.act_pose_y_,
             fabs(x-ptr_csg_cache_->ros_status_.act_pose_x_), fabs(y-ptr_csg_cache_->ros_status_.act_pose_y_));

    /*** Check if the task point is the current location ***/
    /*** if task point is the current station ***/
    /*** don't move and report arrived ***/
    if(fabs(x-ptr_csg_cache_->ros_status_.act_pose_x_) < 0.15
            && fabs(y-ptr_csg_cache_->ros_status_.act_pose_y_) < 0.15) {
        LM_WARN("Target station is coinside!");
        boost::property_tree::ptree pt_data;
        pt_data.add<string>("action", "ArriveNode");
        pt_data.add<string>("result", "0000");
        pt_data.add<string>("msg", "");
        pt_data.add<string>("serial", patrol_arrive_node_serial_);
        patrol_arrive_node_serial_ = "";
        SendDataFrame(pt_data);
        return;
    }
    /*** Check if the task point is the current location ***/

    station_graph::Station* ptr_start = ptr_csg_cache_->station_graph_.ptr_current_station_;
    if (ptr_start == NULL) {
         LM_WARN("Current station is NULL!");
         boost::property_tree::ptree pt_data;
         pt_data.add<string>("action", "ArriveNode");
         pt_data.add<string>("result", "0001");
         pt_data.add<string>("msg", "Current station is NULL!");
         pt_data.add<string>("serial", patrol_arrive_node_serial_);
         patrol_arrive_node_serial_ = "";
         SendDataFrame(pt_data);
         return;
    }
    station_graph::Station* ptr_end = ptr_csg_cache_->station_graph_.GetStationPtr(x, y);
    if (!ptr_end) {
        LM_WARN("can NOT find target station(%.2f, %.2f)", x, y);
        boost::property_tree::ptree pt_data;
        pt_data.add<string>("action", "ArriveNode");
        pt_data.add<string>("result", "0002");
        pt_data.add<string>("msg", "Can NOT find target station!");
        pt_data.add<string>("serial", patrol_arrive_node_serial_);
        patrol_arrive_node_serial_ = "";
        SendDataFrame(pt_data);
        return;
    }
    bool is_block = ptr_csg_cache_->ros_status_.have_obstacle_;

    /*** Debug ***/
//    bool is_block;
//    if(ptr_csg_cache_->ros_exception_state_.pavement_ == 0) {
//        is_block = true;
//        LM_DEBUG("is_block is: %d", is_block);
//    } else {
//        is_block = false;
//        LM_DEBUG("is_block is: %d", is_block);
//    }
    /*** Debug ***/

    list<station_graph::Station *> path = ptr_csg_cache_->station_graph_.FindPath(ptr_end, ptr_start, is_block);
    LM_DEBUG("Here is [%02d](%.2f, %.2f), arriving to [%02d](%.2f, %.2f) is_block: %d",
              ptr_start->GetIndex(), ptr_start->x_, ptr_start->y_,
              ptr_end->GetIndex(), ptr_end->x_, ptr_end->y_, is_block);
    for (auto ptr_pose : path) {
        LM_DEBUG("[%s](%.4f, %.4f)", ptr_pose->id_.c_str(), ptr_pose->x_, ptr_pose->y_);
    }
#if 1
    list<station_graph::Station *> filted_path;
    FiltPath(path, filted_path);
    ptr_csg_cache_->SetNewPath(filted_path);
#else
    ptr_csg_cache_->SetNewPath(path);
#endif
//    ptr_csg_cache_->current_path_ = path;
    if (ptr_csg_cache_->cur_navi_state_ != wizrobo_npu::ACTIVE) {
        patrol_arrive_node_mode_ = WAIT_FOR_MOVING;
    } else {
        patrol_arrive_node_mode_ = WAIT_FOR_STOP;
    }
}

void CsgSocketHandler::ReportPatrolArriveNode() //Whether to reach the mission point
{
    if (patrol_arrive_node_serial_ == "") {
//        LM_DEBUG("Have no serial!");
        return;
    }
    if (patrol_arrive_node_mode_ == WAIT_FOR_MOVING
            && ptr_csg_cache_->cur_navi_state_ != wizrobo_npu::ACTIVE) {
//        LM_DEBUG("Wait for move and not active!");
        return;
    } else {
//        LM_INFO("Wait for stop or active!");
        patrol_arrive_node_mode_ = WAIT_FOR_STOP;
    }
    if (patrol_arrive_node_mode_ == WAIT_FOR_STOP
            && ptr_csg_cache_->cur_navi_state_ != wizrobo_npu::SUCCEEDED) {
//        LM_DEBUG("Wait for stop and not succeeded!");
        return;
    } else {
        patrol_arrive_node_mode_ = WAIT_FOR_MOVING;
//        LM_INFO("Wait for move or succeeded");
    }
    int n = ptr_csg_cache_->need_report_;
    LM_DEBUG("need report is: %d", n);
    if(n != 1) {
        LM_DEBUG("Don't need report!");
        return;
    }
    if(ptr_csg_cache_->station_graph_.ptr_current_station_->GetIndex() != ptr_csg_cache_->goal_id_) {
        return;
    };
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "ArriveNode");
    pt_data.add<string>("result", "0000");
    pt_data.add<string>("msg", "");
    pt_data.add<string>("serial", patrol_arrive_node_serial_);
    patrol_arrive_node_serial_ = "";
    LM_DEBUG("Send Arrived Goal!");
    SendDataFrame(pt_data);
    ptr_csg_cache_->need_report_ = -1;

}

void CsgSocketHandler::ActionStop() //Cancel the current task and let the robot stop
{
    LM_DEBUG("ActionStop");
    string serial = GetValueFromPtree("serial");
    ptr_csg_cache_->cancel_task_ = true;
    ptr_csg_cache_->action_stop_ = true;
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "Stop");
    pt_data.add<string>("result", "0000");
    pt_data.add<string>("serial", serial);
    SendDataFrame(pt_data);
}

void CsgSocketHandler::DoRegist()   //Registration message
{    
#if 0
    std::string msg =
            "{\"action\":\"Regist\",\"deviceId\":\"ROS\",\"serial\":\"LM000\"}";
    SendDataFrame(msg);
#else
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "Regist");
    pt_data.add<string>("deviceId", "ROS");
    pt_data.add<string>("serial", "LM000");
    SendDataFrame(pt_data);
#endif
}

void CsgSocketHandler::ReportArriveCrossing()
{
    if(ptr_csg_cache_->station_graph_.ptr_current_station_ != NULL){
        if(inflection_no_bk_ == ptr_csg_cache_->station_graph_.ptr_current_station_->GetIndex()) {
            return;
        }
        string serial = GetValueFromPtree("serial");
        boost::property_tree::ptree pt_data;
        pt_data.add<string>("action", "ArriveCrossing");
        pt_data.add<string>("serial", serial);
        pt_data.add<double>("x", ptr_csg_cache_->station_graph_.ptr_current_station_->x_);
        pt_data.add<double>("y", ptr_csg_cache_->station_graph_.ptr_current_station_->y_);
        LM_DEBUG("Arrive crossing [%d](%.2f, %.2f).",
                 ptr_csg_cache_->station_graph_.ptr_current_station_->GetIndex(),
                 ptr_csg_cache_->station_graph_.ptr_current_station_->x_,
                 ptr_csg_cache_->station_graph_.ptr_current_station_->y_);
        SendDataFrame(pt_data);
        inflection_no_bk_ = ptr_csg_cache_->station_graph_.ptr_current_station_->GetIndex();
        return;
    }
    return;
}

void CsgSocketHandler::ReportRosExceptionState()    //Upload power status
{
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "RosState");
    boost::property_tree::ptree pt_content;
//    LM_DEBUG("pavement is: %d", ptr_csg_cache_->ros_exception_state_.pavement_);
    pt_content.add<int>("pavement", ptr_csg_cache_->ros_exception_state_.pavement_);
    string s,s01,s02,s03,s04,s05,s06,s07,s08,s09,s10,s11,s12;
    s01 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[0]);
    s02 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[1]);
    s03 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[2]);
    s04 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[3]);
    s05 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[4]);
    s06 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[5]);
    s07 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[6]);
    s08 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[7]);
    s09 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[8]);
    s10 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[9]);
    s11 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[10]);
    s12 = to_string(ptr_csg_cache_->ros_exception_state_.power_single_[11]);
    s.append(s01).append(",").append(s02).append(",").append(s03).append(",").append(s04).append(",").append(s05).append(",").append(s06).append(",").append(s07).append(",").append(s08).append(",").append(s09).append(",").append(s10).append(",").append(s11).append(",").append(s12);
    pt_content.add<string>("powerNode", s);
    pt_content.add<float>("powerTemp", ptr_csg_cache_->ros_exception_state_.power_temp_);
    pt_content.add<float>("power", ptr_csg_cache_->ros_exception_state_.power_);
    pt_content.add<int>("powerCharge", ptr_csg_cache_->ros_exception_state_.power_charge_);
    pt_content.add<string>("infrared", "Have no~");
    pt_content.add<string>("boxTemp", "Have no~");
    pt_content.add<string>("stray", "Have no~");
    pt_data.add_child("content", pt_content);
    SendDataFrame(pt_data);
}

void CsgSocketHandler::ReportRosRealState() //Upload robot status
{
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "RosRealState");
    boost::property_tree::ptree pt_content;
    pt_content.add<string>("mapName", ptr_csg_cache_->station_graph_.map_name_);
    pt_content.add<double>("x", ptr_csg_cache_->ros_status_.act_pose_x_);
    pt_content.add<double>("y", ptr_csg_cache_->ros_status_.act_pose_y_);
    boost::property_tree::ptree pt_speed;
    pt_speed.add<float>("carSpeed", ptr_csg_cache_->ros_status_.speed_.carSpeed);
    pt_speed.add<float>("flSpeed",ptr_csg_cache_->ros_status_.speed_.flSpeed);
    pt_speed.add<float>("frSpeed",ptr_csg_cache_->ros_status_.speed_.frSpeed);
    pt_speed.add<float>("rlSpeed",ptr_csg_cache_->ros_status_.speed_.rlSpeed);
    pt_speed.add<float>("rrSpeed",ptr_csg_cache_->ros_status_.speed_.rrSpeed);
    pt_content.add_child("speed", pt_speed);
    boost::property_tree::ptree pt_direction;
    pt_direction.add<float>("carDirection", ptr_csg_cache_->ros_status_.act_pose_theta_);
//    pt_direction.add<int>("flTire", 0);
//    pt_direction.add<int>("frTire", 0);
//    pt_direction.add<int>("rlTire", 0);
//    pt_direction.add<int>("rrTire", 0);
    pt_content.add_child("direction", pt_direction);
    pt_content.add<int>("model", ptr_csg_cache_->ros_status_.mode_);
    pt_content.add<double>("current", ptr_csg_cache_->ros_status_.charge_current_);
    pt_content.add<double>("voltage", ptr_csg_cache_->ros_status_.charge_voltage_);
    pt_content.add<double>("odometry", ptr_csg_cache_->ros_status_.odometry_);
    pt_data.add_child("content", pt_content);
    SendDataFrame(pt_data);
}

void CsgSocketHandler::ReportChargeHome()
{
    if(ptr_csg_cache_->arrived_charge_home_) {
//        string serial = GetValueFromPtree("serial");
//        LM_INFO("Serial is: %s", serial.c_str());
        boost::property_tree::ptree pt_data;
        pt_data.add<string>("action", "Charge");
        pt_data.add<string>("result", "0000");
        pt_data.add<string>("serial", chargeSerial_);
        SendDataFrame(pt_data);
        ptr_csg_cache_->arrived_charge_home_ = false;
        ptr_csg_cache_->charge_known = true;
    }
    return;
}

void CsgSocketHandler::ReportHeartBeat()
{
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "HEARTBEAT");
    boost::property_tree::ptree pt_content;
    pt_content.add<string>("msg", "Test");
    pt_data.add_child("content", pt_content);
    SendDataFrame(pt_data);
}

bool CsgSocketHandler::DoConnect()
{
    const int retry_interval = 30;
    static int failed_flag = false;
    static ros::Time failed_stamp;
    socket_connected_ = false;
    heart_beating_ = false;
    float interval = (ros::Time::now()-failed_stamp).toSec();
#if 0
    LM_ERROR("failed_flag: %d, interval: %.2f(%d)", failed_flag, interval, retry_interval);
#endif
    if (failed_flag == true && interval < retry_interval) {
        return false;
    }
    failed_flag = false;
    if (socket_ < 0) {
        socket_ = socket(PF_INET, SOCK_STREAM, 0);
        int flags = fcntl(socket_, F_GETFL, 0);
        fcntl(socket_, F_SETFL, flags);
    }
    if(socket_ < 0) {
        LM_DEBUG("Aolloc socket failed!\n");
        return false;
    }
    struct sockaddr_in serv_addr;
    memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_addr.s_addr = inet_addr(server_ip_.c_str());
    serv_addr.sin_port = htons(server_port_);
    int res = connect(socket_, (struct sockaddr*) &serv_addr, sizeof(serv_addr));
    LM_ERROR("res: %d", res);
    if(res != 0) {
        failed_flag = true;
        failed_stamp = ros::Time::now();
        LM_ERROR("Connection failed! Will try again after 30secs!");
        return false;
    }

    LM_ERROR("Connection successed!");
    failed_flag = false;
    socket_connected_ = true;
    heart_beating_ = true;
    return true;
}

static void print_memory(char *p, int length)
{
    auto _func_is_v = [] (char c) -> bool {
        return ( 32 <= c && c <= 126);
    };
    int i = 0;
    stringstream ss1(""), ss2("");
    while (i < length) {
        if (i % 64 == 0){
            ROS_DEBUG("> %s | %s", ss1.str().c_str(), ss2.str().c_str());
            ss1.str("");
            ss2.str("");
        }
        char c = *(p+i);
        unsigned char uc = (unsigned char)c;
        unsigned int ui = (unsigned int)uc;
        ss1 << hex << setw(2) << setfill('0') << ui << " ";
        c = (_func_is_v(c))?c:'?';
        ss2 << c;
        i++;
    }
}

static int make_frame(char *p)
{
    const int seq[] = {0, 15, 4, 7, 3, 0, 0, 10, 1};
    static int i = 0;
    int len = 0;
    if (i<9) {
        for(int j=0;j<seq[i];j++) {
            boost::property_tree::ptree pt;
            pt.add("action", "Move");
            pt.add("angle", "0");
            pt.add("mode", "0");
            pt.add("role", "LOCAL");
            stringstream s_serial;
            s_serial << "a9344f5f354b4bfdbca75e6b5832c7" << i << j;
            pt.add("serial", s_serial.str());
            pt.add("speed", "0.1");
            pt.add("userId", "100");
            stringstream ss;
            boost::property_tree::write_json(ss, pt, false);
            string s = ss.str();
            *(p+0) = 0x00;
            *(p+1) = 0x00;
            *(p+2) = 0x00;
            *(p+3) = 0x00;
            *(p+4) = 0x00;
            *(p+5) = static_cast<unsigned char>(s.size()-1);
            for(int n=0;n<s.size()-1;n++) {
                *(p+6+n) = s[n];
            }
            len += (6 + s.size()-1);
            p += (6 + s.size()-1);
        }
        i++;
    }
    return len;
}

#if 1
void CsgSocketHandler::RecvOnce()
{
#define RecvOnce_LogOn 0
    try{
        if (socket_ < 0 || !socket_connected_) {
            return;
        }
        fd_set fds;
        timeval timeout = {1, 0};
        FD_ZERO(&fds);
        FD_SET(socket_, &fds);
        rtn_ = select(socket_+1, &fds, NULL, NULL, &timeout); //Is there a readable file(1 is yes!).
        if (rtn_ <= 0) {
            return;
        }
        if (FD_ISSET(socket_, &fds)) {
#if RecvOnce_LogOn
            LM_DEBUG("len: %d, rb_avaliable_length_: %d, rb_free_length_: %d", len, rb_avaliable_length_, rb_free_length_);
            print_memory(ptr_rb_free_, 1024);
#endif
            int len = read(socket_, ptr_rb_free_+rb_avaliable_length_, rb_free_length_);//The number of bytes read from the file, less than 0 is error.
            if (len < 0) {
                return;
            }
#if RecvOnce_LogOn
            LM_DEBUG("len: %d, rb_avaliable_length_: %d, rb_free_length_: %d", len, rb_avaliable_length_, rb_free_length_);
            print_memory(ptr_rb_free_, 1024);
#endif
            rb_avaliable_length_ += len;
            rb_free_length_ -= len;
        }
    } catch(wizrobo_npu::NpuException &e) {
        LM_DEBUG("Anthor client connected");
    }
    return;
}
#else
void CsgSocketHandler::RecvOnce()
{
#define RecvOnce_LogOn 0
    try {
#if RecvOnce_LogOn
        LM_DEBUG("*");
        LM_DEBUG("len: --, rb_avaliable_length_: %d, rb_free_length_: %d", rb_avaliable_length_, rb_free_length_);
        print_memory(ptr_rb_free_, 1024);
#endif
        //The number of bytes read from the file, less than 0 is error.
        int len = make_frame(&(recv_buffer_[rb_avaliable_length_]));
        if (len < 0) {
            return;
        }
#if RecvOnce_LogOn
        LM_DEBUG("*");
        LM_DEBUG("len: %d, rb_avaliable_length_: %d, rb_free_length_: %d", len, rb_avaliable_length_, rb_free_length_);
        print_memory(ptr_rb_free_, 1024);
#endif
        rb_avaliable_length_ += len;
        rb_free_length_ -= len;

    } catch(wizrobo_npu::NpuException &e) {
        LM_DEBUG("Anthor client connected");
    }
    return;
}
#endif
void CsgSocketHandler::ProcessRecvBuffer()  //Parsing message
{
#define ProcessRecvBuffer_LogOn 0
    try{
        if(rtn_ < 0 || rtn_ == 0) {
            return;
        }
        if (rb_avaliable_length_ < RB_HEADER_LENGTH) {
            return;
        }
        if (recv_buffer_[0] != 0x00 || recv_buffer_[1] != 0x00) {
            int i = 1;
#if ProcessRecvBuffer_LogOn
            {
                LM_WARN("*");
                LM_WARN("-Invalid data, drop -- byte, rb_avaliable_length_ is: %d, rb_free_length_: %d", rb_avaliable_length_, rb_free_length_);
                LM_WARN("-%02X %02X %02X %02X %02X %02X",
                         static_cast<unsigned char>(recv_buffer_[0]), static_cast<unsigned char>(recv_buffer_[1]),
                         static_cast<unsigned char>(recv_buffer_[2]), static_cast<unsigned char>(recv_buffer_[3]),
                         static_cast<unsigned char>(recv_buffer_[4]), static_cast<unsigned char>(recv_buffer_[5]));
                string s((char*)(&recv_buffer_[DATA_OFFSET]));
                LM_WARN("s: %s", s.c_str());
                print_memory(recv_buffer_, 1024);
            }
#endif
            for (;i<rb_avaliable_length_;i++) {
                if (recv_buffer_[i] == 0x00) {
                    break;
                }
            }
            rb_avaliable_length_ -= i;
            rb_free_length_ += i;

#if 0
            memcpy(&(recv_buffer_[0]), &(recv_buffer_[i]), rb_avaliable_length_);
#else
            for (int j=0;j<rb_avaliable_length_;j++) {
                recv_buffer_[j] = recv_buffer_[j+i];
                recv_buffer_[j+i] = 0x00;
            }
#endif

#if ProcessRecvBuffer_LogOn
            {
                LM_WARN("*");
                LM_WARN("+Invalid data, drop %d byte, rb_avaliable_length_ is: %d, rb_free_length_: %d", i, rb_avaliable_length_, rb_free_length_);
                LM_WARN("-%02X %02X %02X %02X %02X %02X",
                         static_cast<unsigned char>(recv_buffer_[0]), static_cast<unsigned char>(recv_buffer_[1]),
                         static_cast<unsigned char>(recv_buffer_[2]), static_cast<unsigned char>(recv_buffer_[3]),
                         static_cast<unsigned char>(recv_buffer_[4]), static_cast<unsigned char>(recv_buffer_[5]));
                string s((char*)(&recv_buffer_[DATA_OFFSET]));
                LM_WARN("s: %s", s.c_str());
                print_memory(recv_buffer_, 1024);
            }
#endif
            return;
        }
        int length = ENDIAN_EXCHANGE_WORD(*((int *)(&(recv_buffer_[LEN_OFFSET]))));
#if ProcessRecvBuffer_LogOn
        LM_WARN("length(%d) ? rb_avaliable_length_(%d)-RB_HEADER_LENGTH(%d) ? ", length, rb_avaliable_length_, RB_HEADER_LENGTH);
        LM_WARN("recv_buffer_[%d+%d]: %02X", DATA_OFFSET, length, static_cast<unsigned char>(recv_buffer_[DATA_OFFSET+length]));
#endif
        if ((rb_avaliable_length_-RB_HEADER_LENGTH) < length ) {
            return;
        }
        recv_buffer_[RB_HEADER_LENGTH+length] = 0x00;
        stringstream ss(string((char*)(&recv_buffer_[DATA_OFFSET])));
#if ProcessRecvBuffer_LogOn
        LM_WARN("ss[%ld]: %s", ss.str().size(), ss.str().c_str());
#endif

#if ProcessRecvBuffer_LogOn
        {
            LM_DEBUG("*");
            LM_DEBUG("-Valid data, drop -- byte, rb_avaliable_length_ is: %d, rb_free_length_: %d", rb_avaliable_length_, rb_free_length_);
            LM_DEBUG("-%02X %02X %02X %02X %02X %02X",
                     static_cast<unsigned char>(recv_buffer_[0]), static_cast<unsigned char>(recv_buffer_[1]),
                     static_cast<unsigned char>(recv_buffer_[2]), static_cast<unsigned char>(recv_buffer_[3]),
                     static_cast<unsigned char>(recv_buffer_[4]), static_cast<unsigned char>(recv_buffer_[5]));
            string s((char*)(&recv_buffer_[RB_HEADER_LENGTH]));
            LM_DEBUG("s: %s", s.c_str());
            print_memory(recv_buffer_, 1024);
        }
#endif
        rb_avaliable_length_ -= (length + RB_HEADER_LENGTH);
        rb_free_length_ += (length + RB_HEADER_LENGTH);

#if 0
        memcpy(&(recv_buffer_[0]), &(recv_buffer_[length+RB_HEADER_LENGTH]), rb_avaliable_length_);
#else
        for (int i=0;i<rb_avaliable_length_;i++) {
            recv_buffer_[i] = recv_buffer_[i+length+RB_HEADER_LENGTH];
            recv_buffer_[i+length+RB_HEADER_LENGTH] = 0x00;
        }
#endif

#if ProcessRecvBuffer_LogOn
        {
            LM_DEBUG("*");
            LM_DEBUG("+Valid data, drop %d byte, rb_avaliable_length_ is: %d, rb_free_length_: %d", length + RB_HEADER_LENGTH, rb_avaliable_length_, rb_free_length_);
            LM_DEBUG("+%02X %02X %02X %02X %02X %02X",
                     static_cast<unsigned char>(recv_buffer_[0]), static_cast<unsigned char>(recv_buffer_[1]),
                     static_cast<unsigned char>(recv_buffer_[2]), static_cast<unsigned char>(recv_buffer_[3]),
                     static_cast<unsigned char>(recv_buffer_[4]), static_cast<unsigned char>(recv_buffer_[5]));
            string s((char*)(&recv_buffer_[DATA_OFFSET]));
            LM_DEBUG("s: %s", s.c_str());
            print_memory(recv_buffer_, 1024);
        }
#endif
        string action;
        try {
            boost::property_tree::json_parser::read_json(ss, ptree_cmd_);
            action = GetValueFromPtree("action");
        } catch (boost::property_tree::ptree_error& e) {
            LM_ERROR("what(): %s", e.what());
            action = "error";
        }
        LM_DEBUG("action: %s", action.c_str());
        transform(action.begin(), action.end(), action.begin(), (int (*)(int))tolower);
        auto iter = action_func_map_.find(action);
        if (iter != action_func_map_.end()) {
            ((*iter).second)();
        } else {
            LM_WARN("Action[\"%s\"] is NOT supported!", action.c_str());
        }
#if 0
        LM_DEBUG("Valid data, rb_avaliable_length_(%d)/rb_free_length_(%d)",
                  rb_avaliable_length_, rb_free_length_);
#endif
    } catch(wizrobo_npu::NpuException &e) {
        LM_WARN("NpuException: %s", e.msg.c_str());
    }
}

void CsgSocketHandler::SendDataFrame(const char* json_str)
{
    if (!socket_connected_) {
        return;
    }
    int length = 0;
    for (length=0;json_str[length]!='\0';length++);
    send_buffer_[0] = 0x00;
    send_buffer_[1] = 0x00;
    send_buffer_[2] = (char)(0x000000FF & length>>24);
    send_buffer_[3] = (char)(0x000000FF & length>>16);
    send_buffer_[4] = (char)(0x000000FF & length>>8);
    send_buffer_[5] = (char)(0x000000FF & length>>0);
#if 0
    LM_DEBUG("%02X %02X %02X %02X %02X %02X [%d]",
              send_buffer_[0], send_buffer_[1], send_buffer_[2], send_buffer_[3],
            send_buffer_[4], send_buffer_[5], length);
    LM_DEBUG("%s", json_str);
#endif
    memcpy(&(send_buffer_[DATA_OFFSET]), json_str, length);
    int total_length = length + RB_HEADER_LENGTH;
    write(socket_, send_buffer_, total_length);
}

void CsgSocketHandler::CompletedStartNavi()
{
    if(!ptr_csg_cache_->completedStartNavi_) {
        return;
    }
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "GSStart");
    pt_data.add<string>("result", "0000");
    pt_data.add<string>("serial", startNavi_serial_);
    SendDataFrame(pt_data);

    ptr_csg_cache_->completedStartNavi_ = false;
}

void CsgSocketHandler::CompletedStopNavi()
{
    if(!ptr_csg_cache_->completedStopNavi_) {
        return;
    }
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "GSStop");
    pt_data.add<string>("result", "0000");
    pt_data.add<string>("serial", stopNavi_serial_);
    boost::property_tree::ptree pt_content;
    pt_content.add<string>("msg", "CompletedStopNavi");
    pt_data.add_child("content", pt_content);
    SendDataFrame(pt_data);

    ptr_csg_cache_->completedStopNavi_ = false;
}

void CsgSocketHandler::CompletedSwitchMap()
{
    if(!ptr_csg_cache_->completedSwitchMap_) {
        return;
    }
    LM_DEBUG("Complete switchmap is: %d", ptr_csg_cache_->completedSwitchMap_);
    boost::property_tree::ptree pt_data;
    pt_data.add<string>("action", "GSMapSwitch");
    pt_data.add<string>("result", "0000");
    pt_data.add<string>("serial", switchMap_serial_);
    pt_data.add<string>("mapCode", ptr_csg_cache_->switched_map);
    boost::property_tree::ptree pt_content;
    pt_content.add<string>("msg", "CompletedSwitchMap");
    pt_data.add_child("content", pt_content);
    SendDataFrame(pt_data);

    ptr_csg_cache_->completedSwitchMap_ = false;
}

void CsgSocketHandler::ActionHeartBeat()
{
    heart_beating_ = true;
    return;
}

void CsgSocketHandler::HeartbeatDetection()
{
}


bool CsgSocketHandler::ApiTestCallback(wr_npu_msgs::CsgControl::Request &req, wr_npu_msgs::CsgControl::Response &rep)
{
    string& json_str = req.csg_client_cmd;
    LM_INFO("json_str: %s", json_str.c_str());
    stringstream ss(json_str);
    boost::property_tree::json_parser::read_json(ss, ptree_cmd_);
    string action = GetValueFromPtree("action");
    auto iter = action_func_map_.find(action);
    if (iter != action_func_map_.end()) {
        ((*iter).second)();
    } else {
        LM_INFO("Action[\"%s\"] is NOT supported!", action.c_str());
    }
    return true;
}

} // namespace wizrobo
