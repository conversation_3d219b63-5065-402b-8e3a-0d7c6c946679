#include "station_graph.h"
#include "csg_socket_handler.h"

#include <stdlib.h>
#include <iostream>

int main_1(int argc, char** argv)
{
/*

    11-12-13     2--3--4
     |           |
    10--9--8--0--1--5
     |           |
    14-------15--6--7
*/
    int start_index = atoi(argv[1]);
    int end_index = atoi(argv[2]);
    std::string msg_a = "{ \
       \"0\": [ \"1\", \"8\" ], \
       \"1\": [ \"0\", \"2\", \"5\", \"6\" ], \
       \"2\": [ \"1\", \"3\" ], \
       \"3\": [ \"2\", \"4\" ], \
       \"4\": [ \"3\" ], \
       \"5\": [ \"1\" ], \
       \"6\": [ \"1\", \"7\", \"15\" ], \
       \"7\": [ \"6\" ], \
       \"8\": [ \"0\", \"9\" ], \
       \"9\": [ \"8\", \"10\" ], \
      \"10\": [ \"9\", \"11\" ], \
      \"11\": [ \"10\", \"12\" ], \
      \"12\": [ \"11\", \"13\" ], \
      \"13\": [ \"12\" ], \
      \"14\": [ \"10\", \"15\" ], \
      \"15\": [ \"6\", \"14\" ] \
    }";
    std::string msg_b = "{ \"5\" : [ \"25\", \"76\" ] }";

    boost::property_tree::ptree pt;
    stringstream ss(msg_a);
    boost::property_tree::json_parser::read_json(ss, pt);
    StationGraph station_graph;

    auto pt_iter = pt.begin();
    while (pt_iter != pt.end()) {
        boost::property_tree::ptree::value_type& element = (*pt_iter);
        int station_index = atoi(element.first.c_str());
        cout << "station_index: " << station_index << endl;
        Station* ptr_station = new Station(station_index);
        station_graph.InsertStation(ptr_station);
        pt_iter++;
    }
    pt_iter = pt.begin();
    while ( pt_iter != pt.end() ) {
        boost::property_tree::ptree::value_type& element = (*pt_iter);
        int station_index = atoi(element.first.c_str());
        cout << "station_index: " << station_index << " --> ";
        Station* ptr_station =  station_graph.GetStationPtr(station_index);
        boost::property_tree::ptree adjacent_array = element.second;
        auto adjacent_iter = adjacent_array.begin();
        while (adjacent_iter != adjacent_array.end()) {
            boost::property_tree::ptree::value_type& e = (*adjacent_iter);
            int adjacent_index = atoi(e.second.data().c_str());
            cout << adjacent_index << ", ";
            Station* ptr_adjacent = station_graph.GetStationPtr(adjacent_index);
            if (ptr_adjacent != NULL) {
                station_graph.AddPath(ptr_station, ptr_adjacent);
            }
            adjacent_iter++;
        }
        pt_iter++;
        cout << "X" << endl;
    }
    station_graph.PrintStationGraph();
    Station* ptr_start = station_graph.GetStationPtr(start_index);
    Station* ptr_end = station_graph.GetStationPtr(end_index);
    list<Station*> path = station_graph.FindPath(ptr_start, ptr_end);
    cout << "PATH: ";
    for( auto& node : path ) {
        cout << node->GetIndex() << ", ";
    }
    cout << "X" << endl;
    return 0;
}

int main_2(int argc, char** argv)
{
    wizrobo::CsgSocketHandler csg_h(NULL);
    csg_h.DoConnect();
    csg_h.Run();
    return 0;
}

int main(int argc, char** argv)
{
    return main_2(argc, argv);
}
