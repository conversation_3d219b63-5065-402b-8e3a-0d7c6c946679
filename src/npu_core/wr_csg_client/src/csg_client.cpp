#include <climits>

#include "csg_client.h"
#include "usblock.h"

#define CSG_DEBUG(fmt, arg...) ROS_DEBUG_NAMED(__FUNCTION__, "CsgServer::%s() " fmt, __FUNCTION__,##arg)
#define CSG_INFO(fmt, arg...)  ROS_INFO_NAMED(__FUNCTION__,  "CsgServer::%s() " fmt, __FUNCTION__,##arg)
#define CSG_WARN(fmt, arg...)  ROS_WARN_NAMED(__FUNCTION__,  "CsgServer::%s() " fmt, __FUNCTION__,##arg)
#define CSG_ERROR(fmt, arg...) ROS_ERROR_NAMED(__FUNCTION__, "CsgServer::%s() " fmt, __FUNCTION__,##arg)
#define CSG_FATAL(fmt, arg...) ROS_FATAL_NAMED(__FUNCTION__, "CsgServer::%s() " fmt, __FUNCTION__,##arg)

namespace wizrobo {

CsgClient::CsgClient(int argc, char **argv, std::string node_name)
{
    ros::init(argc, argv, node_name);
    ptr_cache_ = new CsgCache;
    ptr_socket_handler_ = new CsgSocketHandler(ptr_cache_);
    ptr_ros_handler_ = new CsgRosHandler(ptr_cache_);
}

void CsgClient::Init()
{
#if WR_LOCK == true
    if (!wizrobo::UsbLocker::Unlock()) {
        CSG_FATAL("Failed to unlock! Pls heck you usblock.");
        //return;
    }
#endif
    is_inited_ = false;
    ros::NodeHandle prv_nh;
    // logger level
    LoggerLevel logger_level;
    GetEnumParam<LoggerLevel>(prv_nh, "logger_level", logger_level, DEBUG);
    SetLoggerLevel(logger_level);

    if (!ptr_socket_handler_->Init()){
        CSG_ERROR("Socket handler init failed.");
        return;
    }
    if(!ptr_ros_handler_->Init()) {
        CSG_ERROR("Ros handler init failed.");
        return;
    }
    is_inited_ = true;
    return;
}

void CsgClient::Run()
{
    if (!IsInited()) {
        CSG_ERROR("Node is NOT inited.");
        return;
    }

    ptr_socket_thread_ = new boost::thread(boost::bind(&CsgSocketHandler::Run, ptr_socket_handler_));
    ptr_ros_thread_ = new boost::thread(boost::bind(&CsgRosHandler::Run, ptr_ros_handler_));

    ros::Rate r(1);

    ptr_cache_->PrintStationGraph();
    while (ros::ok()) {
        r.sleep();
#if 0
        static int i = 0;
        if ( i%10 == 0) {
            CSG_DEBUG("*");
        }
        i++;
#endif
    }
    CSG_DEBUG("The End.");
}



}// namespace
