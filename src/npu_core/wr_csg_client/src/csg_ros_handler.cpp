#include "csg_ros_handler.h"

#include <cmath>
#include <algorithm>

#define _IS_ROS_ 1
#if _IS_ROS_
#else
#  define LM_DEBUG(fmt, arg...) printf("CsgRosHdl::%s() " fmt "\n", __FUNCTION__, ##arg)
#  define LM_INFO(fmt, arg...)  printf("CsgRosHdl::%s() " fmt "\n", __FUNCTION__, ##arg)
#  define LM_WARN(fmt, arg...)  printf("CsgRosHdl::%s() " fmt "\n", __FUNCTION__, ##arg)
#  define LM_ERROR(fmt, arg...) printf("CsgRosHdl::%s() " fmt "\n", __FUNCTION__, ##arg)
#  define LM_FATAL(fmt, arg...) printf("CsgRosHdl::%s() " fmt "\n", __FUNCTION__, ##arg)
#endif

namespace wizrobo {

static double normRad(double rad)
{
    while (rad >= (M_PI)) {
        rad -= 2.0 * M_PI;
    }
    while (rad <= (-M_PI)) {
        rad += 2.0 * M_PI;
    }
    return rad;
}

bool CsgRosHandler::Init()
{
    LM_DEBUG("Init()");
    ros::NodeHandle nh;

    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform_, host_platform_);

    act_pose_sub_ = nh.subscribe<geometry_msgs::PoseStamped>("/wizrobo/pose", 1, &CsgRosHandler::ActPoseCallback_, this);//TODO: "/act_pose"
    act_vel_sub_ = nh.subscribe<geometry_msgs::Twist>("/act_vel", 1, &CsgRosHandler::ActVelCallback_, this);
    cmd_vel_sub_ = nh.subscribe<geometry_msgs::Twist>("/cmd_vel", 1, &CsgRosHandler::CmdVelCallback_, this);
    act_motor_spd_sub_ = nh.subscribe<wr_npu_msgs::MotorSpd>("/act_motor_spd", 1, &CsgRosHandler::ActMotorSpdCallback_, this);
    navi_status_sub_ = nh.subscribe<actionlib_msgs::GoalStatusArray>("/navi_core/status", 1, &CsgRosHandler::TaskStatusCallback_, this);// TODO: "/navi/status"
    navi_status_pf_sub_ = nh.subscribe<actionlib_msgs::GoalStatusArray>("/navi_core/status_pf", 10, &CsgRosHandler::TaskStatusPfCallback_, this);// TODO: "/navi/status"
    battery_data_sub_ = nh.subscribe<wr_npu_msgs::CsgBatterySonar>("/csgbatterysonar", 1, &CsgRosHandler::BatteryDataCallback_, this);
    reach_waypoint_ = nh.subscribe<std_msgs::Bool>("/reach_waypoint", 1, &CsgRosHandler::ReachWaypointCallback_, this);
    runtime_status_sub_ = nh.subscribe<wr_npu_msgs::RuntimeStatus>("/runtime_status", 1, &CsgRosHandler::RuntimeStatusCallback_, this);
    match_score_sub_ = nh.subscribe<std_msgs::Float32>("/matching_score", 1,&CsgRosHandler::MatchScoreCallback_, this);
    odom_distance_sub_ = nh.subscribe<std_msgs::Float32>("/odom_distance", 1, &CsgRosHandler::OdomDistanceCallback_, this);
    initial_pose_sub_ = nh.subscribe<geometry_msgs::PoseStamped>(STD_INITIAL_POSE_TOPIC_NAME, 1, &CsgRosHandler::InitialPoseCallback, this);
    initial_pose_area_sub_ = nh.subscribe<geometry_msgs::Polygon>("/initial_region", 1, &CsgRosHandler::InitialPoseAreaCallback, this);

    manual_cmd_pub_ = nh.advertise<geometry_msgs::Twist>("/manual_cmd_vel", 1, true);
    safe_cmd_pub_ = nh.advertise<geometry_msgs::Twist>("/safe_cmd_vel", 1, true);
    waypoints_pub_ = nh.advertise<geometry_msgs::PoseArray>("/waypoints", 1, true);
    navi_pub_ = nh.advertise<geometry_msgs::PoseStamped>("/navi_core_simple/goal", 1, true);
    stop_mode_pub_ = nh.advertise<std_msgs::Bool>("/stop_mode", 1, true);
    csg_station_pub_ = nh.advertise<wr_npu_msgs::StationArray>("/csg_stations", 1, true);
    csg_current_task_pub_ = nh.advertise<geometry_msgs::PoseArray>("/csg_current_task_list", 1, true);
    csg_current_path_pub_ = nh.advertise<geometry_msgs::PoseArray>("/csg_current_ptah_list", 1, true);
    csg_current_station_pub_ = nh.advertise<wr_npu_msgs::Station>("/csg_current_station", 1, true);
    csg_current_station_pose_pub_ = nh.advertise<geometry_msgs::PoseStamped>("/csg_current_station_pose", 1, true);
    csg_pre_station_pose_pub_ = nh.advertise<geometry_msgs::PoseStamped>("/csg_pre_station_pose", 1, true);
    csg_current_theta_pub_ = nh.advertise<std_msgs::Float32>("/current_theta", 1, true);

    client_ = nh.serviceClient<wr_npu_msgs::CsgMode>("/stopmode");
    client_npu_ = nh.serviceClient<wr_npu_msgs::CsgTsNpu>("/csg_transfer_npu");
    server_ = nh.advertiseService("/change_csg_station", &CsgRosHandler::ChangeCsgStation, this);
    server_path_ = nh.advertiseService("/change_csg_path", &CsgRosHandler::ChangeCsgPath, this);

    nh.param<float>(STD_TELEOP_PARAM_NS + "/csg/charge_cur_limit", charge_cur_limit_, charge_cur_limit_);
    LM_WARN("charge_cur_limit: %.2f", charge_cur_limit_);
    nh.param<float>(STD_TELEOP_PARAM_NS + "/csg/discharge_cur_limit", discharge_cur_limit_, discharge_cur_limit_);
    LM_WARN("discharge_cur_limit: %.2f", discharge_cur_limit_);
    nh.param<float>(STD_TELEOP_PARAM_NS + "/csg/charge_zero", charge_zero_, charge_zero_);
    LM_WARN("charge_zero: %.2f", charge_zero_);
    nh.param<float>(STD_TELEOP_PARAM_NS + "/csg/angle_filt_path", angle_filt_path_, angle_filt_path_);
    LM_WARN("angle_filt_path: %.2f", angle_filt_path_);
    float int_use_target_station_theta = 1.0;
    nh.param<float>(STD_TELEOP_PARAM_NS + "/csg/use_target_station_theta", int_use_target_station_theta, int_use_target_station_theta);
    use_target_station_theta_ = (int_use_target_station_theta>0.9)?true:false;
    LM_WARN("use_target_station_theta: %d", use_target_station_theta_);
    nh.param<float>(STD_TELEOP_PARAM_NS + "/csg/get_pose_dead_time", get_pose_dead_time_, get_pose_dead_time_);
    LM_WARN("get_pose_dead_time: %.2f", get_pose_dead_time_);

    return true;
}

void CsgRosHandler::Run()
{
    LM_INFO("Run");
    ros::Rate r(30);
    ros::Time stame1 = ros::Time::now();
    while(ros::ok()) {
        r.sleep();
        GetMapName();
        StartNavi();
        float interval = (ros::Time::now() - stame1).toSec();
        //LM_WARN("interval: %.2f", interval);
        if ( interval > get_pose_dead_time_) {
            GetActPose();
        }
        ptr_csg_cache_->cur_navi_state_ = GetNaviState();
        GetActVel();
        GetActMotorSpd();
        StopMode();
        if(ptr_csg_cache_->enb_action_move_) {
            RemoteMove();
        }
        StopMove();
        NeedReport();
        PubPath();
        PubCsgStation();
        PubCsgCurStation();
        PubCsgCurStationPose();
        PubCsgPreStationPose();
        PubTaskList();
        PubTaskPath();
        BackToChargeHome();
        JudgmentPoint();
        OutChargeHome();
        StopNavi();
        ShutDown();
        SelectMap();
        CancelTask();
        ros::spinOnce();
    }
}

void CsgRosHandler::GetActPose()
{
    if (act_pose_is_inited_ != true) {
        return;
    }
    ptr_csg_cache_->ros_status_.act_pose_x_ = act_pose_.x;
    ptr_csg_cache_->ros_status_.act_pose_y_ = act_pose_.y;
    ptr_csg_cache_->ros_status_.act_pose_theta_ = act_pose_.yaw;

    std_msgs::Float32 current_theta;
    current_theta.data = act_pose_.yaw;
    csg_current_theta_pub_.publish(current_theta);
    float interval = (ros::Time::now() - new_initial_pose_stamp_).toSec();
    if (get_new_initial_pose_ && interval > new_initial_interval_) {
        get_new_initial_pose_ = false;
        ptr_csg_cache_->UpdateCurrentStation(true);
        return;
    }
    ptr_csg_cache_->UpdateCurrentStation();
    return;
}

NpuState CsgRosHandler::GetNpuState()
{
    // LM_DEBUG("GetNpuState");
    transfer_npu_.request.mode = 5;
    if(client_npu_.call(transfer_npu_)) {
        int npu_state = transfer_npu_.response.npu_state;
        switch(npu_state) {
        case 0:
            return NpuState::IDLE_STATE;
            break;
        case 1:
            return NpuState::SLAM_STATE;
            break;
        case 2:
            return NpuState::NAVI_STATE;
            break;
        case 3:
            return NpuState::TELEOP_STATE;
            break;
        case 4:
            return NpuState::SWITCH_STATE;
            break;
        default:
            return NpuState::SLAM_NAVI_STATE;
            break;
        }
    }
    return NpuState::SWITCH_STATE;
}

NaviState CsgRosHandler::GetNaviState()
{
    // LM_DEBUG("GetNaviState");
    ros::NodeHandle n;
    int navi_mode = -1;
    n.getParam("/csg_navi_mode", navi_mode);
//    LM_WARN("In charge home is: %d", in_charge_home);
    if(navi_mode == 1 || is_in_charge_home_ == true){
        navi_mode_ = PF_NAVI;
        return navi_state_pf_;
    }else if(navi_mode == 0) {
        navi_mode_ = P2P_NAVI;
        return navi_state_;
    }
}

void CsgRosHandler::GetActVel() //Get act velocity
{
//    LM_DEBUG("GetActVel");
    ptr_csg_cache_->ros_status_.speed_.carSpeed = act_vel_.v_x;
    float v_x = act_vel_.v_x;
    if(v_x < 0.01 && v_x > -0.01) {
        ptr_csg_cache_->ros_status_.mode_ = 2;
    } else {
        ptr_csg_cache_->ros_status_.mode_ = 1;
    }
    return;
}

void CsgRosHandler::GetActMotorSpd()    //Get act motor speed
{
//    LM_DEBUG("GetActMotorSpd");
    if (act_motor_spd_.rpms.size() <= 0) {
        return;
    }
    ptr_csg_cache_->ros_status_.speed_.flSpeed = act_motor_spd_.rpms[0];
    ptr_csg_cache_->ros_status_.speed_.frSpeed = act_motor_spd_.rpms[1];
    ptr_csg_cache_->ros_status_.speed_.rlSpeed = act_motor_spd_.rpms[2];
    ptr_csg_cache_->ros_status_.speed_.rrSpeed = act_motor_spd_.rpms[3];
    return;
}


void CsgRosHandler::InitialPoseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg)
{
    get_new_initial_pose_ = true;
    new_initial_pose_stamp_ = ros::Time::now();
}

void CsgRosHandler::InitialPoseAreaCallback(const geometry_msgs::Polygon::ConstPtr& msg)
{
    get_new_initial_pose_ = true;
    new_initial_pose_stamp_ = ros::Time::now();
}

void CsgRosHandler::ActPoseCallback_(const geometry_msgs::PoseStamped::ConstPtr &message)
{
    act_pose_is_inited_ = true;
    act_pose_msg_ = *message;
    geometry_msgs::PoseStamped currPose = *message;
    tf::Quaternion quat;
    quat.setX(currPose.pose.orientation.x);
    quat.setY(currPose.pose.orientation.y);
    quat.setZ(currPose.pose.orientation.z);
    quat.setW(currPose.pose.orientation.w);
    act_pose_.x = currPose.pose.position.x;
    act_pose_.y = currPose.pose.position.y;
    if(currPose.pose.orientation.z > 0) {
        act_pose_.yaw = quat.getAngle(); // the default angle is positive
    } else {
        act_pose_.yaw = -quat.getAngle();
    }
    //LM_WARN("Current theta is: %.2f", act_pose_.yaw);
}

void CsgRosHandler::TaskStatusCallback_(const actionlib_msgs::GoalStatusArray::ConstPtr &status)
{
//    LM_DEBUG("TaskStatusCallback_()");
    if(!status->status_list.empty()) {//in case status_list is empty which will return error.
        actionlib_msgs::GoalStatus robotstatus = status->status_list[0];
        switch(robotstatus.status) {//change actionlib_msgs to int number for ice transfer.
        case  actionlib_msgs::GoalStatus::PENDING:
            navi_state_ = PENDING;
            break;
        case actionlib_msgs::GoalStatus::ACTIVE:
            navi_state_ = ACTIVE;
            break;
        case actionlib_msgs::GoalStatus::PREEMPTED:
            navi_state_ = PREEMPTED;
            break;
        case actionlib_msgs::GoalStatus::SUCCEEDED:
//            LM_WARN("navi success!");
            navi_state_ = SUCCEEDED;
            break;
        case actionlib_msgs::GoalStatus::ABORTED:
            navi_state_ = ABORTED;
            break;
        case actionlib_msgs::GoalStatus::REJECTED:
            navi_state_ = REJECTED;
            break;
        case actionlib_msgs::GoalStatus::PREEMPTING:
            navi_state_ = PREEMPTED;
            break;
        case actionlib_msgs::GoalStatus::RECALLING:
            navi_state_ = RECALLING;
            break;
        case actionlib_msgs::GoalStatus::RECALLED:
            navi_state_ = RECALLED;
            break;
        case actionlib_msgs::GoalStatus::LOST:
            navi_state_ = LOST;
            break;
        default:
            break;
        }
    }
    else {
        navi_state_ = IDLE;
    }
//    LM_DEBUG("TaskStatusCallback_() Done");
}

void CsgRosHandler::TaskStatusPfCallback_(const actionlib_msgs::GoalStatusArray::ConstPtr &status)
{
    LM_WARN("TaskStatusPfCallback_()");
    if(!status->status_list.empty()) {//in case status_list is empty which will return error.
        actionlib_msgs::GoalStatus robotstatus = status->status_list[0];
        LM_WARN("pf callback robot.status:%d",robotstatus.status);
        switch(robotstatus.status) {//change actionlib_msgs to int number for ice transfer.
        case  actionlib_msgs::GoalStatus::PENDING:
            navi_state_pf_ = PENDING;
            break;
        case actionlib_msgs::GoalStatus::ACTIVE:
            navi_state_pf_ = ACTIVE;
            break;
        case actionlib_msgs::GoalStatus::PREEMPTED:
            navi_state_pf_ = PREEMPTED;
            break;
        case actionlib_msgs::GoalStatus::SUCCEEDED:
            LM_WARN("pf success!");
            navi_state_pf_ = SUCCEEDED;
            break;
        case actionlib_msgs::GoalStatus::ABORTED:
            navi_state_pf_ = ABORTED;
            break;
        case actionlib_msgs::GoalStatus::REJECTED:
            navi_state_pf_ = REJECTED;
            break;
        case actionlib_msgs::GoalStatus::PREEMPTING:
            navi_state_pf_ = PREEMPTED;
            break;
        case actionlib_msgs::GoalStatus::RECALLING:
            navi_state_pf_ = RECALLING;
            break;
        case actionlib_msgs::GoalStatus::RECALLED:
            navi_state_pf_ = RECALLED;
            break;
        case actionlib_msgs::GoalStatus::LOST:
            navi_state_pf_ = LOST;
            break;
        default:
            break;
        }
    }
    else {
        navi_state_pf_ = IDLE;
    }
//    LM_DEBUG("TaskStatusPfCallback_() Done");
}

void CsgRosHandler::ActVelCallback_(const geometry_msgs::Twist::ConstPtr &msg)
{
//    LM_DEBUG("ActVelCallback_()");
    act_vel_.v_x = msg->linear.x;
    act_vel_.v_y = msg->linear.y;
    act_vel_.v_z = msg->linear.z;
    act_vel_.v_roll = msg->angular.x;
    act_vel_.v_pitch = msg->angular.y;
    act_vel_.v_yaw = msg->angular.z;
//    LM_DEBUG("ActVelCallback_() Done");
}

void CsgRosHandler::CmdVelCallback_(const geometry_msgs::Twist::ConstPtr &msg)
{
    cmd_vel_.v_x = msg->linear.x;
    cmd_vel_.v_yaw = msg->angular.z;
}

void CsgRosHandler::ActMotorSpdCallback_(const wr_npu_msgs::MotorSpd::ConstPtr &msg)
{
//    LM_DEBUG("ActMotorSpdCallback_()");
    act_motor_spd_.motor_num = msg->motor_num;
    act_motor_spd_.rpms.resize(msg->motor_num);
    for(int i = 0; i < msg->motor_num; i++) {
        act_motor_spd_.rpms[i] = msg->rpms[i];
    }
}

void CsgRosHandler::BatteryDataCallback_(const wr_npu_msgs::CsgBatterySonar::ConstPtr &msg)
{
    for(int i=0;i<msg->vol.size();i++) {
        ptr_csg_cache_->ros_exception_state_.power_single_[i] = msg->vol[i];
    }
    ptr_csg_cache_->ros_exception_state_.power_ = msg->soc;
    ptr_csg_cache_->ros_exception_state_.power_temp_ = msg->avrg_temperature;
    ptr_csg_cache_->ros_exception_state_.pavement_ = (msg->obstacle==0)?1:0;
    ptr_csg_cache_->ros_status_.charge_voltage_ = msg->total_voltage;
    ptr_csg_cache_->ros_status_.charge_current_ = msg->charge_current;

    if(msg->charge_current > charge_cur_limit_ && msg->discharge_current < charge_zero_) {
        ptr_csg_cache_->ros_exception_state_.power_charge_ = 1;
        /** ptr_csg_cache_->charge_known means if the charging state has been reported,
            it will be true after reported. */
        if(!ptr_csg_cache_->charge_known) {
            ptr_csg_cache_->arrived_charge_home_ = true;
        }
    } else if (msg->discharge_current > discharge_cur_limit_ && msg->charge_current < charge_zero_) {
        ptr_csg_cache_->ros_exception_state_.power_charge_ = 0;
        ptr_csg_cache_->charge_known = false;
    } else {
        ; // Do nothing.
    }
}

void CsgRosHandler::ReachWaypointCallback_(const std_msgs::Bool::ConstPtr &msg)
{
//    LM_DEBUG("ReachWaypointCallback_");
    bool arrive;
    arrive = msg->data;
    if(arrive) {
        ptr_csg_cache_->arrive_crossing_ = true;
    }
    ptr_csg_cache_->arrive_crossing_ = false;
}

void CsgRosHandler::RuntimeStatusCallback_(const wr_npu_msgs::RuntimeStatus::ConstPtr &msg)
{
    have_obstacle_ = (msg->runtime_status_1&msg->RT1_NAVI_OBSTACLE_BLOCK)?true:false;
    ptr_csg_cache_->ros_status_.have_obstacle_ = have_obstacle_;
    if (have_obstacle_) {
        LM_DEBUG("have_obstacle: %d, msg->runtime_status_1: %08X",
                  have_obstacle_, msg->runtime_status_1);
    }
}

void CsgRosHandler::MatchScoreCallback_(const std_msgs::Float32::ConstPtr &msg)
{
    match_score_data_ = msg->data;
//    LM_WARN("match_score_data is: %f", match_score_data_);
    return;
}

void CsgRosHandler::OdomDistanceCallback_(const std_msgs::Float32::ConstPtr &msgs)
{
    ptr_csg_cache_->ros_status_.odometry_ = msgs->data;
}

void CsgRosHandler::StopMode()
{
    if(ptr_csg_cache_->stop_mode_ == -1) {
        return;
    }
    int stop_mode_ = ptr_csg_cache_->stop_mode_;
    switch(stop_mode_) {
    case 0: {
        csg_mode_.request.mode = 0;
        if(client_.call(csg_mode_)) {
            LM_DEBUG("Call client 0");
        }
        ptr_csg_cache_->stop_mode_ = -1;
        break;
    }
    case 1: {
        csg_mode_.request.mode = 1;
        if(client_.call(csg_mode_)) {
            LM_DEBUG("Call client 1");
        }
        ptr_csg_cache_->stop_mode_ = -1;
        break;
    }
    case 2: {
        csg_mode_.request.mode = 2;
        if(client_.call(csg_mode_)) {
            LM_DEBUG("Call client 2");
        }
        ptr_csg_cache_->stop_mode_ = -1;
        break;
    }
    default:
        break;
    }
}

void CsgRosHandler::RemoteMove()
{
//    LM_DEBUG("RemoteMove");
    geometry_msgs::Twist vel_;
    int angle = ptr_csg_cache_->action_move_.angle;
    int mode = ptr_csg_cache_->action_move_.mode;
    double spd = ptr_csg_cache_->action_move_.spd;
    switch(mode) {
    case 0: {
        LM_WARN("Case 0!");
        if ((angle == 0 && spd > 0.01) || (angle == 0 && spd < -0.01)) {
            vel_.linear.x = spd;
            vel_.angular.z = 0;
            manual_cmd_pub_.publish(vel_);
        } else if(angle < 0) {
            vel_.linear.x = spd;
            vel_.angular.z = spd;
            manual_cmd_pub_.publish(vel_);
        } else if(angle > 0) {
            vel_.linear.x = spd;
            vel_.angular.z = -spd;
            manual_cmd_pub_.publish(vel_);
        } else {
            vel_.linear.x = 0;
            vel_.angular.z = 0;
            int ticks = 0;
            for(ticks; ticks < 5; ticks++) {
                manual_cmd_pub_.publish(vel_);
                ptr_csg_cache_->enb_action_move_ = false;
            }
        }
        break;
    }
    case 1: {
        if (spd > 0.01) {
            vel_.linear.x = 0;
            vel_.angular.z = spd;
            LM_WARN("turn left");
            manual_cmd_pub_.publish(vel_);
        } else if(spd < -0.01) {
            vel_.linear.x = 0;
            vel_.angular.z = spd;
            LM_WARN("turn right");
            manual_cmd_pub_.publish(vel_);
        } else {
            vel_.linear.x = 0;
            vel_.angular.z = 0;
            int ticks =0;
            for(ticks; ticks < 5; ticks++) {
                manual_cmd_pub_.publish(vel_);
                ptr_csg_cache_->enb_action_move_ = false;
            }
        }
//        LM_DEBUG("Case 1!");
        break;
    }
    case 2: {
        vel_.linear.x = 0;
        vel_.linear.y = 0;
        int ticks = 0;
        for(ticks; ticks < 5; ticks++) {
            manual_cmd_pub_.publish(vel_);
            ptr_csg_cache_->enb_action_move_ = false;
        }
        break;
    }
    default:
        ptr_csg_cache_->enb_action_move_ = false;
        break;
    }
}

void CsgRosHandler::StopMove()
{
    geometry_msgs::Twist vel_safe_;
    vel_safe_.linear.x = 0;
    vel_safe_.angular.z = 0;
    if (ptr_csg_cache_->action_stop_ == true) {
#if 0
        if(npu_state_ == NAVI_STATE) {
#else
        if(true) {
#endif
            LM_WARN("bash /npu.x86_64/script/task_csg_stop.sh &");
            system("bash /npu.x86_64/script/task_csg_stop.sh &");
#if 0
            LM_WARN("bash /npu.x86_64/script/task_stop.sh &");
            system("bash /npu.x86_64/script/task_stop.sh &");
            LM_WARN("bash /npu.x86_64/script/npu_stop.sh &");
            system("bash /npu.x86_64/script/npu_stop.sh &");
            npu_state_ = IDLE_STATE;
            nh.setParam("/npu_param/runtime/npu_state", EnumString<NpuState>::EnumToStr(npu_state_).c_str());
#endif
        }
        manual_cmd_pub_.publish(vel_safe_);
        ptr_csg_cache_->action_stop_ = false;
    }
    return;
}

/*** Optimize the path, the point on the same line, directly to the position of the last point ***/
void CsgRosHandler::FiltPath(list<station_graph::Station *>& path, list<station_graph::Station *>& filted_path)
{
    int path_length = path.size();
#if 0
    for (auto e : path) {
        LM_INFO("Origin: [%d](%.2f, %.2f, %.2f)", e->GetIndex(), e->x_, e->y_, e->theta_);
    }
#endif
    if (path_length <= 0) {
        LM_WARN("There is no pose in path.");
        return;
    } else if (path_length >= 3) {
        filted_path.clear();
        list<station_graph::Station *>::iterator iter_temp = path.begin();
        list<station_graph::Station *>::iterator iter_s_station = iter_temp;
        iter_temp++;
        list<station_graph::Station *>::iterator iter_m_station = iter_temp;
        iter_temp++;
        list<station_graph::Station *>::iterator iter_e_station = iter_temp;
        filted_path.push_back((*iter_s_station));
        while(iter_e_station != path.end()) {
            float x_s = (*iter_s_station)->x_;
            float y_s = (*iter_s_station)->y_;
            float x_m = (*iter_m_station)->x_;
            float y_m = (*iter_m_station)->y_;
            float x_e = (*iter_e_station)->x_;
            float y_e = (*iter_e_station)->y_;

            float dx_m2s = x_m - x_s;
            float dy_m2s = y_m - y_s;
            float theta_m2s = normRad(atan2(dy_m2s, dx_m2s));

            float dx_e2m = x_e - x_m;
            float dy_e2m = y_e - y_m;
            float theta_e2m = normRad(atan2(dy_e2m, dx_e2m));

            float angle_differ = ABS(normRad(theta_e2m - theta_m2s));

            if(angle_differ > angle_filt_path_) {
                LM_DEBUG("* S: %d, M: %d, E: %d, theta_m2s: %.2f, theta_e2m: %.2f, angle_differ: %.2f(%.2f), pushed back",
                         (*iter_s_station)->GetIndex(),
                         (*iter_m_station)->GetIndex(),
                         (*iter_e_station)->GetIndex(),
                         theta_m2s, theta_e2m, angle_differ, angle_filt_path_);
                filted_path.push_back((*iter_m_station));
                iter_s_station = iter_m_station;
            } else {
                LM_DEBUG("# S: %d, M: %d, E: %d, theta_m2s: %.2f, theta_e2m: %.2f, angle_differ: %.2f(%.2f), droped",
                         (*iter_s_station)->GetIndex(),
                         (*iter_m_station)->GetIndex(),
                         (*iter_e_station)->GetIndex(),
                         theta_m2s, theta_e2m, angle_differ, angle_filt_path_);
            }
            iter_e_station++;
            iter_m_station++;
        }
        filted_path.push_back((*iter_m_station));
    } else {
        filted_path = path;
    }
#if 1
    for (auto e : filted_path) {
//        LM_INFO("Filter: [%d](%.2f, %.2f, %.2f)", e->GetIndex(), e->x_, e->y_, e->theta_);
    }
#endif
}

void CsgRosHandler::PubPath()
{
    LM_DEBUG("");
    list<station_graph::Station *> path;
    list<station_graph::Station *> filted_path;

    ptr_csg_cache_->GetNewPath(path);
    if (path.empty() && path_backup_.empty()) {
        LM_DEBUG("");
        return;
    }
    if (!path.empty()) {
        path_backup_ = path;
        is_in_charge_home_ = false;
        LM_DEBUG("Get new path, reset path_backup_ and is_in_charge_home_");
        path.clear();
    }
    if (is_in_charge_home_) {
        LM_DEBUG("");
        return;
    }

    float dist_ = DIST(act_pose_.x, act_pose_.y, ptr_csg_cache_->charger_x_, ptr_csg_cache_->charger_y_);
    LM_DEBUG("Distance to charge home is: %.2f/%.2f, act_pose(%.2f, %.2f), charger(%.2f, %.2f)",
             dist_, charge_home_dist_threshold_,
             act_pose_.x, act_pose_.y, ptr_csg_cache_->charger_x_, ptr_csg_cache_->charger_y_);
    if(dist_ < charge_home_dist_threshold_) {
        is_in_charge_home_ = true;
        stringstream ss;
        ss << "bash /npu." << host_platform_ << "/script/task_run.sh "
           << "\""
           << "action_name:=2, action_args:=0.26, duration:=3, times:=1 taskloop:=false "
           << "map_name:=" << ptr_csg_cache_->station_graph_.map_name_
           << "\"" << "&\n";
        LM_INFO("In ChargeHome: %s", ss.str().c_str());
        system(ss.str().c_str());
        return;
    }

    LM_DEBUG("Origin Path, length: %ld.", path_backup_.size());
    for (auto ptr_pose : path_backup_) {
        LM_DEBUG("[%s](%.4f, %.4f, %.4f)",
                 ptr_pose->id_.c_str(), ptr_pose->x_, ptr_pose->y_, ptr_pose->theta_);
    }
    FiltPath(path_backup_, filted_path);
    path_backup_.clear();
    LM_DEBUG("Filted Path, length: %ld.", filted_path.size());
    for (auto ptr_pose : filted_path) {
        LM_DEBUG("[%s](%.4f, %.4f, %.4f)",
                 ptr_pose->id_.c_str(), ptr_pose->x_, ptr_pose->y_, ptr_pose->theta_);
    }
    ptr_csg_cache_->current_path_ = filted_path;
    ptr_csg_cache_->goal_id_ = filted_path.back()->GetIndex();

    bool target_is_charge_station = false;
    float target_x = filted_path.back()->x_;
    float target_y = filted_path.back()->y_;
    if(abs(target_x - ptr_csg_cache_->charge_station_x_) < 0.1
            && abs(target_y - ptr_csg_cache_->charge_station_y_) < 0.1){
        target_is_charge_station = true;
    }

    list<station_graph::Station *>::iterator iter_cur_station, iter_nxt_station;
    string param_to_task_x, param_to_task_y, param_to_task_theta, param_to_task_p, param_to_task;
    float theta_cur, theta_last;
    int enb_navi, pub_path_length;

    iter_cur_station = filted_path.begin();
    iter_nxt_station = iter_cur_station;
    iter_nxt_station++;

    param_to_task_x.append("x:=");
    param_to_task_y.append(" y:=");
    param_to_task_theta.append(" theta:=");
    param_to_task_p.append(" enb_navi:=");

    pub_path_length = filted_path.size();
    for(int i=0; i < pub_path_length; i++) {
        LM_DEBUG("i: %d(%d), current_station: (%.4f, %.4f, %.4f)", i, pub_path_length,
                 (*iter_cur_station)->x_, (*iter_cur_station)->y_, (*iter_cur_station)->theta_);

        string x_s = to_string((*iter_cur_station)->x_);
        param_to_task_x.append(x_s).append(",");
        string y_s = to_string((*iter_cur_station)->y_);
        param_to_task_y.append(y_s).append(",");

        if(target_is_charge_station){
            theta_cur = (*iter_cur_station)->theta_;
            LM_DEBUG("theta_cur: %.2f", theta_cur);
        } else if (pub_path_length == 1) {
            if (use_target_station_theta_) {
                theta_cur = (*iter_cur_station)->theta_;
                LM_DEBUG("theta_cur: %.2f", theta_cur);
            } else {
                double x_differ = (*iter_cur_station)->x_ - act_pose_.x;
                double y_differ = (*iter_cur_station)->y_ - act_pose_.y;
                theta_cur = NormRad(atan2(y_differ, x_differ));
                LM_DEBUG("theta_cur: %.2f, differ(%.2f, %.2f)", theta_cur, abs(x_differ), abs(y_differ));
            }
        } else if (i < (pub_path_length - 1)) {
            double x_differ = (*iter_nxt_station)->x_ - (*iter_cur_station)->x_;
            double y_differ = (*iter_nxt_station)->y_ - (*iter_cur_station)->y_;
            theta_cur = NormRad(atan2(y_differ, x_differ));
            theta_last = theta_cur;
            LM_DEBUG("theta_cur: %.2f, differ(%.2f, %.2f), cur_station(%.2f, %.2f, %.2f), nxt_station(%.2f, %.2f, %.2f)",
                     theta_cur, abs(x_differ), abs(y_differ),
                     (*iter_cur_station)->x_, (*iter_cur_station)->y_, (*iter_cur_station)->theta_,
                     (*iter_nxt_station)->x_, (*iter_nxt_station)->y_, (*iter_nxt_station)->theta_);
            ++iter_nxt_station;
        } else if (i == (pub_path_length - 1)) {
            theta_cur = use_target_station_theta_?(*iter_cur_station)->theta_:theta_last;
            LM_DEBUG("theta_cur: %.2f, use_target_station_theta_: %d",
                     theta_cur, use_target_station_theta_);
        }
        param_to_task_theta.append(to_string(theta_cur)).append(",");

        enb_navi = (*iter_cur_station)->enb_navi_arrived_;
        param_to_task_p.append(to_string(enb_navi)).append(",");
        ++iter_cur_station;
    }

    param_to_task.append(param_to_task_x);
    param_to_task.append(param_to_task_y);
    param_to_task.append(param_to_task_theta);
    param_to_task.append(param_to_task_p);
    stringstream ss;
    ss << "bash /npu.x86_64/script/task_csg_run.sh \"" << " " << param_to_task << "\"&\n";
    LM_INFO("%s", ss.str().c_str());
    system(ss.str().c_str());
}

#if 0  // for backup
void CsgRosHandler::PubPath()
{
    ros::NodeHandle nh;
    int angle = 15;
    nh.getParam("/csg_path_angle", angle);

    list<station_graph::Station *> path;
    list<station_graph::Station *> pub_path_;
    ptr_csg_cache_->GetNewPath(path);
    if (path.empty()) {
        return;
    }

    int cnt = path.size();
    LM_WARN("point num is: %d", cnt);
    if (cnt <= 0) {
        LM_WARN("There is no pose in path.");
        return;
    }
    pub_path_.clear();
    pub_path_.push_back(path.begin());
    if(cnt > 2) {
        list<station_graph::Station *>::iterator iter_s_station = path.begin();
        list<station_graph::Station *>::iterator iter_m_station;
        list<station_graph::Station *>::iterator iter_e_station;
        float x_s = (*iter_s_station)->x_;
        float y_s = (*iter_s_station)->y_;
        ++iter_s_station;
        iter_m_station = &(*iter_s_station);
        if(iter_m_station != path.end()) {
            float x_m = (*iter_m_station)->x_;
            float y_m = (*iter_m_station)->y_;
            ++iter_s_station;
            iter_e_station = &(*iter_s_station);
            while(iter_e_station != path.end()) {
                float x_differ_s = x_m - x_s;
                float y_differ_s = y_m - y_s;
                float theta_f_ = atan2(y_differ_s, x_differ_s)*180/M_PI;
                float x_e = (*iter_e_station)->x_;
                float y_e = (*iter_e_station)->y_;
                float x_differ_e = x_e - x_s;
                float y_differ_e = y_e - y_s;
                float theta_s_ = atan2(y_differ_e, x_differ_e)*180/M_PI;
                float angle_differ;
                if(((theta_f_ > 90 && theta_f_ < 180) && (theta_s_>-180 && theta_s_ < -90))
                        || ((theta_s_ > 90 && theta_s_ < 180) && (theta_f_>-180 && theta_f_ < -90))) {
                    angle_differ = 360-abs(theta_f_)-abs(theta_s_);
                }else {
                    angle_differ = abs(theta_f_ - theta_s_);
                }
                if(angle_differ < angle) {
                    x_m = x_e;
                    y_m = y_e;
                    ++iter_s_station;
                }else {
                    pub_path_.push_back((*iter_e_station));
                    ++iter_s_station;
                }
            }
        }
    }

    list<station_graph::Station *>::iterator iter_station = pub_path_.begin();
    string s;
    s.append("x:=");
    for(int i=0;i<cnt;i++) {
        float x;
        if(i == (cnt-1)){
            charge_x_ = (*iter_station)->x_;
            printf("charge_x_ is: %f", charge_x_);
            x = (*iter_station)->x_;
        }else {
            x = (*iter_station)->x_;
        }
        string x_s = to_string(x);
        s.append(x_s).append(",");
        ++iter_station;
    }

    list<station_graph::Station *>::iterator iter_station_y = pub_path_.begin();
    s.append(" ").append("y:=");
    for(int i=0;i<cnt;i++) {
        float y;
        if(i == (cnt-1)){
            charge_y_ = (*iter_station_y)->y_;
            printf("charge_y_ is: %f", charge_y_);
            y = (*iter_station_y)->y_;
        }else {
            y = (*iter_station_y)->y_;
        }
        string y_s = to_string(y);
        s.append(y_s).append(",");
        ++iter_station_y;
    }

    list<station_graph::Station *>::iterator iter_station_t = pub_path_.begin();
    s.append(" ").append("theta:=");
    for(int i=0;i<cnt;i++) {
        if(i < (cnt-1)) {
            LM_WARN("(cnt-1) i=: %d", i);
            float x_c = (*iter_station_t)->x_;
            float y_c = (*iter_station_t)->y_;
            ++iter_station_t;
            float x_n = (*iter_station_t)->x_;
            float y_n = (*iter_station_t)->y_;
            double x_differ = x_n - x_c;
            double y_differ = y_n - y_c;
            double theta_differ = atan2(y_differ, x_differ);
            if(i == (cnt-2)) {
                theta_d_ = theta_differ;
            }
            string theta_s = to_string(theta_differ);
            s.append(theta_s).append(",");
        }else if(i == (cnt-1)) {
            LM_WARN("(cnt) i=: %d", i);
//            --iter_station_t;
            string theta_s;
            if((charge_x_ - 44.91) < 0.1 && (charge_y_ - 17.47) < 0.1){
                float theta = (*iter_station_t)->theta_;
                theta_s = to_string(theta);
            }else {
                float theta = theta_d_;
                theta_s = to_string(theta);
            }
            s.append(theta_s).append(",");
            ++iter_station_t;
        }
    }

    list<station_graph::Station *>::iterator iter_station_e = pub_path_.begin();
    s.append(" ").append("enb_navi:=");
    for(int i=0;i<cnt;i++) {
        float enb_navi = (*iter_station_e)->enb_navi_arrived_;
        string enb_navi_s = to_string(enb_navi);
        s.append(enb_navi_s).append(",");
        ++iter_station_e;
    }
    ptr_csg_cache_->goal_id_ = pub_path_.back()->GetIndex();
    ros::NodeHandle nh;
    string host_platform;
    nh.param<string>("/npu_param/core/PLATFORM", host_platform, "");
    printf("%s ", s.c_str());
    stringstream ss;
    ss << "bash /npu.x86_64/script/task_csg_run.sh \"" << " "
       << s << "\"&\n";
    printf("%s", ss.str().c_str());
    system(ss.str().c_str());
}
#endif

void CsgRosHandler::NeedReport()
{
    ros::NodeHandle nh;
    int n_ = -1;
    nh.getParam("/need_get_navi_state", n_);
//    LM_WARN("need is: %d", n_);
    ptr_csg_cache_->need_report_ = n_;
}

void CsgRosHandler::PubCsgStation()
{
//    list<station_graph::Station *> station;
    wr_npu_msgs::StationArray msg;
    int cnt = ptr_csg_cache_->station_graph_.stations_.size();
//    if(cnt <= 0) {
////        LM_WARN("station list size is: %d", cnt);
//        return;
//    }
    msg.station_array.clear();
    msg.station_array.resize(cnt);
//    LM_WARN("msg.station_array: %d", static_cast<int>(msg.station_array.size()));
    list<station_graph::Station *>::iterator iter_station = ptr_csg_cache_->station_graph_.stations_.begin();
    for(int i=0;i<cnt;i++) {
        msg.station_array[i].id = (*iter_station)->GetIndex();
        msg.station_array[i].enb_navi_arrived = (*iter_station)->enb_navi_arrived_;
        msg.station_array[i].csg_station = (*iter_station)->csg_station_;
        msg.station_array[i].inflection_point = (*iter_station)->inflection_point_;
        msg.station_array[i].is_charger = (*iter_station)->is_charger_;
        msg.station_array[i].is_charge_station = (*iter_station)->is_charge_station_;
        msg.station_array[i].d2s = (*iter_station)->d2s_;
        msg.station_array[i].x = (*iter_station)->x_;
        msg.station_array[i].y = (*iter_station)->y_;
        msg.station_array[i].theta = (*iter_station)->theta_;
        for (auto ptr_adjacent : (*iter_station)->GetAdjacentStation()) {
            msg.station_array[i].adjacent_id.push_back(ptr_adjacent->GetIndex());
        }
        ++iter_station;
    }
    csg_station_pub_.publish(msg);
}

void CsgRosHandler::PubTaskList()
{
    return;
    geometry_msgs::PoseArray msg;
    geometry_msgs::Pose _pose;
    msg.header.frame_id = STD_MAP_FRAME_ID;
    msg.header.stamp = ros::Time::now();
    int cnt_path = ptr_csg_cache_->station_graph_.paths_.size();
    if(cnt_path <= 0) {
        return;
    }
    msg.poses.clear();
    list<list<station_graph::Station*>> path_;
    list<station_graph::Station*>::iterator iter_goal_ = ptr_csg_cache_->goal_path_.begin();
    path_ = ptr_csg_cache_->station_graph_.paths_;
    //LM_ERROR("path_.size(): %d", path_.size());
    for(auto ptr_path : path_) {
        for(int i=0; i<cnt_path; i++) {
            int goal_id_ = (*iter_goal_)->GetIndex();
            //LM_ERROR("goal_id_: %d, (*iter_goal_)->GetIndex(): %d", goal_id_, (*iter_goal_)->GetIndex());
            if(goal_id_ == ptr_path.back()->GetIndex()) {
                _pose.position.x = (*iter_goal_)->x_;
                _pose.position.y = (*iter_goal_)->y_;
                _pose.position.z = 0.0;
                tf::Quaternion quat;
                quat.setRPY(0.0, 0.0, (*iter_goal_)->theta_);
                tf::quaternionTFToMsg(quat, _pose.orientation);
            }
            ++iter_goal_;
        }
        msg.poses.push_back(_pose);
    }
    csg_current_task_pub_.publish(msg);
}

void CsgRosHandler::PubTaskPath()
{
    return;
    geometry_msgs::PoseArray msg;
    msg.header.frame_id = STD_MAP_FRAME_ID;
    msg.header.stamp = ros::Time::now();
    int cnt = ptr_csg_cache_->current_path_.size();
    msg.poses.clear();
    msg.poses.resize(cnt);
//    LM_WARN("msg.station_array: %d", static_cast<int>(msg.station_array.size()));
    list<station_graph::Station *>::iterator iter_station = ptr_csg_cache_->current_path_.begin();
    for(int i=0;i<cnt;i++) {
        msg.poses[i].position.x = (*iter_station)->x_;
        msg.poses[i].position.y = (*iter_station)->y_;
        msg.poses[i].position.z = 0.0;
        tf::Quaternion quat;
        quat.setRPY(0.0, 0.0, (*iter_station)->theta_);
        tf::quaternionTFToMsg(quat, msg.poses[i].orientation);
        ++iter_station;
    }
    csg_current_path_pub_.publish(msg);

}

void CsgRosHandler::PubCsgCurStation()
{
    if(ptr_csg_cache_->station_graph_.ptr_current_station_ == NULL) {
//        LM_WARN("Current station is null!");
        return;
    }
    wr_npu_msgs::Station msg;
    msg.d2s = ptr_csg_cache_->station_graph_.ptr_current_station_->d2s_;
    msg.x = ptr_csg_cache_->station_graph_.ptr_current_station_->x_;
    msg.y = ptr_csg_cache_->station_graph_.ptr_current_station_->y_;
    msg.theta = ptr_csg_cache_->station_graph_.ptr_current_station_->theta_;
    msg.enb_navi_arrived = ptr_csg_cache_->station_graph_.ptr_current_station_->enb_navi_arrived_;
    msg.inflection_point = ptr_csg_cache_->station_graph_.ptr_current_station_->inflection_point_;
    msg.csg_station = ptr_csg_cache_->station_graph_.ptr_current_station_->csg_station_;
    msg.id = ptr_csg_cache_->station_graph_.ptr_current_station_->GetIndex();
    for (auto ptr_adjacent : ptr_csg_cache_->station_graph_.ptr_current_station_->GetAdjacentStation()) {
        msg.adjacent_id.push_back(ptr_adjacent->GetIndex());
    }
    csg_current_station_pub_.publish(msg);
}

void CsgRosHandler::PubCsgPreStationPose()
{
    if(ptr_csg_cache_->station_graph_.ptr_pre_station_ == NULL) {
        return;
    }
    geometry_msgs::PoseStamped msg;
    msg.header.frame_id = STD_MAP_FRAME_ID;
    msg.header.stamp = ros::Time::now();
    msg.pose.position.x = ptr_csg_cache_->station_graph_.ptr_pre_station_->x_;
    msg.pose.position.y = ptr_csg_cache_->station_graph_.ptr_pre_station_->y_;
    msg.pose.position.z = 0.0;
    tf::Quaternion quat;
    quat.setRPY(0.0, 0.0, ptr_csg_cache_->station_graph_.ptr_pre_station_->theta_);
    tf::quaternionTFToMsg(quat, msg.pose.orientation);
    csg_pre_station_pose_pub_.publish(msg);
}

void CsgRosHandler::PubCsgCurStationPose()
{
    if(ptr_csg_cache_->station_graph_.ptr_current_station_ == NULL) {
        return;
    }
    geometry_msgs::PoseStamped msg;
    msg.header.frame_id = STD_MAP_FRAME_ID;
    msg.header.stamp = ros::Time::now();
    msg.pose.position.x = ptr_csg_cache_->station_graph_.ptr_current_station_->x_;
    msg.pose.position.y = ptr_csg_cache_->station_graph_.ptr_current_station_->y_;
    msg.pose.position.z = 0.0;
    tf::Quaternion quat;
    quat.setRPY(0.0, 0.0, ptr_csg_cache_->station_graph_.ptr_current_station_->theta_);
    tf::quaternionTFToMsg(quat, msg.pose.orientation);
    csg_current_station_pose_pub_.publish(msg);
}

void CsgRosHandler::BackToChargeHome()
{
    if(!ptr_csg_cache_->arrived_charge_point_) {
        return;
    }

    string mapname;
    mapname = ptr_csg_cache_->station_graph_.map_name_;
    string s;
    s.append(" action_name:=6,6,3, action_args:=charge2,charge,0.26, duration:=3,3,3, times:=1 taskloop:=false map_name:=").append(mapname);
    ros::NodeHandle nh;
    string host_platform;
    nh.param<string>(STD_CORE_PARAM_NS + "/PLATFORM", host_platform, "");
    stringstream ss;
    ss << "bash /npu." << host_platform << "/script/task_run.sh \"" << " "
       << s << "\"&\n";
    system(ss.str().c_str());
    ptr_csg_cache_->arrived_charge_point_ = false;
    ROS_INFO("%s",ss.str().c_str());
}

bool CsgRosHandler::ChangeCsgStation(wr_npu_msgs::CsgStation::Request &req, wr_npu_msgs::CsgStation::Response &rep)
{
    if(req.action_mode == 0) {
        LM_DEBUG("Add new station!\n");
        LM_DEBUG("(%.2f, %.2f, %.2f)[%d, %d, %d, %d, %d]",
                 req.station.x, req.station.y, req.station.theta,
                 req.station.enb_navi_arrived, req.station.csg_station, req.station.inflection_point,
                 req.station.is_charger, req.station.is_charge_station);
        ptr_csg_cache_->station_graph_.station_index_ += 1;
        station_graph::Station* ptr_station = new station_graph::Station(ptr_csg_cache_->station_graph_.station_index_);
        ptr_station->x_ = req.station.x;
        ptr_station->y_ = req.station.y;
        ptr_station->theta_ = req.station.theta;
        ptr_station->enb_navi_arrived_ = req.station.enb_navi_arrived;
        ptr_station->csg_station_ = req.station.csg_station;
        ptr_station->inflection_point_ = req.station.inflection_point;
        ptr_station->is_charger_ = req.station.is_charger;
        ptr_station->is_charge_station_ = req.station.is_charge_station;
        ptr_csg_cache_->station_graph_.InsertStation(ptr_station);
        UpdateChargeData();
        ptr_csg_cache_->station_graph_.DumpToFile();
    } else if(req.action_mode == 1) {
        LM_DEBUG("Delete station!");
        int cnt_ = ptr_csg_cache_->station_graph_.stations_.size();
        list<station_graph::Station *>::iterator iter_station = ptr_csg_cache_->station_graph_.stations_.begin();
        for(int i=0; i<cnt_; i++) {
            int no_ = (*iter_station)->GetIndex();
            LM_DEBUG("The no is: %d, req id is: %d", no_, req.station.id);
            if(req.station.id == (*iter_station)->GetIndex()) {
                ptr_csg_cache_->station_graph_.DeleteStation((*iter_station));
                ptr_csg_cache_->station_graph_.DumpToFile();
                return true;
            }
            ++iter_station;
        }
    } else if(req.action_mode == 2) {
        LM_DEBUG("Update station!");
        int cnt_ = ptr_csg_cache_->station_graph_.stations_.size();
        if(cnt_ <= 0) {
            LM_DEBUG("station list size is: %d", cnt_);
            return false;
        }
        list<station_graph::Station *>::iterator iter_station = ptr_csg_cache_->station_graph_.stations_.begin();
        for(int i=0; i<cnt_; i++) {
            if(req.station.id == (*iter_station)->GetIndex()) {
                (*iter_station)->x_ = req.station.x;
                (*iter_station)->y_ = req.station.y;
                (*iter_station)->theta_ = req.station.theta;
                (*iter_station)->enb_navi_arrived_ = req.station.enb_navi_arrived;
                (*iter_station)->inflection_point_ = req.station.inflection_point;
                (*iter_station)->csg_station_ = req.station.csg_station;
                (*iter_station)->is_charger_ = req.station.is_charger;
                (*iter_station)->is_charge_station_ = req.station.is_charge_station;
                UpdateChargeData();
                ptr_csg_cache_->station_graph_.DumpToFile();
                LM_DEBUG("(%.2f, %.2f, %.2f)[%d, %d, %d, %d, %d]",
                         req.station.x, req.station.y, req.station.theta,
                         req.station.enb_navi_arrived, req.station.csg_station, req.station.inflection_point,
                         req.station.is_charger, req.station.is_charge_station);
                LM_DEBUG("station_ size is: %ld", ptr_csg_cache_->station_graph_.stations_.size());
                return true;
            }
            ++iter_station;
        }
    }
    return true;
}

bool CsgRosHandler::ChangeCsgPath(wr_npu_msgs::CsgPath::Request &req, wr_npu_msgs::CsgPath::Response &rep)
{
    LM_WARN("Going!");
    if(req.action_mode_path == 0) {
        LM_WARN("Add new path!");
        int index_1 = 0;
        int index_2 = 0;
        index_1 = req.station_array.station_array[0].id;
        index_2 = req.station_array.station_array[1].id;
        ptr_csg_cache_->station_graph_.AddPath(ptr_csg_cache_->station_graph_.GetStationPtr(index_1),
                                               ptr_csg_cache_->station_graph_.GetStationPtr(index_2));
        ptr_csg_cache_->station_graph_.AddPath(ptr_csg_cache_->station_graph_.GetStationPtr(index_2),
                                               ptr_csg_cache_->station_graph_.GetStationPtr(index_1));
        ptr_csg_cache_->station_graph_.DumpToFile();
    }else if(req.action_mode_path == 1) {
        LM_WARN("Delete path!");
        int index_1 = req.station_array.station_array[0].id;
        int index_2 = req.station_array.station_array[1].id;
        ptr_csg_cache_->station_graph_.DeletePath(index_1, index_2);
        ptr_csg_cache_->station_graph_.DumpToFile();
    }
    return true;
}

/*** Automatically set the inflection point ***/
void CsgRosHandler::JudgmentPoint()
{
    ros::NodeHandle nh;
    int enb_automark;
    nh.getParam("/csg_enb_automark", enb_automark);
    if(enb_automark == 1) {
        if((-0.01 < cmd_vel_.v_x && cmd_vel_.v_x < 0.01) && (-0.01 < cmd_vel_.v_yaw && cmd_vel_.v_yaw < 0.01)) {
            x_ = act_pose_.x;
            y_ = act_pose_.y;
            yaw_ = act_pose_.yaw;
        } else if(cmd_vel_.v_yaw > 0.05 || cmd_vel_.v_yaw < -0.05) {
            float angle_before_ = 180*yaw_/M_PI;
            float angle_current_ = 180*act_pose_.yaw/M_PI;
            float angle_differ;
            if(((angle_before_ > 90 && angle_before_ < 180) && (angle_current_>-180 && angle_current_ < -90))
                    || ((angle_current_ > 90 && angle_current_ < 180) && (angle_before_>-180 && angle_before_ < -90))) {
                angle_differ = 360-abs(angle_before_)-abs(angle_current_);
            }else {
                angle_differ = abs(angle_before_ - angle_current_);
            }
//            LM_WARN("angle differ is: %f", angle_differ);
            if(angle_differ > 70) {
//                LM_WARN("angle more than 30");
                ptr_csg_cache_->station_graph_.station_index_ += 1;
                station_graph::Station* ptr_station = new station_graph::Station(ptr_csg_cache_->station_graph_.station_index_);
                ptr_station->x_ = x_;
                ptr_station->y_ = y_;
                ptr_station->theta_ = yaw_;
                ptr_station->enb_navi_arrived_ = false;
                ptr_station->csg_station_ = false;
                ptr_station->inflection_point_ = true;
                ptr_csg_cache_->station_graph_.InsertStation(ptr_station);
                ptr_csg_cache_->station_graph_.DumpToFile();
                yaw_ = act_pose_.yaw;
            }else {
//                LM_WARN("angle less than 30");
                return;
            }
        }
    }
    return;
}
void CsgRosHandler::ConnectNpu()
{
    transfer_npu_.request.mode = 0;
    if(client_npu_.call(transfer_npu_)) {
        return;
    } else {
        LM_ERROR("Client Connect Failed!");
    }

}

void CsgRosHandler::StartNavi()
{
    if(auto_start_ == false && ptr_csg_cache_->start_navi_ == false) {
        return;
    }
    NpuState npu_state = GetNpuState();
    if(npu_state == NpuState::NAVI_STATE) {
        LM_WARN("Navigation has been launched already! CurNpuState is: %d", npu_state);
    } else {
        if(npu_state != NpuState::IDLE_STATE) {
            LM_WARN("Navigation is not in IDLE_STATE! CurNpuState is: %d", npu_state);
            return;
        }
        transfer_npu_.request.mode = 2;
        if (!client_npu_.call(transfer_npu_)) {
            LM_WARN("Service(\"\") return FALSE, mode = 2.");
            LM_ERROR("Start navigation FAILED!");
            return;
        }
    }
    auto_start_ = false;
    ptr_csg_cache_->start_navi_ = false;
    ptr_csg_cache_->completedStartNavi_ = true;
    return;
}

void CsgRosHandler::OutChargeHome()
{
    if(!is_in_charge_home_) {
        return;
    }
    if(ptr_csg_cache_->cur_navi_state_ != wizrobo_npu::SUCCEEDED){
        return;
    }
    is_in_charge_home_ = false;
}

void CsgRosHandler::GetMapName()
{
    if(is_mapname_loaded_) {
        return;
    }
    ConnectNpu();
    transfer_npu_.request.mode = 1;
    if(client_npu_.call(transfer_npu_)) {
        ptr_csg_cache_->station_graph_.map_name_ = transfer_npu_.response.current_map_name;
    } else {
        LM_ERROR("Can NOT get current map name!!!");
        return;
    }
    ptr_csg_cache_->station_graph_.LoadFromFile();
    UpdateChargeData();
    is_mapname_loaded_ = true;
}

void CsgRosHandler::ShutDown()
{
    if(!ptr_csg_cache_->shut_down_) {
        return;
    } else {
        stringstream ss;
        ss << "sudo shutdown now -h &\n";
        system(ss.str().c_str());
        ptr_csg_cache_->shut_down_ = true;
    }
}

void CsgRosHandler::StopNavi()
{
    if(!ptr_csg_cache_->stop_navi_) {
        return;
    }
    NpuState npu_state = GetNpuState();
    LM_DEBUG("CurNpuState is: %d", npu_state);
    if(npu_state == NpuState::NAVI_STATE) {
        transfer_npu_.request.mode = 3;
        if(client_npu_.call(transfer_npu_)) {
            ptr_csg_cache_->completedStopNavi_ = true;
            ptr_csg_cache_->stop_navi_ = false;
            return;
        }
    }
    LM_WARN("NpuState is not navi! Can't StopNavi");
    ptr_csg_cache_->stop_navi_ = false;
    ptr_csg_cache_->completedStopNavi_ = true;
    return;

}

void CsgRosHandler::SelectMap()
{
    if(!ptr_csg_cache_->enb_switch_map_) {
        return;
    }
//    ConnectNpu();
    transfer_npu_.request.mode = 4;
    transfer_npu_.request.switch_map_name = ptr_csg_cache_->switched_map;
    if(client_npu_.call(transfer_npu_)) {
        ptr_csg_cache_->enb_switch_map_ = false;
        ptr_csg_cache_->completedSwitchMap_ = true;
    }
}

void CsgRosHandler::UpdateChargeData()
{
    int cnt_ = ptr_csg_cache_->station_graph_.stations_.size();
    list<station_graph::Station *>::iterator iter_station = ptr_csg_cache_->station_graph_.stations_.begin();
    for(int i=0; i<cnt_; i++) {
        if((*iter_station)->is_charger_ == true) {
            ptr_csg_cache_->charger_x_ = (*iter_station)->x_;
            ptr_csg_cache_->charger_y_ = (*iter_station)->y_;
            LM_WARN("charger_x is: %.2f", ptr_csg_cache_->charger_x_);
            LM_WARN("charger_y is: %.2f", ptr_csg_cache_->charger_y_);
        } else if((*iter_station)->is_charge_station_ == true) {
            ptr_csg_cache_->charge_station_x_ = (*iter_station)->x_;
            ptr_csg_cache_->charge_station_y_ = (*iter_station)->y_;
            LM_WARN("Charge_station_x is: %.2f", ptr_csg_cache_->charge_station_x_);
            LM_WARN("Charge_station_y is: %.2f", ptr_csg_cache_->charge_station_y_);
        }
        ++iter_station;
    }
}

void CsgRosHandler::CancelTask()
{
    if(!ptr_csg_cache_->cancel_task_) {
        return;
    }
    transfer_npu_.request.mode = 6;
    if(client_npu_.call(transfer_npu_)) {
        ptr_csg_cache_->cancel_task_ = false;
    }
}

}
