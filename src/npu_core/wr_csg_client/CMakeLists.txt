cmake_minimum_required(VERSION 2.8.3)
project(wr_csg_client)

add_compile_options(-std=c++11)

find_package(catkin REQUIRED 
    COMPONENTS
    roscpp
    rospy
    std_msgs
    wr_npu_core
    wr_npu_ice
    wr_npu_msgs
    wr_map_server
    wr_npu_server
)

find_package(Boost REQUIRED signals)

catkin_package(
    INCLUDE_DIRS include
#   LIBRARIES wr_csg_client
#   CATKIN_DEPENDS other_catkin_pkg
#   DEPENDS system_lib
)

include_directories(
    include
    ${catkin_INCLUDE_DIRS}
    ${WR_LOCK_INCLUDE}
    ${ICE_INCLUDE_DIRS}
)

add_executable(csg_client_node
    src/csg_client_node.cpp
    src/csg_client.cpp
    src/csg_ros_handler.cpp
    src/csg_socket_handler.cpp
    src/station_graph.cpp
)

add_dependencies(csg_client_node
  ${${PROJECT_NAME}_EXPORTED_TARGETS}
  ${catkin_EXPORTED_TARGETS}
)

target_link_libraries(csg_client_node
    ${catkin_LIBRARIES}
    ${WR_LOCK_LIBS}
    ${Ice_LIBRARIES}
)

install(TARGETS csg_client_node
    ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

install(DIRECTORY include/
    DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
    FILES_MATCHING PATTERN "*.h"
    PATTERN ".svn" EXCLUDE
)
