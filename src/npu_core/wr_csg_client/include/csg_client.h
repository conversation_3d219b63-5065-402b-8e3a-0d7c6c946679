/*
    client for China South Grid
*/
#ifndef WIZROBO_CSG_CLIENT_H
#define WIZROBO_CSG_CLIENT_H
#include <sstream>

#include <pthread.h>

#include <ros/ros.h>

/*
#include <geometry_msgs/Vector3Stamped.h>
#include <geometry_msgs/Twist.h>
#include <dynamic_reconfigure/server.h>
#include <sensor_msgs/Imu.h>
#include <sensor_msgs/BatteryState.h>
#include <sensor_msgs/NavSatFix.h>
#include <sensor_msgs/NavSatStatus.h>
#include <sensor_msgs/Range.h>

#include "std_msgs/Float32.h"
#include "std_msgs/Int32.h"
#include "std_msgs/UInt8.h"

#include <wr_npu_msgs/MotorSpd.h>
#include <wr_npu_msgs/MotorEnc.h>
#include <wr_npu_msgs/SonarData.h>
#include <wr_npu_msgs/InfrdData.h>
#include <wr_npu_msgs/BumperData.h>
#include <wr_npu_msgs/EmergencyStopData.h>
#include <wr_npu_msgs/ComStatus.h>
#include <wr_npu_msgs/DynParam.h>
#include <wr_npu_msgs/ClearMotorEnc.h>
#include <wr_npu_msgs/ExceptionInformation.h>
#include <wr_npu_msgs/VoltageData.h>
#include <wr_npu_msgs/FuncRtnBool.h>

#include <wr_bcu_server/BcuServerConfig.h>
#include <wr_bcu_server/PidParamConfig.h>
#include "bcu_driver_creator.h"
*/

#include "usblock.h"

#include "npu.h"
#include "npu_ros_ext.h"
#include "npu_math.h"
#include "npuice_param.h"
#include "npuice_enum_ext.h"

#include "csg_ros_handler.h"
#include "csg_socket_handler.h"
#include "csg_cache.h"

namespace wizrobo {
using namespace wizrobo::ros_ext;

class CsgClient
{
public:
    CsgClient(int argc, char **argv, std::string node_name);
    void Init();
    inline bool IsInited() { return is_inited_; }
    void Run();
private:
    bool is_inited_;
    boost::thread* ptr_socket_thread_;
    boost::thread* ptr_socket_thread_heart_;
    boost::thread* ptr_ros_thread_;

    CsgCache* ptr_cache_;
    CsgSocketHandler* ptr_socket_handler_;
    CsgRosHandler* ptr_ros_handler_;
};
}// namespace
#endif// WIZROBO_CSG_CLIENT_H
