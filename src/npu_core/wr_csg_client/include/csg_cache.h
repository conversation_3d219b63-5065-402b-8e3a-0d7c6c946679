#ifndef CSG_CACHE_H
#define CSG_CACHE_H

#include <float.h>
#include <string.h>

#include "npuice_geometry.h"
#include "npuice_api.h"
#include "npu_math.h"
#include "npuice_data.h"

#include "station_graph.h"

#define _IS_ROS_ 1
#if _IS_ROS_
#define CHE_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("csh", "CsgCache::%s() " fmt, __FUNCTION__, ##arg)
#define CHE_INFO(fmt, arg...)  ROS_INFO_NAMED("csh",  "CsgCache::%s() " fmt, __FUNCTION__, ##arg)
#define CHE_WARN(fmt, arg...)  ROS_WARN_NAMED("csh",  "CsgCache::%s() " fmt, __FUNCTION__, ##arg)
#define CHE_ERROR(fmt, arg...) ROS_ERROR_NAMED("csh", "CsgCache::%s() " fmt, __FUNCTION__, ##arg)
#define CHE_FATAL(fmt, arg...) ROS_FATAL_NAMED("csh", "CsgCache::%s() " fmt, __FUNCTION__, ##arg)
#else
#define CHE_DEBUG(fmt, arg...) printf("CsgCache::%s() " fmt "\n", __FUNCTION__, ##arg)
#define CHE_INFO(fmt, arg...)  printf("CsgCache::%s() " fmt "\n", __FUNCTION__, ##arg)
#define CHE_WARN(fmt, arg...)  printf("CsgCache::%s() " fmt "\n", __FUNCTION__, ##arg)
#define CHE_ERROR(fmt, arg...) printf("CsgCache::%s() " fmt "\n", __FUNCTION__, ##arg)
#define CHE_FATAL(fmt, arg...) printf("CsgCache::%s() " fmt "\n", __FUNCTION__, ##arg)
#endif

namespace wizrobo {

using namespace std;

struct ActionMove {
    int angle;
    int mode = -1;
    double spd;
};

typedef struct Speed {
    float carSpeed;
    float flSpeed;
    float frSpeed;
    float rlSpeed;
    float rrSpeed;
}Speed;

typedef struct Direction {
    string carDirection;
    int flTire;
    int frTire;
    int rlTire;
    int rrTire;
}Direction;

struct RosStatus {
public:
    bool is_located_;
    double act_pose_x_;
    double act_pose_y_;
    double act_pose_theta_;
    Speed speed_;
    Direction derection_;
    int mode_;
    bool have_obstacle_;
    double charge_current_;
    double charge_voltage_;
    double odometry_;
};

struct RosExceptionState {
    int pavement_;
    double power_single_[12];
    float power_temp_;
    float power_;
    int power_charge_;
    string infrared_;
    string boxTemp;
    string stray;
    RosExceptionState(){
        pavement_ = 1;
        power_temp_ = 0.0;
        power_ = 0.0;
        power_charge_ = 0;
    }
};

class CsgCache
{
public:
    wizrobo_npu::Pose3D act_pose_;
//    wizrobo_npu::NpuState cur_npu_state_;
    wizrobo_npu::NaviState cur_navi_state_;
    station_graph::StationGraph station_graph_;
    wizrobo_npu::Vel3D act_vel_;
    wizrobo_npu::MotorSpd act_motor_data_;
    struct ActionMove action_move_;
    struct RosStatus ros_status_;
    struct RosExceptionState ros_exception_state_;
    bool action_stop_ = false;
//    bool charge_ = false;
    bool arrived_charge_home_ = false;
    bool charge_known = false;
    bool need_charge_ = false;
    bool arrive_crossing_ = false;
    bool arrived_charge_point_ = false;
    int stop_mode_ = -1;
    bool enb_action_move_ = false;
    int arrived_inflection_point_ = -1;
    int need_report_ = -1;
    int goal_id_;
    bool shut_down_ = false;
    bool start_navi_ = false;
    bool stop_navi_ = false;
    bool enb_switch_map_ = false;

    bool completedStartNavi_ = false;
    bool completedStopNavi_ = false;
    bool completedSwitchMap_ = false;

    bool cancel_task_ = false;

    float charger_x_;
    float charger_y_;
    float charge_station_x_;
    float charge_station_y_;

    std::string switched_map;

    list<station_graph::Station*> path_;
    list<station_graph::Station*> goal_path_;
    list<station_graph::Station*> current_path_;
    list<station_graph::Station*> path_csg_;

    typedef boost::unique_lock<boost::mutex> UniLock;
    boost::mutex mutex_current_station_;
    boost::mutex mutex_path_;

    ::wizrobo_npu::NpuIcePrx ptr_npuice;
    Ice::CommunicatorPtr ptr_npuic;
    Ice::ObjectPrx ptr_npubase;

    double theta_differ_;
private:

public:
    CsgCache()
    {
        ros_status_.is_located_ = false;
        ros_status_.act_pose_x_ = 0.0;
        ros_status_.act_pose_y_ = 0.0;
        ros_status_.act_pose_theta_ = 0.0;
        act_pose_.x = 0.0;
        act_pose_.y = 0.0;
        act_pose_.yaw = 0.0;
        ptr_npuice = 0;
    }

    station_graph::Station* AddNewStation()
    {
        UniLock lock(mutex_current_station_);
        printf("AddNewStation!");
        double a_x, a_y, a_theta, c_x, c_y, dist;
        a_x = ros_status_.act_pose_x_;
        a_y = ros_status_.act_pose_y_;
        a_theta = ros_status_.act_pose_theta_;
        if (station_graph_.ptr_current_station_ != NULL) {
            c_x = station_graph_.ptr_current_station_->x_;
            c_y = station_graph_.ptr_current_station_->y_;
            dist = DIST(a_x, a_y, c_x, c_y);
            if (dist < 1.0) {
                return station_graph_.ptr_current_station_;
            }
        }
        station_graph_.station_index_ += 1;
        station_graph::Station* ptr_station = new station_graph::Station(station_graph_.station_index_);
        ptr_station->x_ = a_x;
        ptr_station->y_ = a_y;
        ptr_station->theta_ = a_theta;
        station_graph_.InsertStation(ptr_station);
        if (station_graph_.ptr_current_station_ == NULL) {
            station_graph_.ptr_pre_station_ = station_graph_.ptr_current_station_;
            station_graph_.ptr_current_station_ = ptr_station;
            station_graph_.DumpToFile();
            return ptr_station;
        }

        /*** Optimize connectivity ***/
        printf("Optimize connectivity.\n");
        float x_s = station_graph_.ptr_current_station_->x_;
        printf("current x is: %f\n", x_s);
        float y_s = station_graph_.ptr_current_station_->y_;
        printf("current y is: %f\n", y_s);
        float x_m = ptr_station->x_;
        printf("act x is: %f\n", x_m);
        float y_m = ptr_station->y_;
        printf("act y is: %f\n", y_m);

        station_graph::Station* pre_adjacent_station_;
        float pre_dist;
        list<station_graph::Station*> adjacent_station_;
        adjacent_station_ = station_graph_.ptr_current_station_->GetAdjacentStation();
        int adjacent_length = adjacent_station_.size();
        printf("adjacent_station num is: %d\n", adjacent_length);
        if(adjacent_length > 0) {
            list<station_graph::Station *>::iterator ptr_adjacent_station = adjacent_station_.begin();
            for(int i=0; i<adjacent_length; i++) {
                float x_e = (*ptr_adjacent_station)->x_;
                float y_e = (*ptr_adjacent_station)->y_;

                float sa,sb,sc,cosA,sinA,H;
                sa = sqrt((x_m - x_e)*(x_m - x_e) + (y_m - y_e)*(y_m - y_e));
                sb = sqrt((x_e - x_s)*(x_e - x_s) + (y_e - y_s)*(y_e - y_s));
                sc = sqrt((x_m - x_s)*(x_m - x_s) + (y_m - y_s)*(y_m - y_s));
                cosA = (sb*sb + sc*sc - sa*sa)/(2*sb*sc);
                if(cosA < 0) {
                    sinA = sqrt(1 - cosA*cosA);
                    H = sc * sinA;
                    printf("H is: %f\n", H);
                    printf("pre_dist is: %f\n", pre_dist);
                    if(pre_adjacent_station_ == NULL) {
                        pre_dist = H;
                        pre_adjacent_station_ = (*ptr_adjacent_station);
                    }
                    if(H <= pre_dist) {
                        pre_adjacent_station_ = (*ptr_adjacent_station);
                        printf(",,,,,,\n");
                        if (!station_graph_.AddPath(pre_adjacent_station_, ptr_station)
                                || !station_graph_.AddPath(ptr_station, pre_adjacent_station_)) {
                            return NULL;
                        }
                        printf("......\n");
                    }

                }
                ++ptr_adjacent_station;
            }
        }
        /*** Optimize connectivity ***/

        if (!station_graph_.AddPath(station_graph_.ptr_current_station_, ptr_station)
                || !station_graph_.AddPath(ptr_station, station_graph_.ptr_current_station_)) {
            return NULL;
        }
        station_graph_.ptr_pre_station_ = station_graph_.ptr_current_station_;
        station_graph_.ptr_current_station_ = ptr_station;
        station_graph_.DumpToFile();
        return ptr_station;
    }

    static bool CheckIsInRange(station_graph::Station* ptr_s, station_graph::Station* ptr_e, double px, double py)
    {
        if (ptr_s == NULL || ptr_e == NULL) {
            return false;
        }
        printf("sx: %.2f, sy: %.2f, ex: %.2f, ey: %.2f, px: %.2f, py: %.2f\n",
               ptr_s->x_, ptr_s->y_, ptr_e->x_, ptr_e->y_, px, py);
        if ((ptr_s->x_<=px && px<=ptr_e->x_ && ptr_s->y_<=py && py<=ptr_e->y_)
                || (ptr_s->x_<=px && px<=ptr_e->x_ && ptr_e->y_<=py && py<=ptr_s->y_)
                || (ptr_e->x_<=px && px<=ptr_s->x_ && ptr_s->y_<=py && py<=ptr_e->y_)
                || (ptr_e->x_<=px && px<=ptr_s->x_ && ptr_e->y_<=py && py<=ptr_s->y_)) {
            return true;
        } else {
            return false;
        }
    }

    void UpdateCurrentStation(bool reset = false)
    {
        UniLock lock(mutex_current_station_);

        double c_x = ros_status_.act_pose_x_;
        double c_y = ros_status_.act_pose_y_;
        station_graph_.current_x_ = c_x;
        station_graph_.current_y_ = c_y;
        station_graph_.current_theta_ = ros_status_.act_pose_theta_;

        if(station_graph_.station_index_ == -1) {
            return;
        }
        if (reset == true || station_graph_.ptr_current_station_ == NULL) {
            station_graph_.ptr_current_station_ == NULL;
            station_graph_.ptr_pre_station_ = NULL;
            station_graph_.ptr_current_station_ = station_graph_.GetClosestStation(c_x, c_y);
            return;
        }
        double min_dist = DIST(c_x, c_y, station_graph_.ptr_current_station_->x_, station_graph_.ptr_current_station_->y_);
        station_graph::Station* ptr_closest_adjacent = station_graph_.ptr_current_station_;
        for (auto ptr_adjacent : station_graph_.ptr_current_station_->GetAdjacentStation()) {
            double dist = DIST(c_x, c_y, ptr_adjacent->x_, ptr_adjacent->y_);
            if (dist < min_dist) {
                min_dist = dist;
                //station_graph_.ptr_current_station_ = ptr_adjacent;
                ptr_closest_adjacent = ptr_adjacent;
            }
        }
        if (ros_status_.have_obstacle_ == false) {
            if (station_graph_.ptr_current_station_ != ptr_closest_adjacent) {
                station_graph_.ptr_pre_station_ = station_graph_.ptr_current_station_;
                station_graph_.ptr_current_station_ = ptr_closest_adjacent;
            }
        } else {
            if (CheckIsInRange(station_graph_.ptr_pre_station_, station_graph_.ptr_current_station_, c_x, c_y) == true) {
                station_graph_.ptr_current_station_ = station_graph_.ptr_pre_station_;
                station_graph_.ptr_pre_station_ = NULL;
            }
        }
        return;
    }

    void SetNewPath(list<station_graph::Station *> path)
    {
        UniLock lock(mutex_path_);
        path_ = path;
    }

    void GetNewPath(list<station_graph::Station *>& path)
    {
        UniLock lock(mutex_path_);
        if (!path_.empty()) {
            path = path_;
            path_.clear();
        }
    }

    void PrintStationGraph()
    {
        CHE_DEBUG("station_index: %d", station_graph_.station_index_);
        for (auto ptr_station : station_graph_.stations_) {
            CHE_DEBUG("[%03d] (%.2f, %.2f)",
                      ptr_station->GetIndex(), ptr_station->x_, ptr_station->y_);
            for (auto ptr_adjacent : ptr_station->GetAdjacentStation()) {
                CHE_DEBUG("  -> adj: %03d", ptr_adjacent->GetIndex());
            }
        }
    }
};

} // namespace wizrobo

#endif // CSG_CACHE_H
