#ifndef CSG_SOCKET_HANDLER_H
#define CSG_SOCKET_HANDLER_H

#include <string>
#include <map>

#include <boost/function.hpp>
#include <boost/property_tree/ptree.hpp>
#include <boost/property_tree/json_parser.hpp>
#include <queue>

#include <ros/ros.h>

#include "wr_npu_msgs/CsgControl.h"

#include "csg_cache.h"
#include "station_graph.h"


namespace wizrobo {

using namespace std;

class CsgSocketHandler
{
private:
    bool is_inited_;
    bool is_registed_;
    CsgCache* ptr_csg_cache_;

    int socket_;
    string server_ip_;
    int server_port_;
    static const int BUFFER_LENGTH = 1024000;
    static const int RB_HEADER_LENGTH = 6;
    static const int LEN_OFFSET = 2;
    static const int DATA_OFFSET = 6;
    char send_buffer_[BUFFER_LENGTH];
    char recv_buffer_[BUFFER_LENGTH];
    char* ptr_rb_free_;
    int rb_avaliable_length_;
    int rb_free_length_;
    int inflection_no_bk_ = -1;
    bool heart_beating_ = false;
    bool socket_connected_ = false;
    bool registed_ = false;
    int counter_ = 666;
    int rtn_;

    std::string startNavi_serial_;
    std::string stopNavi_serial_;
    std::string switchMap_serial_;
    std::string chargeSerial_;

    boost::property_tree::ptree ptree_cmd_;
    typedef boost::function<void(void)> ActionFunction;
    map<string, ActionFunction> action_func_map_;
    enum CsgTaskStatus {UNSTARTED, EXECUTING, DONE};
    struct CsgTask {
        CsgTaskStatus status;
        station_graph::Station* ptr_station;
        CsgTask(station_graph::Station* ptr)
        {
            status = UNSTARTED;
            ptr_station = ptr;
        }
    };
    list<CsgTask> task_q_;
    string patrol_arrive_node_serial_;
    enum CsgPatrolArriveMode {IDLE, WAIT_FOR_MOVING, WAIT_FOR_STOP};
    CsgPatrolArriveMode patrol_arrive_node_mode_;

    ros::ServiceServer api_test_;

public:
    CsgSocketHandler(CsgCache* ptr_csg_cache);

    bool Init();
    bool IsInited() { return is_inited_; }
    void Run();
    void HeartBeat();

//private:
    void ActionRegist();
    void ActionHeartBeat();
    void ActionMove();
    void ActionGsPostionParamAdd();
    void ActionGsPostionParamModify();
    void ActionGsPostionParamDelete();
    void ActionPatrolStartPathPlan();
    void ActionPatrolArriveNode();
    void ActionStop();
    void Gotocharge();
    void ShutDown();
    void StartNavi();
    void StopNavi();
    void SwitchMap();

    void DoRegist();
    void ReportArriveCrossing();
    void ReportRosExceptionState();
    void ReportRosRealState();
    void ReportCharge();
    void ReportChargeHome();
    void ReportHeartBeat();

    void CompletedStartNavi();
    void CompletedStopNavi();
    void CompletedSwitchMap();
    void HeartbeatDetection();

//private:
    bool DoConnect();
    void RecvOnce();
    void ProcessRecvBuffer();

    string GetValueFromPtree(const string& key)
    {
        string res;
        try {
            res = ptree_cmd_.get<string>(key, "");
        } catch (boost::property_tree::ptree_error& e) {
            ROS_ERROR("<GetValueFromPtree>::exception: %s", e.what());
            ROS_ERROR("<GetValueFromPtree>::key: %s", key.c_str());
            res = "";
        }
        return res;
    }
	
    int GetIntValueFromPtree(const string& key)
    {
        int res;
        try {
            res = ptree_cmd_.get<int>(key, 0);
        } catch (boost::property_tree::ptree_error& e) {
            ROS_ERROR("<GetIntValueFromPtree>::exception: %s", e.what());
            ROS_ERROR("<GetIntValueFromPtree>::key: %s", key.c_str());
            res = 0;
        }
        return res;
    }

    double GetFloatValueFromPtree(const string& key)
    {
        double res;
        try {
            res = ptree_cmd_.get<double>(key, 0.0);
        } catch (boost::property_tree::ptree_error& e) {
            ROS_ERROR("<GetFloatValueFromPtree>::exception: %s", e.what());
            ROS_ERROR("<GetFloatValueFromPtree>::key: %s", key.c_str());
            res = 0.0;
        }
        return res;
    }

    string GetValueFromPtree(const char* key) { string _key(key); return GetValueFromPtree(_key); }
    int GetIntValueFromPtree(const char* key) { string _key(key); return GetIntValueFromPtree(_key); }
    double GetFloatValueFromPtree(const char* key) { string _key(key); return GetFloatValueFromPtree(_key); }

    void SendDataFrame(const char* json_str);
    void SendDataFrame(const string& json_str)
    {
        SendDataFrame(json_str.c_str());
    }
    void SendDataFrame(const boost::property_tree::ptree& pt)
    {
        stringstream ss;
        boost::property_tree::json_parser::write_json(ss, pt);
        SendDataFrame(ss.str().c_str());
    }
    void ReportPatrolArriveNode();

    void FiltPath(list<station_graph::Station *>& path, list<station_graph::Station *>& filted_path);
    bool ApiTestCallback(wr_npu_msgs::CsgControl::Request &req, wr_npu_msgs::CsgControl::Response &rep);

};

} // namespace wizrobo

#endif // CSG_SOCKET_HANDLER_H
