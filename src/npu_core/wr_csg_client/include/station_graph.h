#ifndef CSG_STATION_GRAPH_H
#define CSG_STATION_GRAPH_H

//#define BOOST_SPIRIT_THREADSAFE

#include <vector>
#include <list>
#include <queue>
#include <climits>
#include <boost/property_tree/ptree.hpp>
#include <boost/property_tree/json_parser.hpp>
#include "npu_math.h"

namespace station_graph {

using namespace std;

class Station {
public:
    Station* ptr_from_;
    double d2s_;    /* distence to start point */
    double x_;
    double y_;
    double theta_;
    bool enb_navi_arrived_;
    bool csg_station_;
    bool inflection_point_;
    bool is_charger_;
    bool is_charge_station_;
    const double _esp_ = 0.001;
    string id_;

private:
    int index_;
    list<Station*> adjacent_stations_;

public:
    Station(boost::property_tree::ptree& pt);
    Station(int index);
    void AddAdjacentStation(Station& station) { AddAdjacentStation(&station); }
    void AddAdjacentStation(Station* ptr) { adjacent_stations_.push_back(ptr); }
    void DeleteAdjacentStation(int index);
    void DeleteAdjacentStation(Station& station) { DeleteAdjacentStation(station.GetIndex()); }
    void DeleteAdjacentStation(Station* ptr) { DeleteAdjacentStation(ptr->GetIndex()); }

    bool IsAdjacent(int index);
    bool IsAdjacent(Station& station) { return IsAdjacent(station.GetIndex()); }
    bool IsAdjacent(Station* ptr) { return IsAdjacent(ptr->GetIndex()); }

    list<Station*>& GetAdjacentStation() { return adjacent_stations_; }
    int GetIndex() { return index_; }
    void ToPtree(boost::property_tree::ptree& pt);

    bool operator== (Station &target) { return (target.GetIndex() == index_); }
    bool HasNoAdjacentStation() { return adjacent_stations_.size()<=0?true:false; }
};

typedef Station* StationPtr;
typedef list<StationPtr> StationPtrList;

class StationGraph {
public:
    int station_index_;
    Station* ptr_current_station_;
    Station* ptr_pre_station_;
    list<Station*> stations_;
    list<list<Station*>> paths_;
    double current_x_;
    double current_y_;
    double current_theta_;
    std::string map_name_;

private:
    int station_cnt_;
    int path_cnt_;
    const double _esp_ = 0.01;
//    double inflection_;

public:
    StationGraph();
    int GetStationCnt() { return station_cnt_; }
    int GetPathCnt() { return path_cnt_; }

    void InsertStation(Station& station) { InsertStation(&station); }
    void InsertStation(Station* ptr) { stations_.push_back(ptr); }

    bool AddPath(Station& station_1, Station& station_2) { return AddPath(&station_1, &station_2); }
    bool AddPath(Station* ptr_1, Station* ptr_2);

    bool DeletePath(int index_1, int index_2);

    void DeleteStation(Station& station) { DeleteStation(&station); }
    void DeleteStation(Station* ptr_station);

    list<Station *> FindPath(Station* ptr_target_station, Station* ptr_current_station, bool is_block);
    void MakePlan(list<Station *>& station_list, bool is_block);
    Station* GetStationPtr(int index);
    Station* GetStationPtr(double x, double y);
    Station* GetClosestStation(double x, double y);
    Station* GetCurrentStation();

    void PrintStationGraph();
    void LoadFromFile();
    void DumpToFile();
    double CheckThetaDiffer(Station* ptr_s, Station* ptr_e, double px, double py, double ptheta);
    double CheckCurStation(Station* ptr_s, double px, double py, double ptheta);

private:
    void MakeDistenceField(Station* ptr_current_station, bool is_block);
    Station* HasStation(Station* ptr_target);
};

} // namespace station_graph

#endif // CSG_STATION_GRAPH_H
