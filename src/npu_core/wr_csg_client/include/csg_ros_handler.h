#ifndef CSG_ROS_HANDLER_H
#define CSG_ROS_HANDLER_H

#include "csg_cache.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <dirent.h>
#include <math.h>

#include <sys/socket.h>
#include <unistd.h>
#include <sys/types.h>
#include <netdb.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <string>
#include <Ice/Ice.h>

#include <iostream>
#include <fstream>
#include <sstream>
#include <map>
#include <yaml-cpp/yaml.h>

#include <ros/ros.h>
#include <ros/console.h>
#include <actionlib/client/simple_action_client.h>
#include <std_msgs/Bool.h>
#include <std_msgs/String.h>
#include <std_msgs/Int64MultiArray.h>
#include <std_msgs/Float32MultiArray.h>
#include <std_msgs/Float32.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Polygon.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <geometry_msgs/Twist.h>
#include <geometry_msgs/PoseArray.h>
#include <nav_msgs/OccupancyGrid.h>
#include <sensor_msgs/LaserScan.h>
#include <nav_msgs/Path.h>
#include <actionlib_msgs/GoalStatusArray.h>
#include <actionlib_msgs/GoalStatus.h>
#include <tf/transform_listener.h>
#include <sensor_msgs/Image.h>
#include <sensor_msgs/CameraInfo.h>
#include <actionlib/client/simple_action_client.h>
#include <wr_navi_core_msgs/WRNaviCoreAction.h>
#include <std_msgs/Float32.h>
#include <std_srvs/Empty.h>
#include <ecl/threads.hpp>
#include <sensor_msgs/LaserScan.h>
#include <tf/message_filter.h>
#include <tf/tf.h>
#include <message_filters/subscriber.h>
#include <geometry_msgs/PolygonStamped.h>
#include <wr_npu_msgs/VsensorStatus.h>
#include <wr_npu_msgs/SensorStatus.h>

#include <wr_npu_msgs/DynParam.h>
#include <wr_npu_msgs/WrMapServer.h>
#include <wr_npu_msgs/ForceLoopClosure.h>
#include <wr_npu_msgs/ClearMotorEnc.h>
#include <wr_npu_msgs/CsgBatterySonar.h>
#include <wr_dyparam/FeedBoolParam.h>
#include <wr_dyparam/FeedFloatParam.h>
#include <wr_dyparam/FeedIntParam.h>
#include <wr_dyparam/FeedStrParam.h>
#include <wr_dyparam/GetBoolParam.h>
#include <wr_dyparam/GetFloatParam.h>
#include <wr_dyparam/GetIntParam.h>
#include <wr_dyparam/GetStrParam.h>
#include <wr_dyparam/AddConfig.h>
#include <wr_dyparam/DeleteConfig.h>

#include <wr_npu_msgs/MotorEnc.h>
#include <wr_npu_msgs/MotorSpd.h>
#include <wr_npu_msgs/GetOptMap.h>
#include <wr_npu_msgs/BumperData.h>
#include <wr_npu_msgs/InfrdData.h>
#include <wr_npu_msgs/ExceptionInformation.h>
#include <wr_npu_msgs/EmergencyStopData.h>
#include <wr_npu_msgs/RuntimeStatus.h>
#include <wr_navi_core_msgs/WRNaviCoreAction.h>

#include <usblock.h>
#include <npu.h>
#include <npuice_api.h>
#include <npuice_map.h>
#include <npuice_enum_ext.h>
#include <npuice_geometry.h>
#include <fstream>

#include "map_handler.h"
#include "map_server.h"
#include "station_graph.h"
#include <pthread.h>
//#include "wr_npu_msg/"
#include <wr_npu_msgs/CsgMode.h>
#include <wr_npu_msgs/Station.h>
#include <wr_npu_msgs/StationArray.h>
#include <wr_npu_msgs/CsgStation.h>
#include <wr_npu_msgs/CsgPath.h>
#include <wr_npu_msgs/ChargeData.h>
#include <wr_npu_msgs/CsgTsNpu.h>
#include <boost/function.hpp>
#include <boost/property_tree/ptree.hpp>
#include <boost/property_tree/json_parser.hpp>

namespace wizrobo {
using namespace std;
using namespace wizrobo::enum_ext;
using namespace wizrobo::str_ext;
using namespace wizrobo::ros_ext;
using namespace wizrobo_npu;
//using namespace bcu_driver;

class CsgRosHandler
{
private:
    ///sub
    ros::Subscriber act_pose_sub_;
    ros::Subscriber act_motor_spd_sub_;
    ros::Subscriber act_vel_sub_;
    ros::Subscriber navi_status_sub_;
    ros::Subscriber navi_status_pf_sub_;
    ros::Subscriber battery_data_sub_;
    ros::Subscriber reach_waypoint_;
    ros::Subscriber charge_signal_sub_;
    ros::Subscriber cmd_vel_sub_;
    ros::Subscriber runtime_status_sub_;
    ros::Subscriber match_score_sub_;
    ros::Subscriber odom_distance_sub_;
    ros::Subscriber initial_pose_sub_;
    ros::Subscriber initial_pose_area_sub_;
//    ros::Subscriber voltage_data_sub_;

    ///pub
    ros::Publisher manual_cmd_pub_;
    ros::Publisher safe_cmd_pub_;
    ros::Publisher waypoints_pub_;
    ros::Publisher navi_pub_;
    ros::Publisher stop_mode_pub_;
    ros::Publisher csg_station_pub_;
    ros::Publisher csg_current_station_pub_;
    ros::Publisher csg_current_task_pub_;
    ros::Publisher csg_current_path_pub_;
    ros::Publisher csg_current_station_pose_pub_;
    ros::Publisher csg_pre_station_pose_pub_;
    ros::Publisher csg_current_theta_pub_;

    ///service
    ros::ServiceClient client_;
    ros::ServiceClient client_npu_;
    ros::ServiceServer server_;
    ros::ServiceServer server_path_;

    CsgCache* ptr_csg_cache_;
    MapHandler* ptr_map_handler_;

    NpuState npu_state_;
    NaviMode navi_mode_;

    ///pose
    Pose3D act_pose_;
    geometry_msgs::PoseStamped act_pose_msg_;
    wr_npu_msgs::CsgMode csg_mode_;
    wr_npu_msgs::CsgTsNpu transfer_npu_;

    ///navi
    NaviState navi_state_;
    NaviState navi_state_pf_;

    ///vel
    Vel3D act_vel_;
    Vel3D cmd_vel_;
    MotorSpd act_motor_spd_;

    list<station_graph::Station *> path_backup_;

    ///pose
    float x_, y_, yaw_;

    bool act_pose_is_inited_;
    bool is_pgv_;
    float match_score_data_;

    float angle_filt_path_;
    bool auto_start_;
    float charge_cur_limit_;
    float charge_home_dist_threshold_;
    float discharge_cur_limit_;
    float charge_zero_;
    bool have_obstacle_;
    bool is_in_charge_home_;
    bool is_mapname_loaded_;
    float get_pose_dead_time_;
    string host_platform_;
    bool use_target_station_theta_;

    bool get_new_initial_pose_;
    float new_initial_interval_;
    ros::Time new_initial_pose_stamp_;

public:
    CsgRosHandler(CsgCache* ptr_csg_cache)
        : ptr_csg_cache_(ptr_csg_cache)
        , ptr_map_handler_(nullptr)
        , x_(0.0)
        , y_(0.0)
        , yaw_(0.0)
        , act_pose_is_inited_(false)
        , is_pgv_(false)
        , match_score_data_(0.0)
        , angle_filt_path_(0.26) // means 15 degree
        , auto_start_(true)
        , charge_cur_limit_(0.5)
        , charge_home_dist_threshold_(0.4)
        , discharge_cur_limit_(1.5)
        , charge_zero_(0.1)
        , have_obstacle_(false)
        , is_in_charge_home_(false)
        , is_mapname_loaded_(false)
        , get_pose_dead_time_(15)
        , host_platform_("x86_64")
        , use_target_station_theta_(true)
        , get_new_initial_pose_(false)
        , new_initial_interval_(5)
    {
    }

    bool Init();
    void Run();

    void GetActPose();
    NpuState GetNpuState();
    NaviState GetNaviState();
    void GetActVel();
    void GetActMotorSpd();

    void StopMode();
    void RemoteMove();
    void StopMove();
    void FiltPath(list<station_graph::Station *>& path, list<station_graph::Station *>& filted_path);
    void PubPath();
    void PubCsgStation();
    void PubCsgCurStation();
    void PubCsgCurStationPose();
    void PubCsgPreStationPose();
    void PubTaskList();
    void PubTaskPath();
    void NeedReport();

private:
    ///callback
    void MatchScoreCallback_(const std_msgs::Float32::ConstPtr &msg);
    void ActPoseCallback_(const geometry_msgs::PoseStamped::ConstPtr& msg);
    void TaskStatusCallback_(const actionlib_msgs::GoalStatusArray::ConstPtr& msg);
    void TaskStatusPfCallback_(const actionlib_msgs::GoalStatusArray::ConstPtr& msg);
    void ActVelCallback_(const geometry_msgs::Twist::ConstPtr& msg);
    void CmdVelCallback_(const geometry_msgs::Twist::ConstPtr& msg);
    void ActMotorSpdCallback_(const wr_npu_msgs::MotorSpd::ConstPtr& msg);
    void BatteryDataCallback_(const wr_npu_msgs::CsgBatterySonar::ConstPtr& msg);
    void ReachWaypointCallback_(const std_msgs::Bool::ConstPtr& msg);
    void ReceiveChargeSignalCallback_(const std_msgs::Bool::ConstPtr& msg);
    void RuntimeStatusCallback_(const wr_npu_msgs::RuntimeStatus::ConstPtr& msg);
    void OdomDistanceCallback_(const std_msgs::Float32::ConstPtr& msgs);
    bool ChangeCsgStation(wr_npu_msgs::CsgStation::Request &req, wr_npu_msgs::CsgStation::Response &rep);
    bool ChangeCsgPath(wr_npu_msgs::CsgPath::Request &req, wr_npu_msgs::CsgPath::Response &rep);
    void InitialPoseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg);
    void InitialPoseAreaCallback(const geometry_msgs::Polygon::ConstPtr& msg);


    void BackToChargeHome();
//    void StartNavi();
    void SelectMap();
    void JudgmentPoint();
    void OutChargeHome();
    void ConnectNpu();
    void GetMapName();
    void ShutDown();
    void StartNavi();
    void StopNavi();
    void UpdateChargeData();
    void CancelTask();
};

} // namespace wizrobo

#endif // CSG_ROS_HANDLER_H
