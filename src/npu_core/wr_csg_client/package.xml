<?xml version="1.0"?>
<package>
  <name>wr_csg_client</name>
  <version>0.0.0</version>
  <description>The wr_csg_client package</description>

  <maintainer email="<EMAIL>">sctu</maintainer>

  <license>TODO</license>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>wr_npu_core</build_depend>
  <build_depend>wr_npu_ice</build_depend>
  <build_depend>wr_npu_msgs</build_depend>
  <build_depend>wr_map_server</build_depend>
  <build_depend>wr_npu_server</build_depend>
  <build_depend>std_msgs</build_depend>

  <run_depend>roscpp</run_depend>
  <run_depend>rospy</run_depend>
  <run_depend>wr_npu_msgs</run_depend>
  <run_depend>wr_npu_core</run_depend>
  <run_depend>wr_npu_ice</run_depend>
  <run_depend>wr_map_server</run_depend>
  <run_depend>wr_npu_server</run_depend>
  <run_depend>std_msgs</run_depend>

  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <!-- Other tools can request additional information be placed here -->

  </export>
</package>
