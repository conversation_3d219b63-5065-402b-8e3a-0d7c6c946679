#ifndef WIZROBO_LIDAR_FILTER_H
#define WIZROBO_LIDAR_FILTER_H
#include <string>
#include <sstream>
#include <ros/ros.h>
#include <tf/tf.h>
#include <tf/transform_listener.h>
#include <sensor_msgs/LaserScan.h>
#include <geometry_msgs/PoseArray.h>
#include <nav_msgs/Path.h>
#include "laser_geometry/laser_geometry.h"

#include <dynamic_reconfigure/server.h>
#include <wr_lidar_server/LidarFilterConfig.h>
#include <npu.h>
#include <npu_math.h>
#include <npu_ros_ext.h>
#include <usblock.h>

namespace wizrobo {
using namespace enum_ext;

class LidarFilter
{
public:
    LidarFilter(int argc, char** argv)
        : flag_inited_(false)
        , p_recfg_server_(NULL)
    {
        ros::init(argc, argv, "lidar_filter_node");
        ros::start();
        p_tf_sub_ = new tf::TransformListener();
    }
    ~LidarFilter()
    {
        RELEASE_POINTER(p_recfg_server_);
        RELEASE_POINTER(p_tf_sub_);
    }
    void Init();
    void Run();

    void ItlFilterScan(sensor_msgs::LaserScan& pre_flt_scan, sensor_msgs::LaserScan& itl_flt_scan);
protected:
    void RecfgCallback(wr_lidar_filter::LidarFilterConfig &config, uint32_t level);
    void RawScanCallback(const sensor_msgs::LaserScan::ConstPtr& scan_msg);
    void SlaveScanCallback(const sensor_msgs::LaserScan::ConstPtr& scan_msg);
    void PreFilterScan(const sensor_msgs::LaserScan& raw_scan, sensor_msgs::LaserScan& rlt_scan);

private:
    inline bool IsInFov(int idx, const std::vector<int>& fov_min_idxs, const std::vector<int>& fov_max_idxs)
    {
        for (int i = 0; i < fov_min_idxs.size(); i++)
        {
            if (idx >= fov_min_idxs[i] && idx <= fov_max_idxs[i])
            {
                return true;
            }
        }
        return false;
    }

private:
    /// flags
    bool flag_inited_;
    bool flag_scan_fuser_;
    bool flag_has_intensity_;

    /// params
    int lidar_id_;
    std::string flt_scan_frame_id_;
    bool enb_itl_flt_;
    float itl_flt_ori_fac_;
    float itl_flt_dist_fac_;
    bool enb_beam_skip_;
    int beam_skip_step_;
    bool enb_fov_mask_;
    std::vector<std::vector<float> > fov_mask_deg_;
    std::vector<int> fov_min_idxs_;
    std::vector<int> fov_max_idxs_;
    bool pub_debug_info_;

    /// sub & pub
    ros::Subscriber raw_scan_sub_;
    ros::Publisher pre_flt_scan_pub_;
    ros::Publisher flt_scan_pub_;
    ros::Publisher scan_path_pub_;
    ros::Publisher focus_beam_pub_;
    tf::TransformListener* p_tf_sub_;

    // scan fusion
    std::vector<ros::Subscriber> slave_scan_subs_;
    std::vector<sensor_msgs::LaserScan> slave_scans_;
    std::vector<int> slave_scan_cnts_;
    //std::vector<tf::StampedTransform> slave_lidar_tfs_;
    ros::Publisher fused_scan_pub_;
    boost::mutex fuse_lock_;
    /// srv
    dynamic_reconfigure::Server<wr_lidar_filter::LidarFilterConfig>* p_recfg_server_;

    /// debug info
    int focus_beam_idx_;
};
}// namespace wizrobo
#endif // WIZROBO_LIDAR_FILTER_H
