<?xml version="1.0"?>
<launch>
<!-- wizrobo standard lidar arg -->
<arg name="scan_topic"            default="/scan"/>
<arg name="lidar_ethernet_ip"     default="************" />
<arg name="lidar_serial_port_id"  default="" />
<!-- product-specific lidar args -->
<arg name="frame_id" default="lidar_frame"/>
<arg name="scan_frequency" default="15"/>
<arg name="samples_per_scan" default="1440"/>

<!-- R2000 Driver -->
<node pkg="wr_r2000" type="r2000_node" name="r2000_node" respawn="true" output="screen">
    <remap from="r2000_node/scan" to="$(arg scan_topic)" />
    <param name="scanner_ip" value="$(arg lidar_ethernet_ip)"/>
    <param name="frame_id" value="$(arg frame_id)"/>
    <param name="scan_frequency" value="$(arg scan_frequency)"/>
    <param name="samples_per_scan" value="$(arg samples_per_scan)"/>
</node>

</launch>
