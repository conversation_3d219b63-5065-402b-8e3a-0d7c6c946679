<!-- wizrobo standard lidar arg -->
<launch>
<arg name="scan_topic" default="/scan"/>
<arg name="lidar_ethernet_ip" default="" />
<arg name="lidar_serial_port_id" default="/dev/rplidar" />

<node pkg="wr_rplidar" type="rplidar_node" name="rplidar_node" respawn="true" output="screen">
    <param name="serial_port"         type="string" value="$(arg lidar_serial_port_id)"/>
    <param name="serial_baudrate"     type="int"    value="115200"/>
    <param name="frame_id"            type="string" value="lidar_frame"/>
    <param name="inverted"            type="bool"   value="false"/>
    <param name="angle_compensate"    type="bool"   value="true"/>
    <param name="scan_topic"          type="string" value="$(arg scan_topic)"/>
</node>
</launch>
