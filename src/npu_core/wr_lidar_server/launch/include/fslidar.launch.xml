<!--
  Author: <EMAIL>
  Copyright (c) 2015-2016, LinkMiao Robotics Co., Ltd.
  All rights reserved.
  Update: 2017-01-06
-->
<launch>
<!-- wizrobo standard lidar arg -->
<arg name="scan_topic"            default="/scan"/>
<arg name="lidar_ethernet_ip"     default="" />
<arg name="lidar_serial_port_id"  default="/dev/fslidar" />

  <node pkg="wr_fslidar"  type="fslidar_node" name="fslidar_node" output="screen">
  <param name="scan_topic"        type="string" value="$(arg scan_topic)"/>
  <param name="dev_path"          type="string" value="$(arg lidar_serial_port_id)"/>
  <param name="inverted"          type="bool"   value="false"/>
  <param name="frame_id"          type="string" value="/lidar_frame"/>
  <param name="sample_rate"       type="int"    value="5000"/>
  <param name="rotational_speed"  type="int"    value="5"/>
  </node>
</launch>
