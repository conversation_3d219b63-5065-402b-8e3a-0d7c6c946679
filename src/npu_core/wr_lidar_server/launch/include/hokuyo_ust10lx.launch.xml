<launch>
<!-- wizrobo standard lidar arg -->
<arg name="scan_topic"            default="scan"/>
<arg name="lidar_ethernet_ip"     default="************"/>
<arg name="lidar_serial_port_id"  default="" />

  <node name="urg_node" pkg="urg_node" type="urg_node" output="screen">
    <param name="scan_topic" type="str" value="$(arg scan_topic)" />
    <param name="ip_address" value="$(arg lidar_ethernet_ip)"/>
    <param name="serial_port" value="/dev/ttyACM0"/>
    <param name="serial_baud" value="115200"/>
    <param name="frame_id" value="lidar_frame"/>
    <param name="calibrate_time" value="true"/>
    <param name="publish_intensity" value="true"/>
    <param name="publish_multiecho" value="false"/>
    <param name="angle_min" value="-2.3561994"/>
    <param name="angle_max" value="2.3561994"/>
  </node>

</launch>
