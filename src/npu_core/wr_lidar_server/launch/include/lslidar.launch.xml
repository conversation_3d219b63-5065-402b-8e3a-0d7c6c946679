<launch>
<!-- wizrobo standard lidar arg -->
<arg name="scan_topic" default="/scan"/>
<arg name="lidar_ethernet_ip" default="0" />
<arg name="lidar_serial_port_id" default="/dev/lslidar" />

  <node pkg="wr_lslidar"  type="lslidar_node" name="lslidar_node" output="screen">
    <param name="serial_port"         type="string" value="$(arg lidar_serial_port_id)"/>
    <param name="serial_baudrate"     type="int"    value="115200"/>
    <param name="frame_id"            type="string" value="lidar_frame"/>
    <param name="inverted"            type="bool"   value="false"/>
    <param name="angle_compensate"    type="bool"   value="true"/>
    <param name="scan_topic" 	      type="string" value="$(arg scan_topic)"/>
    
    <!--remap from="scan" to="scan_raw"/-->
  </node>
</launch>
