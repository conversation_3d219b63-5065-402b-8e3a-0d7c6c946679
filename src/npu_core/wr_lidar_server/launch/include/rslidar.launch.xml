<!-- -*- mode: XML -*- -->
<!-- v1.0 created by lawrence.han @2017-03-31 -->
<launch>
<!-- wizrobo standard lidar arg -->
<arg name="scan_topic"            default="/scan"/>
<arg name="lidar_ethernet_ip"     default="" />
<arg name="lidar_serial_port_id"  default="/dev/rslidar" />
<arg name="logger_level" default="info" />

  <node pkg="wr_rslidar" type="rslidar_node" name="rslidar_node" output="screen">
      <param name="port_name" value="$(arg lidar_serial_port_id)" />
      <param name="scan_topic" value="$(arg scan_topic)" />
      <param name="lidar_frame_id" value="lidar_frame" />
      <param name="logger_level" value="$(arg logger_level)" />
  </node>
</launch>
