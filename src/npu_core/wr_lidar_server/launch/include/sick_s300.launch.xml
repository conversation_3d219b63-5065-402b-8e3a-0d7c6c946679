<?xml version="1.0"?>
<launch>
<!-- wizrobo standard lidar arg -->
<arg name="scan_topic"            default="/scan"/>
<arg name="lidar_ethernet_ip"     default="" />
<arg name="lidar_serial_port_id"  default="/dev/wrsicks300" />

  <node name="wr_sick_s300" pkg="wr_s300" type="wr_sick_s300" output="screen" >
  <param name="port"      type="string" value="$(arg lidar_serial_port_id)"/>
  <param name="baud"      type="int"    value="115200"/>
  <param name="frame_id"  type="string" value="lidar_frame"/>
  <remap from="scan" to="$(arg scan_topic)" />
  </node>
</launch>
