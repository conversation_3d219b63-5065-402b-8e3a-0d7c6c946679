<?xml version="1.0"?>
<!-- updated by lhan@2017-06-02
-->

<launch>
<!-- wizrobo standard lidar arg -->
<arg name="scan_topic" default="/scan"/>
<arg name="lidar_ethernet_ip" default="************" />
<arg name="lidar_serial_port_id" default="" />

<node name="sick_tim561_2050101" pkg="wr_timxxx" type="sick_tim561_2050101" respawn="true" output="screen">
    <!-- default values: -->
    <!--
    <param name="min_ang" type="double" value="-2.35619449019" />
    <param name="max_ang" type="double" value="2.35619449019" />
    <param name="intensity" type="bool" value="True" />
    <param name="skip" type="int" value="0" />
    <param name="frame_id" type="str" value="laser" />
    <param name="time_offset" type="double" value="-0.001" />
    <param name="publish_datagram" type="bool" value="False" />
    <param name="subscribe_datagram" type="bool" value="false" />
    <param name="device_number" type="int" value="0" />
    <param name="time_increment" type="double" value="0.000061722" />
    <param name="range_min" type="double" value="0.05" />
    <param name="range_max" type="double" value="10.0" />
    -->
    <param name="time_increment" type="double" value="0.000061722" />
    <param name="frame_id" type="str" value="lidar_frame" />
    <param name="scan_topic" type="str" value="$(arg scan_topic)" />

    <!-- Uncomment this to enable TCP instead of USB connection; 'hostname' is the host name or IP address of the laser scanner
    In cases where a race condition exists and the computer boots up before the TIM is ready, increase 'timelimit.'-->
    <param name="hostname" type="string" value="$(arg lidar_ethernet_ip)" />
    <param name="port" type="string" value="2112" />
    <param name="timelimit" type="int" value="5" />
</node>
</launch>
