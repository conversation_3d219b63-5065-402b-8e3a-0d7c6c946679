<?xml version="1.0"?>
<!-- v1.0 created by lawrence.han @2017-04-03;
updated by lhan@2017-06-02
-->

<launch>
<param name="/use_sim_time" value="true" />
<arg name="launch_dir" default="$(find wr_lidar_server)/launch"/>
<arg name="logger_level" default="info"/>
<arg name="lidar_type" default="rplidar"/>
<arg name="lidar_ethernet_ip" default=""/>
<arg name="lidar_serial_port_id" default="/dev/$(arg lidar_type)"/>
<arg name="lidar_enb_lidar_filter" default="true"/>

<!-- 1. using lidar filter -->
<group if="$(arg lidar_enb_lidar_filter)">
<!--include file="$(arg launch_dir)/include/$(arg lidar_type).launch.xml">
    <arg name="scan_topic" value="scan_raw"/>
    <arg name="lidar_ethernet_ip" value="$(arg lidar_ethernet_ip)"/>
    <arg name="lidar_serial_port_id" value="$(arg lidar_serial_port_id)"/>
</include-->
<node pkg="wr_lidar_server" name="lidar_filter_node" type="lidar_filter_node" output="screen" >
    <param name="logger_level" value="$(arg logger_level)"/>
    <param name="raw_scan_topic" value="scan"/>
    <param name="flt_scan_topic" value="scan_filter"/>
    <param name="filter_step" value="1"/>
    <param name="strictness_fac" value="0.1"/>
</node>
</group>

<!-- 2. not using lidar filter -->
<group unless="$(arg lidar_enb_lidar_filter)">
<include file="$(arg launch_dir)/include/$(arg lidar_type).launch.xml">
    <arg name="scan_topic" value="scan"/>
    <arg name="lidar_ethernet_ip" value="$(arg lidar_ethernet_ip)"/>
    <arg name="lidar_serial_port_id" value="$(arg lidar_serial_port_id)"/>
</include>
</group>
</launch>
