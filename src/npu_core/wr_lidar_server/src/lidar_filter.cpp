#include "lidar_filter.h"

namespace wizrobo {
void LidarFilter::Init()
{
#if WR_LOCK == true
    if (!wizrobo::UsbLocker::Unlock())
    {
        ROS_FATAL("Failed to unlock! Pls heck you usblock.");
        return;
    }
#endif
    flag_inited_ = false;

    ROS_INFO("LidarFilter::Init()");
    ros::NodeHandle nh;
    ros::NodeHandle prv_nh("~");

    // logger level
    LoggerLevel logger_level;
    GetEnumParam<LoggerLevel>(prv_nh, "logger_level", logger_level, INFO);
    SetLoggerLevel(logger_level);

    /// ros param
    // check lidar num
    int lidar_num;
    nh.param<int>(STD_SENSOR_PARAM_NS + "/lidar_num", lidar_num, 1);
    if (lidar_num < 1)
    {
        ROS_WARN("LidarFilter::Init(): No lidar, quit.");
        return;
    }
    // check lidar id
    prv_nh.param<int>("lidar_id", lidar_id_, 0);
    if (lidar_id_ < 0 || lidar_id_ >= lidar_num)
    {
        ROS_WARN("LidarFilter#%d::Init(): Unmatched lidar_num and lidar_id, quit.", lidar_id_);
        return;
    }

    // filtering param
    std::string param_ns = STD_SENSOR_PARAM_NS + "/lidar_params/lidar_" + IntToStr(lidar_id_);
    nh.param<bool>(param_ns + "/filter/enb_itl_flt", enb_itl_flt_, false);
    nh.param<bool>(param_ns + "/filter/enb_beam_skip", enb_beam_skip_, false);
    nh.param<bool>(param_ns + "/filter/enb_fov_mask", enb_fov_mask_, false);
    if (!enb_itl_flt_ && !enb_beam_skip_ && !enb_fov_mask_)
    {
        ROS_WARN("LidarFilter#%d::Init(): Filtering disabled, quit.", lidar_id_);
        return;
    }
    if (enb_itl_flt_)
    {
        GetFloatParam(nh, param_ns + "/filter/itl_flt_ori_fac", itl_flt_ori_fac_, 0.1);
        if (itl_flt_ori_fac_ < 0 || itl_flt_ori_fac_ > 1.0)
        {
            itl_flt_ori_fac_ = RANGE(itl_flt_ori_fac_, 0, 1.0);
            //ROS_WARN("Init(): [Illegal value] itl_flt_ori_fac_ must in [0,1]");
        }
        GetFloatParam(nh, param_ns + "/filter/itl_flt_dist_fac", itl_flt_dist_fac_, 0);
        if (itl_flt_dist_fac_ < 0 || itl_flt_dist_fac_ > 1.0)
        {
            itl_flt_dist_fac_ = RANGE(itl_flt_dist_fac_, 0, 1.0);
            //ROS_WARN("Init(): [Illegal value] itl_flt_dist_fac_ must in [0,1]");
        }
    }
    if (enb_beam_skip_)
    {
        nh.param<int>(param_ns + "/filter/beam_skip_step", beam_skip_step_, false);
    }
    else
    {
        beam_skip_step_ = 1;
    }
    if (enb_fov_mask_)
    {
        std::vector<float> default_fov_deg;
        default_fov_deg.push_back(-180);
        default_fov_deg.push_back(180);
        GetFloatMatParam(nh, param_ns + "/filter/fov_mask_deg", fov_mask_deg_, 1, default_fov_deg);
        ROS_WARN("LidarFilter#%d::Init(): fov_mask_deg = %s", lidar_id_, FloatMatParamToStr(fov_mask_deg_).c_str());
    }

    std::string raw_scan_topic;
    std::string flt_scan_topic;
    raw_scan_topic = STD_SCAN_TOPIC_NAME + "_raw_" + IntToStr(lidar_id_);
    if (lidar_num == 1)// only one lidar
    {
        flt_scan_topic = STD_SCAN_TOPIC_NAME;
        flt_scan_frame_id_ = STD_LIDAR_FRAME_ID;
    }
    else
    {
        flt_scan_topic = STD_SCAN_TOPIC_NAME + "_" + IntToStr(lidar_id_);
        flt_scan_frame_id_ = STD_LIDAR_FRAME_ID + "_" + IntToStr(lidar_id_);
    }
    flag_scan_fuser_ = (lidar_num > 1) && (lidar_id_ == 0);

    prv_nh.param<bool>("pub_debug_info", pub_debug_info_, false);

//    /// reconfig
//    RELEASE_POINTER(p_recfg_server_);
//    p_recfg_server_ = new dynamic_reconfigure::Server<wr_lidar_filter::LidarFilterConfig>();
//    dynamic_reconfigure::Server<wr_lidar_filter::LidarFilterConfig>::CallbackType recfg_callback;
//    recfg_callback = boost::bind(&LidarFilter::RecfgCallback, this, _1, _2);
//    p_recfg_server_->setCallback(recfg_callback);

    /// sub & pub
    raw_scan_sub_ = nh.subscribe<sensor_msgs::LaserScan>(raw_scan_topic, 1, &LidarFilter::RawScanCallback, this);
    flt_scan_pub_ = nh.advertise<sensor_msgs::LaserScan>(flt_scan_topic, 1);
    if (flag_scan_fuser_)
    {
        ROS_WARN("LidarFilter#%d::Init(): This is fuser node.", lidar_id_);
        for (int i = 1; i < lidar_num; i++)
        {
            std::string slave_scan_topic = STD_SCAN_TOPIC_NAME + "_" + IntToStr(i);
            ros::Subscriber slave_scan_sub = nh.subscribe<sensor_msgs::LaserScan>(slave_scan_topic, 1, &LidarFilter::SlaveScanCallback, this);
            slave_scan_subs_.push_back(slave_scan_sub);
            sensor_msgs::LaserScan slave_scan;
            slave_scans_.push_back(slave_scan);
            slave_scan_cnts_.push_back(0);
            //tf::StampedTransform tf;
            //slave_lidar_tfs_.push_back(tf);
        }
        fused_scan_pub_ = nh.advertise<sensor_msgs::LaserScan>(STD_SCAN_TOPIC_NAME, 1);
    }

    /// debug info
    if (pub_debug_info_)
    {
        pre_flt_scan_pub_ = nh.advertise<sensor_msgs::LaserScan>(raw_scan_topic + "_pre_flt", 1);
        scan_path_pub_ = nh.advertise<nav_msgs::Path>(raw_scan_topic + "_path", 1, true);
        focus_beam_pub_ = nh.advertise<nav_msgs::Path>(raw_scan_topic + "_focus_beam", 1, true);
    }
    flag_inited_ = true;
}
void LidarFilter::Run()
{
    if (!flag_inited_)
    {
        return;
    }
    ros::spin();
}

void LidarFilter::RecfgCallback(wr_lidar_filter::LidarFilterConfig &config, uint32_t level)
{
    beam_skip_step_ = config.filter_step;
    itl_flt_ori_fac_ = config.ori_strictness_fac;
    itl_flt_dist_fac_ = config.dist_strictness_fac;
    focus_beam_idx_ = config.focus_beam_idx;
}

void LidarFilter::PreFilterScan(const sensor_msgs::LaserScan& raw_scan, sensor_msgs::LaserScan& rlt_scan)
{
    // pre-process raw scan. Intensity is used for storing debugging info.
    int raw_beam_num = raw_scan.ranges.size();
    if (beam_skip_step_ < 1 || beam_skip_step_ > raw_scan.ranges.size())
    {
        //ROS_WARN("LidarFilter#%d::PreprocessScan(): beam_skip_step is out of range %d. Crop it into [1, %d]", lidar_id_, beam_skip_step_, raw_beam_num);
        beam_skip_step_ = RANGE(beam_skip_step_, 1, raw_beam_num);
    }
    int flt_beam_num =  raw_beam_num / beam_skip_step_;
    rlt_scan.header.stamp = ros::Time::now();
    rlt_scan.header.frame_id = raw_scan.header.frame_id;
    rlt_scan.scan_time = raw_scan.scan_time;
    rlt_scan.time_increment = raw_scan.time_increment * beam_skip_step_;
    rlt_scan.angle_min = raw_scan.angle_min;
    rlt_scan.angle_max = raw_scan.angle_min + raw_scan.angle_increment * beam_skip_step_;
    rlt_scan.angle_increment = raw_scan.angle_increment * beam_skip_step_;
    rlt_scan.range_min = raw_scan.range_min;
    rlt_scan.range_max = raw_scan.range_max;

    bool enb_intensity = raw_scan.intensities.size() > 0;
    if (enb_fov_mask_)
    {
        rlt_scan.ranges.resize(flt_beam_num, 0);
        if (enb_intensity)
        {
            rlt_scan.intensities.resize(flt_beam_num, 0);
        }
        for (int i = 0; i < flt_beam_num; i++)
        {
            if (IsInFov(i * beam_skip_step_, fov_min_idxs_, fov_max_idxs_))
            {
                rlt_scan.ranges[i] = raw_scan.ranges[i * beam_skip_step_];
            }
            if (enb_intensity)
            {
                rlt_scan.intensities[i] = raw_scan.intensities[i * beam_skip_step_];
            }
        }
    }
    else if (enb_beam_skip_ && beam_skip_step_ > 1)
    {
        rlt_scan.ranges.resize(flt_beam_num, 0);
        if (enb_intensity)
        {
            rlt_scan.intensities.resize(flt_beam_num, 0);
        }
        for (int i = 0; i < flt_beam_num; i++)
        {
            rlt_scan.ranges[i] = raw_scan.ranges[i * beam_skip_step_];
            if (enb_intensity)
            {
                rlt_scan.intensities[i] = raw_scan.intensities[i * beam_skip_step_];
            }
        }
    }
    else
    {
        rlt_scan.ranges.reserve(raw_beam_num);
        rlt_scan.ranges.insert(rlt_scan.ranges.begin(), raw_scan.ranges.begin(), raw_scan.ranges.end());
        if (enb_intensity)
        {
            rlt_scan.intensities.reserve(raw_beam_num);
            rlt_scan.intensities.insert(rlt_scan.intensities.begin(), raw_scan.intensities.begin(), raw_scan.intensities.end());
        }
    }
}

void LidarFilter::ItlFilterScan(sensor_msgs::LaserScan& pre_flt_scan, sensor_msgs::LaserScan& itl_flt_scan)
{
    itl_flt_scan.header = pre_flt_scan.header;
    itl_flt_scan.scan_time = pre_flt_scan.scan_time;
    itl_flt_scan.time_increment = pre_flt_scan.time_increment;
    itl_flt_scan.angle_min = pre_flt_scan.angle_min;
    itl_flt_scan.angle_max = pre_flt_scan.angle_max;
    itl_flt_scan.angle_increment = pre_flt_scan.angle_increment;
    itl_flt_scan.range_min = pre_flt_scan.range_min;
    itl_flt_scan.range_max = pre_flt_scan.range_max;
    itl_flt_scan.ranges.resize(pre_flt_scan.ranges.size());
    if (pre_flt_scan.intensities.size() >= 0)// save raw intensity to itl_flt_scan
    {
        itl_flt_scan.intensities.reserve(pre_flt_scan.intensities.size());
        itl_flt_scan.intensities.insert(itl_flt_scan.intensities.begin()
                                        , pre_flt_scan.intensities.begin(), pre_flt_scan.intensities.end());
    }
    pre_flt_scan.intensities.resize(pre_flt_scan.ranges.size(), 0);// virtual intensity

    // scan path
    nav_msgs::Path scan_path;

    if (pub_debug_info_)
    {
        // focus beam
        focus_beam_idx_ = RANGE(focus_beam_idx_, 0, pre_flt_scan.ranges.size());
        nav_msgs::Path focus_beam;
        focus_beam.header.stamp = ros::Time::now();
        focus_beam.header.frame_id = pre_flt_scan.header.frame_id;
        geometry_msgs::PoseStamped start_ps;
        start_ps.header.stamp = ros::Time::now();
        start_ps.header.frame_id = pre_flt_scan.header.frame_id;
        start_ps.pose.orientation.w = 1;
        focus_beam.poses.push_back(start_ps);
        geometry_msgs::PoseStamped end_ps;
        end_ps.header.stamp = ros::Time::now();
        end_ps.header.frame_id = pre_flt_scan.header.frame_id;
        double r_idx = pre_flt_scan.ranges[focus_beam_idx_];
        double yaw_idx = pre_flt_scan.angle_increment * focus_beam_idx_ + pre_flt_scan.angle_min;
        end_ps.pose.position.x = r_idx * cos(yaw_idx);
        end_ps.pose.position.y = r_idx * sin(yaw_idx);
        tf::Quaternion quat_idx;
        quat_idx.setRPY(0.0, 0.0, yaw_idx);
        tf::quaternionTFToMsg(quat_idx, end_ps.pose.orientation);
        focus_beam.poses.push_back(end_ps);
        focus_beam_pub_.publish(focus_beam);
    }

    float ori_equ_thrs = itl_flt_ori_fac_ * M_PI;
    std::vector<double> ori_diff_arr;
    int flt_flag = 0;
    double flt_range = DBL_MAX;
    int int_flt_flag = 0;
    double int_flt_range = DBL_MAX;
    for (int i = 0; i < pre_flt_scan.ranges.size() - 1; i++)
    {
        bool preset_flag = false;
        double d0 = pre_flt_scan.ranges[i];
        double d1 = pre_flt_scan.ranges[i + 1];
        if (i != 0 && d0 < pre_flt_scan.range_min && d1 >= pre_flt_scan.range_min)
        {
            d0 = 0.5 * (pre_flt_scan.ranges[i - 1] + d1);
        }
        else if (d0 >= pre_flt_scan.range_min && d1 < pre_flt_scan.range_min)
        {
            d1 = 0.5* (d0 + pre_flt_scan.ranges[i + 2]);
        }
//        if (i >= 490 && i < 500)
//        {
//            ROS_DEBUG("beam%d: range=%.4f, d0=%.4f, d1=%.4f", i, pre_flt_scan.ranges[i], d0, d1);
//        }
        if (d0 < pre_flt_scan.range_min || d1 < pre_flt_scan.range_min)
        {
            if (flt_flag != 0)// end of flt-mode
            {
                flt_range = MIN(flt_range, d0);
                for (int j = 1; j < fabs(flt_flag); j++)
                {
                    itl_flt_scan.ranges[i - j] = 0;
                }
                flt_range = DBL_MAX;
                flt_flag = 0;
            }
            itl_flt_scan.ranges[i] = 0;//smp_flt_scan.ranges[i];
            pre_flt_scan.intensities[i] = 0;
        }
        else
        {
            double yaw0 = pre_flt_scan.angle_increment * i + pre_flt_scan.angle_min;
            double yaw1 = pre_flt_scan.angle_increment * (i + 1) + pre_flt_scan.angle_min;
            double x0 = d0 * cos(yaw0);
            double y0 = d0 * sin(yaw0);
            double x1 = d1 * cos(yaw1);
            double y1 = d1 * sin(yaw1);
            double gama = atan2(y1 - y0, x1 - x0);
            double lin_dist = sqrt(SQU(x1 - x0) + SQU(y1 - y0));
            double arc_dist = itl_flt_scan.angle_increment * MIN(x0,x1);

            double ori_diff = yaw0 - gama;
            if (fabs(ori_diff) < ori_equ_thrs)
            {
                if (flt_flag == 0)// start pos-flt-mode
                {
                    if (int_flt_flag > 0)// found int-pos-flt-mode
                    {
                        ROS_ASSERT(int_flt_flag == 1);
                        flt_range = MIN(int_flt_range, d0);
                        itl_flt_scan.ranges[i] = 0;
                        preset_flag = true;
                        itl_flt_scan.ranges[i - 1] = 0;

                        if (pub_debug_info_)
                        {
                            geometry_msgs::PoseStamped& last_ps = scan_path.poses.back();
                            last_ps.pose.orientation = ros_ext::RotateGeoQuat(last_ps.pose.orientation, tf::Vector3(0,0,1), M_PI/2);
                        }
                        int_flt_flag = 0;
                        pre_flt_scan.intensities[i - 1] = 1;
                    }
                    else
                    {
                        flt_range = d0;
                    }
                    flt_flag = 1;
                }
                else if (flt_flag > 0)// continue pos-flt-mode
                {
                    flt_range = MIN(flt_range, d0);
                    flt_flag++;
                }
                else// flt_flag < 0// end of neg-flt-mode and start pos-flt-mode
                {
                    flt_range = MIN(flt_range, d0);
                    for (int j = 1; j < fabs(flt_flag); j++)
                    {
                        itl_flt_scan.ranges[i - j] = 0;//flt_range;
                    }
                    flt_range = d0;
                    flt_flag = 1;
                }
                gama += M_PI/4;
                pre_flt_scan.intensities[i] = 2;
            }
            else if (fabs(fabs(ori_diff) - M_PI) < ori_equ_thrs)
            {
                if (flt_flag == 0)// start neg-flt-mode
                {
                    if (int_flt_flag < 0)// found int-neg-flt-mode
                    {
                        ROS_ASSERT(int_flt_flag == -1);
                        flt_range = MIN(int_flt_range, d0);
                        itl_flt_scan.ranges[i] = 0;
                        preset_flag = true;
                        itl_flt_scan.ranges[i - 1] = 0;

                        if (pub_debug_info_)
                        {
                            geometry_msgs::PoseStamped& last_ps = scan_path.poses.back();
                            last_ps.pose.orientation = ros_ext::RotateGeoQuat(last_ps.pose.orientation, tf::Vector3(0,0,1), -M_PI/2);
                        }
                        int_flt_flag = 0;
                        pre_flt_scan.intensities[i - 1] = -1;
                    }
                    else
                    {
                        flt_range = d0;
                    }
                    flt_flag = -1;
                }
                else if (flt_flag < 0)// continue neg-flt-mode
                {
                    flt_range = MIN(flt_range, d0);
                    flt_flag--;
                }
                else// flt_flag > 0// end of pos-flt-mode and start neg-flt-mode
                {
                    flt_range = MIN(flt_range, d0);
                    for (int j = 1; j < fabs(flt_flag); j++)
                    {
                        itl_flt_scan.ranges[i - j] = 0;//flt_range;
                    }
                    flt_range = d0;
                    flt_flag = -1;
                }
                gama -= M_PI/4;
                pre_flt_scan.intensities[i] = -2;
            }
            else// end flt-mode
            {
                if (flt_flag != 0)
                {
                    if ((fabs(ori_diff) < M_PI/2 || fabs(fabs(ori_diff) - M_PI) < M_PI/2)
                            && (lin_dist <=  10 * itl_flt_dist_fac_ * arc_dist))// double check: x0 is to close to x1, still in last mode
                    {
                        int_flt_range = MIN(flt_range, d0);
                        int_flt_flag = SIGN(flt_flag);
                        pre_flt_scan.intensities[i] = SIGN(flt_flag);
                    }
                    else
                    {
                        pre_flt_scan.intensities[i] = 0;
                    }
                    for (int j = 1; j < fabs(flt_flag); j++)
                    {
                        itl_flt_scan.ranges[i - j] = 0;//flt_range;
                    }
                    flt_range = DBL_MAX;
                    flt_flag = 0;
                }
                else
                {
                    if (int_flt_flag != 0)
                    {
                        pre_flt_scan.intensities[i - 1] = 0;
                    }
                    int_flt_range = DBL_MAX;
                    int_flt_flag = 0;
                    pre_flt_scan.intensities[i] = 0;
                }
            }

            if (pub_debug_info_)
            {
                geometry_msgs::Pose p;
                p.position = ros_ext::XYZ2GeoPoint(d0 * cos(yaw0), d0 * sin(yaw0), 0);
                p.orientation = ros_ext::RPY2GeoQuat(0, 0, gama);;
                geometry_msgs::PoseStamped ps;
                ps.header.seq = i;
                ps.header.stamp = ros::Time::now();
                ps.header.frame_id = pre_flt_scan.header.frame_id;
                ps.pose = p;
                scan_path.poses.push_back(ps);
            }

            ori_diff_arr.push_back(ori_diff);
        }
        if (!preset_flag)
        {
            itl_flt_scan.ranges[i] = pre_flt_scan.ranges[i];
        }
        //itl_flt_scan.intensities[i] = pre_flt_scan.intensities[i];
    }

    pre_flt_scan.intensities.back() = 0;//pre_flt_scan.intensities[i];
    if (pub_debug_info_)
    {
        pre_flt_scan_pub_.publish(pre_flt_scan);
        scan_path.header.stamp = ros::Time::now();
        scan_path.header.frame_id = pre_flt_scan.header.frame_id;
        scan_path_pub_.publish(scan_path);
    }
}

void LidarFilter::RawScanCallback(const sensor_msgs::LaserScan::ConstPtr& scan_msg)
{
    static int callback_cnt = 0;
    ROS_DEBUG_ONCE("LidarFilter#%d::RawScanCallback()#%d: frame_id = %s", lidar_id_, callback_cnt, scan_msg->header.frame_id.c_str());
    const sensor_msgs::LaserScan& raw_scan = *scan_msg;
    if (enb_fov_mask_ && callback_cnt == 0)
    {
        fov_min_idxs_.clear();
        fov_max_idxs_.clear();
        for (int i = 0; i < fov_mask_deg_.size(); i++)
        {
            float min_ang = RANGE(DEG2RAD(fov_mask_deg_[i][0]), raw_scan.angle_min, raw_scan.angle_max);
            float max_ang = RANGE(DEG2RAD(fov_mask_deg_[i][1]), raw_scan.angle_min, raw_scan.angle_max);
            ROS_INFO("LidarFilter#%d::RawScanCallback(): fov_mask#%d: min_ang = %.2f, max_ang = %.2f", lidar_id_, i, min_ang, max_ang);
            ROS_ASSERT(min_ang <= max_ang);
            int min_idx = static_cast<int>(ceil((min_ang - raw_scan.angle_min) / raw_scan.angle_increment));
            int max_idx = static_cast<int>(ceil((max_ang - raw_scan.angle_min) / raw_scan.angle_increment));
            fov_min_idxs_.push_back(min_idx);
            fov_max_idxs_.push_back(max_idx);
            ROS_INFO("LidarFilter#%d::RawScanCallback(): fov_mask#%d: min_idx = %d, max_idx = %d", lidar_id_, i, min_idx, max_idx);
        }
    }

    /// beam reduce
    sensor_msgs::LaserScan pre_flt_scan;
    if (enb_fov_mask_ || enb_beam_skip_)
    {
        PreFilterScan(raw_scan, pre_flt_scan);
    }
    else
    {
        pre_flt_scan = raw_scan;
    }

    /// intelligent filtering
    sensor_msgs::LaserScan itl_flt_scan;
    if (enb_itl_flt_)
    {
        ItlFilterScan(pre_flt_scan, itl_flt_scan);
    }
    else
    {
        itl_flt_scan = pre_flt_scan;
    }
    itl_flt_scan.header.frame_id = flt_scan_frame_id_;
    flt_scan_pub_.publish(itl_flt_scan);

    /// scan fusion
    if (flag_scan_fuser_)
    {
        fuse_lock_.lock();
        double fuse_range_min = itl_flt_scan.range_min;
        double fuse_range_max = itl_flt_scan.range_max;
        double fuse_ang_min = -M_PI;
        double fuse_ang_max = M_PI;
        double fuse_ang_inc = itl_flt_scan.angle_increment;
        int fuse_beam_num = static_cast<int>(ceil(2 * M_PI / fuse_ang_inc));
        std::vector<float> fuse_ranges;
        std::vector<float> fuse_intensities;

        int offset = static_cast<int>(ceil((itl_flt_scan.angle_min - fuse_ang_min)/fuse_ang_inc));
        int emp_num = fuse_beam_num - itl_flt_scan.ranges.size();
        fuse_ranges.resize(emp_num, 0);
        fuse_ranges.insert(fuse_ranges.begin() + offset, itl_flt_scan.ranges.begin(), itl_flt_scan.ranges.end());
        if (itl_flt_scan.intensities.empty())
        {
            fuse_intensities.resize(fuse_beam_num, 0);
        }
        else
        {
            fuse_intensities.resize(emp_num, 0);
            fuse_ranges.insert(fuse_ranges.begin() + offset, itl_flt_scan.ranges.begin(), itl_flt_scan.ranges.end());
        }

        laser_geometry::LaserProjection prj;
        for (int i = 0; i < slave_scans_.size(); i++)
        {
            const sensor_msgs::LaserScan& slave_scan = slave_scans_[i];
            double dt = (raw_scan.header.stamp - slave_scan.header.stamp).toSec();
            ROS_DEBUG("LidarFilter#%d::RawScanCallback(): Fusion#%d: dt = %.4f [s]", lidar_id_, i, dt);
            if (dt > raw_scan.scan_time)// slave_scan is too old
            {
                continue;
            }
            sensor_msgs::PointCloud slave_scan_pc;
            try
            {
                prj.transformLaserScanToPointCloud(STD_LIDAR_FRAME_ID, slave_scan, slave_scan_pc, *p_tf_sub_, -1);
            }
            catch (tf::TransformException e)
            {
                ROS_ERROR("LidarFilter#%d::RawScanCallback(): Fusion#%d: Failed to get transofrm (%s -> %s) [%s]"
                          , lidar_id_, i, slave_scan.header.frame_id.c_str(), STD_LIDAR_FRAME_ID.c_str(), e.what());
                continue;
            }
            //ROS_WARN("slave_scan_ps.points.size() = %d", static_cast<int>(slave_scan_pc.points.size()));
            for (int j = 0; j < slave_scan_pc.points.size(); j++)
            {
                geometry_msgs::Point32& p = slave_scan_pc.points[j];
                double radius = sqrt(SQU(p.x) + SQU(p.y));
                double theta = atan2(p.y, p.x);
                int idx = static_cast<int>(ceil((theta - fuse_ang_min)/fuse_ang_inc));
                if (idx < 0 || idx > fuse_beam_num)
                {
                    continue;
                }
                else if (radius < fuse_ranges[idx])
                {
                    fuse_ranges[idx] = radius;
                    if (!slave_scan.intensities.empty())
                    {
                        fuse_intensities[idx] = slave_scan.intensities[j];
                    }
                }
            }
            fuse_range_min = MIN(fuse_range_min, slave_scan.range_min);
            fuse_range_max = MAX(fuse_range_max, slave_scan.range_max);
        }
        sensor_msgs::LaserScan fuse_scan;
        fuse_scan.header.frame_id = STD_LIDAR_FRAME_ID;
        fuse_scan.header.stamp = ros::Time::now();
        fuse_scan.angle_min = fuse_ang_min;
        fuse_scan.angle_max = fuse_ang_max;
        fuse_scan.angle_increment = fuse_ang_inc;
        fuse_scan.range_min = fuse_range_min;
        fuse_scan.range_max = fuse_range_max;
        fuse_scan.scan_time = itl_flt_scan.scan_time;
        fuse_scan.time_increment = itl_flt_scan.time_increment;
        fuse_scan.ranges.swap(fuse_ranges);
        fuse_scan.intensities.swap(fuse_intensities);
        fused_scan_pub_.publish(fuse_scan);
        fuse_lock_.unlock();
    }

    ROS_DEBUG_ONCE("LidarFilter#%d::RawScanCallback()#%d: raw_scan beam num = %d, beam_skip_step = %d, itl_flt_ori_fac = %.2f, itl_flt_scan beam num = %d"
             , lidar_id_, callback_cnt, static_cast<int>(raw_scan.ranges.size()), beam_skip_step_, itl_flt_ori_fac_, static_cast<int>(itl_flt_scan.ranges.size()));
    callback_cnt++;
}

void LidarFilter::SlaveScanCallback(const sensor_msgs::LaserScan::ConstPtr &scan_msg)
{
    static int callback_cnt = 0;
    const sensor_msgs::LaserScan& slave_scan = *scan_msg;
    std::string frame_id_str = slave_scan.header.frame_id;
    int idx = frame_id_str.rfind("_");
    std::string str = frame_id_str.substr(idx + 1, frame_id_str.size() - idx - 1);
    int scan_id = atoi(str.c_str());
    ROS_DEBUG_ONCE("LidarFilter#%d::SlaveScanCallback()#%d: frame_id = %s, scan_id = %d", lidar_id_, callback_cnt, scan_msg->header.frame_id.c_str(), scan_id);

    fuse_lock_.lock();
    slave_scans_[scan_id - 1] = slave_scan;
//    if (slave_scan_cnts_[scan_id - 1] == 0)
//    {
//        try
//        {
//            p_tf_sub_->waitForTransform(STD_LIDAR_FRAME_ID, slave_scan.header.frame_id, ros::Time::now(), ros::Duration(0.1));
//            p_tf_sub_->lookupTransform(STD_LIDAR_FRAME_ID, slave_scan.header.frame_id, ros::Time(0), slave_lidar_tfs_[scan_id - 1]);
//        }
//        catch (tf::TransformException e)
//        {
//            ROS_ERROR("MapBuilder::SetLidarParam_(): Failed to get lidar_pose by TF (%s -> %s) [%s]"
//                      , STD_BASE_FRAME_ID.c_str(), STD_LIDAR_FRAME_ID.c_str(), e.what());
//            return;
//        }
//    }
    slave_scan_cnts_[scan_id - 1]++;
    fuse_lock_.unlock();
    callback_cnt++;
}
}// namespace wizrobo
