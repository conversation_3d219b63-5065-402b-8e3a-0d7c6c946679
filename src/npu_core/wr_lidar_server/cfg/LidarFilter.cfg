#! /usr/bin/env python
#
# Copyright (C) 2017, Linkmiao Robotics Co,. LTD.
# All rights reserved.
#  Created on: 29.03.2017
#
#      Author: <PERSON> <<EMAIL>>
#

PACKAGE='wr_lidar_filter'
from dynamic_reconfigure.parameter_generator_catkin import *

from math import pi

gen = ParameterGenerator()
#       Name                Type      Reconfiguration level     Description                                    Default     Min         Max
gen.add("filter_step",      int_t,      0,                      "The scan reserving factor.",       1,      1)
gen.add("ori_strictness_fac",   double_t,   0,                      "The stricteness of orientation filtering.",    0.1,    0,      1.0)
gen.add("dist_strictness_fac",  double_t,   0,                      "The stricteness of distance filtering.",    0.5,    0,      1.0)
gen.add("focus_beam_idx",  int_t,   0,                      "The index of focus scan beam.",    0,    0,      9999)

exit(gen.generate(PACKAGE, "lidar_filter_node", "LidarFilter"))
