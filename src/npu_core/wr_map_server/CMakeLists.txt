cmake_minimum_required(VERSION 2.8.3)
project(wr_map_server)

find_package(catkin REQUIRED
    COMPONENTS
    roscpp
    tf
    nav_msgs
    geometry_msgs
    sensor_msgs
    message_filters
    wr_npu_core
    wr_npu_msgs
    wr_npu_ice
    cv_bridge
    image_transport
)
find_package(OpenCV)

#set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fpermissive")

find_package(Boost REQUIRED COMPONENTS system)
find_package(Ice REQUIRED Ice IceUtil)

find_package(PkgConfig)
  pkg_check_modules(NEW_YAMLCPP yaml-cpp>=0.5)
if(NEW_YAMLCPP_FOUND)
  add_definitions(-DHAVE_NEW_YAMLCPP)
endif(NEW_YAMLCPP_FOUND)

catkin_package(
    INCLUDE_DIRS include
    LIBRARIES map_handler
    CATKIN_DEPENDS roscpp tf nav_msgs
)

include_directories(
  ${WR_LOCK_INCLUDE}
  ${catkin_INCLUDE_DIRS} 
  ${Boost_INCLUDE_DIRS}
  ${ICE_INCLUDE_DIRS}
  ${EIGEN3_INCLUDE_DIR}
  include 
  include/ice
)

#add_library(image_loader src/image_loader.cpp)
#target_link_libraries(image_loader SDL SDL_image ${Boost_LIBRARIES})

add_library(map_handler
  src/map_handler.cpp
)
add_dependencies(map_handler wr_npu_msgs_generate_messages_cpp)
target_link_libraries(map_handler
  ${Boost_LIBRARIES}
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  ${Ice_LIBRARIES}
  SDL
  SDL_image
  opencv_core
  opencv_imgproc
  opencv_highgui
  opencv_imgproc
  yaml-cpp
  pthread
  #npuice
)

add_executable(map_server_node
  src/map_server_main.cpp
  src/map_server.cpp
)
add_dependencies(map_server_node wr_npu_msgs_generate_messages_cpp)
target_link_libraries(map_server_node
  ${Boost_LIBRARIES}
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  ${Ice_LIBRARIES}
  pthread
  map_handler
  #npuice
)

add_executable(map_publisher_node
  src/map_publisher.cpp
)
target_link_libraries(map_publisher_node
  ${Boost_LIBRARIES}
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  ${Ice_LIBRARIES}
  map_handler
)

add_executable(map_saver_node
  src/map_saver.cpp
)
target_link_libraries(map_saver_node
  ${Boost_LIBRARIES}
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
)

## copy test data to same place as tests are run
#function(copy_test_data)
#    cmake_parse_arguments(PROJECT "" "" "FILES" ${ARGN})
#    foreach(datafile ${PROJECT_FILES})
#        file(COPY ${datafile} DESTINATION ${PROJECT_BINARY_DIR}/test)
#    endforeach()
#endfunction()

### Tests
#if(CATKIN_ENABLE_TESTING)
#  copy_test_data( FILES
#      test/testmap.bmp
#      test/testmap.png )
#  catkin_add_gtest(${PROJECT_NAME}_utest test/utest.cpp test/test_constants.cpp)
#  target_link_libraries(${PROJECT_NAME}_utest map_loader_node SDL SDL_image)

#  add_executable(rtest test/rtest.cpp test/test_constants.cpp)
#  target_link_libraries( rtest
#      gtest
#      ${catkin_LIBRARIES}
#  )
#  add_dependencies(rtest nav_msgs_gencpp)

#  # This has to be done after we've already built targets, or catkin variables get borked
#  find_package(rostest)
#  add_rostest(test/rtest.xml)
#endif()

## Install executables and/or libraries
install(TARGETS map_server_node map_publisher_node map_handler map_saver_node
  ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})

## Install project namespaced headers
install(DIRECTORY include/
  DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
  FILES_MATCHING PATTERN "*.h"
  PATTERN ".svn" EXCLUDE
)

install(DIRECTORY launch
  DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
  USE_SOURCE_PERMISSIONS
)
set(CMAKE_CXX_FLAGS "-std=c++11 ${CMAKE_CXX_FLAGS}")

## Install excutable python script
# install( 
#     PROGRAMS
#     scripts/crop_map
#     DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})
