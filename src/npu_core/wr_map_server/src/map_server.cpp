#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <npu.h>
#include <map_server.h>
#include <map_handler.h>
#include <math.h>

#define MAPS_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("map_server", "MapServer::" fmt, ##arg)
#define MAPS_INFO(fmt, arg...)  ROS_INFO_NAMED("map_server",  "MapServer::" fmt, ##arg)
#define MAPS_WARN(fmt, arg...)  ROS_WARN_NAMED("map_server",  "MapServer::" fmt, ##arg)
#define MAPS_ERROR(fmt, arg...) ROS_ERROR_NAMED("map_server", "MapServer::" fmt, ##arg)

#define MAPS_DEBUG_ONCE(fmt, arg...) ROS_DEBUG_ONCE_NAMED("map_server", "MapServer::" fmt, ##arg)

namespace wizrobo_npu {

using namespace std;

template<typename T>
int FindObjectByName(const T& list, string& name)
{
    int i;
    for(i=0;i<list.size();i++)
    {
        if (list[i].info.id == name)
        {
            return i;
        }
    }
    return -1;
}

int FindMapObject(const MapObjectList& list, string& name)
{
    return FindObjectByName<MapObjectList>(list, name);
}

int FindTask(const TaskList& tasks, string& name)
{
//    return FindObjectByName<TaskList>(tasks, name);
    int i;
    for(i=0;i<tasks.size();i++)
    {
        if (tasks[i].info.task_id == name)
        {
            return i;
        }
    }
    return -1;
}

int FindPath(const PathList& paths, string& name)
{
    return FindObjectByName<PathList>(paths, name);
}

int FindPath(const ImgPathList& paths, string& name)
{
    return FindObjectByName<ImgPathList>(paths, name);
}

int FindVirtualWall(const VirtualWallList& virtual_walls, string& name)
{
    return FindObjectByName<VirtualWallList>(virtual_walls, name);
}

int FindVirtualWall(const ImgVirtualWallList& virtual_walls, string& name)
{
    return FindObjectByName<ImgVirtualWallList>(virtual_walls, name);
}

int FindStation(const StationList& stations, string& name)
{
    return FindObjectByName<StationList>(stations, name);
}

void MapServer::Init()
{
    MAPS_INFO("Map server initialize.");
    ros::NodeHandle nh;
    ros::NodeHandle prv_nh("~");

    /// get param
    prv_nh.param<string>("map_dir_path", map_dir_path_, "");
    prv_nh.param<string>("frame_id", map_frame_id_, "map_frame");
    nh.param<string>(STD_CORE_PARAM_NS + "/MAP_ID", current_map_id_, "");

    if (map_dir_path_ == "")
    {
        throw "Map directory is invalid!";
    }
    if (current_map_id_ == "")
    {
        throw "Current map name is invalid";
    }

    MAPS_INFO("Map dir path: \"%s\".", map_dir_path_.c_str());
    MAPS_INFO("Current map id: \"%s\".", current_map_id_.c_str());

    if (!p_map_handler_)
    {
        SAFE_RELEASE(p_map_handler_);
    }
    p_map_handler_ = new MapHandler(map_dir_path_);

    service_ = prv_nh.advertiseService("map_server", &MapServer::Serve, this);
    service_set_map_ = nh.advertiseService("/static_map", &MapServer::SetMap, this);

    service_function_map_["GetCurrentMap"] = &MapServer::GetCurrentMap;
    service_function_map_["GetCurrentImgMap"] = &MapServer::GetCurrentImgMap;
    service_function_map_["GetMapInfos"] = &MapServer::GetMapInfos;
    service_function_map_["SetMapInfos"] = &MapServer::SetMapInfos;
    service_function_map_["LoadMap"] = &MapServer::LoadMap;
    service_function_map_["SaveMap"] = &MapServer::SaveMap;
    service_function_map_["ExportMapFile"] = &MapServer::ExportMapFile;
    service_function_map_["ImportMapFile"] = &MapServer::ImportMapFile;
    service_function_map_["SetTasks"] = &MapServer::SetTasks;
    service_function_map_["GetTaskList"] = &MapServer::GetTaskList;
//    service_function_map_["ExecuteTask"] = &MapServer::ExecuteTask;
    service_function_map_["SetImgPaths"] = &MapServer::SetImgPaths;
    service_function_map_["GetImgPaths"] = &MapServer::GetImgPaths;
    service_function_map_["SetPaths"] = &MapServer::SetPaths;
    service_function_map_["GetPaths"] = &MapServer::GetPaths;
    service_function_map_["GetPath"] = &MapServer::GetPath;
    service_function_map_["GetCurrentImgPath"] = &MapServer::GetCurrentImgPath;
    service_function_map_["GetCurrentPath"] = &MapServer::GetCurrentPath;
    service_function_map_["SetImgVirtualWalls"] = &MapServer::SetImgVirtualWalls;
    service_function_map_["GetImgVirtualWalls"] = &MapServer::GetImgVirtualWalls;
    service_function_map_["SetVirtualWalls"] = &MapServer::SetVirtualWalls;
    service_function_map_["GetVirtualWalls"] = &MapServer::GetVirtualWalls;
    service_function_map_["GetVirtualWall"] = &MapServer::GetVirtualWall;
    service_function_map_["GetCurrentImgVirtualWall"] = &MapServer::GetCurrentImgVirtualWall;
    service_function_map_["GetCurrentVirtualWall"] = &MapServer::GetCurrentVirtualWall;
    service_function_map_["SetImgStations"] = &MapServer::SetImgStations;
    service_function_map_["GetImgStations"] = &MapServer::GetImgStations;
    service_function_map_["SetStations"] = &MapServer::SetStations;
    service_function_map_["GetStations"] = &MapServer::GetStations;
    service_function_map_["GetStation"] = &MapServer::GetStation;
    service_function_map_["SetServerMode"] = &MapServer::SetServerMode;
    service_function_map_["TransformImgPosesTo3DPoses"] = &MapServer::TransformImgPosesTo3DPoses;
    service_function_map_["Transform3DPosesToImgPoses"] = &MapServer::Transform3DPosesToImgPoses;

    flc_srv_ = prv_nh.advertiseService("force_loop_closure", &MapServer::ForceLoopClosureCallback, this);

    map_list_.clear();
    pthread_spin_init(&map_object_spinlock_, PTHREAD_PROCESS_PRIVATE);
}

void MapServer::Run()
{
    MAPS_INFO("Map server is running.");
    LoadAllMapInfo();

    string mode;
    ros::NodeHandle prv_nh("~");
    prv_nh.param<string>("map_server_mode", mode, "NAVI_MODE");
    SwitchServerMode(mode);

    /// Use 4 threads
    ros::AsyncSpinner spinner(4);
    spinner.start();
    ros::waitForShutdown();
}

static void _FilterOutLoaded(MapObjectList &map_list,
    vector<string> &maps_to_be_loaded, map<string, string> &map_md5_list)
{
    MapObjectList::iterator p_loaded_map = map_list.begin();
    while(p_loaded_map!=map_list.end())
    {
        vector<string>::iterator p_map_to_be_loaded = maps_to_be_loaded.begin();
        for(;p_map_to_be_loaded!=maps_to_be_loaded.end();p_map_to_be_loaded++)
        {
            if ((*p_map_to_be_loaded) == (*p_loaded_map).info.id)
            {
                MAPS_DEBUG("Map \"%s\" is loaded already(%s/%s).",
                    (*p_map_to_be_loaded).c_str(),
                    map_md5_list[(*p_map_to_be_loaded)].c_str(),
                    (*p_loaded_map).add_info.md5sum.c_str());
                break;
            }
        }
        if (p_map_to_be_loaded == maps_to_be_loaded.end())
        {
            p_loaded_map ++;
            continue;
        }
        if (map_md5_list[(*p_map_to_be_loaded)] == (*p_loaded_map).add_info.md5sum)
        {
            maps_to_be_loaded.erase(p_map_to_be_loaded);
            p_loaded_map ++;
            continue;
        }
        MAPS_DEBUG("But md5 is different, should be reloaded.");
        p_loaded_map = map_list.erase(p_loaded_map);
    }
}

static void _FilterOutRemoved(MapObjectList &map_list, vector<string> &map_files)
{
    MapObjectList::iterator p_loaded_map = map_list.begin();
    while(p_loaded_map!=map_list.end())
    {
        if ((*p_loaded_map).info.id == "IDLE_MAP"
                || (*p_loaded_map).info.id == "NEW_MAP")
        {
            p_loaded_map++;
            continue;
        }
        vector<string>::iterator p_map_file = map_files.begin();
        for(;p_map_file!=map_files.end();p_map_file++)
        {
            if ((*p_map_file) == (*p_loaded_map).info.id)
            {
                break;
            }
        }
        if(p_map_file == map_files.end())
        {            
            MAPS_DEBUG("\"%s\" have been removed.", p_loaded_map->info.id.c_str());
            p_loaded_map = map_list.erase(p_loaded_map);
        }
        else
        {
            p_loaded_map++;
        }
    }
}
static void _InsertIdleMap(MapObjectList &map_list, int map_index)
{
    MapObject *p_map_obj;
    p_map_obj = new MapObject;
    p_map_obj->info.index = map_index;
    p_map_obj->info.id = "IDLE_MAP";
    map_list.push_back(*p_map_obj);
    delete p_map_obj;
}
void MapServer::LoadAllMapInfo()
{
    vector<string> maps_to_be_loaded;
    map<string, string> map_md5_list;
    MAPS_DEBUG("Load all map info.");

    if (map_list_.size() == 0)
    {
        /* map_list_ is empty means there is no map loaded, insert an idle one. */
        _InsertIdleMap(map_list_, NewMapIndex());
    }
    /* Get map filenames in MAP_DIR */
    p_map_handler_->ListAllMap(maps_to_be_loaded);
    if (maps_to_be_loaded.size() == 0)
    {
        MAPS_WARN("LoadAllMapInfo(): No map avaliable.");
        MAPS_WARN("LoadAllMapInfo(): Set IDLE map as current map.");
        return;
    }

    /* delete the map been removed from map_list_ */
    _FilterOutRemoved(map_list_, maps_to_be_loaded);

    /* ignore the map been loaded into map_list_ already  */
    p_map_handler_->ReadMapMD5(maps_to_be_loaded, map_md5_list);
    _FilterOutLoaded(map_list_, maps_to_be_loaded, map_md5_list);

    /* load map */
    for(int i=0;i<maps_to_be_loaded.size();i++)
    {
        MapObject *p_map_obj;
        MAPS_DEBUG("LoadAllMapInfo(): Loading map: \"%s\".", maps_to_be_loaded[i].c_str());
        p_map_obj = new MapObject;
        if (p_map_obj == NULL)
        {
            MAPS_DEBUG("!!!!!!!!!!!!!!!!!!!!!");
        }

        p_map_handler_->ReadMapInfo(maps_to_be_loaded[i], *p_map_obj);

        if (p_map_obj->info.id == "")
        {
            MAPS_DEBUG("LoadAllMapInfo(): FAILED.");
            delete p_map_obj;
            continue;
        }
        p_map_obj->info.index = NewMapIndex();
        map_list_.push_back(*p_map_obj);
        MAPS_DEBUG("LoadAllMapInfo(): SUCCESSED.");
        delete p_map_obj;
    }
    return;
}

void MapServer::PubCurrentMap()
{
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("PubCurrentMap(): Current map id: \"%s\" is unavaliable!", current_map_id_.c_str());
        return;
    }
    MapObject& map_obj = map_list_[index];
    MAPS_INFO("PubCurrentMap(): Publish current map: \"%s\"", map_obj.info.id.c_str());
    ros::NodeHandle nh;

    nh.setParam(STD_SENSOR_PARAM_NS + "/base_pose_x", map_obj.add_info.position_x);
    nh.setParam(STD_SENSOR_PARAM_NS + "/base_pose_y", map_obj.add_info.position_y);
    nh.setParam(STD_SENSOR_PARAM_NS + "/base_pose_yaw", map_obj.add_info.position_yaw);
    nh.setParam(STD_SENSOR_PARAM_NS + "/longitude", map_obj.add_info.longitude);
    nh.setParam(STD_SENSOR_PARAM_NS + "/latitude", map_obj.add_info.latitude);
    nh.setParam(STD_SENSOR_PARAM_NS + "/angles", map_obj.add_info.angles);

    nav_msgs::OccupancyGrid map_msg;
    wr_npu_msgs::Virtualwalls virtualwalls_msg;
    map_msg.header.frame_id = map_frame_id_;
    map_msg.header.stamp = ros::Time::now();
    p_map_handler_->MakeMapMessage(map_list_[index], map_msg);
    p_map_handler_->MakeVirtualwallMessage(map_list_[index], virtualwalls_msg);
    map_pub_ = nh.advertise<nav_msgs::OccupancyGrid>(STD_MAP_TOPIC_NAME, 1, true);
    virtualwall_pub_ = nh.advertise<wr_npu_msgs::Virtualwalls>(STD_VIRTUALWALL_TOPIC_NAME, 1, true);
    map_pub_.publish(map_msg);
    virtualwall_pub_.publish(virtualwalls_msg);

    map_meta_pub_ = nh.advertise<nav_msgs::MapMetaData>("/mate_map", 1, true);
    map_meta_pub_.publish(map_msg.info);

}

void MapServer::RawMapCallback_(const nav_msgs::OccupancyGridConstPtr& msg)
{
    static int rindex = 0;
    MAPS_DEBUG("RawMapCallback_(): ReceiveMap index = %d", rindex);
    MAPS_DEBUG("RawMapCallback_(): current_map_id_ is \"%s\"", current_map_id_.c_str());
    rindex ++;

    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("RawMapCallback_():Current map id: \"%s\" is unavaliable!", current_map_id_.c_str());
        return;
    }
    MapObject& map_object = map_list_[index];
    pthread_spin_lock(&map_object_spinlock_);
    map_raw_ = *msg;
    if (!enb_map_opt_)
    {
        p_map_handler_->UpdateMapObject(map_raw_, map_object);
    }
    pthread_spin_unlock(&map_object_spinlock_);
    MAPS_DEBUG("RawMapCallback_() return");
}

void MapServer::OptMapCallback_(const nav_msgs::OccupancyGridConstPtr& msg)
{
    static int rindex = 0;
    MAPS_DEBUG("OptMapCallback_(): ReceiveMap index = %d", rindex);
    MAPS_DEBUG("RawMapCallback_(): current_map_id_ is \"%s\"", current_map_id_.c_str());
    rindex ++;

    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("OptMapCallback_():Current map id: \"%s\" is unavaliable!", current_map_id_.c_str());
        return;
    }
    MapObject& map_object = map_list_[index];
    pthread_spin_lock(&map_object_spinlock_);
    map_opt_ = *msg;
    if (enb_map_opt_)
    {
        p_map_handler_->UpdateMapObject(map_opt_, map_object);
    }
    pthread_spin_unlock(&map_object_spinlock_);
    MAPS_DEBUG("OptMapCallback_() return");
}

void MapServer::InsPoseCallback_(const geometry_msgs::PoseArrayConstPtr &msg)
{
    MAPS_INFO("InsPoseCallback_");
    pthread_spin_lock(&map_object_spinlock_);
    ins_pose_ = *msg;
    pthread_spin_unlock(&map_object_spinlock_);

}

void MapServer::ShutdownCurrentMap()
{
    MAPS_INFO("ShutdownCurrentMap()");
    map_pub_.shutdown();
    raw_map_sub_.shutdown();
    virtualwall_pub_.shutdown();
}

bool MapServer::SwitchServerMode(string mode)
{
    if (mode == "NAVI_MODE")
    {
        MAPS_DEBUG("SwitchServerMode(): To \"NAVI_MODE\".");
    }
    else if (mode == "SLAM_MODE")
    {
        MAPS_DEBUG("SwitchServerMode(): To \"SLAM_MODE\".");
    }
    else
    {
        MAPS_WARN("SwitchServerMode(): Unknown mode: %s", mode.c_str());
        return false;
    }

    if (mode == map_server_mode_)
    {
        MAPS_DEBUG("SwitchServerMode(): Already in %s mode", mode.c_str());
        return true;
    }
    map_server_mode_ = mode;
    ShutdownCurrentMap();

    if (map_server_mode_ == "NAVI_MODE")
    {
        ros::NodeHandle nh;
        PubCurrentMap();
        nh.setParam(STD_RUNTIME_PARAM_NS + "/map_server_mode", map_server_mode_.c_str());
    }
    else if (map_server_mode_ == "SLAM_MODE")
    {
        ros::NodeHandle nh;
        nh.param<bool>(STD_SLAM_OPTIMIZER_PARAM_NS + "/enabled", enb_map_opt_, false);
        nh.setParam(STD_RUNTIME_PARAM_NS + "/map_server_mode", map_server_mode_.c_str());        
        _SetupNewMap();
        raw_map_sub_ = nh.subscribe(STD_MAP_TOPIC_NAME, 1, &MapServer::RawMapCallback_, this);
        opt_map_sub_ = nh.subscribe(STD_MAP_TOPIC_NAME + "_opt", 1, &MapServer::OptMapCallback_, this);
        ins_pose_sub_ = nh.subscribe("correlation_pose",1, &MapServer::InsPoseCallback_, this);
        /*
        if (!enb_map_opt_)
        {
            raw_map_sub_ = nh.subscribe(STD_MAP_TOPIC_NAME, 1, &MapServer::RawMapCallback_, this);
        }
        else
        {
            opt_map_sub_ = nh.subscribe(STD_MAP_TOPIC_NAME + "_opt", 1, &MapServer::OptMapCallback_, this);
        }
        */
     }
    return true;
}

bool MapServer::ForceLoopClosureCallback(wr_npu_msgs::ForceLoopClosure::Request& rqst, wr_npu_msgs::ForceLoopClosure::Response& resp)
{
    MAPS_INFO("ForceLoopClosureCallback()");
    ros::NodeHandle nh;
    wr_npu_msgs::ForceLoopClosure srv;
    srv.request = rqst;
    std::string srv_name = "/map_optimizer_node/force_loop_closure";
    ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::ForceLoopClosure>(srv_name);
    bool rlt = client.call(srv);
    if (!rlt)
    {
        MAPS_ERROR("ForceLoopClosureCallback(): Call service \"%s\": rlt = %s", srv_name.c_str(), BoolToStr(rlt).c_str());
    }
    else
    {
        MAPS_INFO("ForceLoopClosureCallback(): Call service \"%s\": rlt = %s", srv_name.c_str(), BoolToStr(rlt).c_str());
        map_opt_ = srv.response.map;
        MAPS_WARN("ForceLoopClosureCallback(): srv.response: size_pix = (%d, %d), res_mpp = %.2f, data_size = %d"
                 , srv.response.map.info.width, srv.response.map.info.height
                 , srv.response.map.info.resolution, static_cast<int>(srv.response.map.data.size()));
    }
    return rlt;
}

bool MapServer::SetMap(nav_msgs::GetMap::Request &request, nav_msgs::GetMap::Response &response)
{
    nav_msgs::OccupancyGrid msg;
    msg.header.frame_id = map_frame_id_;
    msg.header.stamp = ros::Time::now();
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("SetMap(): Current map id: \"%s\" is unavaliable!", current_map_id_.c_str());
        return false;
    }
    MapObject& map_object = map_list_[index];
    p_map_handler_->MakeMapMessage(map_object, msg);
    response.map = msg;
    return true;
}

bool MapServer::Serve(SrvRqst &request, SrvRspns &response)
{
    ServiceFunctionMap::iterator iter = service_function_map_.find(request.function);
    if (iter == service_function_map_.end())
    {
        MAPS_WARN("MapServer-> Can NOT find %s", request.function.c_str());
        response.return_data.resize(0);
        return false;
    }
    ServiceFunctionPointer p_func = iter->second;
    bool rtn = (*this.*p_func)(request.arguments, response.return_data);

    if( rtn == false)
    {
        MAPS_WARN("MapServer-> %s return FALSE", request.function.c_str());
        response.return_data.resize(0);
        return false;
    }
    return true;
}

int MapServer::_SetupNewMap()
{
    if (current_map_id_ != "NEW_MAP")
    {
        MapObject *p_map_obj = new MapObject();
        p_map_obj->info.index = NewMapIndex();
        p_map_obj->info.id = "NEW_MAP";
        current_map_id_ = "NEW_MAP";
        map_list_.push_back(*p_map_obj);
        delete p_map_obj;
    }

    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map id: \"%s\" is unavaliable!", current_map_id_.c_str());
        return -1;
    }
    return index;
}

bool MapServer::GetCurrentMapInNaviMode(SrvArgument &args, SrvReturn &rtn)
{
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map id: \"%s\" is unavaliable!", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    int length = 0;
    length += p_map_handler_->Sizeof(map_obj.info);
    length += p_map_handler_->Sizeof(map_obj.map_2d.mat);
    length += p_map_handler_->Sizeof(map_obj.map_2d.stations);
    length += p_map_handler_->Sizeof(map_obj.map_2d.paths);
    length += p_map_handler_->Sizeof(map_obj.map_2d.virtual_walls);

    rtn.resize(length);
    SrvReturn::iterator head = rtn.begin();
    SrvReturn::iterator rear = head;

    p_map_handler_->Pack(map_obj.info, rear);
    p_map_handler_->Pack(map_obj.map_2d.mat, rear);
    p_map_handler_->Pack(map_obj.map_2d.stations, rear);
    p_map_handler_->Pack(map_obj.map_2d.paths, rear);
    p_map_handler_->Pack(map_obj.map_2d.virtual_walls, rear);

    //MAPS_DEBUG("Length: %d / %d", length, static_cast<int>(rear-head));
    return true;
}

bool MapServer::GetCurrentMapInSlamMode(SrvArgument &args, SrvReturn &rtn)
{
    /// _SetupNewMap() return the index fo "NEW_MAP".
    /// If "NEW_MAP" is not exists, _SetupNewMap() will create one.
    int index = _SetupNewMap();
    MapObject& map_obj = map_list_[index];

    pthread_spin_lock(&map_object_spinlock_);
    MAPS_DEBUG("map_obj.map_2d.mat.data.size: %d", static_cast<int>(map_obj.map_2d.mat.data.size()));
    int length = 0;
    length += p_map_handler_->Sizeof(map_obj.info);
    length += p_map_handler_->Sizeof(map_obj.map_2d.mat);
    length += p_map_handler_->Sizeof(map_obj.map_2d.stations);
    length += p_map_handler_->Sizeof(map_obj.map_2d.paths);
    length += p_map_handler_->Sizeof(map_obj.map_2d.virtual_walls);
    rtn.resize(length);
    SrvReturn::iterator head = rtn.begin();
    SrvReturn::iterator rear = head;
    p_map_handler_->Pack(map_obj.info, rear);
    p_map_handler_->Pack(map_obj.map_2d.mat, rear);
    p_map_handler_->Pack(map_obj.map_2d.stations, rear);
    p_map_handler_->Pack(map_obj.map_2d.paths, rear);
    p_map_handler_->Pack(map_obj.map_2d.virtual_walls, rear);
    pthread_spin_unlock(&map_object_spinlock_);
    return true;
}

bool MapServer::GetCurrentMap(SrvArgument &args, SrvReturn &rtn)
{
    MAPS_DEBUG_ONCE("GetCurrentMap(\"%s\")", current_map_id_.c_str());
    if (map_server_mode_ == "NAVI_MODE")
    {
        return GetCurrentMapInNaviMode(args, rtn);
    }
    else
    {
        return GetCurrentMapInSlamMode(args, rtn);
    }
}

bool MapServer::GetCurrentImgMapInNaviMode(SrvArgument &args, SrvReturn &rtn)
{
    MAPS_DEBUG_ONCE("GetCurrentImgMapInNaviMode(\"%s\")", current_map_id_.c_str());
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("GetCurrentImgMapInNaviMode(): Current map id: \"%s\" is unavaliable!", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    int length = 0;
    length += p_map_handler_->Sizeof(map_obj.info);
    length += p_map_handler_->Sizeof(map_obj.img_map.mat);
    length += p_map_handler_->Sizeof(map_obj.img_map.stations);
    length += p_map_handler_->Sizeof(map_obj.img_map.paths);
    length += p_map_handler_->Sizeof(map_obj.img_map.virtual_walls);

    rtn.resize(length);
    SrvReturn::iterator head = rtn.begin();
    SrvReturn::iterator rear = head;

    p_map_handler_->Pack(map_obj.info, rear);
    p_map_handler_->Pack(map_obj.img_map.mat, rear);
    p_map_handler_->Pack(map_obj.img_map.stations, rear);
    p_map_handler_->Pack(map_obj.img_map.paths, rear);
    p_map_handler_->Pack(map_obj.img_map.virtual_walls, rear);

    return true;
}

bool MapServer::GetCurrentImgMapInSlamMode(SrvArgument &args, SrvReturn &rtn)
{
    MAPS_DEBUG("GetCurrentImgMapInSlamMode()");
    int index = _SetupNewMap();
    MapObject& map_obj = map_list_[index];

    pthread_spin_lock(&map_object_spinlock_);
    MAPS_DEBUG("GetCurrentImgMapInSlamMode(): Data size = %d", static_cast<int>(map_obj.img_map.mat.data.size()));
    int length = 0;
    length += p_map_handler_->Sizeof(map_obj.info);
    length += p_map_handler_->Sizeof(map_obj.img_map.mat);
    length += p_map_handler_->Sizeof(map_obj.img_map.stations);
    length += p_map_handler_->Sizeof(map_obj.img_map.paths);
    length += p_map_handler_->Sizeof(map_obj.img_map.virtual_walls);
    rtn.resize(length);
    SrvReturn::iterator head = rtn.begin();
    SrvReturn::iterator rear = head;
    p_map_handler_->Pack(map_obj.info, rear);
    p_map_handler_->Pack(map_obj.img_map.mat, rear);
    p_map_handler_->Pack(map_obj.img_map.stations, rear);
    p_map_handler_->Pack(map_obj.img_map.paths, rear);
    p_map_handler_->Pack(map_obj.img_map.virtual_walls, rear);
    pthread_spin_unlock(&map_object_spinlock_);
    return true;
}

bool MapServer::GetCurrentImgMap(SrvArgument &args, SrvReturn &rtn)
{
    if (map_server_mode_ == "NAVI_MODE")
    {
        return GetCurrentImgMapInNaviMode(args, rtn);
    }
    else
    {
        return GetCurrentImgMapInSlamMode(args, rtn);
    }
}

bool MapServer::GetMapInfos(SrvArgument &args, SrvReturn &rtn)
{
    MAPS_DEBUG("GetMapInfos()");
    LoadAllMapInfo();
    int cnt = map_list_.size();
    int length = 0;
    int act_cnt = 0;
    for (int i=0;i<cnt;i++)
    {
        if(map_list_[i].info.id == "NEW_MAP"
                || map_list_[i].info.id == "IDLE_MAP")
        {
            continue;
        }
        act_cnt += 1;
        length += p_map_handler_->Sizeof(map_list_[i].info);
    }
    length += sizeof(int);
    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->PackFixLength<int>(act_cnt, rear);
    for (int i=0;i<cnt;i++)
    {
        if(map_list_[i].info.id == "NEW_MAP" || map_list_[i].info.id == "IDLE_MAP")
        {
            continue;
        }
        //MAPS_DEBUG("GetMapInfos() %s (thumbnail: %d %d %d)",
        //           map_list_[i].info.id.c_str(),
        //           map_list_[i].info.thumbnail.width, map_list_[i].info.thumbnail.height,
        //           static_cast<int>(map_list_[i].info.thumbnail.data.size()));
        p_map_handler_->Pack(map_list_[i].info, rear);
    }
    return true;
}

bool MapServer::SetMapInfos(SrvArgument &args, SrvReturn &rtn)
{
    MapInfoList map_info_list;
    map_info_list.clear();
    vector<unsigned char>::iterator p = args.begin();

    int cnt = 0;
    p_map_handler_->UnpackFixLength<int>(cnt, p);
    map_info_list.resize(cnt);
    for (int i=0;i<cnt;i++)
    {
        p_map_handler_->Unpack(map_info_list[i], p);
    }
    MapObjectList::iterator p_map_obj = map_list_.begin();
    MapInfoList::iterator p_map_info = map_info_list.begin();
    while(p_map_obj != map_list_.end())
    {
        if(p_map_obj->info.id == "NEW_MAP" || p_map_obj->info.id == "IDLE_MAP")
        {
            p_map_obj++;
            continue;
        }
        if(p_map_obj->info.index != p_map_info->index)
        {/// map have been delete
            p_map_handler_->DeleteMapFile(*p_map_obj);
            map_list_.erase(p_map_obj);
        }
        else
        {
            /*
            p_map_handler_->DeleteMapFile(*p_map_obj);
            p_map_obj->info = (*p_map_info);
            p_map_handler_->SaveMapYaml(*p_map_obj);
            */
            p_map_info++;
            p_map_obj++;
        }
    }
    return true;
}

bool MapServer::LoadMap(SrvArgument &args, SrvReturn &rtn)
{
    string id;
    SrvArgument::iterator p = args.begin();
    p_map_handler_->UnpackVarLength<string>(id, p);
    if (map_server_mode_ != "NAVI_MODE")
    {
        MAPS_WARN("LoadMap(): Load map ONLY avaliable in NAVI_MODE.");
        return false;
    }

    int index = FindObjectByName<MapObjectList>(map_list_, id);
    if (index == -1)
    {
        MAPS_WARN("LoadMap(): Invalid map id: \"%s\".", id.c_str());
        return false;
    }

    current_map_id_ = id;
    MAPS_INFO("LoadMap(): Set map: \"%s\" as current map.", id.c_str());
    ShutdownCurrentMap();
    PubCurrentMap();
    MAPS_INFO("LoadMap(): Load map success.");
    return true;
}

bool MapServer::SaveMap(SrvArgument &args, SrvReturn &rtn)
{
    MAPS_DEBUG("SaveMap()");
    if (map_server_mode_ != "SLAM_MODE")
    {
        MAPS_WARN("SaveMap(): Save map only avaliable in SLAM mode.");
        return false;
    }

    string map_id;
    SrvArgument::iterator ap = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, ap);

    string new_map = "NEW_MAP";
    int index = FindMapObject(map_list_, new_map);

    if (map_id == "")
    {
        if (index == -1)
        {
            return true;
        }
        map_list_.erase(map_list_.begin() + index);
        current_map_id_ = "IDLE_MAP";
        MAPS_WARN("SaveMap(): Cancel to save map.");
        return true;
    }

    for(int i=0;i<map_list_.size();i++)
    {
        if (map_id == map_list_[i].info.id)
        {
            MAPS_WARN("SaveMap(): Map id is already exist.");
            return false;
        }
    }

    MapObject& map_obj = map_list_[index];
    map_obj.info.id = map_id;
    current_map_id_ = map_id;

//    map_obj.add_info.position_x = ins_pose_.poses[0].position.x;
//    map_obj.add_info.position_y = ins_pose_.poses[0].position.y;
//    double x = ins_pose_.poses[0].orientation.x;
//    double y = ins_pose_.poses[0].orientation.y;
//    double z = ins_pose_.poses[0].orientation.z;
//    double w = ins_pose_.poses[0].orientation.w;
//    double yaw = atan2(2*(w*z+x*y),1-2*(y*y+z*z));
//    map_obj.add_info.position_yaw = yaw;
//    map_obj.add_info.longitude = ins_pose_.poses[1].position.x;
//    map_obj.add_info.latitude = ins_pose_.poses[1].position.y;
//    double x_ = ins_pose_.poses[1].orientation.x;
//    double y_ = ins_pose_.poses[1].orientation.y;
//    double z_ = ins_pose_.poses[1].orientation.z;
//    double w_ = ins_pose_.poses[1].orientation.w;
//    double yaw_ = atan2(2*(w_*z_+x_*y_),1-2*(y_*y_+z_*z_));
//    map_obj.add_info.angles = yaw_;

    ros::NodeHandle nh;
    nh.param<std::string>(STD_CORE_PARAM_NS + "/CONFIG_ID", map_obj.add_info.config_id, "");
    nh.param<std::string>(STD_SENSOR_PARAM_NS + "/lidar_params/lidar_0/type", map_obj.add_info.lidar_type, "");
    GetFloatParam(nh, STD_SENSOR_PARAM_NS + "/lidar_params/lidar_0/range_m", map_obj.add_info.lidar_range_m, 0);
    GetFloatParam(nh, STD_SENSOR_PARAM_NS + "/lidar_params/lidar_0/frq_hz", map_obj.add_info.lidar_frq_hz, 1);
    nh.param<std::string>(STD_SLAM_PARAM_NS + "/slam_mode", map_obj.add_info.slam_mode, "");

    if (enb_map_opt_)
    {
        map_obj.info.id = map_id + "_raw";
        p_map_handler_->UpdateMapObject(map_raw_, map_obj);
        p_map_handler_->SaveMapObject(map_obj);
        MAPS_WARN("Save raw map: size_pix = (%d, %d), res_mpp = %.2f", map_raw_.info.width, map_raw_.info.height, map_raw_.info.resolution);

        map_obj.info.id = map_id;
        p_map_handler_->UpdateMapObject(map_opt_, map_obj);
        p_map_handler_->SaveMapObject(map_obj);
        MAPS_WARN("Save opt map: size_pix = (%d, %d), res_mpp = %.2f", map_opt_.info.width, map_opt_.info.height, map_opt_.info.resolution);
    }
    else
    {
        p_map_handler_->UpdateMapObject(map_raw_, map_obj);
        p_map_handler_->SaveMapObject(map_obj);
        MAPS_WARN("Save raw map: size_pix = (%d, %d), res_mpp = %.2f", map_raw_.info.width, map_raw_.info.height, map_raw_.info.resolution);
    }
    MAPS_DEBUG("SaveMap(): Save map success.");
    LoadAllMapInfo();
    return true;
}

bool MapServer::ExportMapFile(SrvArgument &args, SrvReturn &rtn)
{
    return false;
}

bool MapServer::ImportMapFile(SrvArgument &args, SrvReturn &rtn)
{
    return false;
}

bool MapServer::SetTasks(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", map_id.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];
    TaskList tasks;
    SrvArgument::iterator t = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, t);
    if(map_id != map_obj.info.id)
    {
        MAPS_WARN("Map id \"%s\" is not current map.", map_id.c_str());
        return false;
    }
    p_map_handler_->Unpack(tasks, t);
    map_obj.map_2d.tasks = tasks;
    int length = p_map_handler_->Sizeof(map_obj.map_2d.tasks);
    MAPS_INFO("tasks size is: %d",length);
    /***** test task_loop_tiimes *****/
    int n = tasks.size();
    for(int i=0; i<n; i++)
    {
        int m = tasks[i].task_loop_times;
        MAPS_INFO("MapServer settasks() times is: %d", m);
    }
    /***** test task_loop_times *****/
    p_map_handler_->SaveMapYaml(map_obj);
    return true;
}

bool MapServer::GetTaskList(SrvArgument &args, SrvReturn &rtn)
{
//    string map_id;
//    SrvArgument::iterator ap = args.begin();
//    p_map_handler_->UnpackVarLength<string>(map_id, ap);
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Invalid map id.");
        return false;
    }
    MapObject &map_obj = map_list_[index];
    int length = p_map_handler_->Sizeof(map_obj.map_2d.tasks);
    MAPS_DEBUG("Function GetTaskList tasks length is: %d", length);
    rtn.resize(length);
    MAPS_DEBUG("check 00000000001");
    vector<unsigned char>::iterator head = rtn.begin();
    MAPS_DEBUG("check 00000000002");
    vector<unsigned char>::iterator rear = head;
    MAPS_DEBUG("check 00000000003");
    p_map_handler_->Pack(map_obj.map_2d.tasks, rear);
    MAPS_DEBUG("check 00000000004");
    return true;
}


bool MapServer::SetImgPaths(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", map_id.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];
    ImgPathList img_paths;
    PathList paths;
    SrvArgument::iterator p = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, p);
    if(map_id != map_obj.info.id)
    {
        MAPS_WARN("Map id \"%s\" is not current map.", map_id.c_str());
        return false;
    }

    p_map_handler_->Unpack(img_paths, p);
    paths.resize(img_paths.size());
    for(int i=0;i<paths.size();i++)
    {
        paths[i].poses.resize(img_paths[i].poses.size());
        for (int j=0;j<paths[i].poses.size();j++)
        {
            p_map_handler_->TransformImgPose2Pose3D(map_obj, img_paths[i].poses[j], paths[i].poses[j]);
        }
        paths[i].info = img_paths[i].info;
    }


    map_obj.img_map.paths = img_paths;
    map_obj.map_2d.paths = paths;
    p_map_handler_->SaveMapYaml(map_obj);
    return true;
}

bool MapServer::GetImgPaths(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    SrvArgument::iterator ap = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, ap);

    int index = FindMapObject(map_list_, map_id);
    if (index == -1)
    {
        MAPS_WARN("Invalid map id.");
        return false;
    }

    MapObject &map_obj = map_list_[index];

    int length = p_map_handler_->Sizeof(map_obj.img_map.paths);

    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(map_obj.img_map.paths, rear);
    return true;
}

bool MapServer::SetPaths(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", map_id.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];
    PathList paths;
    ImgPathList img_paths;
    SrvArgument::iterator p = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, p);
    if(map_id != map_obj.info.id)
    {
        MAPS_WARN("Map id \"%s\" is not current map.", map_id.c_str());
        return false;
    }

    p_map_handler_->Unpack(paths, p);
    img_paths.resize(paths.size());
    for(int i=0;i<img_paths.size();i++)
    {
        img_paths[i].poses.resize(paths[i].poses.size());
        for (int j=0;j<img_paths[i].poses.size();j++)
        {
            p_map_handler_->TransformPose3D2ImgPose(map_obj, paths[i].poses[j], img_paths[i].poses[j]);
        }
        img_paths[i].info = paths[i].info;
    }
    map_obj.map_2d.paths = paths;
    map_obj.img_map.paths = img_paths;
    p_map_handler_->SaveMapYaml(map_obj);
    return true;
}

bool MapServer::GetPaths(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    SrvArgument::iterator ap = args.begin();

    p_map_handler_->UnpackVarLength<string>(map_id, ap);
    MAPS_DEBUG("Map id: %s", map_id.c_str());
    int index = FindMapObject(map_list_, map_id);
    if (index == -1)
    {
        MAPS_WARN("Invalid map id.");
        return false;
    }

    MapObject &map_obj = map_list_[index];

    int length = p_map_handler_->Sizeof(map_obj.map_2d.paths);

    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(map_obj.map_2d.paths, rear);
    return true;
}

bool MapServer::GetPath(SrvArgument &args, SrvReturn &rtn)
{
    int map_index = FindMapObject(map_list_, current_map_id_);
    if (map_index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[map_index];

    string path_id;
    SrvArgument::iterator ap = args.begin();
    p_map_handler_->UnpackVarLength<string>(path_id, ap);

    int path_index = FindPath(map_obj.map_2d.paths, path_id);
    if (path_index == -1)
    {
        MAPS_WARN("Invalid path id.");
        return false;
    }

    Path& path = map_obj.map_2d.paths[path_index];

    int length = p_map_handler_->Sizeof(path);
    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(path, rear);
    return true;
}

bool MapServer::SetImgVirtualWalls(SrvArgument &args, SrvReturn &rtn)
{

    MAPS_DEBUG("Come in SetImgVirtualWalls().");

    string map_id;
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", map_id.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];
    ImgVirtualWallList img_virtual_walls;
    VirtualWallList virtual_walls;
    SrvArgument::iterator p = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, p);
    if(map_id != map_obj.info.id)
    {
        MAPS_WARN("Map id \"%s\" is not current map.", map_id.c_str());
        return false;
    }

    p_map_handler_->Unpack(img_virtual_walls, p);
    virtual_walls.resize(img_virtual_walls.size());
    for(int i=0;i<virtual_walls.size();i++)
    {
        virtual_walls[i].points.resize(img_virtual_walls[i].points.size());
        for (int j=0;j<virtual_walls[i].points.size();j++)
        {
            p_map_handler_->TransformImgPoint2Point3D(map_obj, img_virtual_walls[i].points[j], virtual_walls[i].points[j]);
        }
        virtual_walls[i].info = img_virtual_walls[i].info;
    }

    map_obj.img_map.virtual_walls = img_virtual_walls;
    map_obj.map_2d.virtual_walls = virtual_walls;
    p_map_handler_->SaveMapYaml(map_obj);
    wr_npu_msgs::Virtualwalls virtualwalls_msg;
    p_map_handler_->MakeVirtualwallMessage(map_obj, virtualwalls_msg);
    ros::NodeHandle nh;
    virtualwall_pub_.shutdown();
    virtualwall_pub_ = nh.advertise<wr_npu_msgs::Virtualwalls>(STD_VIRTUALWALL_TOPIC_NAME, 1, true);
    virtualwall_pub_.publish(virtualwalls_msg);
    return true;
}

bool MapServer::GetImgVirtualWalls(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    SrvArgument::iterator ap = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, ap);

    int index = FindMapObject(map_list_, map_id);
    if (index == -1)
    {
        MAPS_WARN("Invalid map id.");
        return false;
    }
    MapObject &map_obj = map_list_[index];
    int length = p_map_handler_->Sizeof(map_obj.img_map.virtual_walls);
    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;
    p_map_handler_->Pack(map_obj.img_map.virtual_walls, rear);
    return true;
}

bool MapServer::SetVirtualWalls(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", map_id.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];
    VirtualWallList virtual_walls;
    ImgVirtualWallList img_virtual_walls;
    SrvArgument::iterator p = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, p);
    if(map_id != map_obj.info.id)
    {
        MAPS_WARN("Map id \"%s\" is not current map.", map_id.c_str());
        return false;
    }

    p_map_handler_->Unpack(virtual_walls, p);
    img_virtual_walls.resize(virtual_walls.size());
    for(int i=0;i<img_virtual_walls.size();i++)
    {
        for (int j=0;j<img_virtual_walls[i].points.size();j++)
        {
            p_map_handler_->TransformPoint3D2ImgPoint(map_obj, virtual_walls[i].points[j], img_virtual_walls[i].points[j]);
        }
        img_virtual_walls[i].info = virtual_walls[i].info;
    }
    map_obj.map_2d.virtual_walls = virtual_walls;
    map_obj.img_map.virtual_walls = img_virtual_walls;
    p_map_handler_->SaveMapYaml(map_obj);
    return true;
}

bool MapServer::GetVirtualWalls(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    SrvArgument::iterator ap = args.begin();

    p_map_handler_->UnpackVarLength<string>(map_id, ap);
    MAPS_DEBUG("Map id: %s", map_id.c_str());
    int index = FindMapObject(map_list_, map_id);
    if (index == -1)
    {
        MAPS_WARN("Invalid map id.");
        return false;
    }

    MapObject &map_obj = map_list_[index];

    int length = p_map_handler_->Sizeof(map_obj.map_2d.virtual_walls);

    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(map_obj.map_2d.virtual_walls, rear);
    return true;
}

bool MapServer::GetVirtualWall(SrvArgument &args, SrvReturn &rtn)
{
    int map_index = FindMapObject(map_list_, current_map_id_);
    if (map_index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[map_index];

    string virtual_wall_id;
    SrvArgument::iterator ap = args.begin();
    p_map_handler_->UnpackVarLength<string>(virtual_wall_id, ap);

    int virtual_wall_index = FindVirtualWall(map_obj.map_2d.virtual_walls, virtual_wall_id);
    if (virtual_wall_index == -1)
    {
        MAPS_WARN("Invalid virtual_wall id.");
        return false;
    }

    VirtualWall& virtual_wall = map_obj.map_2d.virtual_walls[virtual_wall_index];

    int length = p_map_handler_->Sizeof(virtual_wall);
    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(virtual_wall, rear);
    return true;
}

bool MapServer::GetCurrentImgPath(SrvArgument &args, SrvReturn &rtn)
{
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    if (current_path_index_ == -1)
    {
        MAPS_WARN("No current image path avaliable.");
        return false;
    }

    ImgPath& path = map_obj.img_map.paths[current_path_index_];

    int length = p_map_handler_->Sizeof(path);
    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(path, rear);
    return true;
}

bool MapServer::GetCurrentPath(SrvArgument &args, SrvReturn &rtn)
{
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    if (current_path_index_ == -1)
    {
        MAPS_WARN("No current image path avaliable.");
        return false;
    }
    Path& path = map_obj.map_2d.paths[current_path_index_];

    int length = p_map_handler_->Sizeof(path);
    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(path, rear);
    return true;
}

bool MapServer::GetCurrentImgVirtualWall(SrvArgument &args, SrvReturn &rtn)
{
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    if (current_virtual_wall_index_ == -1)
    {
        MAPS_WARN("No current image virtual_wall avaliable.");
        return false;
    }

    ImgVirtualWall& virtual_wall = map_obj.img_map.virtual_walls[current_virtual_wall_index_];

    int length = p_map_handler_->Sizeof(virtual_wall);
    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(virtual_wall, rear);
    return true;
}

bool MapServer::GetCurrentVirtualWall(SrvArgument &args, SrvReturn &rtn)
{
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    if (current_virtual_wall_index_ == -1)
    {
        MAPS_WARN("No current image virtual_wall avaliable.");
        return false;
    }
    VirtualWall& virtual_wall = map_obj.map_2d.virtual_walls[current_virtual_wall_index_];

    int length = p_map_handler_->Sizeof(virtual_wall);
    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(virtual_wall, rear);
    return true;
}

bool MapServer::SetImgStations(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    ImgStationList img_stations;
    StationList stations;
    SrvArgument::iterator p = args.begin();
    p_map_handler_->UnpackVarLength(map_id, p);

    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", map_id.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    if (map_id != map_obj.info.id)
    {
        MAPS_WARN("Map id \"%s\" is not current map.", map_id.c_str());
        return false;
    }
    p_map_handler_->Unpack(img_stations, p);
    stations.resize(img_stations.size());

    for (int i=0;i<stations.size();i++)
    {
        p_map_handler_->TransformImgPose2Pose3D(map_obj, img_stations[i].pose, stations[i].pose);
        stations[i].info = img_stations[i].info;
    }


    map_obj.map_2d.stations = stations;
    map_obj.img_map.stations = img_stations;

    p_map_handler_->SaveMapYaml(map_obj);
    return true;
}

bool MapServer::GetImgStations(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    SrvArgument::iterator ap = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, ap);

    int index = FindMapObject(map_list_, map_id);
    if (index == -1)
    {
        MAPS_WARN("Invalid map id.");
        return false;
    }

    MapObject &map_obj = map_list_[index];

    int length = p_map_handler_->Sizeof(map_obj.img_map.stations);

    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(map_obj.img_map.stations, rear);
    return true;
}

bool MapServer::SetStations(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    StationList stations;
    ImgStationList img_stations;
    SrvArgument::iterator p = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, p);

    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", map_id.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    if (map_id != map_obj.info.id)
    {
        MAPS_WARN("Map id \"%s\" is not current map.", map_id.c_str());
        return false;
    }
    p_map_handler_->Unpack(stations, p);

    img_stations.resize(stations.size());

    for (int i=0;i<img_stations.size();i++)
    {
        p_map_handler_->TransformPose3D2ImgPose(map_obj, stations[i].pose, img_stations[i].pose);
        img_stations[i].info = stations[i].info;
    }

    map_obj.map_2d.stations = stations;
    map_obj.img_map.stations = img_stations;
    p_map_handler_->SaveMapYaml(map_obj);
    return true;
}

bool MapServer::GetStations(SrvArgument &args, SrvReturn &rtn)
{
    string map_id;
    SrvArgument::iterator ap = args.begin();
    p_map_handler_->UnpackVarLength<string>(map_id, ap);

    int index = FindMapObject(map_list_, map_id);
    if (index == -1)
    {
        MAPS_WARN("Invalid map id.");
        return false;
    }

    MapObject &map_obj = map_list_[index];

    int length = p_map_handler_->Sizeof(map_obj.map_2d.stations);

    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;

    p_map_handler_->Pack(map_obj.map_2d.stations, rear);
    return true;
}

bool MapServer::GetStation(SrvArgument &args, SrvReturn &rtn)
{
    int map_index = FindMapObject(map_list_, current_map_id_);
    if (map_index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[map_index];

    string station_id;
    SrvArgument::iterator ap = args.begin();
    p_map_handler_->UnpackVarLength<string>(station_id, ap);

    int station_index = FindStation(map_obj.map_2d.stations, station_id);
    if (station_index == -1)
    {
        MAPS_WARN("Invalid station id.");
        return false;
    }

    Station& station = map_obj.map_2d.stations[station_index];

    int length = p_map_handler_->Sizeof(station);
    rtn.resize(length);
    vector<unsigned char>::iterator head = rtn.begin();
    vector<unsigned char>::iterator rear = head;
    p_map_handler_->Pack(station, rear);
    return true;
}

bool MapServer::SetServerMode(SrvArgument &args, SrvReturn &rtn)
{
    string mode;
    SrvArgument::iterator p = args.begin();
    p_map_handler_->UnpackVarLength<string>(mode, p);

    MAPS_DEBUG("SetServerMode(): %s", mode.c_str());

    return SwitchServerMode(mode);
}

bool MapServer::TransformImgPosesTo3DPoses(SrvArgument &args, SrvReturn &rtn)
{
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    ImgPoseList img_poses;
    Pose3DList _3d_poses;

    SrvArgument::iterator ap = args.begin();
    p_map_handler_->Unpack(img_poses, ap);
    _3d_poses.resize(img_poses.size());
    for (int i=0;i<img_poses.size();i++)
    {
        p_map_handler_->TransformImgPose2Pose3D(map_obj, img_poses[i], _3d_poses[i]);
    }

    int length = _3d_poses.size() * sizeof(Pose3D) + sizeof(int);
    rtn.resize(length);
    SrvReturn::iterator rp = rtn.begin();
    p_map_handler_->Pack(_3d_poses, rp);

    return true;
}

bool MapServer::Transform3DPosesToImgPoses(SrvArgument &args, SrvReturn &rtn)
{
    int index = FindMapObject(map_list_, current_map_id_);
    if (index == -1)
    {
        MAPS_WARN("Current map \"%s\" is not avaliable.", current_map_id_.c_str());
        return false;
    }
    MapObject& map_obj = map_list_[index];

    Pose3DList _3d_poses;
    ImgPoseList img_poses;

    SrvArgument::iterator ap = args.begin();
    p_map_handler_->Unpack(_3d_poses, ap);
    img_poses.resize(_3d_poses.size());
    for (int i=0;i<_3d_poses.size();i++)
    {
        p_map_handler_->TransformPose3D2ImgPose(map_obj, _3d_poses[i], img_poses[i]);
    }

    int length = img_poses.size() * sizeof(Pose3D) + sizeof(int);
    rtn.resize(length);
    SrvReturn::iterator rp = rtn.begin();
    p_map_handler_->Pack(img_poses, rp);

    return true;
}

}// namespace wizrobo_npu
