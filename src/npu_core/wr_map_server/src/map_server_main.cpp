#include "map_server.h"
#include "usblock.h"

using namespace std;

int main (int argc, char** argv)
{
#if WR_LOCK == true
    if (!wizrobo::UsbLocker::Unlock())
    {
        ROS_FATAL("Failed to unlock! Pls heck you usblock.");
        return -1;
    }
#endif
    ros::init(argc, argv, "map_server_node");
    ros::start();

    ros::NodeHandle nh;
    ros::NodeHandle prv_nh("~");

    int loop_freq_hz = 30, timeout_s = 5;
    int timeout_cnt = loop_freq_hz * timeout_s;
    ros::Rate r(loop_freq_hz);
    bool is_param_loaded = false;
    while (ros::ok() && is_param_loaded==false && timeout_cnt>0)
    {
        nh.param<bool>(wizrobo::STD_RUNTIME_PARAM_NS + "/is_loaded", is_param_loaded, false);
        timeout_cnt--;
        r.sleep();
    }
    if (timeout_cnt <= 0)
    {
        return -1;
    }

#if 1
    string logger_level;
    prv_nh.param<string>("logger_level", logger_level, "debug");
    if (logger_level.compare("debug") == 0)
    {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Debug);
    }
    else if (logger_level.compare("info") == 0)
    {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Info);
    }
    else if (logger_level.compare("warn") == 0)
    {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Warn);
    }
#else
    ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Debug);
#endif
    wizrobo_npu::MapServer map_server(argc, argv);
    try
    {
        map_server.Init();
    }
    catch (char const* e)
    {
        ROS_ERROR("%s", e);
        return 0;
    }
    map_server.Run();
}

