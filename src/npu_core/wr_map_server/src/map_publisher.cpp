#include <string>

#include <ros/ros.h>
#include <nav_msgs/MapMetaData.h>
#include <nav_msgs/GetMap.h>

#include <npu.h>

#include "map_handler.h"

using namespace  std;

int main(int argc, char **argv)
{
    ros::init(argc, argv, "map_publisher");
    string map_dir_path_, current_map_id_;

    ros::start();

    ros::NodeHandle nh;
    ros::NodeHandle pnh("~");

    pnh.param<string>("map_dir_path", map_dir_path_, "");
    pnh.param<string>("current_map_id", current_map_id_, "");

    if (map_dir_path_ == "")
    {
        ROS_ERROR("Map directory is invalid!");
        return 0;
    }
    if (current_map_id_ == "")
    {
        ROS_ERROR("Current map name is invalid");
        return 0;
    }

    nav_msgs::OccupancyGrid msg;
    wizrobo_npu::MapObject map_object;
    wizrobo_npu::MapHandler map_handler(map_dir_path_);
    ros::Publisher map_publisher = \
            nh.advertise<nav_msgs::OccupancyGrid>(wizrobo::STD_MAP_TOPIC_NAME, 1, true);
    map_handler.ReadMapInfo(current_map_id_, map_object);
    map_handler.FillMapMateData(map_object);
    map_handler.MakeMapMessage(map_object, msg);
    msg.header.frame_id = wizrobo::STD_MAP_FRAME_ID;
    map_publisher = nh.advertise<nav_msgs::OccupancyGrid>("map", 1, true);
    map_publisher.publish( msg );
    ROS_INFO("Publish map: \"%s/%s\".",
             map_dir_path_.c_str(),
             current_map_id_.c_str());
    ros::spin();
    return 0;
}
