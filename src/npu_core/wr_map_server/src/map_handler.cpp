#include <iostream>
#include <fstream>
#include <sstream>
#include <cstring>
#include <string.h>
#include <dirent.h>

#include <opencv2/opencv.hpp>

#include <nav_msgs/MapMetaData.h>
#include <nav_msgs/GetMap.h>
#include <tf/tf.h>

#include <wr_npu_msgs/Virtualwalls.h>
#include <map_handler.h>
#include <npuice_map.h>
#include <math.h>

#define MAPH_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("map_server", "MapHandler::" fmt, ##arg)
#define MAPH_INFO(fmt, arg...)  ROS_INFO_NAMED("map_server",  "MapHandler::" fmt, ##arg)
#define MAPH_WARN(fmt, arg...)  ROS_WARN_NAMED("map_server",  "MapHandler::" fmt, ##arg)
#define MAPH_ERROR(fmt, arg...) ROS_ERROR_NAMED("map_server", "MapHandler::" fmt, ##arg)

#define MAPH_DEBUG_ONCE(fmt, arg...) ROS_DEBUG_ONCE_NAMED("map_server", "MapServer::" fmt, ##arg)


// compute linear index for given map coords
#define MAP_IDX(sx, i, j) ((sx) * (j) + (i))

namespace wizrobo_npu {

using namespace std;

MapHandler::MapHandler(string map_dir_path)
{
    map_dir_path_ = map_dir_path;
    //ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME,
    //  ros::console::levels::Debug);
}

MapHandler::~MapHandler()
{
    return;
}

void MapHandler::TransformPose3D2ImgPose(const MapObject& map_obj, const Pose3D& pose_3d, ImgPose& img_pose)
{
    img_pose.u = static_cast<int>(((pose_3d.x - map_obj.info.offset.x) / map_obj.info.resolution) * map_obj.img_map.mat.ratio);
    img_pose.v = static_cast<int>(map_obj.img_map.mat.height - ((pose_3d.y - map_obj.info.offset.y) / map_obj.info.resolution) *  map_obj.img_map.mat.ratio);
    img_pose.theta = pose_3d.yaw;
}

void MapHandler::TransformImgPose2Pose3D(const MapObject& map_obj, const ImgPose& img_pose, Pose3D& pose_3d)
{
    pose_3d.x = img_pose.u * 1.0  / map_obj.img_map.mat.ratio * map_obj.info.resolution + map_obj.info.offset.x;
    pose_3d.y = (map_obj.img_map.mat.height - img_pose.v) * 1.0 / map_obj.img_map.mat.ratio * map_obj.info.resolution + map_obj.info.offset.y;
    pose_3d.z = 0.0;
    pose_3d.roll = 0.0;
    pose_3d.pitch = 0.0;
    pose_3d.yaw = img_pose.theta;
}

void MapHandler::TransformPoint3D2ImgPoint(const MapObject& map_obj, const Point3D& point_3d, ImgPoint& img_point)
{
    img_point.u = static_cast<int>(((point_3d.x - map_obj.info.offset.x) / map_obj.info.resolution) * map_obj.img_map.mat.ratio);
    img_point.v = static_cast<int>(map_obj.img_map.mat.height - ((point_3d.y - map_obj.info.offset.y) / map_obj.info.resolution) *  map_obj.img_map.mat.ratio);
}


void MapHandler::TransformImgPoint2Point3D(const MapObject& map_obj, const ImgPoint& img_point, Point3D& point_3d)
{
    point_3d.x = img_point.u * 1.0  / map_obj.img_map.mat.ratio * map_obj.info.resolution + map_obj.info.offset.x;
    point_3d.y = (map_obj.img_map.mat.height - img_point.v) * 1.0 / map_obj.img_map.mat.ratio * map_obj.info.resolution + map_obj.info.offset.y;
    point_3d.z = 0.0;
}

void MapHandler::ListAllMap(vector<string> &maplist)
{
    DIR *map_dir;
    struct dirent *dirp;
    map_dir = opendir(map_dir_path_.c_str());
    // __FILE__, __LINE__, __FUNCTION__
    if(map_dir == NULL) {
        throw "Open map directory error.";
    }
    while ((dirp = readdir(map_dir)) != NULL) {
        // a buffer to store names.
        // copy names in directory to buffer.
        string name = string(dirp->d_name);
        //if there is no name with suffix ".yaml", have will be -1
        int have = name.find(".yaml");
        if (have != -1)
        {
            string pure_name = name.substr(0, have);
            maplist.push_back(pure_name);
        }
    }
    closedir(map_dir);
    return;
}

bool MapHandler::_MakeMD5(string &map_name, string &md5sum)
{
    if(!_GetMD5(map_name, md5sum))
    {
        stringstream ss;
        ss << "md5sum " << map_dir_path_ << "/" << map_name << ".pgm | awk \'{print $1}\' "
            << "> " << map_dir_path_ << "/" << map_name << ".md5 ";
        system(ss.str().c_str());
    }
    else
    {
        return true;
    }
    bool rtn = _GetMD5(map_name, md5sum);
    MAPH_DEBUG("_MakeMD5() make md5sum failed!");
    return rtn;
}

bool MapHandler::_GetMD5(string &map_name, string &md5sum)
{
    stringstream ss;
    ss << map_dir_path_ << "/" << map_name << ".md5";

    ifstream md5_file_stream;
    md5_file_stream.open(ss.str().c_str());
    if(!md5_file_stream)
    {
        return false;
    }

    char sum[64] = {0};
    md5_file_stream.getline(sum, 64);
    md5_file_stream.close();
    md5sum = sum;
    return true;
}

void MapHandler::ReadMapMD5(vector<string> &maplist, map<string, string> &md5list)
{
    vector<string>::iterator p_map_name = maplist.begin();
    while(p_map_name != maplist.end())
    {
        string md5sum = "";
        _GetMD5((*p_map_name), md5sum);
        md5list[(*p_map_name)] = md5sum;
        p_map_name++;
    }
    return;
}

bool MapHandler::_ReadMapInfoBaseInfo(YAML::Node &map_yaml, MapObject& map_obj)
{
    string image_field = map_yaml["image"].as<string>();
    if ( image_field == "")
    {
        MAPH_DEBUG("_ReadMapInfoBaseInfo() map_yaml[\"image\"] is \"\"");
        return false;
    }
    int dot_index = image_field.rfind('.');
    map_obj.info.id = image_field.substr(0, dot_index);
    try
    {
        map_obj.info.creation_time = map_yaml["time"].as<string>();
    }
    catch (YAML::TypedBadConversion<string>& e)
    {
        MAPH_DEBUG("_ReadMapInfoBaseInfo() %s", e.msg.c_str());
        map_obj.info.creation_time = "NA";
    }
    map_obj.info.resolution = map_yaml["resolution"].as<float>();
    map_obj.info.offset.x = map_yaml["origin"][0].as<float>();
    map_obj.info.offset.y = map_yaml["origin"][1].as<float>();
    map_obj.info.offset.z = map_yaml["origin"][2].as<float>();
    map_obj.add_info.negate = map_yaml["negate"].as<int>();
    map_obj.add_info.occupied_thresh = map_yaml["occupied_thresh"].as<float>();
    map_obj.add_info.free_thresh = map_yaml["free_thresh"].as<float>();
    try
    {
        map_obj.add_info.position_x = map_yaml["base_pose"][0].as<double>();
        map_obj.add_info.position_y = map_yaml["base_pose"][1].as<double>();
        map_obj.add_info.position_yaw = map_yaml["base_pose"][2].as<double>();
        map_obj.add_info.longitude = map_yaml["gps"][0].as<double>();
        map_obj.add_info.latitude = map_yaml["gps"][1].as<double>();
        map_obj.add_info.angles = map_yaml["gps"][2].as<double>();
    }
    catch (YAML::Exception& e)
    {
        MAPH_DEBUG("_ReadMapInfoBaseInfo() %s", e.msg.c_str());
        map_obj.add_info.position_x = 0.0;
        map_obj.add_info.position_y = 0.0;
        map_obj.add_info.position_yaw = 0.0;
        map_obj.add_info.longitude = 0.0;
        map_obj.add_info.latitude = 0.0;
        map_obj.add_info.angles = 0.0;
    }
    try
    {
        map_obj.map_2d.mat.data.resize(0);
        map_obj.map_2d.mat.width = map_yaml["width"].as<int>();
        map_obj.map_2d.mat.height = map_yaml["height"].as<int>();
        map_obj.img_map.mat.data.resize(0);
        map_obj.img_map.mat.width = map_yaml["width"].as<int>();
        map_obj.img_map.mat.height = map_yaml["height"].as<int>();
    }
    catch (YAML::TypedBadConversion<int>& e)
    {
        MAPH_DEBUG("_ReadMapInfoBaseInfo() %s", e.msg.c_str());
        need_save_ = true;
        return LoadMapImage(map_obj);
    }
    return true;
}

bool MapHandler::_ReadMapInfoStation(YAML::Node &map_yaml, MapObject& map_obj)
{
    map_obj.map_2d.stations.resize(0);
    map_obj.img_map.stations.resize(0);
    map_obj.map_2d.stations.clear();
    map_obj.img_map.stations.clear();
    if (map_yaml["stations"].size() > 0)
    {
        int cnt = map_yaml["stations"].size();
        map_obj.map_2d.stations.resize(cnt);
        map_obj.info.station_num = cnt;
        Station *p_station;
        ImgStation *p_img_station;

        for(int i=0;i<cnt;i++)
        {
            p_station = new Station;
            p_img_station = new ImgStation;
            p_station->info.map_id = map_obj.info.id;
            p_station->info.id = map_yaml["stations"][i]["info"]["name"].as<string>();
            if (map_yaml["stations"][i]["info"]["type"].as<string>() == "START")
            {
                p_station->info.type = START;
            }
            else if (map_yaml["stations"][i]["info"]["type"].as<string>() == "CHARGER")
            {
                p_station->info.type = CHARGER;
            }
            else
            {
                p_station->info.type = USER_DEFINED;
            }
            p_station->info.artag_id = map_yaml["stations"][i]["info"]["artag_id"].as<int>();
            p_img_station->info = p_station->info;
            p_station->pose.x = map_yaml["stations"][i]["pose"]["x"].as<float>();
            p_station->pose.y = map_yaml["stations"][i]["pose"]["y"].as<float>();
            p_station->pose.z = map_yaml["stations"][i]["pose"]["z"].as<float>();
            p_station->pose.roll = map_yaml["stations"][i]["pose"]["roll"].as<float>();
            p_station->pose.pitch = map_yaml["stations"][i]["pose"]["pitch"].as<float>();
            p_station->pose.yaw = map_yaml["stations"][i]["pose"]["yaw"].as<float>();
            map_obj.map_2d.stations[i] = *p_station;
            delete p_station;
        }

//        map_obj.img_map.stations.resize(map_obj.map_2d.stations.size());
//        for (int i=0;i<map_obj.map_2d.stations.size();i++)
//        {
//            map_obj.img_map.stations[i].info = map_obj.map_2d.stations[i].info;
//            TransformPose3D2ImgPose(map_obj,
//                                    map_obj.map_2d.stations[i].pose,
//                                    map_obj.img_map.stations[i].pose);
//        }
    }
    else
    {
        map_obj.map_2d.stations.resize(0);
        map_obj.img_map.stations.resize(0);
        map_obj.info.station_num = 0;
    }
    return true;
}

bool MapHandler::_ReadMapInfoPath(YAML::Node &map_yaml, MapObject& map_obj)
{
    map_obj.map_2d.paths.resize(0);
    map_obj.img_map.paths.resize(0);
    map_obj.map_2d.paths.clear();
    map_obj.img_map.paths.clear();
    if (map_yaml["paths"].size() > 0)
    {
        //map_obj.map_2d.paths.resize(map_yaml["paths"].size());
        map_obj.info.path_num = map_yaml["paths"].size();
        YAML::Node::iterator path_iteartor = map_yaml["paths"].begin();
        Path *p_path;
        while (path_iteartor != map_yaml["paths"].end())
        {
            p_path = new Path;
            p_path->info.map_id = map_obj.info.id;
            p_path->info.id = (*path_iteartor)["info"]["name"].as<string>();
            p_path->info.length = (*path_iteartor)["info"]["length"].as<float>();
            if ((*path_iteartor)["poses"].size() > 0)
            {
                //p_path->poses.resize((*path_iteartor)["poses"].size());
                p_path->poses.clear();
                p_path->info.pose_num = (*path_iteartor)["poses"].size();
                YAML::Node::iterator pose_iterator = (*path_iteartor)["poses"].begin();
                Pose3D *p_pose;
                while(pose_iterator != (*path_iteartor)["poses"].end())
                {
                    p_pose = new Pose3D;
                    p_pose->x = (*pose_iterator)["x"].as<float>();
                    p_pose->y = (*pose_iterator)["y"].as<float>();
                    p_pose->z = (*pose_iterator)["z"].as<float>();
                    p_pose->roll = (*pose_iterator)["roll"].as<float>();
                    p_pose->pitch = (*pose_iterator)["pitch"].as<float>();
                    p_pose->yaw = (*pose_iterator)["yaw"].as<float>();
                    pose_iterator++;
                    p_path->poses.push_back(*p_pose);
                    delete p_pose;
                }
            }
            else
            {
                p_path->poses.resize(0);
                p_path->info.pose_num = 0;
            }
            p_path->info.pose_num = p_path->poses.size();
            path_iteartor++;
            map_obj.map_2d.paths.push_back(*p_path);
            delete p_path;
        }

//        map_obj.img_map.paths.resize(map_obj.map_2d.paths.size());
//        for (int i=0;i<map_obj.map_2d.paths.size();i++)
//        {
//            map_obj.img_map.paths[i].info = map_obj.map_2d.paths[i].info;
//            map_obj.img_map.paths[i].poses.clear();
//            map_obj.img_map.paths[i].poses.resize(map_obj.map_2d.paths[i].poses.size());
//            for (int j=0;j<map_obj.map_2d.paths[i].poses.size();j++)
//            {
//                TransformPose3D2ImgPose(map_obj,
//                                        map_obj.map_2d.paths[i].poses[j],
//                                        map_obj.img_map.paths[i].poses[j]);
//            }
//        }
    }
    else
    {
        map_obj.map_2d.paths.resize(0);
        map_obj.img_map.paths.resize(0);
        map_obj.info.path_num = 0;
    }
    return true;
}

bool MapHandler::_ReadMapInfoTask(YAML::Node &map_yaml, MapObject& map_obj)
{
    map_obj.map_2d.tasks.resize(0);
    map_obj.map_2d.tasks.clear();
    if (map_yaml["tasks"].size() > 0)
    {
        //map_obj.map_2d.tasks.resize(map_yaml["tasks"].size());
        map_obj.info.task_num = map_yaml["tasks"].size();
        MAPH_DEBUG("~!~!~!task num is: %d", map_obj.info.task_num);
        YAML::Node::iterator task_iteartor = map_yaml["tasks"].begin();
        Task *p_task;
        while (task_iteartor != map_yaml["tasks"].end())
        {
            p_task = new Task;
            p_task->info.map_id = (*task_iteartor)["info"]["map_id"].as<string>();
            p_task->info.task_id = (*task_iteartor)["info"]["task_id"].as<string>();
            if ((*task_iteartor)["info"]["action_lists"].size() > 0)
            {
                //p_task->poses.resize((*task_iteartor)["actions"].size());
                p_task->info.action_list.clear();
                YAML::Node::iterator action_iterator = (*task_iteartor)["info"]["action_lists"].begin();
                TaskAction *p_action;
                while(action_iterator != (*task_iteartor)["info"]["action_lists"].end())
                {
                    p_action = new TaskAction;
                    if ((*action_iterator)["action_name"].as<string>() == "0")
                    {
                        p_action->action_name = navi;
                    }
                    else if ((*action_iterator)["action_name"].as<string>() == "1")
                    {
                        p_action->action_name = follow;
                    }
                    else if ((*action_iterator)["action_name"].as<string>() == "4")
                    {
                        p_action->action_name = turnleft;
                    }
                    else if ((*action_iterator)["action_name"].as<string>() == "5")
                    {
                        p_action->action_name = turnright;
                    }
                    else if ((*action_iterator)["action_name"].as<string>() == "2")
                    {
                        p_action->action_name = forward;
                    }
                    else if((*action_iterator)["action_name"].as<string>() == "3")
                    {
                        p_action->action_name = backward;
                    }
                    else
                    {
                        p_action->action_name = backtopoint;
                    }
                    p_action->action_args = (*action_iterator)["action_args"].as<string>();
                    p_action->duration = (*action_iterator)["duration"].as<int>();
                    action_iterator++;
                    p_task->info.action_list.push_back(*p_action);
                    delete p_action;
                }
            }
            else
            {
                p_task->info.action_list.resize(0);
            }
            string enb_taskloop_ = (*task_iteartor)["enb_taskloop"].as<string>();
            p_task->enb_taskloop = (enb_taskloop_ == "ture")?true:false;
            p_task->task_loop_times = (*task_iteartor)["task_loop_times"].as<int>();
            task_iteartor++;
            map_obj.map_2d.tasks.push_back(*p_task);
            delete p_task;
        }
    }
    else
    {
        map_obj.map_2d.tasks.resize(0);
        map_obj.info.task_num = 0;
    }
    return true;
}

bool MapHandler::_ReadMapInfoVirtualWall(YAML::Node &map_yaml, MapObject& map_obj)
{
    map_obj.map_2d.virtual_walls.resize(0);
    map_obj.img_map.virtual_walls.resize(0);
    map_obj.map_2d.virtual_walls.clear();
    map_obj.img_map.virtual_walls.clear();
    if (map_yaml["virtual_walls"].size() > 0)
    {
        YAML::Node::iterator virtual_wall_iteartor = map_yaml["virtual_walls"].begin();
        VirtualWall *p_virtual_wall;
        while (virtual_wall_iteartor != map_yaml["virtual_walls"].end())
        {
            p_virtual_wall = new VirtualWall;
            p_virtual_wall->info.map_id = map_obj.info.id;
            p_virtual_wall->info.id = (*virtual_wall_iteartor)["info"]["name"].as<string>();
            p_virtual_wall->info.length = (*virtual_wall_iteartor)["info"]["length"].as<float>();
            string closed = (*virtual_wall_iteartor)["info"]["closed"].as<string>();
            p_virtual_wall->info.closed = (closed=="true")?true:false;
            if ((*virtual_wall_iteartor)["points"].size() > 0)
            {
                p_virtual_wall->points.clear();
                YAML::Node::iterator point_iterator = (*virtual_wall_iteartor)["points"].begin();
                Point3D *p_point;
                while(point_iterator != (*virtual_wall_iteartor)["points"].end())
                {
                    p_point = new Point3D;
                    p_point->x = (*point_iterator)["x"].as<float>();
                    p_point->y = (*point_iterator)["y"].as<float>();
                    p_point->z = (*point_iterator)["z"].as<float>();
                    point_iterator++;
                    p_virtual_wall->points.push_back(*p_point);
                    delete p_point;
                }
            }
            else
            {
                p_virtual_wall->points.resize(0);
            }
            virtual_wall_iteartor++;
            map_obj.map_2d.virtual_walls.push_back(*p_virtual_wall);


            delete p_virtual_wall;
        }

//        map_obj.img_map.virtual_walls.resize(map_obj.map_2d.virtual_walls.size());
//        for (int i=0;i<map_obj.map_2d.virtual_walls.size();i++)
//        {
//            map_obj.img_map.virtual_walls[i].info = map_obj.map_2d.virtual_walls[i].info;
//            map_obj.img_map.virtual_walls[i].points.clear();
//            map_obj.img_map.virtual_walls[i].points.resize(map_obj.map_2d.virtual_walls[i].points.size());

//            for (int j=0;j<map_obj.map_2d.virtual_walls[i].points.size();j++)
//            {
//                TransformPoint3D2ImgPoint(map_obj,
//                                        map_obj.map_2d.virtual_walls[i].points[j],
//                                        map_obj.img_map.virtual_walls[i].points[j]);
//            }
//        }
    }
    else
    {
        map_obj.map_2d.virtual_walls.resize(0);
        map_obj.img_map.virtual_walls.resize(0);
    }
    return true;
}



bool MapHandler::ReadMapInfo(const string& map_name, MapObject& map_obj)
{

    try
    {
        need_save_ = false;
        string yaml_file_path = map_dir_path_ + "/" + map_name + ".yaml";
        ifstream map_yaml_stream(yaml_file_path.c_str());
        YAML::Node map_yaml = YAML::Load(map_yaml_stream);
        if(!_ReadMapInfoBaseInfo(map_yaml, map_obj)
            || !_ReadMapInfoStation(map_yaml, map_obj)
            || !_ReadMapInfoTask(map_yaml, map_obj)
            || !_ReadMapInfoPath(map_yaml, map_obj)
            || !_ReadMapInfoVirtualWall(map_yaml, map_obj)
            || !LoadThumbnail(map_obj)
            || !_MakeMD5(map_obj.info.id, map_obj.add_info.md5sum))
        {
            map_obj.info.id = "";
            map_yaml_stream.close();
            need_save_ = false;
            return false;
        }
        if (need_save_ == true)
        {
            SaveMapYaml(map_obj);
            need_save_ = false;
        }
    }
    catch (YAML::TypedBadConversion<string>& e)
    {
        map_obj.info.id = "";
        MAPH_DEBUG("YAML::TypedBadConversion<string> e: %s", e.what());
        return false;
    }
    catch (YAML::TypedBadConversion<int>& e)
    {
        map_obj.info.id = "";
        MAPH_DEBUG("YAML::TypedBadConversion<int> e: %s", e.what());
        return false;
    }
    catch (YAML::TypedBadConversion<float>& e)
    {
        map_obj.info.id = "";
        MAPH_DEBUG("YAML::TypedBadConversion<float> e: %s", e.what());
        return false;
    }
    catch (YAML::TypedBadConversion<bool>& e)
    {
        map_obj.info.id = "";
        MAPH_DEBUG("YAML::TypedBadConversion<bool> e: %s", e.what());
        return false;
    }
    catch(exception& e)
    {
        map_obj.info.id = "";
        MAPH_DEBUG("YAML::Load_error: %s",e.what());
        return false;
    }
    return true;
}

bool MapHandler::LoadMapImage(MapObject& map_obj)
{
    if (map_obj.map_2d.mat.data.size() != 0
        && map_obj.img_map.mat.data.size() != 0 )
    {
        return true;
    }
    string pgm_file_path = map_dir_path_ + "/" + map_obj.info.id + ".pgm";
    SDL_Surface *p_matedata = IMG_Load(pgm_file_path.c_str());
    if (p_matedata == NULL)
    {
        MAPH_DEBUG("LoadMapImage() load pgm file failed!");
        map_obj.map_2d.mat.data.resize(0);
        map_obj.img_map.mat.data.resize(0);
        return false;
    }
    int total_length = p_matedata->w * p_matedata->h;
    map_obj.info.dimension.x = p_matedata->w * map_obj.info.resolution;
    map_obj.info.dimension.y = p_matedata->h * map_obj.info.resolution;
    map_obj.info.dimension.z = 0.0;

    map_obj.map_2d.mat.width = p_matedata->w;
    map_obj.map_2d.mat.height = p_matedata->h;
    map_obj.map_2d.mat.data.clear();
    map_obj.map_2d.mat.data.resize(total_length);

    map_obj.img_map.mat.width = p_matedata->w;
    map_obj.img_map.mat.height = p_matedata->h;
    map_obj.img_map.mat.ratio = 1.0;
    map_obj.img_map.mat.data.clear();
    map_obj.img_map.mat.data.resize(total_length);

    bool trinary = true;
    double occ_th = map_obj.add_info.occupied_thresh;
    double free_th = map_obj.add_info.free_thresh;
    int rowstride, n_channels, avg_channels, alpha, color_sum;
    unsigned char *pixels, *p;
    CellType cell_value;
    int pixel_value;
    double color_avg, occ;

    /// Get values that we'll need to iterate through the pixels
    rowstride = p_matedata->pitch;
    n_channels = p_matedata->format->BytesPerPixel;

    avg_channels = (trinary || n_channels == 1)?n_channels:(n_channels-1);

    /// Copy pixel data into the map structure
    pixels = (unsigned char*)(p_matedata->pixels);
    for(int j=0;j<map_obj.map_2d.mat.height;j++)
    {
        for (int i=0;i<map_obj.map_2d.mat.width;i++)
        {
            /// Compute mean of RGB for this pixel
            p = pixels + j*rowstride + i*n_channels;
            color_sum = 0;
            for(int k=0;k<avg_channels;k++)
            {
                color_sum += *(p + (k));
            }
            color_avg = color_sum / (double)avg_channels;
            color_avg = (color_avg>255.0)?255.0:color_avg;
            alpha = (n_channels == 1)?1:(*(p+n_channels-1));
            /// If negate is true, we consider blacker pixels free, and whiter
            /// pixels free.  Otherwise, it's vice versa.
            occ = (map_obj.add_info.negate)?(color_avg/255.0):((255-color_avg)/255.0);
            /// Apply thresholds to RGB means to determine occupancy values for
            /// map.  Note that we invert the graphics-ordering of the pixels to
            /// produce a map with cell (0,0) in the lower-left corner.
            pixel_value = static_cast<int>((map_obj.add_info.negate)?(color_avg):(255-color_avg));
            pixel_value = (pixel_value>255)?255:pixel_value;
            if(occ > occ_th)
            {
                cell_value = OCCUPIED_CELL;
            }
            else if(occ < free_th)
            {
                cell_value = FREE_CELL;
            }
            else if(trinary || alpha < 1.0)
            {
                cell_value = UNKNOWN_CELL;
            }
            else
            {
                cell_value = UNKNOWN_CELL;
            }
            int map_2d_index = MAP_IDX(p_matedata->w, i, p_matedata->h-j-1);
            int img_map_index = MAP_IDX(p_matedata->w, i, j);
            map_obj.map_2d.mat.data[map_2d_index] = cell_value;
            map_obj.img_map.mat.data[img_map_index] = pixel_value;
        }
    }
    ResizeImgMap(map_obj, 1000);

    map_obj.img_map.virtual_walls.resize(map_obj.map_2d.virtual_walls.size());
    for (int i=0;i<map_obj.map_2d.virtual_walls.size();i++)
    {
        map_obj.img_map.virtual_walls[i].info = map_obj.map_2d.virtual_walls[i].info;
        map_obj.img_map.virtual_walls[i].points.clear();
        map_obj.img_map.virtual_walls[i].points.resize(map_obj.map_2d.virtual_walls[i].points.size());

        for (int j=0;j<map_obj.map_2d.virtual_walls[i].points.size();j++)
        {
            TransformPoint3D2ImgPoint(map_obj,
                                    map_obj.map_2d.virtual_walls[i].points[j],
                                    map_obj.img_map.virtual_walls[i].points[j]);
        }
    }

    map_obj.img_map.stations.resize(map_obj.map_2d.stations.size());
    for (int i=0;i<map_obj.map_2d.stations.size();i++)
    {
        map_obj.img_map.stations[i].info = map_obj.map_2d.stations[i].info;
        TransformPose3D2ImgPose(map_obj,
                                map_obj.map_2d.stations[i].pose,
                                map_obj.img_map.stations[i].pose);
    }

    map_obj.img_map.paths.resize(map_obj.map_2d.paths.size());
    for (int i=0;i<map_obj.map_2d.paths.size();i++)
    {
        map_obj.img_map.paths[i].info = map_obj.map_2d.paths[i].info;
        map_obj.img_map.paths[i].poses.clear();
        map_obj.img_map.paths[i].poses.resize(map_obj.map_2d.paths[i].poses.size());
        for (int j=0;j<map_obj.map_2d.paths[i].poses.size();j++)
        {
            TransformPose3D2ImgPose(map_obj,
                                    map_obj.map_2d.paths[i].poses[j],
                                    map_obj.img_map.paths[i].poses[j]);
        }
    }
    return true;
}

bool MapHandler::LoadThumbnail(MapObject &map_obj)
{
    string pgm_file_path = map_dir_path_ + "/" + map_obj.info.id + "_thum.pgm";
    SDL_Surface *p_matedata = IMG_Load(pgm_file_path.c_str());
    if (p_matedata == NULL)
    {
        return (LoadMapImage(map_obj) && MakeThumbnail(map_obj)
            && SaveThumbnail(map_obj));
    }
    int total_length = p_matedata->w * p_matedata->h;

    map_obj.info.thumbnail.width = p_matedata->w;
    map_obj.info.thumbnail.height = p_matedata->h;
    map_obj.info.thumbnail.data.clear();
    map_obj.info.thumbnail.data.resize(total_length);

    bool trinary = true;
    double occ_th = map_obj.add_info.occupied_thresh;
    double free_th = map_obj.add_info.free_thresh;
    int rowstride, n_channels, avg_channels, alpha, color_sum;
    unsigned char *pixels, *p;
    CellType cell_value;
    int pixel_value;
    double color_avg, occ;

    /// Get values that we'll need to iterate through the pixels
    rowstride = p_matedata->pitch;
    n_channels = p_matedata->format->BytesPerPixel;

    avg_channels = (trinary || n_channels == 1)?n_channels:(n_channels-1);

    /// Copy pixel data into the map structure
    pixels = (unsigned char*)(p_matedata->pixels);
    for(int j=0;j<map_obj.info.thumbnail.height;j++)
    {
        for (int i=0;i<map_obj.info.thumbnail.width;i++)
        {
            /// Compute mean of RGB for this pixel
            p = pixels + j*rowstride + i*n_channels;
            color_sum = 0;
            for(int k=0;k<avg_channels;k++)
            {
                color_sum += *(p + (k));
            }
            color_avg = color_sum / (double)avg_channels;
            color_avg = (color_avg>255.0)?255.0:color_avg;
            alpha = (n_channels == 1)?1:(*(p+n_channels-1));
            /// If negate is true, we consider blacker pixels free, and whiter
            /// pixels free.  Otherwise, it's vice versa.
            occ = (map_obj.add_info.negate)?(color_avg/255.0):((255-color_avg)/255.0);
            /// Apply thresholds to RGB means to determine occupancy values for
            /// map.  Note that we invert the graphics-ordering of the pixels to
            /// produce a map with cell (0,0) in the lower-left corner.
            pixel_value = static_cast<int>((map_obj.add_info.negate)?(color_avg):(255-color_avg));
            pixel_value = (pixel_value>255)?255:pixel_value;
            int index = MAP_IDX(p_matedata->w, i, j);
            map_obj.info.thumbnail.data[index] = pixel_value;
        }
    }
    return true;
}

bool MapHandler::MakeThumbnail(MapObject& map_obj)
{
    map_obj.info.thumbnail.data.clear();
    map_obj.info.thumbnail.data.resize(0);
    return ResizePixelMat(map_obj.img_map.mat, map_obj.info.thumbnail, 100);
}

bool MapHandler::SaveThumbnail(MapObject &map_obj)
{
    string pgm_file_path = map_dir_path_ + "/" + map_obj.info.id + "_thum.pgm";
    ofstream pgm_file(pgm_file_path.c_str());
    pgm_file << "P5\n";
    pgm_file << "# CREATOR: map_handler.cpp thumbnail.\n";
    pgm_file << map_obj.info.thumbnail.width << " " << map_obj.info.thumbnail.height << "\n";
    pgm_file << "255\n";
    int max = map_obj.info.thumbnail.height * map_obj.info.thumbnail.width;

    for (int i=0;i<max;i++)
    {
        pgm_file.put(255-map_obj.info.thumbnail.data[i]);
    }
    pgm_file.close();
    return true;
}

bool MapHandler::FillMapMateData(MapObject& map_obj)
{
    return LoadMapImage(map_obj);
}

bool MapHandler::ResizePixelMat(PixelMat &input_pixel_mat, PixelMat &output_pixel_mat, int size_max)
{
    if (input_pixel_mat.width > size_max || input_pixel_mat.height > size_max)
    {
        double ratio_w = 1.0 * size_max / input_pixel_mat.width;
        double ratio_h = 1.0 * size_max / input_pixel_mat.height;
        double ratio = MIN(ratio_w, ratio_h);
        unsigned char *p = reinterpret_cast<unsigned char *>(&(input_pixel_mat.data[0]));
        cv::Mat src = cv::Mat(input_pixel_mat.height, input_pixel_mat.width, CV_8UC1, (void *)p);
#if 0
        int height = (int)(input_pixel_mat.height * ratio);
        int width = (int)(input_pixel_mat.width * ratio);
        cv::Mat tgt(height, width, CV_8UC1);
        cv::resize(src, tgt, cv::Size(height, width));
#else
        cv::Mat tgt;
        cv::resize(src, tgt, cv::Size(), ratio, ratio);
#endif
        output_pixel_mat.ratio = ratio;
        output_pixel_mat.height = tgt.rows;
        output_pixel_mat.width = tgt.cols;
        output_pixel_mat.data.clear();
        output_pixel_mat.data.resize(tgt.total());
        for(int i=0;i<tgt.total();i++)
        {
            output_pixel_mat.data[i] = tgt.data[i];
        }
    }
    return true;
}

bool MapHandler::ResizeImgMap(MapObject &map_obj, int size_max)
{
   return ResizePixelMat(map_obj.img_map.mat, map_obj.img_map.mat, size_max);
}

bool MapHandler::MakeMapMessage(const MapObject& map_obj, nav_msgs::OccupancyGrid& msg)
{
    //Print(map_obj);
    msg.info.map_load_time = ros::Time::now();
    msg.info.width = map_obj.map_2d.mat.width;
    msg.info.height = map_obj.map_2d.mat.height;
    msg.info.resolution = map_obj.info.resolution;
    msg.info.origin.position.x = map_obj.info.offset.x;
    msg.info.origin.position.y = map_obj.info.offset.y;
    msg.info.origin.position.z = 0.0;
    tf::Quaternion q;
    q.setRPY(0.0, 0.0, map_obj.info.offset.z);
    msg.info.origin.orientation.x = q.x();
    msg.info.origin.orientation.y = q.y();
    msg.info.origin.orientation.z = q.z();
    msg.info.origin.orientation.w = q.w();
    int length = msg.info.width * msg.info.height;
    msg.data.clear();
    msg.data.resize(length);
    if (map_obj.map_2d.mat.data.size() == 0)
    {
        FillMapMateData(const_cast<MapObject&>(map_obj));
    }
    for (int i=0;i<length;i++)
    {
        if (map_obj.map_2d.mat.data[i] == OCCUPIED_CELL)
        {
            msg.data[i] = 100;
        }
        else if (map_obj.map_2d.mat.data[i] == FREE_CELL)
        {
            msg.data[i] = 0;
        }
        else
        {
            msg.data[i] = -1;
        }
    }

    return true;
}

bool MapHandler::MakeVirtualwallMessage(const MapObject& map_obj, wr_npu_msgs::Virtualwalls& msg)
{
    msg.info.map_load_time = ros::Time::now();
    msg.info.resolution = map_obj.info.resolution;
    msg.info.width = map_obj.map_2d.mat.width;
    msg.info.height = map_obj.map_2d.mat.height;
    msg.info.origin.position.x = map_obj.info.offset.x;
    msg.info.origin.position.y = map_obj.info.offset.y;
    msg.info.origin.position.z = 0.0;
    msg.info.origin.orientation.x = 0.0;
    msg.info.origin.orientation.y = 0.0;
    msg.info.origin.orientation.z = 0.0;
    msg.info.origin.orientation.w = 0.0;

    int wall_cnt = map_obj.map_2d.virtual_walls.size();
    msg.virtualwalls.resize(wall_cnt);
    for (int i=0;i<wall_cnt;++i)
    {
        int point_cnt = map_obj.map_2d.virtual_walls[i].points.size();
        msg.virtualwalls[i].point_array.resize(point_cnt);
        for (int j=0;j<point_cnt;++j)
        {
            msg.virtualwalls[i].point_array[j].x = map_obj.map_2d.virtual_walls[i].points[j].x;
            msg.virtualwalls[i].point_array[j].y = map_obj.map_2d.virtual_walls[i].points[j].y;
        }
    }
    return true;
}

void MapHandler::CopyMap(const nav_msgs::OccupancyGrid& map, MapObject& map_obj, bool need_chop)
{
    map_obj.info.resolution = map.info.resolution;
    map_obj.add_info.negate = 0;
    map_obj.add_info.occupied_thresh = 0.65;
    map_obj.add_info.free_thresh = 0.196;

    time_t time_now = ros::Time::now().toSec();
    struct tm *p_time_stamp = localtime(&time_now);
    char str_buf[80];
    strftime(str_buf, 80, "%Y-%m-%d %H:%M:%S", p_time_stamp);
    map_obj.info.creation_time = string(str_buf);

#define MAP_SERVER_MAX(x,y) ((x)>=(y)?(x):(y))
#define MAP_SERVER_MIN(x,y) ((x)<=(y)?(x):(y))
    int chopped_width = map.info.width;
    int chopped_height = map.info.height;
    int max_x = 0;
    int max_y = 0;
    int min_x = map.info.width - 1;
    int min_y = map.info.height - 1;
    if (need_chop == true)
    {
        bool get_map = false;
        for (int y = 0; y < map.info.height; y++)
        {
            for (int x = 0; x < map.info.width; x++)
            {
                int idx = x + y * map.info.width;
                if (map.data[idx] == 0 || map.data[idx] == 100)
                {
                    min_x = MAP_SERVER_MIN(min_x, x);
                    min_y = MAP_SERVER_MIN(min_y, y);
                    max_x = MAP_SERVER_MAX(max_x, x);
                    max_y = MAP_SERVER_MAX(max_y, y);
                    get_map = true;
                }
            }
        }
        if (get_map == false)
        {
            map_obj.map_2d.mat.width = 1;
            map_obj.map_2d.mat.height = 1;
            map_obj.map_2d.mat.data.resize(1);
            map_obj.map_2d.mat.data[0] = FREE_CELL;
            map_obj.img_map.mat.width = 1;
            map_obj.img_map.mat.height = 1;
            map_obj.img_map.mat.data.resize(1);
            map_obj.img_map.mat.data[0] = 0;
            map_obj.img_map.mat.ratio = 1.0;
            map_obj.info.offset.x = 0.0;
            map_obj.info.offset.x = 0.0;
            map_obj.info.offset.z = 0.0;
            return;
        }
        MAPH_DEBUG_ONCE("UpdateMapObject(): Find max_x=%d, max_y=%d, min_x=%d, min_y=%d.",
                  max_x, max_y, min_x, min_y);
        chopped_width = max_x - min_x + 1;
        chopped_height = max_y - min_y + 1;
    }
    int total_length = chopped_width * chopped_height;
    MAPH_DEBUG_ONCE("UpdateMapObject(): Chopped map into %d x %d, total length %d",
             chopped_width, chopped_height, total_length);
    map_obj.map_2d.mat.data.resize(total_length);
    map_obj.img_map.mat.data.resize(total_length);

    //bool need_rotation = (chopped_width >= chopped_height)?false:true;
    bool need_rotation = false;
    int rotate_chopped_width = (need_rotation==false)?chopped_width:chopped_height;
    int rotate_chopped_height = (need_rotation==false)?chopped_height:chopped_width;
    map_obj.map_2d.mat.width = rotate_chopped_width;
    map_obj.img_map.mat.width = rotate_chopped_width;
    map_obj.map_2d.mat.height = rotate_chopped_height;
    map_obj.img_map.mat.height = rotate_chopped_height;
    for(int y=0;y<rotate_chopped_height;y++)
    {
        for(int x=0;x<rotate_chopped_width;x++)
        {
            CellType cell_value;
            unsigned char pixel_value;
            int ori_index = (need_rotation==false)\
                    ?((min_x + x) + (min_y + y) * map.info.width)\
                    :((min_x + y) + (max_y - x) * map.info.width);
            int cell_index = x + y * rotate_chopped_width;
            int img_index = x + (rotate_chopped_height - y - 1) * rotate_chopped_width;
            if (map.data[ori_index] == 0)          //occ [0,0.1) 0;
            {
                cell_value = FREE_CELL;
                pixel_value = 0;
            }
            else if (map.data[ori_index] == +100)  //occ (0.65,1] 254;
            {
                cell_value = OCCUPIED_CELL;
                pixel_value = 254;
            }
            else                                    //occ [0.1,0.65] 205;
            {
                cell_value = UNKNOWN_CELL;
                pixel_value = 205;
            }
            map_obj.map_2d.mat.data[cell_index] = cell_value;
            map_obj.img_map.mat.data[img_index] = static_cast<unsigned char>(pixel_value);
        }
    }
    const double half_pi = (3.1415926 / 2);
    geometry_msgs::Quaternion orientation = map.info.origin.orientation;
    tf::Matrix3x3 mat(tf::Quaternion(orientation.x, orientation.y, orientation.z, orientation.w));
    double yaw, pitch, roll;
    mat.getEulerYPR(yaw, pitch, roll);

    double half_x_edge_length = -1.0 * map.info.origin.position.x / map.info.resolution;
    double half_y_edge_length = -1.0 * map.info.origin.position.y / map.info.resolution;

    map_obj.info.offset.x = (need_rotation==false)\
            ?(-1.0 * (half_x_edge_length - min_x) * map.info.resolution)\
            :(-1.0 * (max_y - half_y_edge_length) * map.info.resolution);
    map_obj.info.offset.y = (need_rotation==false)\
            ?(-1.0 * (half_y_edge_length - min_y) * map.info.resolution)\
            :(-1.0 * (half_x_edge_length - min_x) * map.info.resolution);
    map_obj.info.offset.z = (need_rotation==false)?(0.0):(-half_pi);

    if (need_rotation == true)
    {
        map_obj.img_map.mat.ratio = (map_obj.img_map.mat.ratio>0)?(-map_obj.img_map.mat.ratio):(map_obj.img_map.mat.ratio);
    }
}

void MapHandler::UpdateMapObject(const nav_msgs::OccupancyGrid& map, MapObject& map_obj)
{
    MAPH_DEBUG("UpdateMapObject(): Received map (%d x %d)@%.3f m/pix, data length = %d",
           map.info.width,
           map.info.height,
           map.info.resolution,
           static_cast<int>(map.data.size()));
    MAPH_DEBUG("UpdateMapObject(): Map id = %s", map_obj.info.id.c_str());

    CopyMap(map, map_obj, true);
    ResizeImgMap(map_obj, 1000);
    MakeThumbnail(map_obj);
    MAPH_DEBUG("UpdateMapObject(): Origin pose = (%.2f, %.2f, %.2f), ratio = %.2f",
              map_obj.info.offset.x, map_obj.info.offset.y, map_obj.info.offset.z, map_obj.img_map.mat.ratio);
}

void MapHandler::MakeYaml(const MapObject& map_obj, YAML::Node& map_yaml)
{
    map_yaml["image"] = map_obj.info.id + ".pgm";
    map_yaml["width"] = map_obj.map_2d.mat.width;
    map_yaml["height"] = map_obj.map_2d.mat.height;
    map_yaml["resolution"] = map_obj.info.resolution;
    double width_m = map_obj.map_2d.mat.width * map_obj.info.resolution;
    map_yaml["width_m"] = width_m;
    double height_m = map_obj.map_2d.mat.height * map_obj.info.resolution;
    map_yaml["height_m"] = height_m;
    map_yaml["area_m2"] = width_m * height_m;
    map_yaml["origin"][0] = map_obj.info.offset.x;
    map_yaml["origin"][1] = map_obj.info.offset.y;
    map_yaml["origin"][2] = map_obj.info.offset.z;
    map_yaml["base_pose"][0] =  map_obj.add_info.position_x;
    map_yaml["base_pose"][1] =  map_obj.add_info.position_y;
    map_yaml["base_pose"][2] =  map_obj.add_info.position_yaw;
    map_yaml["gps"][0] = map_obj.add_info.longitude;
    map_yaml["gps"][1] = map_obj.add_info.latitude;
    map_yaml["gps"][2] = map_obj.add_info.angles;
    map_yaml["negate"] = map_obj.add_info.negate;
    map_yaml["occupied_thresh"] = map_obj.add_info.occupied_thresh;
    map_yaml["free_thresh"] = map_obj.add_info.free_thresh;
    map_yaml["time"] = map_obj.info.creation_time;
    map_yaml["config_id"] = map_obj.add_info.config_id;
    map_yaml["lidar_type"] = map_obj.add_info.lidar_type;
    map_yaml["lidar_range_m"] = map_obj.add_info.lidar_range_m;
    map_yaml["lidar_frq_hz"] = map_obj.add_info.lidar_frq_hz;
    map_yaml["slam_mode"] = map_obj.add_info.slam_mode;
    // map_yaml["opt_mode"] = map_obj.add_info.opt_mode;

    StationList::const_iterator s_iter = map_obj.map_2d.stations.begin();
    int index_station = 0;
    for(;s_iter!=map_obj.map_2d.stations.end();s_iter++)
    {
        YAML::Node station;
        station["info"]["name"] = s_iter->info.id;
        if (s_iter->info.type == START)
        {
            station["info"]["type"] = "START";
        }
        else if (s_iter->info.type == CHARGER)
        {
            station["info"]["type"] = "CHARGER";
        }
        else
        {
            station["info"]["type"] = "USER_DEFINED";
        }
        station["info"]["artag_id"] = s_iter->info.artag_id;
        station["pose"]["x"] = s_iter->pose.x;
        station["pose"]["y"] = s_iter->pose.y;
        station["pose"]["z"] = s_iter->pose.z;
        station["pose"]["roll"] = s_iter->pose.roll;
        station["pose"]["pitch"] = s_iter->pose.pitch;
        station["pose"]["yaw"] = s_iter->pose.yaw;
        map_yaml["stations"][index_station] = station;
        index_station ++;
    }

    TaskList::const_iterator t_iter = map_obj.map_2d.tasks.begin();
    int index_task = 0;
    for(;t_iter!=map_obj.map_2d.tasks.end();t_iter++)
    {
        YAML::Node task;
        task["info"]["map_id"] = t_iter->info.map_id;
        task["info"]["task_id"] = t_iter->info.task_id;
        TaskActionList::const_iterator o_iter = t_iter->info.action_list.begin();

        int index_action = 0;
        for(;o_iter!=t_iter->info.action_list.end();o_iter++)
        {
            YAML::Node action_list;
            actionname enum_action_name = o_iter->action_name;
            int n = enum_action_name;
            action_list["action_name"] = n;
            action_list["action_args"] = o_iter->action_args;
            action_list["duration"] = o_iter->duration;
            task["info"]["action_lists"][index_action] = action_list;
            index_action++;
        }
        task["enb_taskloop"] = t_iter->enb_taskloop;
        task["task_loop_times"] = t_iter->task_loop_times;
        map_yaml["tasks"][index_task] = task;
        index_task++;
    }

    PathList::const_iterator p_iter = map_obj.map_2d.paths.begin();
    int index_path = 0;
    for(;p_iter!=map_obj.map_2d.paths.end();p_iter++)
    {
        YAML::Node path;
        path["info"]["name"] = p_iter->info.id;
        path["info"]["length"] = p_iter->info.length;

        Pose3DList::const_iterator o_iter = p_iter->poses.begin();

        int index_pose = 0;
        for(;o_iter!=p_iter->poses.end();o_iter++)
        {
            YAML::Node pose;
            pose["x"] = o_iter->x;
            pose["y"] = o_iter->y;
            pose["z"] = o_iter->z;
            pose["roll"] = o_iter->roll;
            pose["pitch"] = o_iter->pitch;
            pose["yaw"] = o_iter->yaw;
            path["poses"][index_pose] = pose;
            index_pose++;
        }

        map_yaml["paths"][index_path] = path;
        index_path++;
    }

    VirtualWallList::const_iterator v_iter = map_obj.map_2d.virtual_walls.begin();
    int index_virtual_wall = 0;
    for(;v_iter!=map_obj.map_2d.virtual_walls.end();v_iter++)
    {
        YAML::Node virtual_wall;
        virtual_wall["info"]["name"] = v_iter->info.id;
        virtual_wall["info"]["length"] = v_iter->info.length;
        string closed = (v_iter->info.closed==true)?"true":"false";
        virtual_wall["info"]["closed"] = closed;

        Point3DList::const_iterator o_iter = v_iter->points.begin();

        int index_point= 0;
        for(;o_iter!=v_iter->points.end();o_iter++)
        {
            YAML::Node point;
            point["x"] = o_iter->x;
            point["y"] = o_iter->y;
            point["z"] = o_iter->z;
            virtual_wall["points"][index_point] = point;
            index_point++;
        }

        map_yaml["virtual_walls"][index_virtual_wall] = virtual_wall;
        index_virtual_wall++;
    }
    return;
}

void MapHandler::SaveMapYaml(const MapObject& map_obj)
{
    string yaml_file_path = map_dir_path_ + "/" + map_obj.info.id + ".yaml";
    ofstream yaml_file(yaml_file_path.c_str());
    YAML::Node map_yaml;
    MakeYaml(map_obj, map_yaml);
    yaml_file << map_yaml;
    /*
    yaml_file << map_yaml["image"];
    yaml_file << map_yaml["resolution"];
    yaml_file << map_yaml["negate"];
    yaml_file << map_yaml["free_thresh"];
    yaml_file << map_yaml["time"];
    yaml_file << map_yaml["occupied_thresh"];
    yaml_file << map_yaml["width"];
    yaml_file << map_yaml["height"];
    yaml_file << map_yaml["width_m"];
    yaml_file << map_yaml["height_m"];
    yaml_file << map_yaml["area_m2"];
    yaml_file << map_yaml["config_id"];
    yaml_file << map_yaml["lidar_type"];
    yaml_file << map_yaml["lidar_range_m"];
    yaml_file << map_yaml["lidar_frq_hz"];
    yaml_file << map_yaml["slam_mode"];
    */
    yaml_file.close();
}

void MapHandler::SaveMapPgm(const MapObject& map_obj)
{
    string pgm_file_path = map_dir_path_ + "/" + map_obj.info.id + ".pgm";
    ofstream pgm_file(pgm_file_path.c_str());
    pgm_file << "P5\n";
    pgm_file << "# CREATOR: map_handler.cpp " << map_obj.info.resolution << "m/pix\n";
    pgm_file << map_obj.map_2d.mat.width << " " << map_obj.map_2d.mat.height << "\n";
    pgm_file << "255\n";

    unsigned char value;
    for(int y=0;y<map_obj.map_2d.mat.height;y++)
    {
        for(int x=0;x<map_obj.map_2d.mat.width;x++)
        {
            int cell_index = x + (map_obj.map_2d.mat.height - y - 1) * map_obj.map_2d.mat.width;
            if (map_obj.map_2d.mat.data[cell_index] == OCCUPIED_CELL)
            {
                value = 0;
            }
            else if (map_obj.map_2d.mat.data[cell_index] == FREE_CELL)
            {
                value = 254;
            }
            else
            {
                value = 205;
            }
            pgm_file.put(value);
        }
    }
    pgm_file.close();
}

void MapHandler::DeleteMapFile(const MapObject& map_obj)
{
    string yaml_file = map_dir_path_ + "/" + map_obj.info.id + ".yaml";
    string thumbnail_file = map_dir_path_ + "/" + map_obj.info.id + "_thum.pgm";
    string pgm_file = map_dir_path_ + "/" + map_obj.info.id + ".pgm";

    stringstream ss;
    ss << "rm -rf " \
        << yaml_file << " "  \
        << thumbnail_file << " " \
        << pgm_file << endl;
    system(ss.str().c_str());
}

void MapHandler::SaveMapObject(const MapObject& map_obj)
{
    //filter map_obj.mat.data
    SaveMapYaml(map_obj);
    SaveThumbnail(const_cast<MapObject&>(map_obj));
    SaveMapPgm(map_obj);
}

string MapHandler::GetMapDirPath()
{
    return map_dir_path_;
}

void MapHandler::Print(const MapInfo &map_info)
{
    MAPH_DEBUG("info.index: %d", map_info.index);
    MAPH_DEBUG("info.id: %s", map_info.id.c_str());
    MAPH_DEBUG("info.creation_time: %s", map_info.creation_time.c_str());
    MAPH_DEBUG("info.resolution: %.02f", map_info.resolution);
    MAPH_DEBUG("info.dimension.x: %.02f", map_info.dimension.x);
    MAPH_DEBUG("info.dimension.y: %.02f", map_info.dimension.y);
    MAPH_DEBUG("info.dimension.z: %.02f", map_info.dimension.z);
    MAPH_DEBUG("info.offset.x: %.02f", map_info.offset.x);
    MAPH_DEBUG("info.offset.y: %.02f", map_info.offset.y);
    MAPH_DEBUG("info.offset.z: %.02f", map_info.offset.z);
    MAPH_DEBUG("info.thumbnail.width: %d", map_info.thumbnail.width);
    MAPH_DEBUG("info.thumbnail.height: %d", map_info.thumbnail.height);
    MAPH_DEBUG("info.thumbnail.data: %d", static_cast<int>(map_info.thumbnail.data.size()));
    MAPH_DEBUG("info.station_num: %d", map_info.station_num);
    MAPH_DEBUG("info.path_num: %d", map_info.path_num);
    MAPH_DEBUG("info.task_num: %d", map_info.task_num);
//    MAPH_DEBUG("info.virtual_wall_num: %d", map_info.virtual_wall_num);
}

void MapHandler::Print(const MapAddInfo &map_add_info)
{
    MAPH_DEBUG("add_info.negate: %d", map_add_info.negate);
    MAPH_DEBUG("add_info.occupied_thresh: %.02f", map_add_info.occupied_thresh);
    MAPH_DEBUG("add_info.free_thresh: %.02f", map_add_info.free_thresh);
}

void MapHandler::Print(const CellMat &cell_mat)
{
    MAPH_DEBUG("cell_mat.width: %d", cell_mat.width);
    MAPH_DEBUG("cell_mat.height: %d", cell_mat.height);
    MAPH_DEBUG("cell_mat.data: %d", static_cast<int>(cell_mat.data.size()));
}

void MapHandler::Print(const Task &task)
{
    MAPH_DEBUG("task.info.map_id: %s", task.info.map_id.c_str());
    MAPH_DEBUG("task.info.task_id: %s", task.info.task_id.c_str());
    MAPH_DEBUG("task.task_loop_times: %d",task.task_loop_times);
    for(int j=0;j<task.info.action_list.size();j++)
    {
        MAPH_DEBUG("task.info.action_list[%d].action_name: %d", j, task.info.action_list[j].action_name);
        MAPH_DEBUG("task.info.action_list[%d].action_args: %s", j, task.info.action_list[j].action_args.c_str());
        MAPH_DEBUG("task.info.action_list[%d].duration: %d", j, task.info.action_list[j].duration);
    }
}

void MapHandler::Print(const Path &path)
{
    MAPH_DEBUG("path.info.map_id: %s", path.info.map_id.c_str());
    MAPH_DEBUG("path.info.id: %s", path.info.id.c_str());
    MAPH_DEBUG("path.info.length: %.02f", path.info.length);
    MAPH_DEBUG("path.info.pose_num: %d", path.info.pose_num);
    for(int j=0;j<path.poses.size();j++)
    {
        MAPH_DEBUG("path.pose[%d].x: %.02f", j, path.poses[j].x);
        MAPH_DEBUG("path.pose[%d].y: %.02f", j, path.poses[j].y);
        MAPH_DEBUG("path.pose[%d].z: %.02f", j, path.poses[j].z);
        MAPH_DEBUG("path.pose[%d].roll: %.02f", j, path.poses[j].roll);
        MAPH_DEBUG("path.pose[%d].pitch: %.02f", j, path.poses[j].pitch);
        MAPH_DEBUG("path.pose[%d].yaw: %.02f", j, path.poses[j].yaw);
    }
}

void MapHandler::Print(const VirtualWall &virtual_wall)
{
    /*
    MAPH_DEBUG("virtual_wall.info.map_id: %s", virtual_wall.info.map_id.c_str());
    MAPH_DEBUG("virtual_wall.id: %s", virtual_wall.info.id.c_str());
    MAPH_DEBUG("virtual_wall.length: %.02f", virtual_wall.info.length);
    MAPH_DEBUG("virtual_wall.point_num: %d", virtual_wall.info.point_num);
    for(int j=0;j<virtual_wall.points.size();j++)
    {
        MAPH_DEBUG("virtual_wall.points[%d].x: %.02f", j, virtual_wall.points[j].x);
        MAPH_DEBUG("virtual_wall.points[%d].y: %.02f", j, virtual_wall.points[j].y);
        MAPH_DEBUG("virtual_wall.points[%d].z: %.02f", j, virtual_wall.points[j].z);
    }
    */
}

void MapHandler::Print(const StationList &stations)
{
    MAPH_DEBUG("stations.size: %d", static_cast<int>(stations.size()));
    for(int i=0;i<stations.size();i++)
    {
        MAPH_DEBUG("stations[%d].info.map_id: %s", i, stations[i].info.map_id.c_str());
        MAPH_DEBUG("stations[%d].info.id: %s", i, stations[i].info.id.c_str());
        MAPH_DEBUG("stations[%d].info.type: %d", i, stations[i].info.type);
        MAPH_DEBUG("stations[%d].info.artag_id: %d", i, stations[i].info.artag_id);
        MAPH_DEBUG("stations[%d].pose.x: %.02f", i, stations[i].pose.x);
        MAPH_DEBUG("stations[%d].pose.y: %.02f", i, stations[i].pose.y);
        MAPH_DEBUG("stations[%d].pose.z: %.02f", i, stations[i].pose.z);
        MAPH_DEBUG("stations[%d].pose.roll: %.02f", i, stations[i].pose.roll);
        MAPH_DEBUG("stations[%d].pose.pitch: %.02f", i, stations[i].pose.pitch);
        MAPH_DEBUG("stations[%d].pose.yaw: %.02f", i, stations[i].pose.yaw);
    }
}

void MapHandler::Print(const TaskList &tasks)
{
    MAPH_DEBUG("tasks.size: %d", static_cast<int>(tasks.size()));
    for(int i=0;i<tasks.size();i++)
    {
        MAPH_DEBUG("---");
        Print(tasks[i]);
    }
}

void MapHandler::Print(const PathList &paths)
{
    MAPH_DEBUG("paths.size: %d", static_cast<int>(paths.size()));
    for(int i=0;i<paths.size();i++)
    {
        MAPH_DEBUG("---");
        Print(paths[i]);
    }
}

void MapHandler::Print(const VirtualWallList &virtual_walls)
{
    MAPH_DEBUG("virtual_walls.size: %d", static_cast<int>(virtual_walls.size()));
    for(int i=0;i<virtual_walls.size();i++)
    {
        MAPH_DEBUG("---");
        Print(virtual_walls[i]);
    }
}

void MapHandler::Print(const PixelMat &pixel_mat)
{
    MAPH_DEBUG("pixel_mat.width: %d", pixel_mat.width);
    MAPH_DEBUG("pixel_mat.height: %d", pixel_mat.height);
    MAPH_DEBUG("pixel_mat.data: %d", static_cast<int>(pixel_mat.data.size()));
}

void MapHandler::Print(const ImgPath &path)
{
    MAPH_DEBUG("imgpath.info.map_id: %s", path.info.map_id.c_str());
    MAPH_DEBUG("imgpath.info.id: %s", path.info.id.c_str());
    MAPH_DEBUG("imgpath.info.length: %.02f", path.info.length);
    MAPH_DEBUG("imgpath.info.pose_num: %d", path.info.pose_num);
    for(int j=0;j<path.poses.size();j++)
    {
        MAPH_DEBUG("imgpath.pose[%d].u: %d", j, path.poses[j].u);
        MAPH_DEBUG("imgpath.pose[%d].v: %d", j, path.poses[j].v);
        MAPH_DEBUG("imgpath.pose[%d].theta: %.02f", j, path.poses[j].theta);
    }
}

void MapHandler::Print(const ImgVirtualWall &virtual_wall)
{
    /*
    MAPH_DEBUG("imgvirtual_wall.info.map_id: %s", virtual_wall.info.map_id.c_str());
    MAPH_DEBUG("imgvirtual_wall.info.id: %s", virtual_wall.info.id.c_str());
    MAPH_DEBUG("imgvirtual_wall.info.length: %.02f", virtual_wall.info.length);
    for(int j=0;j<virtual_wall.points.size();j++)
    {
        MAPH_DEBUG("imgvirtual_wall.points[%d].u: %d", j, virtual_wall.points[j].u);
        MAPH_DEBUG("imgvirtual_wall.points[%d].v: %d", j, virtual_wall.points[j].v);
    }
    */
}

void MapHandler::Print(const ImgStationList &stations)
{
    MAPH_DEBUG("stations.size: %d", static_cast<int>(stations.size()));
    for(int i=0;i<stations.size();i++)
    {
        MAPH_DEBUG("stations[%d].info.map_id: %s", i, stations[i].info.map_id.c_str());
        MAPH_DEBUG("stations[%d].info.id: %s", i, stations[i].info.id.c_str());
        MAPH_DEBUG("stations[%d].info.type: %d", i, stations[i].info.type);
        MAPH_DEBUG("stations[%d].info.artag_id: %d", i, stations[i].info.artag_id);
        MAPH_DEBUG("stations[%d].pose.x: %d", i, stations[i].pose.u);
        MAPH_DEBUG("stations[%d].pose.y: %d", i, stations[i].pose.v);
        MAPH_DEBUG("stations[%d].pose.theta: %.02f", i, stations[i].pose.theta);
    }
}

void MapHandler::Print(const ImgPathList &paths)
{
    MAPH_DEBUG("paths.size: %d", static_cast<int>(paths.size()));
    for(int i=0;i<paths.size();i++)
    {
        MAPH_DEBUG("---");
        Print(paths[i]);
    }
}

void MapHandler::Print(const ImgVirtualWallList &virtual_walls)
{
    MAPH_DEBUG("virtual_walls.size: %d", static_cast<int>(virtual_walls.size()));
    for(int i=0;i<virtual_walls.size();i++)
    {
        MAPH_DEBUG("---");
        Print(virtual_walls[i]);
    }
}

void MapHandler::Print(const RgbaPixelMat &rgba_pixel_mat)
{
    MAPH_DEBUG("rgba_pixel_mat.width: %d", rgba_pixel_mat.width);
    MAPH_DEBUG("rgba_pixel_mat.height: %d", rgba_pixel_mat.height);
    MAPH_DEBUG("rgba_pixel_mat.data: %d", static_cast<int>(rgba_pixel_mat.data.size()));
}

void MapHandler::Print(const GeoPath &geo_path)
{
    MAPH_DEBUG("geo_path.info.map_id: %s", geo_path.info.map_id.c_str());
    MAPH_DEBUG("geo_path.info.id: %s", geo_path.info.id.c_str());
    MAPH_DEBUG("geo_path.info.length: %.02f", geo_path.info.length);
    MAPH_DEBUG("geo_path.info.pose_num: %d", geo_path.info.pose_num);
    for(int j=0;j<geo_path.poses.size();j++)
    {
        MAPH_DEBUG("geo_path.pose[%d].latitude: %.3f", j, geo_path.poses[j].latitude);
        MAPH_DEBUG("geo_path.pose[%d].longitude: %.3f", j, geo_path.poses[j].longitude);
        MAPH_DEBUG("geo_path.pose[%d].altitude: %.3f", j, geo_path.poses[j].altitude);
        MAPH_DEBUG("geo_path.pose[%d].roll: %.3f", j, geo_path.poses[j].roll);
        MAPH_DEBUG("geo_path.pose[%d].pitch: %.3f", j, geo_path.poses[j].pitch);
        MAPH_DEBUG("geo_path.pose[%d].yaw: %.3f", j, geo_path.poses[j].yaw);
    }
}

void MapHandler::Print(const GeoVirtualWall &geo_virtual_wall)
{
    MAPH_DEBUG("geo_virtual_wall.info.map_id: %s", geo_virtual_wall.info.map_id.c_str());
    MAPH_DEBUG("geo_virtual_wall.info.id: %s", geo_virtual_wall.info.id.c_str());
    MAPH_DEBUG("geo_virtual_wall.info.length: %.02f", geo_virtual_wall.info.length);
    for(int j=0;j<geo_virtual_wall.points.size();j++)
    {
        MAPH_DEBUG("geo_virtual_wall.points[%d].latitude: %.3f", j, geo_virtual_wall.points[j].latitude);
        MAPH_DEBUG("geo_virtual_wall.points[%d].longitude: %.3f", j, geo_virtual_wall.points[j].longitude);
        MAPH_DEBUG("geo_virtual_wall.points[%d].altitude: %.3f", j, geo_virtual_wall.points[j].altitude);
    }
}

void MapHandler::Print(const GeoStationList &geo_stations)
{
    MAPH_DEBUG("geo_stations.size: %d", static_cast<int>(geo_stations.size()));
    for(int i=0;i<geo_stations.size();i++)
    {
        MAPH_DEBUG("geo_stations[%d].info.map_id: %s", i, geo_stations[i].info.map_id.c_str());
        MAPH_DEBUG("geo_stations[%d].info.id: %s", i, geo_stations[i].info.id.c_str());
        MAPH_DEBUG("geo_stations[%d].info.type: %d", i, geo_stations[i].info.type);
        MAPH_DEBUG("geo_stations[%d].info.artag_id: %d", i, geo_stations[i].info.artag_id);
        MAPH_DEBUG("geo_stations[%d].pose.latitude: %.3f", i, geo_stations[i].pose.latitude);
        MAPH_DEBUG("geo_stations[%d].pose.longitude: %.3f", i, geo_stations[i].pose.longitude);
        MAPH_DEBUG("geo_stations[%d].pose.altitude: %.3f", i, geo_stations[i].pose.altitude);
        MAPH_DEBUG("geo_stations[%d].pose.roll: %.3f", i, geo_stations[i].pose.roll);
        MAPH_DEBUG("geo_stations[%d].pose.pitch: %.3f", i, geo_stations[i].pose.pitch);
        MAPH_DEBUG("geo_stations[%d].pose.yaw: %.3f", i, geo_stations[i].pose.yaw);
    }
}

void MapHandler::Print(const GeoPathList &geo_paths)
{
    MAPH_DEBUG("geo_paths.size: %d", static_cast<int>(geo_paths.size()));
    for(int i=0;i<geo_paths.size();i++)
    {
        MAPH_DEBUG("---");
        Print(geo_paths[i]);
    }
}

void MapHandler::Print(const GeoVirtualWallList &geo_virtual_walls)
{
    MAPH_DEBUG("geo_virtual_walls.size: %d", static_cast<int>(geo_virtual_walls.size()));
    for(int i=0;i<geo_virtual_walls.size();i++)
    {
        MAPH_DEBUG("---");
        Print(geo_virtual_walls[i]);
    }
}

void MapHandler::Print(const MapObject& o)
{
    Print(o.info);
    Print(o.add_info);
    Print(o.map_2d.mat);
    Print(o.map_2d.stations);
    Print(o.map_2d.tasks);
    Print(o.map_2d.paths);
    Print(o.map_2d.virtual_walls);
}

void MapHandler::Print(const MapObjectList& map_list)
{
    MapObjectList::const_iterator iter = map_list.begin();
    while(iter != map_list.end())
    {
        Print(*iter);
        iter++;
    }
}

template<typename T>
void MapHandler::PackFixLength(const T &var, vector<unsigned char>::iterator &p_data)
{
    const unsigned char * p_char = reinterpret_cast<const unsigned char *>(&var);
    for (int i=0;i<sizeof(var);i++)
    {
        *p_data = p_char[i];
        p_data++;
    }
}

template<typename T>
void MapHandler::PackVarLength(const T &var, vector<unsigned char>::iterator &p_data)
{
    int unit_length = sizeof(typename T::value_type);
    int total_length = unit_length * var.size();
    vector<unsigned char>::iterator s = p_data;
    PackFixLength<int>(total_length, p_data);
    for (int i=0;i<var.size();i++)
    {
        const unsigned char *p_char = reinterpret_cast<const unsigned char *>(&(var[i]));
        for (int j=0;j<unit_length;j++)
        {
            *p_data = p_char[j];
            p_data++;
        }
    }
}

void MapHandler::Pack(const MapInfo& map_info, CharVectorIter &p_data)
{
    //Print(map_info);
    PackFixLength<int>(map_info.index, p_data);
    PackVarLength<string>(map_info.id, p_data);
    PackVarLength<string>(map_info.creation_time, p_data);
    PackFixLength<float>(map_info.resolution, p_data);
    PackFixLength<Vector3D>(map_info.dimension, p_data);
    PackFixLength<Vector3D>(map_info.offset, p_data);
    PackFixLength<int>(map_info.thumbnail.width, p_data);
    PackFixLength<int>(map_info.thumbnail.height, p_data);
    PackVarLength<ByteArray>(map_info.thumbnail.data, p_data);
    PackFixLength<int>(map_info.station_num, p_data);
    PackFixLength<int>(map_info.task_num, p_data);
    PackFixLength<int>(map_info.path_num, p_data);
//    PackFixLength<int>(map_info.virtual_wall_num, p_data);
}

void MapHandler::Pack(const MapInfoList& map_info_list, CharVectorIter &p_data)
{
    int cnt = map_info_list.size();
    PackFixLength<int>(cnt, p_data);
    for (int i=0;i<cnt;i++)
    {
        Pack(map_info_list[i], p_data);
    }
}

void MapHandler::Pack(const CellMat& cell_mat, CharVectorIter &p_data)
{
    //Print(cell_mat);
    PackFixLength<int>(cell_mat.width, p_data);
    PackFixLength<int>(cell_mat.height, p_data);
    PackVarLength<CellArray>(cell_mat.data, p_data);
}

void MapHandler::Pack(const Pose3D &pose, CharVectorIter &p_data)
{
    PackFixLength<float>(pose.x, p_data);
    PackFixLength<float>(pose.y, p_data);
    PackFixLength<float>(pose.z, p_data);
    PackFixLength<float>(pose.roll, p_data);
    PackFixLength<float>(pose.pitch, p_data);
    PackFixLength<float>(pose.yaw, p_data);
}

void MapHandler::Pack(const Point3D &point, CharVectorIter &p_data)
{
    PackFixLength<float>(point.x, p_data);
    PackFixLength<float>(point.y, p_data);
    PackFixLength<float>(point.z, p_data);
}

void MapHandler::Pack(const TaskAction &action, CharVectorIter &p_data)
{
    PackFixLength<actionname>(action.action_name, p_data);
    PackVarLength<string>(action.action_args, p_data);
    PackFixLength<int>(action.duration, p_data);
//    MAPH_DEBUG("");
}

//void MapHandler::Pack(const TaskInfo& info, CharVectorIter &p_data)
//{
//    PackVarLength<string>(info.map_id, p_data);
//    PackVarLength<string>(info.task_id, p_data);
//    TaskActionList::const_iterator j = info.action_list.begin();
//    int actions_size = static_cast<int>(info.action_list.size());
//    PackFixLength<int>(actions_size, p_data);
//    for(;j!=info.action_list.end();j++)
//    {
//        Pack((*j), p_data);
//    }
//}

//void MapHandler::Pack(const Task& task, CharVectorIter &p_data)
//{
//    //Print(task)
//    Pack(task.info, p_data);
////    string enb_taskloop_;
////    enb_taskloop_ = to_string(task.enb_taskloop);
//    PackFixLength<bool>(task.enb_taskloop, p_data);
//    PackFixLength<int>(task.task_loop_times, p_data);
//}

void MapHandler::Pack(const Path& path, CharVectorIter &p_data)
{
    //Print(path);
    PackVarLength<string>(path.info.map_id, p_data);
    PackVarLength<string>(path.info.id, p_data);
    PackFixLength<float>(path.info.length, p_data);
    PackFixLength<int>(path.info.pose_num, p_data);
    Pose3DList::const_iterator j = path.poses.begin();
    int poses_size = static_cast<int>(path.poses.size());
    PackFixLength<int>(poses_size, p_data);
    for (;j!=path.poses.end();j++)
    {
        Pack((*j), p_data);
    }
}

void MapHandler::Pack(const VirtualWall& virtual_wall, CharVectorIter &p_data)
{
    //Print(virtual_wall);
    PackVarLength<string>(virtual_wall.info.map_id, p_data);
    PackVarLength<string>(virtual_wall.info.id, p_data);
    PackFixLength<float>(virtual_wall.info.length, p_data);
    PackFixLength<bool>(virtual_wall.info.closed, p_data);
    Point3DList::const_iterator j = virtual_wall.points.begin();
    int point_size = static_cast<int>(virtual_wall.points.size());
    PackFixLength<int>(point_size, p_data);
    for (;j!=virtual_wall.points.end();j++)
    {
        Pack((*j), p_data);
    }
}

void MapHandler::Pack(const Station& station, CharVectorIter &p_data)
{
    PackVarLength<string>(station.info.map_id, p_data);
    PackVarLength<string>(station.info.id, p_data);
    PackFixLength<StationType>(station.info.type, p_data);
    PackFixLength<int>(station.info.artag_id, p_data);
    Pack(station.pose, p_data);
}

void MapHandler::Pack(const StationList& stations, CharVectorIter &p_data)
{
    //Print(stations);
    int stations_size = static_cast<int>(stations.size());
    PackFixLength<int>(stations_size, p_data);
    StationList::const_iterator i = stations.begin();
    for(;i!=stations.end();i++)
    {
        Pack(*i, p_data);
    }
}

void MapHandler::Pack(const Pose3DList& poses, CharVectorIter &p_data)
{
    int cnt = poses.size();
    PackFixLength<int>(cnt, p_data);
    Pose3DList::const_iterator i = poses.begin();
    for(;i!=poses.end();i++)
    {
        Pack(*i, p_data);
    }
}

void MapHandler::Pack(const Point3DList& points, CharVectorIter &p_data)
{
    int cnt = points.size();
    PackFixLength<int>(cnt, p_data);
    Point3DList::const_iterator i = points.begin();
    for(;i!=points.end();i++)
    {
        Pack(*i, p_data);
    }
}

//void MapHandler::Pack(const TaskActionList& action_list, CharVectorIter &p_data)
//{
//    int cnt = action_list.size();
//    PackFixLength<int>(cnt, p_data);
//    TaskActionList::const_iterator i = action_list.begin();
//    for(;i!=action_list.end();i++)
//    {
//        Pack(*i, p_data);
//    }
//}

void MapHandler::Pack(const TaskList& tasks, CharVectorIter &p_data)
{
    //Print(tasks)
    int tasks_size = static_cast<int>(tasks.size());
    PackFixLength<int>(tasks_size, p_data);
    TaskList::const_iterator i = tasks.begin();
    for(;i!=tasks.end();i++)
    {
        PackVarLength<string>(i->info.map_id, p_data);
        PackVarLength<string>(i->info.task_id, p_data);
        TaskActionList::const_iterator j = i->info.action_list.begin();
        int actions_size = static_cast<int>(i->info.action_list.size());
        PackFixLength<int>(actions_size, p_data);
        for (;j!=i->info.action_list.end();j++)
        {
            Pack((*j), p_data);
        }
        PackFixLength<bool>(i->enb_taskloop, p_data);
        PackFixLength<int>(i->task_loop_times, p_data);
    }
}

void MapHandler::Pack(const PathList& paths, CharVectorIter &p_data)
{
    //Print(paths);
    int paths_size = static_cast<int>(paths.size());
    PackFixLength<int>(paths_size, p_data);
    PathList::const_iterator i = paths.begin();
    for(;i!=paths.end();i++)
    {
        PackVarLength<string>(i->info.map_id, p_data);
        PackVarLength<string>(i->info.id, p_data);
        PackFixLength<float>(i->info.length, p_data);
        PackFixLength<int>(i->info.pose_num, p_data);
        Pose3DList::const_iterator j = i->poses.begin();
        int poses_size = static_cast<int>(i->poses.size());
        PackFixLength<int>(poses_size, p_data);
        for (;j!=i->poses.end();j++)
        {
            Pack((*j), p_data);
        }
    }
}

void MapHandler::Pack(const VirtualWallList& virtual_walls, CharVectorIter &p_data)
{
    //Print(virtual_walls);
    int virtual_walls_size = static_cast<int>(virtual_walls.size());
    PackFixLength<int>(virtual_walls_size, p_data);
    VirtualWallList::const_iterator i = virtual_walls.begin();
    for(;i!=virtual_walls.end();i++)
    {
        PackVarLength<string>(i->info.map_id, p_data);
        PackVarLength<string>(i->info.id, p_data);
        PackFixLength<float>(i->info.length, p_data);
        PackFixLength<bool>(i->info.closed, p_data);
        Point3DList::const_iterator j = i->points.begin();
        int points_size = static_cast<int>(i->points.size());
        PackFixLength<int>(points_size, p_data);
        for (;j!=i->points.end();j++)
        {
            Pack((*j), p_data);
        }
    }
}

void MapHandler::Pack(const PixelMat& pixel_mat, CharVectorIter &p_data)
{
    //Print(pixel_mat);
    PackFixLength<int>(pixel_mat.width, p_data);
    PackFixLength<int>(pixel_mat.height, p_data);
    PackFixLength<float>(pixel_mat.ratio, p_data);
    PackVarLength<ByteArray>(pixel_mat.data, p_data);
}

void MapHandler::Pack(const ImgPose &pose, CharVectorIter &p_data)
{
    PackFixLength<int>(pose.u, p_data);
    PackFixLength<int>(pose.v, p_data);
    PackFixLength<float>(pose.theta, p_data);
}

void MapHandler::Pack(const ImgPoint &point, CharVectorIter &p_data)
{
    PackFixLength<int>(point.u, p_data);
    PackFixLength<int>(point.v, p_data);
}

void MapHandler::Pack(const ImgPath& path, CharVectorIter &p_data)
{
    //Print(path);
    PackVarLength<string>(path.info.map_id, p_data);
    PackVarLength<string>(path.info.id, p_data);
    PackFixLength<float>(path.info.length, p_data);
    PackFixLength<int>(path.info.pose_num, p_data);
    ImgPoseList::const_iterator j = path.poses.begin();
    int poses_size = static_cast<int>(path.poses.size());
    PackFixLength<int>(poses_size, p_data);
    for (;j!=path.poses.end();j++)
    {
        Pack((*j), p_data);
    }
}

void MapHandler::Pack(const ImgVirtualWall& virtual_wall, CharVectorIter &p_data)
{
    //Print(virtual_wall);
    PackVarLength<string>(virtual_wall.info.map_id, p_data);
    PackVarLength<string>(virtual_wall.info.id, p_data);
    PackFixLength<float>(virtual_wall.info.length, p_data);
    PackFixLength<bool>(virtual_wall.info.closed, p_data);
    ImgPointList::const_iterator j = virtual_wall.points.begin();
    int points_size = static_cast<int>(virtual_wall.points.size());
    PackFixLength<int>(points_size, p_data);
    for (;j!=virtual_wall.points.end();j++)
    {
        Pack((*j), p_data);
    }
}

void MapHandler::Pack(const ImgStationList& stations, CharVectorIter &p_data)
{
    //Print(stations);
    int stations_size = static_cast<int>(stations.size());
    PackFixLength<int>(stations_size, p_data);
    ImgStationList::const_iterator i = stations.begin();
    for(;i!=stations.end();i++)
    {
        PackVarLength<string>(i->info.map_id, p_data);
        PackVarLength<string>(i->info.id, p_data);
        PackFixLength<StationType>(i->info.type, p_data);
        PackFixLength<int>(i->info.artag_id, p_data);
        Pack(i->pose, p_data);
    }
}

void MapHandler::Pack(const ImgPoseList& poses, CharVectorIter &p_data)
{
    int cnt = poses.size();
    PackFixLength<int>(cnt, p_data);
    ImgPoseList::const_iterator i = poses.begin();
    for(;i!=poses.end();i++)
    {
        Pack(*i, p_data);
    }
}

void MapHandler::Pack(const ImgPointList& points, CharVectorIter &p_data)
{
    int cnt = points.size();
    PackFixLength<int>(cnt, p_data);
    ImgPointList::const_iterator i = points.begin();
    for(;i!=points.end();i++)
    {
        Pack(*i, p_data);
    }
}

void MapHandler::Pack(const ImgPathList& paths, CharVectorIter &p_data)
{
    //Print(paths);
    int paths_size = static_cast<int>(paths.size());
    PackFixLength<int>(paths_size, p_data);
    ImgPathList::const_iterator i = paths.begin();
    for(;i!=paths.end();i++)
    {
        PackVarLength<string>(i->info.map_id, p_data);
        PackVarLength<string>(i->info.id, p_data);
        PackFixLength<float>(i->info.length, p_data);
        PackFixLength<int>(i->info.pose_num, p_data);
        ImgPoseList::const_iterator j = i->poses.begin();
        int poses_size = static_cast<int>(i->poses.size());
        PackFixLength<int>(poses_size, p_data);
        for (;j!=i->poses.end();j++)
        {
            Pack((*j), p_data);
        }
    }
}

void MapHandler::Pack(const ImgVirtualWallList& virtual_walls, CharVectorIter &p_data)
{
    //Print(virtual_walls);
    int virtual_walls_size = static_cast<int>(virtual_walls.size());
    PackFixLength<int>(virtual_walls_size, p_data);
    ImgVirtualWallList::const_iterator i = virtual_walls.begin();
    for(;i!=virtual_walls.end();i++)
    {
        PackVarLength<string>(i->info.map_id, p_data);
        PackVarLength<string>(i->info.id, p_data);
        PackFixLength<float>(i->info.length, p_data);
        PackFixLength<bool>(i->info.closed, p_data);
        ImgPointList::const_iterator j = i->points.begin();
        int points_size = static_cast<int>(i->points.size());
        PackFixLength<int>(points_size, p_data);
        for (;j!=i->points.end();j++)
        {
            Pack((*j), p_data);
        }
    }
}

void MapHandler::Pack(const RgbaPixelMat& mat, CharVectorIter &p_data)
{
    //Print(mat);
    PackFixLength<int>(mat.width, p_data);
    PackFixLength<int>(mat.height, p_data);
    PackFixLength<float>(mat.ratio, p_data);
    PackVarLength<IntArray>(mat.data, p_data);
}

void MapHandler::Pack(const GeoPose &pose, CharVectorIter &p_data)
{
    PackFixLength<double>(pose.latitude, p_data);
    PackFixLength<double>(pose.longitude, p_data);
    PackFixLength<double>(pose.altitude, p_data);
    PackFixLength<double>(pose.roll, p_data);
    PackFixLength<double>(pose.pitch, p_data);
    PackFixLength<float>(pose.yaw, p_data);
}

void MapHandler::Pack(const GeoPoint &point, CharVectorIter &p_data)
{
    PackFixLength<double>(point.latitude, p_data);
    PackFixLength<double>(point.longitude, p_data);
    PackFixLength<double>(point.altitude, p_data);
}

void MapHandler::Pack(const GeoPath& path, CharVectorIter &p_data)
{
    //Print(path);
    PackVarLength<string>(path.info.map_id, p_data);
    PackVarLength<string>(path.info.id, p_data);
    PackFixLength<float>(path.info.length, p_data);
    PackFixLength<int>(path.info.pose_num, p_data);
    GeoPoseList::const_iterator j = path.poses.begin();
    int poses_size = static_cast<int>(path.poses.size());
    PackFixLength<int>(poses_size, p_data);
    for (;j!=path.poses.end();j++)
    {
        Pack((*j), p_data);
    }
}

void MapHandler::Pack(const GeoVirtualWall& virtual_wall, CharVectorIter &p_data)
{
    //Print(virtual_wall);
    PackVarLength<string>(virtual_wall.info.map_id, p_data);
    PackVarLength<string>(virtual_wall.info.id, p_data);
    PackFixLength<float>(virtual_wall.info.length, p_data);
    PackFixLength<bool>(virtual_wall.info.closed, p_data);
    GeoPointList::const_iterator j = virtual_wall.points.begin();
    int points_size = static_cast<int>(virtual_wall.points.size());
    PackFixLength<int>(points_size, p_data);
    for (;j!=virtual_wall.points.end();j++)
    {
        Pack((*j), p_data);
    }
}

void MapHandler::Pack(const GeoStationList& stations, CharVectorIter &p_data)
{
    //Print(stations);
    int stations_size = static_cast<int>(stations.size());
    PackFixLength<int>(stations_size, p_data);
    GeoStationList::const_iterator i = stations.begin();
    for(;i!=stations.end();i++)
    {
        PackVarLength<string>(i->info.map_id, p_data);
        PackVarLength<string>(i->info.id, p_data);
        PackFixLength<StationType>(i->info.type, p_data);
        PackFixLength<int>(i->info.artag_id, p_data);
        Pack(i->pose, p_data);
    }
}

void MapHandler::Pack(const GeoPoseList& poses, CharVectorIter &p_data)
{
    int cnt = poses.size();
    PackFixLength<int>(cnt, p_data);
    GeoPoseList::const_iterator i = poses.begin();
    for(;i!=poses.end();i++)
    {
        Pack(*i, p_data);
    }
}

void MapHandler::Pack(const GeoPointList& points, CharVectorIter &p_data)
{
    int cnt = points.size();
    PackFixLength<int>(cnt, p_data);
    GeoPointList::const_iterator i = points.begin();
    for(;i!=points.end();i++)
    {
        Pack(*i, p_data);
    }
}

void MapHandler::Pack(const GeoPathList& paths, CharVectorIter &p_data)
{
    //Print(paths);
    int paths_size = static_cast<int>(paths.size());
    PackFixLength<int>(paths_size, p_data);
    GeoPathList::const_iterator i = paths.begin();
    for(;i!=paths.end();i++)
    {
        PackVarLength<string>(i->info.map_id, p_data);
        PackVarLength<string>(i->info.id, p_data);
        PackFixLength<float>(i->info.length, p_data);
        PackFixLength<int>(i->info.pose_num, p_data);
        GeoPoseList::const_iterator j = i->poses.begin();
        int poses_size = static_cast<int>(i->poses.size());
        PackFixLength<int>(poses_size, p_data);
        for (;j!=i->poses.end();j++)
        {
            Pack((*j), p_data);
        }
    }
}

void MapHandler::Pack(const GeoVirtualWallList& virtual_walls, CharVectorIter &p_data)
{
    //Print(virtual_walls);
    int virtual_walls_size = static_cast<int>(virtual_walls.size());
    PackFixLength<int>(virtual_walls_size, p_data);
    GeoVirtualWallList::const_iterator i = virtual_walls.begin();
    for(;i!=virtual_walls.end();i++)
    {
        PackVarLength<string>(i->info.map_id, p_data);
        PackVarLength<string>(i->info.id, p_data);
        PackFixLength<float>(i->info.length, p_data);
        PackFixLength<bool>(i->info.closed, p_data);
        GeoPointList::const_iterator j = i->points.begin();
        int points_size = static_cast<int>(i->points.size());
        PackFixLength<int>(points_size, p_data);
        for (;j!=i->points.end();j++)
        {
            Pack((*j), p_data);
        }
    }
}

template<typename T>
void MapHandler::UnpackFixLength(T &var, CharVectorIter &p_data)
{
    unsigned char *p_char = reinterpret_cast<unsigned char *>(&var);
    for (int i=0;i<sizeof(var);i++)
    {
        p_char[i] = *p_data;
        p_data++;
    }
}

template<typename T>
void MapHandler::UnpackVarLength(T &var, CharVectorIter &p_data)
{
    int unit_length = sizeof(typename T::value_type);
    int total_length = 0;
    UnpackFixLength<int>(total_length, p_data);
    int cnt = total_length / unit_length;
    //MAPH_DEBUG("unpack var length: %d", cnt);
    var.clear();
    var.resize(cnt);
    for (int i=0;i<cnt;i++)
    {
        unsigned char *p_char = reinterpret_cast<unsigned char *>(&var[i]);
        for (int j=0;j<unit_length;j++)
        {
            p_char[j] = *p_data;
            p_data++;
        }
    }
}

void MapHandler::Unpack(MapInfo& map_info, CharVectorIter &p_data)
{
    UnpackFixLength<int>(map_info.index, p_data);
    UnpackVarLength<string>(map_info.id, p_data);
    UnpackVarLength<string>(map_info.creation_time, p_data);
    UnpackFixLength<float>(map_info.resolution, p_data);
    UnpackFixLength<Vector3D>(map_info.dimension, p_data);
    UnpackFixLength<Vector3D>(map_info.offset, p_data);
    UnpackFixLength<int>(map_info.thumbnail.width, p_data);
    UnpackFixLength<int>(map_info.thumbnail.height, p_data);
    UnpackVarLength<ByteArray>(map_info.thumbnail.data, p_data);
    UnpackFixLength<int>(map_info.station_num, p_data);
    UnpackFixLength<int>(map_info.task_num, p_data);
    UnpackFixLength<int>(map_info.path_num, p_data);
//    UnpackFixLength<int>(map_info.virtual_wall_num, p_data);
    //Print(map_info);
}

void MapHandler::Unpack(MapInfoList& map_info_list, CharVectorIter &p_data)
{
    int cnt = 0;
    UnpackFixLength<int>(cnt, p_data);
    map_info_list.resize(cnt);
    MAPH_DEBUG("Map info list cnt: %d", cnt);
    for(int i=0;i<cnt;i++)
    {
        Unpack(map_info_list[i], p_data);
    }
}

void MapHandler::Unpack(CellMat& mat, CharVectorIter &p_data)
{
    UnpackFixLength<int>(mat.width, p_data);
    UnpackFixLength<int>(mat.height, p_data);
    UnpackVarLength<CellArray>(mat.data, p_data);
    //Print(mat);
}

void MapHandler::Unpack(Pose3D &pose, CharVectorIter &p_data)
{
    UnpackFixLength<float>(pose.x, p_data);
    UnpackFixLength<float>(pose.y, p_data);
    UnpackFixLength<float>(pose.z, p_data);
    UnpackFixLength<float>(pose.roll, p_data);
    UnpackFixLength<float>(pose.pitch, p_data);
    UnpackFixLength<float>(pose.yaw, p_data);
}

void MapHandler::Unpack(Point3D &point, CharVectorIter &p_data)
{
    UnpackFixLength<float>(point.x, p_data);
    UnpackFixLength<float>(point.y, p_data);
    UnpackFixLength<float>(point.z, p_data);
}

void MapHandler::Unpack(TaskAction &action, CharVectorIter &p_data)
{
    UnpackFixLength<actionname>(action.action_name, p_data);
    UnpackVarLength<string>(action.action_args, p_data);
    UnpackFixLength<int>(action.duration, p_data);
    MAPH_DEBUG("action_name is: %d", action.action_name);
    MAPH_DEBUG("action_args is: %s", action.action_args.c_str());
    MAPH_DEBUG("duration is: %d", action.duration);
}

//void MapHandler::Unpack(TaskInfo& info, CharVectorIter &p_data)
//{
//    UnpackVarLength<string>(info.map_id, p_data);
//    UnpackVarLength<string>(info.task_id, p_data);
//    int j_cnt = 0;
//    UnpackFixLength<int>(j_cnt, p_data);
//    info.action_list.resize(j_cnt);
//    for (int j=0;j<j_cnt;j++)
//    {
//        Unpack(info.action_list[j], p_data);
//    }
//}

//void MapHandler::Unpack(Task& task, CharVectorIter &p_data)
//{
//    Unpack(task.info, p_data);
//    UnpackFixLength<bool>(task.enb_taskloop, p_data);
//    UnpackFixLength<int>(task.task_loop_times, p_data);

//    MAPH_INFO("MapHandler Unpack(Task) loop is: %d", task.enb_taskloop);
//    MAPH_INFO("MapHandler Unpack(Task) times is: %d", task.task_loop_times);
//    //Print(task);
//}

void MapHandler::Unpack(Path& path, CharVectorIter &p_data)
{
    UnpackVarLength<string>(path.info.map_id, p_data);
    UnpackVarLength<string>(path.info.id, p_data);
    UnpackFixLength<float>(path.info.length, p_data);
    UnpackFixLength<int>(path.info.pose_num, p_data);
    int j_cnt = 0;
    UnpackFixLength<int>(j_cnt, p_data);
    path.poses.resize(j_cnt);
    for (int j=0;j<j_cnt;j++)
    {
        Unpack(path.poses[j], p_data);
    }
    //Print(path);
}

void MapHandler::Unpack(VirtualWall& virtual_wall, CharVectorIter &p_data)
{

    UnpackVarLength<string>(virtual_wall.info.map_id, p_data);
    UnpackVarLength<string>(virtual_wall.info.id, p_data);
    UnpackFixLength<float>(virtual_wall.info.length, p_data);
    UnpackFixLength<bool>(virtual_wall.info.closed, p_data);
    int j_cnt = 0;
    UnpackFixLength<int>(j_cnt, p_data);
    virtual_wall.points.resize(j_cnt);
    for (int j=0;j<j_cnt;j++)
    {
        Unpack(virtual_wall.points[j], p_data);
    }
    //Print(virtual_wall);
}

void MapHandler::Unpack(Station& station, CharVectorIter &p_data)
{
    UnpackVarLength<string>(station.info.map_id, p_data);
    UnpackVarLength<string>(station.info.id, p_data);
    UnpackFixLength<StationType>(station.info.type, p_data);
    UnpackFixLength<int>(station.info.artag_id, p_data);
    Unpack(station.pose, p_data);
}

void MapHandler::Unpack(StationList& stations, CharVectorIter &p_data)
{
    int cnt = 0;
    UnpackFixLength<int>(cnt, p_data);
    stations.resize(cnt);
    for(int i=0;i<cnt;i++)
    {
        Unpack(stations[i], p_data);
    }
    //Print(stations);
}

void MapHandler::Unpack(Pose3DList& poses, CharVectorIter &p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    poses.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        Unpack(poses[i], p_data);
    }
}

void MapHandler::Unpack(Point3DList& points, CharVectorIter &p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    points.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        Unpack(points[i], p_data);
    }
}

//void MapHandler::Unpack(TaskActionList& action_list, CharVectorIter &p_data)
//{
//    int i_cnt = 0;
//    UnpackFixLength<int>(i_cnt, p_data);
//    action_list.resize(i_cnt);
//    for(int i=0;i<i_cnt;i++)
//    {
//        Unpack(action_list[i], p_data);
//    }
//}

void MapHandler::Unpack(TaskList& tasks, CharVectorIter &p_data)
{
//    int i_cnt = 0;
//    UnpackFixLength<int>(i_cnt, p_data);
//    tasks.resize(i_cnt);
//    for(int i=0;i<i_cnt;i++)
//    {
//        MAPH_INFO("tasklist taskloop00000 is: %d", tasks[0].enb_taskloop);
//        MAPH_INFO("tasklist tasktimes00000 is: %d", tasks[0].task_loop_times);
//        Unpack(tasks[i], p_data);
//        MAPH_INFO("tasklist taskloop is: %d", tasks[0].enb_taskloop);
//        MAPH_INFO("tasklist tasktimes is: %d", tasks[0].task_loop_times);
//    }
    //Print(tasks);
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    tasks.resize(i_cnt);
    for(int i=0; i<i_cnt; i++)
    {
        UnpackVarLength<string>(tasks[i].info.map_id, p_data);
        UnpackVarLength<string>(tasks[i].info.task_id, p_data);
        int j_cnt = 0;
        UnpackFixLength<int>(j_cnt, p_data);
        tasks[i].info.action_list.resize(j_cnt);
        for(int j=0; j<j_cnt; j++)
        {
            Unpack(tasks[i].info.action_list[j], p_data);
        }
        UnpackFixLength<bool>(tasks[i].enb_taskloop, p_data);
        UnpackFixLength<int>(tasks[i].task_loop_times, p_data);
    }
}

void MapHandler::Unpack(PathList& paths, CharVectorIter &p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    paths.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        Unpack(paths[i], p_data);
    }
    //Print(paths);
}

void MapHandler::Unpack(VirtualWallList& virtual_walls, CharVectorIter &p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    virtual_walls.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        Unpack(virtual_walls[i], p_data);
    }
    //Print(virtual_walls);
}

void MapHandler::Unpack(PixelMat& mat, CharVectorIter &p_data)
{
    UnpackFixLength<int>(mat.width, p_data);
    UnpackFixLength<int>(mat.height, p_data);
    UnpackFixLength<float>(mat.ratio, p_data);
    UnpackVarLength<ByteArray>(mat.data, p_data);
    //Print(mat);
}

void MapHandler::Unpack(ImgPose &pose, CharVectorIter &p_data)
{
    UnpackFixLength<int>(pose.u, p_data);
    UnpackFixLength<int>(pose.v, p_data);
    UnpackFixLength<float>(pose.theta, p_data);
}

void MapHandler::Unpack(ImgPoint &point, CharVectorIter &p_data)
{
    UnpackFixLength<int>(point.u, p_data);
    UnpackFixLength<int>(point.v, p_data);
}

void MapHandler::Unpack(ImgPath& path, CharVectorIter &p_data)
{
    UnpackVarLength<string>(path.info.map_id, p_data);
    UnpackVarLength<string>(path.info.id, p_data);
    UnpackFixLength<float>(path.info.length, p_data);
    UnpackFixLength<int>(path.info.pose_num, p_data);
    int j_cnt = 0;
    UnpackFixLength<int>(j_cnt, p_data);
    path.poses.resize(j_cnt);
    for (int j=0;j<j_cnt;j++)
    {
        Unpack(path.poses[j], p_data);
    }
    //Print(path);
}

void MapHandler::Unpack(ImgVirtualWall& virtual_wall, CharVectorIter &p_data)
{
    UnpackVarLength<string>(virtual_wall.info.map_id, p_data);
    UnpackVarLength<string>(virtual_wall.info.id, p_data);
    UnpackFixLength<float>(virtual_wall.info.length, p_data);
    UnpackFixLength<bool>(virtual_wall.info.closed, p_data);
    int j_cnt = 0;
    UnpackFixLength<int>(j_cnt, p_data);
    virtual_wall.points.resize(j_cnt);
    for (int j=0;j<j_cnt;j++)
    {
        Unpack(virtual_wall.points[j], p_data);
    }
    //Print(virtual_wall);
}

void MapHandler::Unpack(ImgStationList& stations, CharVectorIter &p_data)
{
    int cnt = 0;
    UnpackFixLength<int>(cnt, p_data);
    stations.resize(cnt);
    for(int i=0;i<cnt;i++)
    {
        UnpackVarLength<string>(stations[i].info.map_id, p_data);
        UnpackVarLength<string>(stations[i].info.id, p_data);
        UnpackFixLength<StationType>(stations[i].info.type, p_data);
        UnpackFixLength<int>(stations[i].info.artag_id, p_data);
        Unpack(stations[i].pose, p_data);
    }
    //Print(stations);
}

void MapHandler::Unpack(ImgPoseList& poses, CharVectorIter &p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    poses.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        Unpack(poses[i], p_data);
    }
}


void MapHandler::Unpack(ImgPointList& point, CharVectorIter &p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    point.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        Unpack(point[i], p_data);
    }
}

void MapHandler::Unpack(ImgPathList& paths, CharVectorIter &p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    paths.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        UnpackVarLength<string>(paths[i].info.map_id, p_data);
        UnpackVarLength<string>(paths[i].info.id, p_data);
        UnpackFixLength<float>(paths[i].info.length, p_data);
        UnpackFixLength<int>(paths[i].info.pose_num, p_data);
        int j_cnt = 0;
        UnpackFixLength<int>(j_cnt, p_data);
        paths[i].poses.resize(j_cnt);
        for (int j=0;j<j_cnt;j++)
        {
            Unpack(paths[i].poses[j], p_data);
        }
    }
    //Print(paths);
}

void MapHandler::Unpack(ImgVirtualWallList& virtual_walls, CharVectorIter &p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    virtual_walls.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        UnpackVarLength<string>(virtual_walls[i].info.map_id, p_data);
        UnpackVarLength<string>(virtual_walls[i].info.id, p_data);
        UnpackFixLength<float>(virtual_walls[i].info.length, p_data);
        UnpackFixLength<bool>(virtual_walls[i].info.closed, p_data);
        int j_cnt = 0;
        UnpackFixLength<int>(j_cnt, p_data);
        virtual_walls[i].points.resize(j_cnt);
        for (int j=0;j<j_cnt;j++)
        {
            Unpack(virtual_walls[i].points[j], p_data);
        }
    }
    //Print(virtual_walls);
}

void MapHandler::Unpack(RgbaPixelMat& rgba_pixel_mat, CharVectorIter& p_data)
{
    UnpackFixLength<int>(rgba_pixel_mat.width, p_data);
    UnpackFixLength<int>(rgba_pixel_mat.height, p_data);
    UnpackFixLength<float>(rgba_pixel_mat.ratio, p_data);
    UnpackVarLength<IntArray>(rgba_pixel_mat.data, p_data);
    //Print(rgba_pixel_mat);
}

void MapHandler::Unpack(GeoPose& geo_pose, CharVectorIter& p_data)
{
    UnpackFixLength<double>(geo_pose.latitude, p_data);
    UnpackFixLength<double>(geo_pose.longitude, p_data);
    UnpackFixLength<double>(geo_pose.altitude, p_data);
    UnpackFixLength<float>(geo_pose.roll, p_data);
    UnpackFixLength<float>(geo_pose.pitch, p_data);
    UnpackFixLength<float>(geo_pose.yaw, p_data);
}

void MapHandler::Unpack(GeoPoint& geo_point, CharVectorIter& p_data)
{
    UnpackFixLength<double>(geo_point.latitude, p_data);
    UnpackFixLength<double>(geo_point.longitude, p_data);
    UnpackFixLength<double>(geo_point.altitude, p_data);
}

void MapHandler::Unpack(GeoPath& geo_path, CharVectorIter& p_data)
{
    UnpackVarLength<string>(geo_path.info.map_id, p_data);
    UnpackVarLength<string>(geo_path.info.id, p_data);
    UnpackFixLength<float>(geo_path.info.length, p_data);
    UnpackFixLength<int>(geo_path.info.pose_num, p_data);
    int j_cnt = 0;
    UnpackFixLength<int>(j_cnt, p_data);
    geo_path.poses.resize(j_cnt);
    for (int j=0;j<j_cnt;j++)
    {
        Unpack(geo_path.poses[j], p_data);
    }
    //Print(geo_path);
}

void MapHandler::Unpack(GeoVirtualWall& geo_virtual_wall, CharVectorIter& p_data)
{
    UnpackVarLength<string>(geo_virtual_wall.info.map_id, p_data);
    UnpackVarLength<string>(geo_virtual_wall.info.id, p_data);
    UnpackFixLength<float>(geo_virtual_wall.info.length, p_data);
    UnpackFixLength<bool>(geo_virtual_wall.info.closed, p_data);
    int j_cnt = 0;
    UnpackFixLength<int>(j_cnt, p_data);
    geo_virtual_wall.points.resize(j_cnt);
    for (int j=0;j<j_cnt;j++)
    {
        Unpack(geo_virtual_wall.points[j], p_data);
    }
    //Print(geo_virtual_wall);
}

void MapHandler::Unpack(GeoStationList& geo_stations, CharVectorIter& p_data)
{
    int cnt = 0;
    UnpackFixLength<int>(cnt, p_data);
    geo_stations.resize(cnt);
    for(int i=0;i<cnt;i++)
    {
        UnpackVarLength<string>(geo_stations[i].info.map_id, p_data);
        UnpackVarLength<string>(geo_stations[i].info.id, p_data);
        UnpackFixLength<StationType>(geo_stations[i].info.type, p_data);
        UnpackFixLength<int>(geo_stations[i].info.artag_id, p_data);
        Unpack(geo_stations[i].pose, p_data);
    }
    //Print(geo_stations);
}

void MapHandler::Unpack(GeoPoseList& geo_poses, CharVectorIter& p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    geo_poses.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        Unpack(geo_poses[i], p_data);
    }
}


void MapHandler::Unpack(GeoPointList& geo_point, CharVectorIter& p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    geo_point.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        Unpack(geo_point[i], p_data);
    }
}

void MapHandler::Unpack(GeoPathList& geo_paths, CharVectorIter& p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    geo_paths.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        UnpackVarLength<string>(geo_paths[i].info.map_id, p_data);
        UnpackVarLength<string>(geo_paths[i].info.id, p_data);
        UnpackFixLength<float>(geo_paths[i].info.length, p_data);
        UnpackFixLength<int>(geo_paths[i].info.pose_num, p_data);
        int j_cnt = 0;
        UnpackFixLength<int>(j_cnt, p_data);
        geo_paths[i].poses.resize(j_cnt);
        for (int j=0;j<j_cnt;j++)
        {
            Unpack(geo_paths[i].poses[j], p_data);
        }
    }
    //Print(paths);
}

void MapHandler::Unpack(GeoVirtualWallList& geo_virtual_walls, CharVectorIter& p_data)
{
    int i_cnt = 0;
    UnpackFixLength<int>(i_cnt, p_data);
    geo_virtual_walls.resize(i_cnt);
    for(int i=0;i<i_cnt;i++)
    {
        UnpackVarLength<string>(geo_virtual_walls[i].info.map_id, p_data);
        UnpackVarLength<string>(geo_virtual_walls[i].info.id, p_data);
        UnpackFixLength<float>(geo_virtual_walls[i].info.length, p_data);
        UnpackFixLength<bool>(geo_virtual_walls[i].info.closed, p_data);
        int j_cnt = 0;
        UnpackFixLength<int>(j_cnt, p_data);
        geo_virtual_walls[i].points.resize(j_cnt);
        for (int j=0;j<j_cnt;j++)
        {
            Unpack(geo_virtual_walls[i].points[j], p_data);
        }
    }
    //Print(geo_virtual_walls);
}

int MapHandler::Sizeof(const MapInfo& map_info)
{
    int length = 0;
    length += sizeof(map_info.index);
    length += map_info.id.size() + sizeof(int);
    length += map_info.creation_time.size() + sizeof(int);
    length += sizeof(map_info.resolution);
    length += sizeof(map_info.dimension.x);
    length += sizeof(map_info.dimension.y);
    length += sizeof(map_info.dimension.z);
    length += sizeof(map_info.offset.x);
    length += sizeof(map_info.offset.y);
    length += sizeof(map_info.offset.z);
    length += sizeof(map_info.thumbnail.width);
    length += sizeof(map_info.thumbnail.height);
    length += sizeof(ByteArray::value_type) * map_info.thumbnail.data.size() + sizeof(int);
    length += sizeof(map_info.station_num);
    length += sizeof(map_info.task_num);
    length += sizeof(map_info.path_num);
//    length += sizeof(map_info.virtual_wall_num);
    return length;
}

int MapHandler::Sizeof(const MapInfoList& map_info_list)
{
    int length = 0;
    int cnt = map_info_list.size();

    length +=sizeof(int);
    for (int i=0;i<cnt;i++)
    {
        length += Sizeof(map_info_list[i]);
    }
    return length;
}

int MapHandler::Sizeof(const MapAddInfo& map_add_info)
{
    int length = sizeof(map_add_info);
    return length;
}

int MapHandler::Sizeof(const MapObject& map_obj)
{
    int length = 0;
    length += Sizeof(map_obj.info);
    length += Sizeof(map_obj.add_info);
    length += Sizeof(map_obj.map_2d.mat);
    length += Sizeof(map_obj.map_2d.tasks);
    length += Sizeof(map_obj.map_2d.paths);
    length += Sizeof(map_obj.map_2d.virtual_walls);
    length += Sizeof(map_obj.map_2d.stations);
    length += Sizeof(map_obj.img_map.mat);
    length += Sizeof(map_obj.img_map.paths);
    length += Sizeof(map_obj.img_map.virtual_walls);
    length += Sizeof(map_obj.img_map.stations);
    return length;
}

int MapHandler::Sizeof(const CellMat& cell_mat)
{
    int length = 0;
    length += sizeof(cell_mat.width);
    length += sizeof(cell_mat.height);
    length += sizeof(CellArray::value_type) * cell_mat.data.size() + sizeof(int);
    return length;
}

int MapHandler::Sizeof(const Station& station)
{
    int length = 0;
    length += station.info.map_id.size() + sizeof(int);
    length += station.info.id.size() + sizeof(int);
    length += sizeof(station.info.type);
    length += sizeof(station.info.artag_id);
    length += sizeof(station.pose);
    return length;
}

int MapHandler::Sizeof(const Task& task)
{
    int length = 0;
    length += task.info.map_id.size() + sizeof(int);
    length += task.info.task_id.size() + sizeof(int);
//    MAPH_DEBUG("In Sizeof action_list.size is: %d", task.info.action_list.size());
    TaskActionList::const_iterator p = task.info.action_list.begin();
    for(;p!=task.info.action_list.end();p++)
    {
        length += Sizeof(*p);
    }
    length += sizeof(task.enb_taskloop);
    length += sizeof(task.task_loop_times);
    return length;
}

int MapHandler::Sizeof(const TaskAction& actions)
{
    int length = 0;
    length += sizeof(actions.action_name);
    length += actions.action_args.size() + sizeof(int);
    length += sizeof(actions.duration);
    return length + sizeof(int);
}

int MapHandler::Sizeof(const Path& path)
{
    int length = 0;
    length += path.info.map_id.size() + sizeof(int);
    length += path.info.id.size() + sizeof(int);
    length += sizeof(path.info.length);
    length += sizeof(path.info.pose_num);
    length += sizeof(Pose3DList::value_type) * path.poses.size() + sizeof(int);
    return length;
}

int MapHandler::Sizeof(const VirtualWall& virtual_wall)
{
    int length = 0;
    length += virtual_wall.info.map_id.size() + sizeof(int);
    length += virtual_wall.info.id.size() + sizeof(int);
    length += sizeof(virtual_wall.info.length);
    length += sizeof(virtual_wall.info.closed);
    length += sizeof(Point3DList::value_type) * virtual_wall.points.size() + sizeof(int);
    return length;
}

int MapHandler::Sizeof(const StationList& station_list)
{
    int length = 0;
    StationList::const_iterator iter = station_list.begin();
    for (;iter!=station_list.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

int MapHandler::Sizeof(const TaskList& task_list)
{
    int length = 0;
    TaskList::const_iterator iter = task_list.begin();
    for (;iter!=task_list.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

int MapHandler::Sizeof(const PathList& path_list)
{
    int length = 0;
    PathList::const_iterator iter = path_list.begin();
    for (;iter!=path_list.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

int MapHandler::Sizeof(const VirtualWallList& virtual_wall_list)
{
    int length = 0;
    VirtualWallList::const_iterator iter = virtual_wall_list.begin();
    for (;iter!=virtual_wall_list.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

int MapHandler::Sizeof(const PixelMat& pixel_mat)
{
    int length = 0;
    length += sizeof(pixel_mat.width);
    length += sizeof(pixel_mat.height);
    length += sizeof(pixel_mat.ratio);
    length += sizeof(ByteArray::value_type) * pixel_mat.data.size() + sizeof(int);
    return length;
}

int MapHandler::Sizeof(const ImgStation& station)
{
    int length = 0;
    length += station.info.map_id.size() + sizeof(int);
    length += station.info.id.size() + sizeof(int);
    length += sizeof(station.info.type);
    length += sizeof(station.info.artag_id);
    length += sizeof(station.pose);
    return length;
}

int MapHandler::Sizeof(const ImgPath& path)
{
    int length = 0;
    length += path.info.map_id.size() + sizeof(int);
    length += path.info.id.size() + sizeof(int);
    length += sizeof(path.info.length);
    length += sizeof(path.info.pose_num);
    length += sizeof(ImgPoseList::value_type) * path.poses.size() + sizeof(int);
    return length;
}

int MapHandler::Sizeof(const ImgVirtualWall& virtual_wall)
{
    int length = 0;
    length += virtual_wall.info.map_id.size() + sizeof(int);
    length += virtual_wall.info.id.size() + sizeof(int);
    length += sizeof(virtual_wall.info.length);
    length += sizeof(virtual_wall.info.closed);
    length += sizeof(ImgPointList::value_type) * virtual_wall.points.size() + sizeof(int);
    return length;
}

int MapHandler::Sizeof(const ImgStationList& path_list)
{
    int length = 0;
    ImgStationList::const_iterator iter = path_list.begin();
    for (;iter!=path_list.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

int MapHandler::Sizeof(const ImgPathList& path_list)
{
    int length = 0;
    ImgPathList::const_iterator iter = path_list.begin();
    for (;iter!=path_list.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

int MapHandler::Sizeof(const ImgVirtualWallList& virtual_wall_list)
{
    int length = 0;
    ImgVirtualWallList::const_iterator iter = virtual_wall_list.begin();
    for (;iter!=virtual_wall_list.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

int MapHandler::Sizeof(const RgbaPixelMat& rgba_pixel_mat)
{
    int length = 0;
    length += sizeof(rgba_pixel_mat.width);
    length += sizeof(rgba_pixel_mat.height);
    length += sizeof(rgba_pixel_mat.ratio);
    length += sizeof(IntArray::value_type) * rgba_pixel_mat.data.size() + sizeof(int);
    return length;
}

int MapHandler::Sizeof(const GeoStation& geo_station)
{
    int length = 0;
    length += geo_station.info.map_id.size() + sizeof(int);
    length += geo_station.info.id.size() + sizeof(int);
    length += sizeof(geo_station.info.type);
    length += sizeof(geo_station.info.artag_id);
    length += sizeof(geo_station.pose);
    return length;
}

int MapHandler::Sizeof(const GeoPath& geo_path)
{
    int length = 0;
    length += geo_path.info.map_id.size() + sizeof(int);
    length += geo_path.info.id.size() + sizeof(int);
    length += sizeof(geo_path.info.length);
    length += sizeof(geo_path.info.pose_num);
    length += sizeof(GeoPoseList::value_type) * geo_path.poses.size() + sizeof(int);
    return length;
}

int MapHandler::Sizeof(const GeoVirtualWall& geo_virtual_wall)
{
    int length = 0;
    length += geo_virtual_wall.info.map_id.size() + sizeof(int);
    length += geo_virtual_wall.info.id.size() + sizeof(int);
    length += sizeof(geo_virtual_wall.info.length);
    length += sizeof(geo_virtual_wall.info.closed);
    length += sizeof(GeoPointList::value_type) * geo_virtual_wall.points.size() + sizeof(int);
    return length;
}

int MapHandler::Sizeof(const GeoStationList& geo_stations)
{
    int length = 0;
    GeoStationList::const_iterator iter = geo_stations.begin();
    for (;iter!=geo_stations.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

int MapHandler::Sizeof(const GeoPathList& geo_paths)
{
    int length = 0;
    GeoPathList::const_iterator iter = geo_paths.begin();
    for (;iter!=geo_paths.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

int MapHandler::Sizeof(const GeoVirtualWallList& geo_virtual_walls)
{
    int length = 0;
    GeoVirtualWallList::const_iterator iter = geo_virtual_walls.begin();
    for (;iter!=geo_virtual_walls.end();iter++)
    {
        length += Sizeof(*iter);
    }
    return length + sizeof(int);
}

}// namespace wizrobo_npu
