/*
 * map_saver
 * Copyright (c) 2008, Willow Garage, Inc.
 * All rights reserved.
 * 
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 * 
 *     * Redistributions of source code must retain the above copyright
 *       notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *       notice, this list of conditions and the following disclaimer in the
 *       documentation and/or other materials provided with the distribution.
 *     * Neither the name of the <ORGANIZATION> nor the names of its
 *       contributors may be used to endorse or promote products derived from
 *       this software without specific prior written permission.
 * 
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */
#include <stdio.h>
#include <cstdio>
#include <iostream>
#include "ros/ros.h"
#include "ros/console.h"
#include "nav_msgs/GetMap.h"
#include "tf/LinearMath/Matrix3x3.h"
#include "geometry_msgs/Quaternion.h"

using namespace std;
 
/**
 * @brief Map generation node.
 */
class MapGenerator 
{

  public:
    MapGenerator(const std::string& map_name, const std::string& topic_name) : map_name_(map_name), saved_map_(false)
    {
      ros::NodeHandle n;
      ROS_INFO("Waiting for the map_topic: %s", topic_name.c_str());
      map_sub_ = n.subscribe(topic_name, 1, &MapGenerator::mapCallback, this);
    }

    void mapCallback(const nav_msgs::OccupancyGridConstPtr& map)// <NAME_EMAIL>
    {
      ROS_INFO("Received a %d X %d map @ %.3f m/pix",
               map->info.width,
               map->info.height,
               map->info.resolution);


      std::string mapdatafile = map_name_ + ".pgm";
      ROS_INFO("Writing map occupancy data to %s", mapdatafile.c_str());
      FILE* out = fopen(mapdatafile.c_str(), "w");
      if (!out)
      {
        ROS_ERROR("Couldn't save map file to %s", mapdatafile.c_str());
        return;
      }
      // chop out useless rows and columns
        int known_thrs = 1;
        int min_y = 0;
        int max_y = map->info.height - 1;
        int min_x = 0;
        int max_x = map->info.width - 1;
        // find min_y
        for (int y = 0; y < map->info.height; y++) {
            int known_cnt = 0;
            for (int x = 0; x < map->info.width; x++) {
                int idx = x + (map->info.height - y - 1) * map->info.width;
                if (map->data[idx] == 0 || map->data[idx] == 100) {// known value
                    known_cnt++;
                }
            }
            if (known_cnt >= known_thrs) {//found known row, found min_y
                min_y = y;
                break;
            }
        }
        // find max_y
        for (int y = map->info.height - 1; y >= 0; y--) {
            int known_cnt = 0;
            for (int x = 0; x < map->info.width; x++) {
                int idx = x + (map->info.height - y - 1) * map->info.width;
                if (map->data[idx] == 0 || map->data[idx] == 100) {// known value
                    known_cnt++;
                }
            }
            if (known_cnt >= known_thrs) {//found known row, found max_y
                max_y = y;
                break;
            }
        }

        // find min_x
        for (int x = 0; x < map->info.width; x++) {
            int known_cnt = 0;
            for (int y = 0; y < map->info.height; y++) {
                int idx = x + (map->info.height - y - 1) * map->info.width;
                if (map->data[idx] == 0 || map->data[idx] == 100) {// known value
                    known_cnt++;
                }
            }
            if (known_cnt >= known_thrs) {//found known column, found min_x
                min_x = x;
                break;
            }
        }
        // find max_x
        for (int x = map->info.width - 1; x >= 0; x--) {
            int known_cnt = 0;
            for (int y = 0; y < map->info.height; y++) {
                int idx = x + (map->info.height - y - 1) * map->info.width;
                if (map->data[idx] == 0 || map->data[idx] == 100) {// known value
                    known_cnt++;
                }
            }
            if (known_cnt >= known_thrs) {//found known row, found max_x
                max_x = x;
                break;
            }
        }
        int chopped_width = max_x - min_x + 1;
        int chopped_height = max_y - min_y + 1;
        ROS_INFO("Chopped map into %d x %d", chopped_width, chopped_height);
        
        bool need_rotation = false;
        if (chopped_width >= chopped_height) {// W >= H, normal
            fprintf(out, "P5\n# CREATOR: Map_generator.cpp %.3f m/pix\n%d %d\n255\n",
                  map->info.resolution, chopped_width, chopped_height);
            for(int y = min_y; y <= max_y; y++) {
                for(int x = min_x; x <= max_x; x++) {
                    int i = x + (map->info.height - y - 1) * map->info.width;
                    //printf("(%3d, %3d) ", y, x);
                    if (map->data[i] == 0) { //occ [0,0.1)
                        fputc(254, out);
                    }
                    else if (map->data[i] == +100) { //occ (0.65,1]
                        fputc(000, out);
                    }
                    else { //occ [0.1,0.65]
                        fputc(205, out);
                    }
                }
                //std::cout << std::endl;
            }
        }
        else {// W < H, rotate left 90deg
            int rotate_chopped_width = chopped_height;
            int rotate_chopped_height = chopped_width;
            ROS_INFO("W < H, rotation the map into %d x %d", rotate_chopped_width, rotate_chopped_height);
            need_rotation = true;
            fprintf(out, "P5\n# CREATOR: Map_generator.cpp %.3f m/pix\n%d %d\n255\n",
                  map->info.resolution, rotate_chopped_width, rotate_chopped_height);
            for(int x = max_x; x >= min_x; x--) {
                for(int y = min_y; y <= max_y; y++) {
                    int i = x + (map->info.height - y - 1) * map->info.width;
                    //printf("(%3d, %3d) ", y, x);
                    if (map->data[i] == 0) { //occ [0,0.1)
                        fputc(254, out);
                    }
                    else if (map->data[i] == +100) { //occ (0.65,1]
                        fputc(000, out);
                    }
                    else { //occ [0.1,0.65]
                        fputc(205, out);
                    }
                }
                //std::cout << std::endl;
            }
        }
      fclose(out);

      std::string mapmetadatafile = map_name_ + ".yaml";
      ROS_INFO("Writing map occupancy data to %s", mapmetadatafile.c_str());
      FILE* yaml = fopen(mapmetadatafile.c_str(), "w");


      /*
resolution: 0.100000
origin: [0.000000, 0.000000, 0.000000]
#
negate: 0
occupied_thresh: 0.65
free_thresh: 0.196

       */

      geometry_msgs::Quaternion orientation = map->info.origin.orientation;
      tf::Matrix3x3 mat(tf::Quaternion(orientation.x, orientation.y, orientation.z, orientation.w));
      double yaw, pitch, roll;
      mat.getEulerYPR(yaw, pitch, roll);

      ROS_INFO("image: %s\nresolution: %f\norigin: [%f, %f, %f]\nnegate: 0\noccupied_thresh: 0.65\nfree_thresh: 0.196\n\n", 
        mapdatafile.c_str(), map->info.resolution, 0.0, 0.0, yaw);
      fprintf(yaml, "image: %s\nresolution: %f\norigin: [%f, %f, %f]\nnegate: 0\noccupied_thresh: 0.65\nfree_thresh: 0.196\n\n",
        //mapdatafile.c_str(), map->info.resolution, map->info.origin.position.x, map->info.origin.position.y, yaw);
        mapdatafile.c_str(), map->info.resolution, 0.0, 0.0, yaw);

      fclose(yaml);

      ROS_INFO("Done\n");
      saved_map_ = true;
    }

    std::string map_name_;
    ros::Subscriber map_sub_;
    bool saved_map_;

};

#define USAGE "Usage: \n" \
              "  map_saver -h\n"\
              "  map_saver [-t <topic name>] [-f <map name>]"

int main(int argc, char** argv) 
{
  ros::init(argc, argv, "map_saver");
  std::string map_name = "map";
  std::string topic_name = "/map";

  for(int i=1; i<argc; i++)
  {
    if(!strcmp(argv[i], "-h"))
    {
      puts(USAGE);
      return 0;
    }
    else if(!strcmp(argv[i], "-t"))
    {
      if(++i < argc)
        topic_name = argv[i];
      else
      {
        puts(USAGE);
        return 1;
      }
    }
    else if(!strcmp(argv[i], "-f"))
    {
      if(++i < argc)
        map_name = argv[i];
      else
      {
        puts(USAGE);
        return 1;
      }
    }
    else
    {
      puts(USAGE);
      return 1;
    }
  }
  
  MapGenerator mg(map_name, topic_name);

  while(!mg.saved_map_ && ros::ok())
    ros::spinOnce();

  return 0;
}


