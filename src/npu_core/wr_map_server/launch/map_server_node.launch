<?xml version="1.0"?>
<!-- -*- mode: XML -*- -->
<!-- v1.0 created by lawrence.han @2017-04-02 -->
<!-- v1.0.1 modified by lawrence.han @2017-04-03 -->
<launch>
    <!-- wizrobo standard launch arg -->
    <arg name="logger_level" default="info"/>
    <arg name="display_rviz" default="false"/>
    <arg name="record_bag" default="false"/>
    <arg name="play_bag" default="false"/>
    <arg name="play_rate" default="1.0"/>
    <arg name="bag_file" default="slam.bag"/>

    <arg name="map_dir_path" default="$(find wr_map_server)/map/" />
    <arg name="current_map_id" default="$(optenv MAP_ID map0)" />
    <arg name="map_server_mode" default="NAVI_MODE" />

    <!-- customized launch arg -->
    <arg name="pkg_name" default="wr_map_server"/>
    <arg name="pkg_path" value="$(find wr_map_server)"/>

    <!-- 0. wizrobo standard launch function -->
    <!-- a) Display rviz -->
    <node pkg="rviz" type="rviz" name="rviz_example"
        args="-d $(find wr_example)/rviz/example.rviz"
        if="$(arg display_rviz)" output="screen"/>
    <!-- b) Record bag -->
    <node pkg="rosbag" type="record" name="rosbag_recorder"
       args="-o $(find wr_example)/bag/$(arg bag_file) /scan"
       if="$(arg record_bag)" output="screen"/>
    <!-- c) Play bag -->
    <param name="/use_sim_time" value="$(arg play_bag)"/>
    <node pkg="rosbag" type="play" name="rosbag_player"
        args="--clock -l -r $(arg play_rate) $(find wr_example)/bag/$(arg bag_file) --topics /scan"
        if="$(arg play_bag)" output="screen"/>

    <node pkg="wr_map_server" type="map_server_node" name="wr_map_server" output="screen">
        <param name="logger_level" value="$(arg logger_level)"/>
        <param name="map_dir_path" value="$(arg map_dir_path)"/>
        <param name="current_map_id" value="$(arg current_map_id)"/>
        <param name="map_server_mode" value="$(arg map_server_mode)"/>
    </node>

</launch>
