#ifndef WIZROBO_MAP_SERVER_H
#define WIZROBO_MAP_SERVER_H

#include <fstream>
#include <cstring>

#include <pthread.h>

#include <ros/ros.h>
#include <tf/tf.h>
#include <nav_msgs/MapMetaData.h>
#include <nav_msgs/GetMap.h>
#include <geometry_msgs/PolygonStamped.h>
#include <geometry_msgs/PoseArray.h>

#include <npu.h>
#include <wr_npu_msgs/WrMapServer.h>
#include <wr_npu_msgs/ForceLoopClosure.h>
#include <npuice_api.h>
#include <npuice_map.h>
#include <npuice_enum_ext.h>
#include <wr_npu_msgs/Virtualwalls.h>
#include "map_struct.h"
#include "map_handler.h"


namespace wizrobo_npu {
using namespace wizrobo::ros_ext;
using namespace wizrobo;
using namespace std;

typedef wr_npu_msgs::WrMapServer::Request SrvRqst;
typedef wr_npu_msgs::WrMapServer::Response SrvRspns;
typedef SrvRqst::_arguments_type SrvArgument;
typedef SrvRspns::_return_data_type SrvReturn;

enum SERVER_MODE
{
    NAVI_MODE,
    SLAM_MODE,
    UNKNOWN_MODE
};

enum SAVING_MODE
{
    IDLE_MODE,
    USING_MODE
};

class MapServer
{
public:
    MapServer(int argc, char** argv)
        : map_server_mode_("")
        , current_path_index_(-1)
        , current_virtual_wall_index_(-1)
        , p_current_map_(NULL)
        , p_map_handler_(NULL)
        , map_index_(0)
        //, map_opt_mode_(wizrobo_npu::NON_OPT)
    {
    }
    virtual ~MapServer()
    {
        if (p_current_map_)
        {
            SAFE_RELEASE(p_current_map_);
        }
        if (p_map_handler_)
        {
            SAFE_RELEASE(p_map_handler_);
        }
    }

    void Init();
    void Run();
    void SaveMapSubThread();

private:
//    bool enb_map_filter_;
//    bool enb_map_optimizer_;
    string map_dir_path_;
    string current_map_id_;
    string map_server_mode_;
    string map_frame_id_;
    int map_index_;
    pthread_spinlock_t map_object_spinlock_;

    struct Buffer
    {
        const char *p;
        int length;
    };
    struct Arguments
    {
        Buffer input;
        Buffer output;
    };
    struct Arguments_
    {
        vector<unsigned char> *input;
        vector<unsigned char> *output;
    };
    typedef bool (MapServer::*ServiceFunctionPointer)(SrvArgument &, SrvReturn &);
    typedef map<string, ServiceFunctionPointer> ServiceFunctionMap;
    ServiceFunctionMap service_function_map_;

    MapObjectList map_list_;
    MapObject* p_current_map_;
    MapHandler* p_map_handler_;
    int current_path_index_;
    int current_virtual_wall_index_;

    SAVING_MODE map_save_mode_;

    ros::Subscriber raw_map_sub_;
    ros::Subscriber opt_map_sub_;
    ros::Subscriber ins_pose_sub_;
    ros::Publisher map_pub_;
    ros::Publisher map_meta_pub_;
    ros::Publisher virtualwall_pub_;

    ros::ServiceServer service_;
    ros::ServiceServer service_set_map_;
    ros::ServiceServer flc_srv_;

    nav_msgs::OccupancyGrid map_raw_;
    bool enb_map_opt_;
    nav_msgs::OccupancyGrid map_opt_;
    geometry_msgs::PoseArray ins_pose_;

    void LoadAllMapInfo();
    void PubCurrentMap();
    void PubTask(const Task& task);
    void PubPath(const Path& path);
    void PubPath(const ImgPath& path);
    void PubStation(const Station& station);
    void PubVirtualWall(const VirtualWall& virtual_wall);
    void PubVirtualWall(const ImgVirtualWall& virtual_wall);
    void RawMapCallback_(const nav_msgs::OccupancyGridConstPtr& msg);
    void OptMapCallback_(const nav_msgs::OccupancyGridConstPtr& msg);
    void InsPoseCallback_(const geometry_msgs::PoseArrayConstPtr& msg);
    void ShutdownCurrentMap();
    bool SwitchServerMode(const string mode);
    int NewMapIndex()
    {
        map_index_++;
        return map_index_;
    }
    
    bool ForceLoopClosureCallback(wr_npu_msgs::ForceLoopClosure::Request& request
                                     , wr_npu_msgs::ForceLoopClosure::Response& response);
	bool SetMap(nav_msgs::GetMap::Request &request, nav_msgs::GetMap::Response &response);
    bool Serve(SrvRqst &request, SrvRspns &response);
    int _SetupNewMap();
    bool GetCurrentMapInNaviMode(SrvArgument &args, SrvReturn &rtn);
    bool GetCurrentMapInSlamMode(SrvArgument &args, SrvReturn &rtn);
    bool GetCurrentImgMapInNaviMode(SrvArgument &args, SrvReturn &rtn);
    bool GetCurrentImgMapInSlamMode(SrvArgument &args, SrvReturn &rtn);

    bool GetCurrentMap(SrvArgument &args, SrvReturn &rtn);
    bool GetCurrentImgMap(SrvArgument &args, SrvReturn &rtn);
    bool GetMapInfos(SrvArgument &args, SrvReturn &rtn);
    bool SetMapInfos(SrvArgument &args, SrvReturn &rtn);
    bool LoadMap(SrvArgument &args, SrvReturn &rtn);
    bool SaveMap(SrvArgument &args, SrvReturn &rtn);
    bool ExportMapFile(SrvArgument &args, SrvReturn &rtn);
    bool ImportMapFile(SrvArgument &args, SrvReturn &rtn);
    bool SetTasks(SrvArgument &args, SrvReturn &rtn);
    bool GetTaskList(SrvArgument &args, SrvReturn &rtn);
//    bool ExecuteTask(SrvArgument &args, SrvReturn &rtn);
    bool SetImgPaths(SrvArgument &args, SrvReturn &rtn);
    bool GetImgPaths(SrvArgument &args, SrvReturn &rtn);
    bool SetPaths(SrvArgument &args, SrvReturn &rtn);
    bool GetPaths(SrvArgument &args, SrvReturn &rtn);
    bool GetPath(SrvArgument &args, SrvReturn &rtn);
    bool GetCurrentImgPath(SrvArgument &args, SrvReturn &rtn);
    bool GetCurrentPath(SrvArgument &args, SrvReturn &rtn);
    bool SetImgStations(SrvArgument &args, SrvReturn &rtn);
    bool GetImgStations(SrvArgument &args, SrvReturn &rtn);
    bool SetStations(SrvArgument &args, SrvReturn &rtn);
    bool GetStation(SrvArgument &args, SrvReturn &rtn);
    bool GetStations(SrvArgument &args, SrvReturn &rtn);
    bool SetServerMode(SrvArgument &args, SrvReturn &rtn);
    bool TransformImgPosesTo3DPoses(SrvArgument &args, SrvReturn &rtn);
    bool Transform3DPosesToImgPoses(SrvArgument &args, SrvReturn &rtn);
    bool GetVirtualWall(SrvArgument &args, SrvReturn &rtn);
    bool GetVirtualWalls(SrvArgument &args, SrvReturn &rtn);
    bool SetVirtualWalls(SrvArgument &args, SrvReturn &rtn);
    bool GetImgVirtualWalls(SrvArgument &args, SrvReturn &rtn);
    bool SetImgVirtualWalls(SrvArgument &args, SrvReturn  &rtn);
    bool GetCurrentImgVirtualWall(SrvArgument &args, SrvReturn  &rtn);
    bool GetCurrentVirtualWall(SrvArgument &args, SrvReturn &rtn);
};

}// namespace wizoro

#endif // WIZROBO_MAP_SERVER_H
