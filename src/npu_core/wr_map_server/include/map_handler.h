#ifndef WIZROBO_MAP_HANDLER_H
#define WIZROBO_MAP_HANDLER_H

#include <yaml-cpp/yaml.h>
#include <SDL/SDL_image.h>

#include <ros/ros.h>
#include <nav_msgs/MapMetaData.h>
#include <nav_msgs/GetMap.h>
#include <string.h>
#include <wr_npu_msgs/Virtualwalls.h>
#include <npuice_api.h>
#include <npuice_map.h>

namespace wizrobo_npu {

using namespace std;

typedef vector<unsigned char>::iterator CharVectorIter;

struct Map2DLite
{
    CellMat mat;
    TaskList tasks;
    StationList stations;
    PathList paths;
    VirtualWallList virtual_walls;
};

struct ImgMapLite
{
    PixelMat mat; // c
    ImgStationList stations; // s
    ImgPathList paths; // c
    ImgVirtualWallList virtual_walls;
};

struct MapAddInfo
{
    int negate;
    float occupied_thresh;
    float free_thresh;
    std::string config_id;
    std::string lidar_type;
    float lidar_range_m;
    float lidar_frq_hz;
    std::string slam_mode;
    bool enb_map_opt;
    bool enb_trace_clearing;
    bool enb_filtering;
    int filter_size;
    double position_x;
    double position_y;
    double position_yaw;
    double longitude;
    double latitude;
    double angles;
	std::string md5sum;
};

class MapObject
{
public:
    MapInfo info;
    MapAddInfo add_info;
    Map2DLite map_2d;
    ImgMapLite img_map;

    MapObject()
    {
        info.index = -1;
        info.id = "";
        info.creation_time = "";
        info.dimension.x = 0.0;
        info.dimension.y = 0.0;
        info.dimension.z = 0.0;
        info.offset.x = 0.0;
        info.offset.y = 0.0;
        info.offset.z = 0.0;
        info.task_num = 0;
        info.path_num = 0;
//        info.virtual_wall_num = 0;
        info.station_num = 0;
        info.thumbnail.width = 0;
        info.thumbnail.height = 0;
        info.thumbnail.ratio = 1.0;
        info.thumbnail.data.resize(1);
        info.thumbnail.data[0] = 0;
        add_info.free_thresh = 0.0;
        add_info.negate = 0;
        add_info.occupied_thresh = 0.0;
        add_info.position_x = 0.0;
        add_info.position_y = 0.0;
        add_info.position_yaw = 0.0;
        add_info.longitude = 0.0;
        add_info.latitude = 0.0;
        add_info.angles = 0.0;
		add_info.config_id = "";
        add_info.lidar_type = "";
        add_info.lidar_range_m = 0.0;
        add_info.lidar_frq_hz = 0.0;
        add_info.slam_mode = "";
        add_info.enb_map_opt = false;
        add_info.enb_trace_clearing = false;
        add_info.enb_filtering = false;
        add_info.filter_size = 0;
        add_info.md5sum = "";
        map_2d.mat.height = 0;
        map_2d.mat.width = 0;
        map_2d.mat.data.resize(1);
        map_2d.mat.data[0] = FREE_CELL;
        map_2d.tasks.resize(0);
        map_2d.paths.resize(0);
        map_2d.stations.resize(0);
        map_2d.virtual_walls.resize(0);
        img_map.mat.height = 0;
        img_map.mat.width = 0;
        img_map.mat.ratio = 1.0;
        img_map.mat.data.resize(1);
        img_map.mat.data[0] = 0;
        img_map.paths.resize(0);
        img_map.stations.resize(0);
        img_map.virtual_walls.resize(0);
    }
};

typedef vector<MapObject> MapObjectList;

class MapHandler
{
private:
    string map_dir_path_;
    SDL_Surface* map_;
    bool need_save_;
public:
    MapHandler(string map_dir_path);
    ~MapHandler();
    void TransformPose3D2ImgPose(const MapObject& map_obj, const Pose3D& pose_3d, ImgPose& img_pose);
//    void TransformPose3D2ImgPoseS(const MapObject& map_obj, const Pose3D& pose_3d, ImgPose& img_pose, double height ,double ratio);
    void TransformImgPose2Pose3D(const MapObject& map_obj, const ImgPose& img_pose, Pose3D& pose_3d);
    void TransformPoint3D2ImgPoint(const MapObject& map_obj, const Point3D& point_3d, ImgPoint& img_point);
//    void TransformPoint3D2ImgPointS(const MapObject& map_obj, const Point3D& point_3d, ImgPoint& img_point , double height ,double ratio);
    void TransformImgPoint2Point3D(const MapObject& map_obj, const ImgPoint& img_point, Point3D& point_3d);

    void ListAllMap(vector<string> &mapNameList);
    bool _MakeMD5(string &map_name, string &md5sum);
    bool _GetMD5(string &map_name, string &md5sum);
    void ReadMapMD5(vector<string> &maplist, map<string, string> &md5list);
    bool _ReadMapInfoBaseInfo(YAML::Node &map_yaml, MapObject& map_obj);
    bool _ReadMapInfoTask(YAML::Node &map_yaml, MapObject& map_obj);
    bool _ReadMapInfoStation(YAML::Node &map_yaml, MapObject& map_obj);
    bool _ReadMapInfoPath(YAML::Node &map_yaml, MapObject& map_obj);
    bool _ReadMapInfoVirtualWall(YAML::Node &map_yaml, MapObject& map_obj);
    bool ReadMapInfo(const string& map_name, MapObject& map_obj);
    bool LoadMapImage(MapObject& map_obj);
    bool LoadThumbnail(MapObject& map_obj);
    bool MakeThumbnail(MapObject& map_obj);
    bool SaveThumbnail(MapObject &map_obj);
    bool FillMapMateData(MapObject& map_obj);
    bool ResizePixelMat(PixelMat &input_pixel_mat, PixelMat &output_pixel_mat, int size_max);
    bool ResizeImgMap(MapObject &map_obj, int width_max);
    bool MakeMapMessage(const MapObject& map_obj, nav_msgs::OccupancyGrid& msg);
    bool MakeVirtualwallMessage(const MapObject& map_obj, wr_npu_msgs::Virtualwalls& msg);
    void UpdateMapObject(const nav_msgs::OccupancyGrid& map, MapObject& map_obj);
    void MakeYaml(const MapObject& map_obj, YAML::Node &map_yaml);
    void DeleteMapFile(const MapObject& map_obj);
    void SaveMapYaml(const MapObject& map_obj);
    void SaveMapPgm(const MapObject& map_obj);
    void SaveMapObject(const MapObject& map_obj);
    string GetMapDirPath();

    void Print(const MapInfo &map_info);
    void Print(const MapAddInfo &map_add_info);

    void Print(const CellMat &cell_mat);
    void Print(const Task &task);
    void Print(const Path &path);
    void Print(const VirtualWall &virtual_wall);
    void Print(const StationList &stations);
    void Print(const TaskList &tasks);
    void Print(const PathList &paths);
    void Print(const VirtualWallList &virtual_walls);

    void Print(const PixelMat &pixel_mat);
    void Print(const ImgPath &path);
    void Print(const ImgVirtualWall &virtual_wall);
    void Print(const ImgStationList &stations);
    void Print(const ImgPathList &paths);
    void Print(const ImgVirtualWallList &virtual_walls);

    void Print(const RgbaPixelMat &rgba_pixel_mat);
    void Print(const GeoPath &geo_path);
    void Print(const GeoVirtualWall &geo_virtual_wall);
    void Print(const GeoStationList &geo_stations);
    void Print(const GeoPathList &geo_paths);
    void Print(const GeoVirtualWallList &geo_virtual_walls);

    void Print(const MapObject& o);
    void Print(const MapObjectList& map_list);

    template<typename T>
    void PackFixLength(const T &list, CharVectorIter &p_data);
    template<typename T>
    void PackVarLength(const T &list, CharVectorIter &p_data);

    void Pack(const MapInfo& map_info, CharVectorIter &p_data);
    void Pack(const MapInfoList& map_info_list, CharVectorIter &p_data);

    void Pack(const CellMat& cell_mat, CharVectorIter &p_data);
    void Pack(const Pose3D &pose, CharVectorIter &p_data);
    void Pack(const Point3D &point, CharVectorIter &p_data);
    void Pack(const TaskAction& action, CharVectorIter &p_data);
    void Pack(const TaskInfo& info, CharVectorIter &p_data);
    void Pack(const Task& task, CharVectorIter &p_data);
    void Pack(const Path& path, CharVectorIter &p_data);
    void Pack(const VirtualWall& virtual_wall, CharVectorIter &p_data);
    void Pack(const Station& station, CharVectorIter &p_data);
    void Pack(const StationList& stations, CharVectorIter &p_data);
    void Pack(const Pose3DList& poses, CharVectorIter &p_data);
    void Pack(const Point3DList& points, CharVectorIter &p_data);
    void Pack(const TaskActionList& action_list, CharVectorIter &p_data);
    void Pack(const TaskList& tasks, CharVectorIter &p_data);
    void Pack(const PathList& paths, CharVectorIter &p_data);
    void Pack(const VirtualWallList& virtual_walls, CharVectorIter &p_data);

    void Pack(const PixelMat& pixel_mat, CharVectorIter &p_data);
    void Pack(const ImgPose &pose, CharVectorIter &p_data);
    void Pack(const ImgPoint &point, CharVectorIter &p_data);
    void Pack(const ImgPath& path, CharVectorIter &p_data);
    void Pack(const ImgVirtualWall& virtual_wall, CharVectorIter &p_data);
    void Pack(const ImgStationList& stations, CharVectorIter &p_data);
    void Pack(const ImgPoseList& poses, CharVectorIter &p_data);
    void Pack(const ImgPointList& points, CharVectorIter &p_data);
    void Pack(const ImgPathList& paths, CharVectorIter &p_data);
    void Pack(const ImgVirtualWallList& virtual_walls, CharVectorIter &p_data);

    void Pack(const RgbaPixelMat& rgba_pixel_mat, CharVectorIter& p_data);
    void Pack(const GeoPose& geo_pose, CharVectorIter& p_data);
    void Pack(const GeoPoint& geo_point, CharVectorIter& p_data);
    void Pack(const GeoPath& geo_path, CharVectorIter& p_data);
    void Pack(const GeoVirtualWall& geo_virtual_wall, CharVectorIter& p_data);
    void Pack(const GeoStationList& geo_stations, CharVectorIter& p_data);
    void Pack(const GeoPoseList& geo_poses, CharVectorIter& p_data);
    void Pack(const GeoPointList& geo_points, CharVectorIter& p_data);
    void Pack(const GeoPathList& geo_paths, CharVectorIter& p_data);
    void Pack(const GeoVirtualWallList& geo_virtual_walls, CharVectorIter& p_data);

    template<typename T>
    void UnpackFixLength(T &list, CharVectorIter &p_data);
    template<typename T>
    void UnpackVarLength(T &list, CharVectorIter &p_data);

    void Unpack(MapInfo& map_info, CharVectorIter &p_data);
    void Unpack(MapInfoList& map_info_list, CharVectorIter &p_data);

    void Unpack(CellMat& cell_mat, CharVectorIter &p_data);
    void Unpack(Pose3D &pose, CharVectorIter &p_data);
    void Unpack(Point3D &point, CharVectorIter &p_data);
    void Unpack(TaskAction &action, CharVectorIter &p_data);
    void Unpack(TaskInfo &info, CharVectorIter &p_data);
    void Unpack(Task& task, CharVectorIter &p_data);
    void Unpack(Path& path, CharVectorIter &p_data);
    void Unpack(VirtualWall& virtual_wall, CharVectorIter &p_data);
    void Unpack(Station& station, CharVectorIter &p_data);
    void Unpack(StationList& stations, CharVectorIter &p_data);
    void Unpack(Pose3DList& poses, CharVectorIter &p_data);
    void Unpack(Point3DList& points, CharVectorIter &p_data);
    void Unpack(TaskActionList& action_list, CharVectorIter &p_data);
    void Unpack(TaskList& tasks, CharVectorIter &p_data);
    void Unpack(PathList& paths, CharVectorIter &p_data);
    void Unpack(VirtualWallList& virtual_walls, CharVectorIter &p_data);

    void Unpack(PixelMat& pixel_mat, CharVectorIter &p_data);
    void Unpack(ImgPose &pose, CharVectorIter &p_data);
    void Unpack(ImgPoint &point, CharVectorIter &p_data);
    void Unpack(ImgPath& path, CharVectorIter &p_data);
    void Unpack(ImgVirtualWall& virtual_wall, CharVectorIter &p_data);
    void Unpack(ImgStationList& stations, CharVectorIter &p_data);
    void Unpack(ImgPoseList& poses, CharVectorIter &p_data);
    void Unpack(ImgPointList& points, CharVectorIter &p_data);
    void Unpack(ImgPathList& paths, CharVectorIter &p_data);
    void Unpack(ImgVirtualWallList& virtual_walls, CharVectorIter &p_data);

    void Unpack(RgbaPixelMat& rgba_pixel_mat, CharVectorIter& p_data);
    void Unpack(GeoPose& geo_pose, CharVectorIter& p_data);
    void Unpack(GeoPoint& geo_point, CharVectorIter& p_data);
    void Unpack(GeoPath& geo_path, CharVectorIter& p_data);
    void Unpack(GeoVirtualWall& geo_virtual_wall, CharVectorIter& p_data);
    void Unpack(GeoStationList& geo_stations, CharVectorIter& p_data);
    void Unpack(GeoPoseList& geo_poses, CharVectorIter& p_data);
    void Unpack(GeoPointList& geo_points, CharVectorIter& p_data);
    void Unpack(GeoPathList& geo_paths, CharVectorIter& p_data);
    void Unpack(GeoVirtualWallList& geo_virtual_walls, CharVectorIter& p_data);

    int Sizeof(const MapInfo& map_info);
    int Sizeof(const MapInfoList& map_info_list);
    int Sizeof(const MapAddInfo& map_add_info);
    int Sizeof(const MapObject& map_obj);

    int Sizeof(const CellMat& cell_mat);
    int Sizeof(const Station& station);
    int Sizeof(const Task& task);
    int Sizeof(const TaskAction& actions);
    int Sizeof(const Path& path);
    int Sizeof(const VirtualWall& virtual_wall);
    int Sizeof(const StationList& station_list);
    int Sizeof(const TaskList& task_list);
    int Sizeof(const PathList& path_list);
    int Sizeof(const VirtualWallList& virtual_wall_list);

    int Sizeof(const PixelMat& pixel_mat);
    int Sizeof(const ImgStation& station);
    int Sizeof(const ImgPath& path);
    int Sizeof(const ImgVirtualWall &virtual_wall);
    int Sizeof(const ImgStationList& station_list);
    int Sizeof(const ImgPathList& path_list);
    int Sizeof(const ImgVirtualWallList& virtual_wall_list);

    int Sizeof(const RgbaPixelMat& rgba_pixel_mat);
    int Sizeof(const GeoStation& geo_station);
    int Sizeof(const GeoPath& geo_path);
    int Sizeof(const GeoVirtualWall& geo_virtual_wall);
    int Sizeof(const GeoStationList& geo_stations);
    int Sizeof(const GeoPathList& geo_paths);
    int Sizeof(const GeoVirtualWallList& geo_virtual_walls);

    void CopyMap(const nav_msgs::OccupancyGrid& map, MapObject &map_obj, bool need_chop);
};

}
#endif // MAP_HANDLER_H
