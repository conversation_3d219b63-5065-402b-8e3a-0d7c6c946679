#ifndef WIZROBO_MAP_STRUCT_H
#define WIZROBO_MAP_STRUCT_H

// We use SDL_image to load the image from disk
#include <SDL/SDL_image.h>

#include <ros/ros.h>

namespace wizrobo_npu
{

using namespace std;



//struct MapOrigin
//{
//    float x;
//    float y;
//    float yaw;
//};

//struct MapPose
//{
//    float x;
//    float y;
//    float z;
//    float roll;
//    float pitch;
//    float yaw;
//};

//class MapStation
//{
//public:
//    string  name;
//    string  type;
//    int     artag_id;
//    MapPose pose;

//    string GetName()
//    {
//        return name;
//    }
//};
//typedef vector<MapStation> MapStationList;

//class MapPath
//{
//public:
//    string          name;
//    float           length;
//    vector<MapPose> poses;

//    string GetName()
//    {
//        return name;
//    }
//};
//typedef vector<MapPath> MapPathList;

//struct MapInfo
//{
//    string              image;
//    float               resolution;
//    MapPose             origin;
//    int                 negate;
//    float               occupied_thresh;
//    float               free_thresh;
//    string              time;
//    MapStationList      stations;
//    MapPathList         paths;
//};

//struct MapObject
//{
//public:
//    MapInfo      info;
//    SDL_Surface* p_matedata;

//    string GetName()
//    {
//        return info.image;
//    }
//};

//typedef vector<MapObject> MapObjectList;

}// namespace wizrobo_npu
#endif // WIZROBO_MAP_STRUCT_H
