#ifndef WIZROBO_NPU_MSGS_ENUM_H
#define WIZROBO_NPU_MSGS_ENUM_H
#include <npu.h>
#include <wr_npu_msgs/GetOptMap.h>

namespace wizrobo { namespace core {
//enum MapOptMode
//{
//    NON_OPT = wr_npu_msgs::GetOptMap::Request::NON_OPT,
//    FLT_OPT = wr_npu_msgs::GetOptMap::Request::FLT_OPT,
//    FLC_OPT = wr_npu_msgs::GetOptMap::Request::FLC_OPT,
//    BOTH_OPT = wr_npu_msgs::GetOptMap::Request::BOTH_OPT,
//};
//}}// namesapce
//namespace wizrobo { namespace enum_ext {
//using namespace wizrobo::core;
//BEGIN_ENUM_STRING(MapOptMode)
//{
//    ENUM_STRING(NON_OPT);
//    ENUM_STRING(FLT_OPT);
//    ENUM_STRING(FLC_OPT);
//    ENUM_STRING(BOTH_OPT);
//}
//END_ENUM_STRING;
}}// namespace

#endif // WIZROBO_NPU_MSGS_ENUM_H
