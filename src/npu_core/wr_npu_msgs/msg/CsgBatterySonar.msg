Header header
# sonar
float32[] sonar        # sonar data
int32 obstacle         # Determine whether to obstacle through Sonar
                       # 0 is no normal, 1 is forward obstacle, 2 is backward obstacle
int32 keep_distance    # Default obstacle avoidance distance
# battery
float32[] vol                   # battery voltage
float32 max_voltage             #
float32 min_voltage             #
float32 max_voltage_pos         #
float32 min_voltage_pos         #
float32 voltage_diff            #
float32 avrg_voltage            #
float32 charge_current          #
float32 discharge_current       #
float32 soc                     #
float32[] temperature           #
float32 max_temperature         #
float32 min_temperature         #
float32 avrg_temperature        #
float32 envirment_temperature   #
float32 total_voltage           #
