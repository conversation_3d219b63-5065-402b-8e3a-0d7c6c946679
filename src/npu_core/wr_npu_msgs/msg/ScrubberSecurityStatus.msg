Header header

#enum warning status
uint8 NORMAL       = 0
uint8 WARNING      = 1
uint8 RESTORING    = 2

uint8[] infrd_warning
uint8[] sonar_warning
uint8[] bumper_warning

float32 infrd_threshold
float32 sonar_threshold

#Location description   |          usefulness description
#infrd(0~3) sonar(0~9)  |       sonar        |          infrd
#0-------Left  rear     |   (edge sensor)    |      (edge sensor)
#1-------Left  front    |   (edge sensor)    |      (edge sensor)
#2-------Front left     | (Collision sensor) |  (Drop prevention sensor)
#3-------Front right    | (Collision sensor) |  (Drop prevention sensor)
#4-------Right front    | (Collision sensor) |  (Drop prevention sensor)
#5-------Right rear     | (Collision sensor) |  (Drop prevention sensor)
#6-------Back  right    | (Collision sensor) |  (Drop prevention sensor)
#7-------Back  left     | (Collision sensor) |  (Drop prevention sensor)
#8-------TOP   left     | (Collision sensor) |  (Drop prevention sensor)
#9-------TOP   right    | (Collision sensor) |  (Drop prevention sensor)

