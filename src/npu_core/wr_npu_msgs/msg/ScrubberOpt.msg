# enum OutputMode, for lamp and beep
uint32 OUTPUT_MODE_KEEP_HIGH = 0
uint32 OUTPUT_MODE_KEEP_LOW  = 1
uint32 OUTPUT_MODE_BLINK_1   = 2
uint32 OUTPUT_MODE_BLINK_2   = 3
uint32 OUTPUT_MODE_BLINK_3   = 4

uint32 OPEN  = 0
uint32 CLOSE = 1

uint32 ON = 0
uint32 OFF = 1

# enum Breathe Light Status
uint32 BLN_WHITE_BREATHE    = 0
uint32 BLN_ALL_BREATHE      = 1
uint32 BLN_RED_BLINK_1HZ    = 2
uint32 BLN_RED_BLINK_2HZ    = 3
uint32 BLN_RED_ALWAYS_ON    = 4
uint32 BLN_ALL_ALWAYS_ON    = 5

# enum Lift Status
uint32 LIFT_DOWN = 0
uint32 LIFT_UP   = 1
uint32 LIFT_HALF   = 2

# enum Fan Status
uint32 FAN_OPEN = 4
uint32 FAN_CLOSE = 0

# enum Brush Status
uint32 BRUSH_OPEN = 4
uint32 BRUSH_CLOSE = 0

# enum Valve Status
uint32 VALVE_OPEN = 4
uint32 VALVE_CLOSE = 0

# enum Beep Status
uint32 BEEP_CLOSE = 0
uint32 BUZZING_1HZ = 1
uint32 BUZZING_2HZ = 2
uint32 BLEW_10S = 3

# enum Level
uint32 LV_STOP    = 0
uint32 LV_LOW     = 1
uint32 LV_MEDIUM  = 2
uint32 LV_HIGH    = 3
uint32 LV_MAX     = 4

uint32[] lamp_status
uint32[] beep_status

uint32[] bl_status
uint32[] lift_status
uint32[] fan_lv
uint32[] brush_lv
uint32[] valve_lv
