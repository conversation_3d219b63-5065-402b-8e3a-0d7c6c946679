# enum BaseModelType
uint8 CARLIKE   = 0
uint8 DIFFDRV   = 1
uint8 UNIVWHEEL = 2
uint8 OMNIWHEEL = 3

Header header
uint8 chassis_type

## diffdrv, univwheel, omniwheel
uint8 wheel_num
float32[] mps # [mps]
float32[] rpss # [rps]

# carlike
# SteerSensorLocationEnum
uint8 CENTER  = 0
uint8 LEFT    = 1
uint8 RIGHT   = 2
uint8 steer_sensor_location # [SteerSensorLocationEnum]
float32 steer_angle_deg           # [deg]
