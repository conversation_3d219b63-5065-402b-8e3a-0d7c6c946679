Header header
# RuntimeStatus0
uint32 RT0_CONECT_LIDAR      = 1          # BIT(0)
uint32 RT0_CONECT_IMU        = 2          # BIT(1)
uint32 RT0_CONECT_BCU_MOTOR  = 4          # BIT(2)
uint32 RT0_CONECT_BCU_SENSOR = 8          # BIT(3)
uint32 RT0_CONECT_GPS        = 16         # BIT(4)
uint32 RT0_DATA_LIDAR        = 65536      # BIT(16)
uint32 RT0_DATA_IMU          = 131072     # BIT(17)
uint32 RT0_DATA_ENCODER      = 262144     # BIT(18)
uint32 RT0_DATA_GPS          = 524288     # BIT(19)
uint32 RT0_DATA_MAP          = 1048576    # BIT(20)
uint32 runtime_status_0 # [RuntimeStatus0]
# RuntimeStatus1
uint32 RT1_INIT_POSE_NOT_SET      = 1       # BIT(0)
uint32 RT1_LOCALIZATION_EXCEPTION = 2       # BIT(1)
uint32 RT1_SOFTWARE_NODE_CRASH    = 4       # BIT(2)
uint32 RT1_BATTERY_LOW            = 8       # BIT(3)
uint32 RT1_NAVI_OBSTACLE_BLOCK    = 65536   # BIT(16)
uint32 RT1_NAVI_PLANNING_FAILED   = 131072  # BIT(17)
uint32 RT1_NAVI_EMB_TRIGGERED     = 262144  # BIT(18)
uint32 RT1_NAVI_BUMPER_TRIGGERED  = 524288  # BIT(19)
uint32 RT1_NAVI_ENTRP_DETECTED    = 1048576 # BIT(20)
uint32 runtime_status_1 # [RuntimeStatus1]
uint32 RT2_LEFT_SUPERHEAT      = 1     # BIT(0)
uint32 RT2_LEFT_OVERVOLTAGE    = 2     # BIT(1)
uint32 RT2_LEFT_UNDERVOLTAGE   = 4     # BIT(2)
uint32 RT2_LEFT_SHORTCIRCUIT   = 8     # BIT(3)
uint32 RT2_LEFT_EMERGENCYSTOP  = 16    # BIT(4)
uint32 RT2_LEFT_SEPEXERROR     = 32    # BIT(5)
uint32 RT2_LEFT_MOSFETERROR    = 64    # BIT(6)
uint32 RT2_LEFT_CONFIGERROR    = 128   # BIT(7)
uint32 RT2_RIGHT_SUPERHEAT     = 256   # BIT(8)
uint32 RT2_RIGHT_OVERVOLTAGE   = 512   # BIT(9)
uint32 RT2_RIGHT_UNDERVOLTAGE  = 1024  # BIT(10)
uint32 RT2_RIGHT_SHORTCIRCUIT  = 2048  # BIT(11)
uint32 RT2_RIGHT_EMERGENCYSTOP = 4096  # BIT(12)
uint32 RT2_RIGHT_SEPEXERROR    = 8192  # BIT(13)
uint32 RT2_RIGHT_MOSFETERROR   = 16384 # BIT(14)
uint32 RT2_RIGHT_CONFIGERROR   = 32768 # BIT(15)
uint32 runtime_status_2 # [RuntimeStatus2]
