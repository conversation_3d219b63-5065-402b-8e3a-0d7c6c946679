Header header
uint32 KEYA_LEFT_SUPERHEAT      = 1     # BIT(0)
uint32 KEYA_LEFT_OVERVOLTAGE    = 2     # BIT(1)
uint32 KEYA_LEFT_UNDERVOLTAGE   = 4     # BIT(2)
uint32 KEYA_LEFT_SHORTCIRCUIT   = 8     # BIT(3)
uint32 KEYA_LEFT_EMERGENCYSTOP  = 16    # BIT(4)
uint32 KEYA_LEFT_SEPEXERROR     = 32    # BIT(5)
uint32 KEYA_LEFT_MOSFETERROR    = 64    # BIT(6)
uint32 KEYA_LEFT_CONFIGERROR    = 128   # BIT(7)
uint32 KEYA_RIGHT_SUPERHEAT     = 256   # BIT(8)
uint32 KEYA_RIGHT_OVERVOLTAGE   = 512   # BIT(9)
uint32 KEYA_RIGHT_UNDERVOLTAGE  = 1024  # BIT(10)
uint32 KEYA_RIGHT_SHORTCIRCUIT  = 2048  # BIT(11)
uint32 KEYA_RIGHT_EMERGENCYSTOP = 4096  # BIT(12)
uint32 KEYA_RIGHT_SEPEXERROR    = 8192  # BIT(13)
uint32 KEYA_RIGHT_MOSFETERROR   = 16384 # BIT(14)
uint32 KEYA_RIGHT_CONFIGERROR   = 32768 # BIT(15)
uint32 keya_status # [keya_status]
