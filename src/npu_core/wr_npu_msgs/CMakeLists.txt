cmake_minimum_required(VERSION 2.8.3)
project(wr_npu_msgs)

## Find catkin macros and libraries
## if COMPONENTS list like find_package(catkin REQUIRED COMPONENTS xyz)
## is used, also find other catkin packages
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  message_generation
  std_msgs
  nav_msgs 
  geometry_msgs
  geographic_msgs
  sensor_msgs
  tf
  wr_npu_core
)

## System dependencies are found with CMake's conventions
# find_package(Boost REQUIRED COMPONENTS system)
#find_package(Ice REQUIRED Ice IceUtil)


## Uncomment this if the package has a setup.py. This macro ensures
## modules and global scripts declared therein get installed
## See http://ros.org/doc/api/catkin/html/user_guide/setup_dot_py.html
# catkin_python_setup()

################################################
## Declare ROS messages, services and actions ##
################################################

## To declare and build messages, services or actions from within this
## package, follow these steps:
## * Let MSG_DEP_SET be the set of packages whose message types you use in
##   your messages/services/actions (e.g. std_msgs, actionlib_msgs, ...).
## * In the file package.xml:
##   * add a build_depend and a run_depend tag for each package in MSG_DEP_SET
##   * If MSG_DEP_SET isn't empty the following dependencies might have been
##     pulled in transitively but can be declared for certainty nonetheless:
##     * add a build_depend tag for "message_generation"
##     * add a run_depend tag for "message_runtime"
## * In this file (CMakeLists.txt):
##   * add "message_generation" and every package in MSG_DEP_SET to
##     find_package(catkin REQUIRED COMPONENTS ...)
##   * add "message_runtime" and every package in MSG_DEP_SET to
##     catkin_package(CATKIN_DEPENDS ...)
##   * uncomment the add_*_files sections below as needed
##     and list every .msg/.srv/.action file to be processed
##   * uncomment the generate_messages entry below
##   * add every package in MSG_DEP_SET to generate_messages(DEPENDENCIES ...)

## Generate messages in the 'msg' folder
add_message_files(
   FILES
   MotorEnc.msg
   MotorSpd.msg
   SonarData.msg
   InfrdData.msg
   BumperData.msg
   GpsData.msg
   EmergencyStopData.msg
   VoltageData.msg
   ComStatus.msg
   PointArray.msg
   Virtualwalls.msg
   SensorStatus.msg
   VsensorStatus.msg
   Pose2DExt.msg
   Pose2DExtArray.msg
   ExceptionInformation.msg
   TaskAction.msg
   RuntimeStatus.msg
   CollisionState.msg
   KeyaStatus.msg
   CsgBatterySonar.msg
   Station.msg
   StationArray.msg
   NaviGoal.msg
   NaviSchedule.msg
   NaviStatus.msg
   ScrubberOpt.msg
   ScrubberStrOpt.msg
   ScrubberWorkingStatus.msg
   ScrubberMissionReport.msg
   ScrubberMotorState.msg
   ScrubberMotorStateALL.msg
   ScrubberWaterLevel.msg
   ScrubberUsageTime.msg
   ScrubberWaterValveStatus.msg
   ScrubberSecurityStatus.msg
   StringArray.msg
   SystemStatus.msg
   Threshold.msg
   Lifetime.msg
   BatteryStatus.msg
   WaterTank.msg
   PgvData.msg
   R2100Data.msg
   R2100.msg
   QRCode.msg
   QRCodeArray.msg
)

## Generate services in the 'srv' folder
add_service_files(
  FILES
  WrMapServer.srv
  DynParam.srv
  ForceLoopClosure.srv
  GetOptMap.srv
  ClearMotorEnc.srv
  FuncRtnBool.srv
  FuncRtnInt.srv
  FuncRtnVoid.srv
  GetPlan.srv
  CsgControl.srv
  CsgMode.srv
  CsgStation.srv
  CsgPath.srv
  TrioooFeedPolyline.srv
  TransGpsGoal.srv
  TransGpsPath.srv
  ReTransGpsGoal.srv
  ReTransGpsPath.srv
  ChargeData.srv
  CsgTsNpu.srv
  CsgQRCode.srv
  MemoryMode.srv
  NpuMode.srv
  SetNaviStatus.srv
  SetInitPose.srv
  SetScrubberMission.srv
  GetSetFloat.srv
  ScrubberStatus.srv
  ScrubberLifecycleReport.srv
  ScrubberGetMotorStatus.srv
  AcvControllorCmd.srv
)

## Generate actions in the 'action' folder
# add_action_files(
#   FILES
#   Action1.action
#   Action2.action
# )

## Generate added messages and services with any dependencies listed here
generate_messages(
   DEPENDENCIES
     geometry_msgs
     nav_msgs
     std_msgs
     geographic_msgs
     sensor_msgs
)

###################################
## catkin specific configuration ##
###################################
## The catkin_package macro generates cmake config files for your package
## Declare things to be passed to dependent projects
## INCLUDE_DIRS: uncomment this if you package contains header files
## LIBRARIES: libraries you create in this project that dependent projects also need
## CATKIN_DEPENDS: catkin_packages dependent projects also need
## DEPENDS: system dependencies of this project that dependent projects also need
catkin_package(
  INCLUDE_DIRS include
  #LIBRARIES npuice
  CATKIN_DEPENDS
    geometry_msgs
    nav_msgs
    roscpp
    std_msgs
    tf
    geographic_msgs
  #DEPENDS ${Ice_LIBRARIES}
)

###########
## Build ##
###########

## Specify additional locations of header files
## Your package locations should be listed before other locations
# include_directories(include)
include_directories(
  ${catkin_INCLUDE_DIRS}
)

## Declare a cpp library
# add_library(wr_npu_msgs
#   src/${PROJECT_NAME}/wr_npu_msgs.cpp
# )


## Declare a cpp executable
# add_executable(wr_npu_msgs_node src/wr_npu_msgs_node.cpp)

## Add cmake target dependencies of the executable/library
## as an example, message headers may need to be generated before nodes
# add_dependencies(wr_npu_msgs_node wr_npu_msgs_generate_messages_cpp)

## Specify libraries to link a library or executable target against

#############
## Install ##
#############

# all install targets should use catkin DESTINATION variables
# See http://ros.org/doc/api/catkin/html/adv_user_guide/variables.html

## Mark executable scripts (Python etc.) for installation
## in contrast to setup.py, you can choose the destination
# install(PROGRAMS
#   scripts/my_python_script
#   DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark executables and/or libraries for installation
# install(TARGETS wr_npu_msgs wr_npu_msgs_node
#   ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#   RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

## Mark cpp header files for installation
 install(DIRECTORY include
   DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
   FILES_MATCHING PATTERN "*.h"
   PATTERN ".svn" EXCLUDE
 )

## Mark other files for installation (e.g. launch and bag files, etc.)
# install(FILES
#   # myfile1
#   # myfile2
#   DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
# )

#############
## Testing ##
#############

## Add gtest based cpp test target and link libraries
# catkin_add_gtest(${PROJECT_NAME}-test test/test_wr_npu_msgs.cpp)
# if(TARGET ${PROJECT_NAME}-test)
#   target_link_libraries(${PROJECT_NAME}-test ${PROJECT_NAME})
# endif()

## Add folders to be run by python nosetests
# catkin_add_nosetests(test)
