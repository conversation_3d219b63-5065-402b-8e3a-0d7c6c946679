#!/bin/bash
source /opt/ros/indigo/setup.bash
source ~/npu_ws/devel/setup.bash

while true
do
echo forward
rostopic pub -1 /navigation_velocity_smoother/raw_cmd_vel geometry_msgs/Twist '{ linear: { x: 0.2, y: 0, z: 0 }, angular: { x: 0, y: 0, z: 0 } }'
sleep 3

echo backward
rostopic pub -1 /navigation_velocity_smoother/raw_cmd_vel geometry_msgs/Twist '{ linear: { x: -0.2, y: 0, z: 0 }, angular: { x: 0, y: 0, z: 0 } }'
sleep 3

echo left
rostopic pub -1 /navigation_velocity_smoother/raw_cmd_vel geometry_msgs/Twist '{ linear: { x: 0, y: 0, z: 0 }, angular: { x: 0, y: 0, z: 0.5 } }'
sleep 3

echo right
rostopic pub -1 /navigation_velocity_smoother/raw_cmd_vel geometry_msgs/Twist '{ linear: { x: 0, y: 0, z: 0 }, angular: { x: 0, y: 0, z: -0.5 } }'
sleep 3

echo stop
rostopic pub -1 /navigation_velocity_smoother/raw_cmd_vel geometry_msgs/Twist '{ linear: { x: 0, y: 0, z: 0 }, angular: { x: 0, y: 0, z: 0 } }'
sleep 1
done
