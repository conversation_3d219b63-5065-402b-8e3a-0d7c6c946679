#!/bin/bash
#source /opt/ros/indigo/setup.bash
#source ~/npu_ws/devel/setup.bash

while true
do
#echo backward
echo 1
rostopic pub -1 /move_base_simple/goal geometry_msgs/PoseStamped '{ header: { frame_id: "/map" }, pose: { position: { x: 27.301, y: 6.234 }, orientation: { x: 0, y: 0, z: 1, w: 0 } } }'
sleep 20

echo 2
#echo left
rostopic pub -1 /move_base_simple/goal geometry_msgs/PoseStamped '{ header: { frame_id: "/map" }, pose: { position: { x: 27.301, y: 6.234 }, orientation: { x: 0, y: 0, z: 0, w: 1 } } }'
sleep 8

echo 3
#echo right
rostopic pub -1 /move_base_simple/goal geometry_msgs/PoseStamped '{ header: { frame_id: "/map" }, pose: { position: { x: 31.954, y: 6.309 }, orientation: { x: 0, y: 0, z: 0, w: 1 } } }'
sleep 20

echo 4
#echo stop
rostopic pub -1 /move_base_simple/goal geometry_msgs/PoseStamped '{ header: { frame_id: "/map" }, pose: { position: { x: 31.954, y: 6.309 }, orientation: { x: 0, y: 0, z: 1, w: 0 } } }'
sleep 8
done
