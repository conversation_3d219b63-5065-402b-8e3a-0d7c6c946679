#ifndef WIZROBO_BCU_SERVER_H
#define WIZROBO_BCU_SERVER_H
#include <sstream>

#include <pthread.h>

#include <ros/ros.h>
#include <geometry_msgs/Vector3Stamped.h>
#include <geometry_msgs/Twist.h>
#include <dynamic_reconfigure/server.h>
#include <sensor_msgs/Imu.h>
#include <sensor_msgs/BatteryState.h>
#include <sensor_msgs/NavSatFix.h>
#include <sensor_msgs/NavSatStatus.h>
#include <sensor_msgs/Range.h>

#include "std_msgs/Float32.h"
#include "std_msgs/Int32.h"
#include "std_msgs/UInt8.h"
#include <std_msgs/Int64MultiArray.h>

#include "npu.h"
#include "npu_ros_ext.h"
#include "npu_math.h"
#include "npuice_param.h"
#include "npuice_enum_ext.h"

#include <wr_npu_msgs/MotorSpd.h>
#include <wr_npu_msgs/MotorEnc.h>
#include <wr_npu_msgs/SonarData.h>
#include <wr_npu_msgs/InfrdData.h>
#include <wr_npu_msgs/BumperData.h>
#include <wr_npu_msgs/EmergencyStopData.h>
#include <wr_npu_msgs/ComStatus.h>
#include <wr_npu_msgs/DynParam.h>
#include <wr_npu_msgs/ClearMotorEnc.h>
#include <wr_npu_msgs/ExceptionInformation.h>
#include <wr_npu_msgs/VoltageData.h>
#include <wr_npu_msgs/FuncRtnBool.h>
#include <wr_npu_msgs/ScrubberSecurityStatus.h>

#include <wr_bcu_server/BcuServerConfig.h>
#include <wr_bcu_server/PidParamConfig.h>
#include "bcu_driver_creator.h"

namespace wizrobo {
using namespace wizrobo::ros_ext;
using namespace wizrobo::bcu_driver;

class BcuServer
{
protected:
    typedef wr_bcu_server::BcuServerConfig BcuDynamicConfig;
    typedef dynamic_reconfigure::Server<BcuDynamicConfig> BcuDynamicConfigServer;
    typedef wr_bcu_server::PidParamConfig PidDynamicConfig;
    typedef dynamic_reconfigure::Server<PidDynamicConfig> PidDynamicConfigServer;

public:
    BcuServer(int argc, char **argv);
    virtual ~BcuServer();
    void Init();
    virtual void OnInit() {}
    inline bool IsInited()
    {
        return is_inited_;
    }
    void Run();
    virtual void SensorRunOnce();
    virtual void CommanderRunOnce();

protected:
    int ComInit();
    int CommanderInit();
    virtual int OnCommanderInit() {}
    int SensorInit();
    virtual int OnSensorInit() {}
    void SyncAllParam();
    virtual void OnSyncAllParam() {}
    void SyncMotorParam();
    void SyncSensorParam();

    bool MotorDynParamSrvCallback(wr_npu_msgs::DynParam::Request& req, wr_npu_msgs::DynParam::Response& res);
    bool PidDynParamSrvCallback(wr_npu_msgs::DynParam::Request& req, wr_npu_msgs::DynParam::Response& res);
    bool SensorDynParamSrvCallback(wr_npu_msgs::DynParam::Request& req, wr_npu_msgs::DynParam::Response& res);
    bool BrakeSrvCallback(wr_npu_msgs::FuncRtnBool::Request &req, wr_npu_msgs::FuncRtnBool::Response &res);
    bool ClearMotorEncCallback(wr_npu_msgs::ClearMotorEncRequest& rqst, wr_npu_msgs::ClearMotorEncResponse& resp);

    void BcuDynamicConfigCallback(BcuDynamicConfig& config, uint32_t level);
    void PidDynamicConfigCallback(PidDynamicConfig& config, uint32_t level);

    void ExceptionInfoCallback(const wr_npu_msgs::ExceptionInformation::ConstPtr& msg);
    void SecurityStatusCallback(const wr_npu_msgs::ScrubberSecurityStatusConstPtr& msg);
    void RestoreStateCallback(const std_msgs::Bool::ConstPtr& msg);
    void AutoEmStopCallback(const wr_npu_msgs::EmergencyStopData::ConstPtr& msg);

    /// [navi_core] -{/cmd_vel}-> [base_model] -*{/cmd_motor_spd}*-> *[bcu_server]* -{0xSPD}-> [bcu]
    void CmdMotorSpdCallback(const wr_npu_msgs::MotorSpd::ConstPtr& msg);
    void CmdVelCallback(const geometry_msgs::Twist::ConstPtr& msg);

    /// [bcu] -{0xSPD}-> *[bcu_server]* -*{/act_motor_spd}*-> [base_model] -{/act_vel}->
    inline int UpdateActMotorSpd()
    {
        MotorSpdArray act_rpms;
        if (!UpdateActMotorSpd(false, act_rpms)) {
            return FALSE;
        }
        return TRUE;
    }
    int UpdateActMotorSpd(bool pub_only, const MotorSpdArray& act_rpms);

    /// [bcu] -{0xENC}-> *[bcu_server]* -*{/motor_enc}*-> [base_model] -{/odom}->
    int UpdateMotorEnc();

    int UpdateImuData();
    int UpdateSensorData();

    void PubLoopThread();

protected:
    ros::NodeHandle* p_nh_;
    ros::NodeHandle* p_prv_nh_;
    bool is_inited_;
    int main_loop_frq_;
    int msg_pub_frq_;
    bool enb_slave_mode_;
    bool is_emergency_stop_;

    // bcu driver
    IBcuDriver* p_bcu_;
    BcuDriverType driver_type_;
    BcuControlMode control_mode_;
    wizrobo::bcu_driver::BcuParam bcu_param_;

    // frame id
    std::string odom_frame_id_;
    std::string base_frame_id_;
    std::string sonar_frame_id_;
    std::string infrd_frame_id_;
    std::string bumper_frame_id_;
    std::string battery_frame_id_;
    std::string exception_frame_id_;
    std::string imu_frame_id_;
    std::string gps_frame_id_;

    // topic
    std::string emergency_stop_status_topic_;
    std::string entrp_detect_topic_;
    std::string cmd_vel_topic_;
    std::string cmd_motor_spd_topic_;
    std::string act_motor_spd_topic_;
    std::string motor_enc_topic_;
    std::string sonar_data_topic_;
    std::string infrd_data_topic_;
    std::string voltage_data_topic_;
    std::string input_io_topic_;
    std::string output_io_topic_;
    std::string bumper_data_topic_;
    std::string battery_data_topic_;
    std::string exception_info_topic_;
    std::string imu_data_topic_;
    std::string all_imu_data_topic_;
    std::string rpy_data_topic_;
    std::string gps_data_topic_;
    std::string bcu_com_status_topic_;

    // sub & pub
    ros::Subscriber cmd_vel_sub_;
    ros::Subscriber cmd_motor_spd_sub_;
    ros::Subscriber exception_info_sub_;
    ros::Subscriber security_status_sub_;
    ros::Subscriber restore_state_sub_;
    ros::Subscriber soft_emergency_stop_sub_;
    ros::Publisher emergency_stop_status_pub_;
    ros::Publisher entrp_detect_pub_;
    ros::Publisher act_motor_spd_pub_;
    ros::Publisher motor_enc_pub_;
    ros::Publisher raw_motor_enc_pub_;
    std::vector<ros::Publisher> sonar_range_pubs_;
    std::vector<ros::Publisher> infrd_range_pubs_;
    ros::Publisher voltage_data_pub_;
    ros::Publisher input_io_pub_;
    ros::Publisher output_io_pub_;
    ros::Publisher bumper_data_pub_;
    ros::Publisher battery_data_pub_;
    ros::Publisher imu_data_pub_;
    ros::Publisher all_imu_data_pub_;
    ros::Publisher gps_data_pub_;
    ros::Publisher com_status_pub_;
    ros::Publisher rpy_data_pub_;

    /// srv
    ros::ServiceServer cme_srv_;
    ros::ServiceServer brake_srv;

    // motor test
    double left_spd_;
    double right_spd_;
    double steer_angle_deg_;
    wizrobo_npu::SteerEncLocation steer_location_;
    double cmd_linear_vel_;
    double cmd_angular_vel_;

    // runtime data
    wizrobo_npu::ChassisModelType model_type_;
    double roll_, pitch_, yaw_;
    bcu_driver::MotorEncArray last_raw_ticks;
    bcu_driver::MotorEncArray last_ticks;

    // dyn param
    ros::ServiceServer motor_dyn_param_srv_ ;
    ros::ServiceServer pid_dyn_param_srv_ ;
    ros::ServiceServer sensor_dyn_param_srv_ ;

    // dyncfg
    bool is_dyncfg_inited_;
    BcuDynamicConfigServer* p_bcu_dyncfg_;
    PidDynamicConfigServer* p_pid_dyncfg_;

    boost::thread* pub_thread_;
    pthread_spinlock_t motor_enc_msg_lock_;
    wr_npu_msgs::MotorEnc motor_enc_msg_;

    std::vector<sensor_msgs::Range> sonars_range_msg_;
    std::vector<sensor_msgs::Range> infrds_range_msg_;

    double linear_acceleration_stdev_;
    double angular_velocity_stdev_;
    double orientation_x_stdev_;
    double orientation_y_stdev_;
    double orientation_z_stdev_;
    double linear_acceleration_cov_;
    double angular_velocity_cov_;
    double orientation_x_covar_;
    double orientation_y_covar_;
    double orientation_z_covar_;

    int auto_em_stop_;
    bool infrd_em_stop_;
    bool sonar_em_stop_;
    bool bumper_em_stop_;
    bool is_em_stop_restoring_;
    std::vector<int> infrd_status_;
    std::vector<int> sonar_status_;
    std::vector<int> bumper_status_;

};
}// namespace
#endif// WIZROBO_BCU_SERVER_H
