#ifndef _BCU_SCRUBBER_H
#define _BCU_SCRUBBER_H

#include <signal.h>

#include <std_msgs/String.h>
#include <std_msgs/Float32MultiArray.h>

#include <wr_npu_msgs/WaterTank.h>
#include <wr_npu_msgs/SystemStatus.h>
#include <wr_npu_msgs/BatteryStatus.h>

#include <wr_npu_msgs/ScrubberOpt.h>
#include <wr_npu_msgs/ScrubberStrOpt.h>
#include <wr_npu_msgs/ScrubberWorkingStatus.h>
#include <wr_npu_msgs/ScrubberMotorStateALL.h>
#include <wr_npu_msgs/ScrubberWaterValveStatus.h>
#include <wr_npu_msgs/ScrubberWaterLevel.h>
#include <wr_npu_msgs/ScrubberUsageTime.h>
#include <wr_npu_msgs/ScrubberGetMotorStatus.h>

#include <scrubber_struct.h>

#include <npuice_data.h>
#include <npuice_scrubber.h>

#include <bcu_server.h>


namespace wizrobo {

using namespace wizrobo::ros_ext;
using namespace wizrobo::bcu_driver;

const float float_threshold_1_ = 0.01;
const float float_threshold_2_ = 0.2;

const InputChannel FAN    = INPUT_USER_0;
const InputChannel VALVE1 = INPUT_USER_1;
const InputChannel VALVE2 = INPUT_USER_2;
const InputChannel VALVE3 = INPUT_USER_3;

const int BEEP         = OUTPUT_MODE_BLINK_3;

const int OPEN         = OUTPUT_MODE_KEEP_HIGH;
const int CLOSE        = OUTPUT_MODE_KEEP_LOW;

const int LAMP_ON      = OUTPUT_MODE_KEEP_HIGH;
const int LAMP_OFF     = OUTPUT_MODE_KEEP_LOW;


const int BRUSH_MAX    = BRUSHRCC_2000;
const int BRUSH_STOP   = BRUSHRCC_0;
const int BRUSH_QUITE  = BRUSHRCC_500;
const int BRUSH_NORMAL = BRUSHRCC_1000;

const int SB_DOWN = SBRAW;
const int SB_UP = SBCHANGE;

class BcuScrubber : public BcuServer
{
private:
    bool enb_pub_battery_status_;
    bool enb_error_reacting_;

    // bcu driver
    wizrobo::bcu_driver::BcuWorkMode working_status_;

    // topic
    std::string scrb_battery_status_topic_;
    std::string scrb_switch_status_topic_;
    std::string scrb_inner_tmp_data_topic_;
    std::string scrb_inner_hmdt_data_topic_;
    std::string scrb_working_status_topic_;
    std::string scrb_opt_topic_;
    std::string scrb_str_opt_topic_;
    std::string scrb_system_error_topic_;
    std::string scrb_motor_status_topic_;
    std::string scrb_water_valve_status_topic_;
    std::string scrb_water_level_topic_;
    std::string scrb_water_level_asta_topic_;
    std::string scrb_clean_water_level_topic_;
    std::string scrb_dirty_water_level_topic_;
    std::string scrb_usage_time_topic_;

    //npu_state
    std::string npu_idle;
    std::string npu_navi;
    std::string npu_slam;

    std::vector<int> lamp_status_;
    wizrobo_npu_scrubber::SystemStatus working_error_;

    int bl_status_;     /* Breathe Light */
    int lift_status_;
    int fan_lv_;
    int brush_lv_;
    int valve_lv_;
    int valve_duty_;
    int beep_status_;
    int beep_loop_;
    bool is_new_cleaning_cmd_;
    unsigned int valve_loop_;

    float low_power_threshold_;
    float infrd_warning_threshold_;
    float sonar_warning_threshold_;

    // sub & pub
    ros::Publisher battery_status_pub_;
    ros::Publisher switch_status_pub_;
    ros::Publisher inner_tmp_data_pub_;
    ros::Publisher inner_hmdt_data_pub_;
    ros::Publisher sht3x_data_pub_;
    ros::Publisher working_status_pub_;
    ros::Publisher motor_state_pub_;
    ros::Publisher water_valve_pub_;
    ros::Publisher water_level_pub_;
    ros::Publisher water_level_asta_pub_;
    ros::Publisher clean_water_level_pub_;
    ros::Publisher dirty_water_level_pub_;
    ros::Publisher usage_time_pub_;
    ros::Publisher security_status_pub_;

    ros::Subscriber scrubber_opt_sub_;
    ros::Subscriber scrubber_str_opt_sub_;
    ros::Subscriber water_valve_ratio_sub_;
    ros::Subscriber system_error_sub_;

    ros::ServiceClient wheel_motor_status_client_;

    //data
    BmsData bms_data_;

    //msg
    wr_npu_msgs::VoltageData vol_msg_;

public:
    BcuScrubber(int argc, char **argv);
    ~BcuScrubber();

    void OnInit();
    void SensorRunOnce();

private:
    void ScrubberOptCallback(const wr_npu_msgs::ScrubberOpt::ConstPtr& msg);
    void ScrubberStrOptCallback(const wr_npu_msgs::ScrubberStrOpt::ConstPtr& msg);
    void SystemErrorCallback(const wr_npu_msgs::SystemStatus::ConstPtr &msg);
    void ObstacleRestoreCallBack(const std_msgs::BoolConstPtr& msg);

    int UpdateSensorData();
    int UpdateSht3xData();
    int UpdateBmsData();
    int UpdateCleaningState();
    int UpdateLampAndIoState();
    int UpdateVoltageData();
    int UpdateMotorState();
    int UpdateWaterLevel(const SensorData &sensor_data);
    int UpdateUsageTime();

    void ProcessWorkErr();
    void FreshWorkingStatus(const SensorData& sensor_data);
    void PubEmergencyButtonStatus(const SensorData& sensor_data);
    void PubSwitchStatus(const SensorData& sensor_data);
    void PubBumperData(const SensorData& sensor_data);
    void PubRangeData(const SensorData& sensor_data);
    void PubVoltageData(const wr_npu_msgs::VoltageData &voltage_msg);
    void PubBmsData(const wr_npu_msgs::BatteryStatus &msg);
    void PubMotorStatus(const wr_npu_msgs::ScrubberMotorStateALL &motor_status);
    void PubWaterValveStatus(int status, int loop_cnt);
    void PubWaterLevel(const wr_npu_msgs::ScrubberWaterLevel &level);
    void PubUsageTime(const wr_npu_msgs::ScrubberUsageTime &time);

    inline void SetLampStatus(int fl, int fr, int bl, int br) const
    {
        p_bcu_->SetLampStatus(LAMP_FRONT_LEFT, OutputMode(fl), -1);
        p_bcu_->SetLampStatus(LAMP_FRONT_RIGHT, OutputMode(fr), -1);
        p_bcu_->SetLampStatus(LAMP_BACK_LEFT, OutputMode(bl), -1);
        p_bcu_->SetLampStatus(LAMP_BACK_RIGHT, OutputMode(br), -1);
    }

    inline void SetBlnStatus(int status) const
    {
        BlnStatus bln_state;
        switch (status) {
        case BreathLightStatus::BLN_WHITE_BREATHE:
            bln_state = _BlnStatus::WHITEOPEN;
            break;
        case BreathLightStatus::BLN_ALL_BREATHE:
            bln_state = _BlnStatus::ALLOPEN;
            break;
        case BreathLightStatus::BLN_RED_BLINK_1HZ:
            bln_state = _BlnStatus::RED1HZ;
            break;
        case BreathLightStatus::BLN_RED_BLINK_2HZ:
            bln_state =  _BlnStatus::RED2HZ;
            break;
        case BreathLightStatus::BLN_RED_ALWAYS_ON:
            bln_state =  _BlnStatus::REDALON;
            break;
        case BreathLightStatus::BLN_ALL_ALWAYS_ON:
            bln_state =  _BlnStatus::ALLON;
            break;
        default:
            break;
        }
        p_bcu_->SetBlnStatus(BlnStatus(bln_state), OutputMode(0));
    }

    inline void SetBeepStatus(int status, int loop_time) const
    {
        OutputMode mode;
        switch (status) {
        case BeepStatus::BEEP_CLOSE:
            mode = OUTPUT_MODE_KEEP_LOW;
            loop_time = 1;
            break;
        case BeepStatus::BUZZING_1HZ:
            mode = OUTPUT_MODE_BLINK_1;
            loop_time = -1;
            break;
        case BeepStatus::BUZZING_2HZ:
            mode = OUTPUT_MODE_BLINK_2;
            loop_time = -1;
            break;
        case BeepStatus::BLEW_10S:
            mode = OUTPUT_MODE_BLINK_3;
            loop_time = 10;
            break;
        default:
            break;
        }
#if 1
            LM_INFO("mode is :%d",mode);
#endif
            p_bcu_->SetBeepStatus(OutputMode(mode), loop_time);
    }

    inline void SetSbStatus(int status) const
    {
        p_bcu_->SetSbStatus(SbStatus(status), 1);
    }

    inline void SetBrushStatus(int status) const
    {
        BrushRcc brush_rcc;
        switch (status) {
        case SpeedLevel::STOP:
            brush_rcc = BRUSHRCC_0;
            break;
        case SpeedLevel::LOW:
            brush_rcc = BRUSHRCC_500;
            break;
        case SpeedLevel::MEDIUM:
            brush_rcc = BRUSHRCC_1000;
            break;
        case SpeedLevel::HIGH:
            brush_rcc = BRUSHRCC_1500;
            break;
        case SpeedLevel::MAXSPD:
            brush_rcc = BRUSHRCC_2000;
            break;
        default:
            break;
        }
        p_bcu_->SetBrushStatus(BRUSHCHANNEL, brush_rcc, 1);
    }

    inline void SetFanStatus(int status) const
    {
        LM_DEBUG("status: %d", status);
        FanRcc fan_rcc;
        switch (status) {
        case SpeedLevel::STOP:
            fan_rcc = FANRCC_STOP;
            break;
        case SpeedLevel::LOW:
            fan_rcc = FANRCC_LOW;
            break;
        case SpeedLevel::MEDIUM:
            fan_rcc = FANRCC_MEDIUM;
            break;
        case SpeedLevel::HIGH:
            fan_rcc = FANRCC_HIGH;
            break;
        case SpeedLevel::MAXSPD:
            fan_rcc = FANRCC_MAX;
            break;
        default:
            break;
        }
        p_bcu_->SetFanStatus(FANCHANNEL, fan_rcc, 1);
    }

    inline void SetValveStatus(int status, int loop_cnt) const
    {
        LM_DEBUG("status: %d, loop_cnt: %d", status, loop_cnt);
        OutputMode output = (status==STOP)?OUTPUT_MODE_KEEP_HIGH:OUTPUT_MODE_KEEP_LOW;
        if(status == 0){
            output = OUTPUT_MODE_KEEP_LOW;
        } else{
            output = OUTPUT_MODE_KEEP_HIGH;
        }
        LM_DEBUG("OutputMode: %d", output);
        p_bcu_->SetUserIo(InputChannel(VALVE1), output, loop_cnt);
        p_bcu_->SetUserIo(InputChannel(VALVE2), output, loop_cnt);
        p_bcu_->SetUserIo(InputChannel(VALVE3), output, loop_cnt);
    }
};
}// namespace
#endif// WIZROBO_BCU_SERVER_H
