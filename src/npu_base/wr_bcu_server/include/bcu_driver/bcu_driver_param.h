#ifndef WIZROBO_BCU_DRIVER_BCU_DRIVER_PARAM_H
#define WIZROBO_BCU_DRIVER_BCU_DRIVER_PARAM_H
#include <npu.h>
#include <npu_math.h>
#include <npu_enum_ext.h>
#include "npuice_enum_ext.h"
#include "bcu_driver.h"
#include <bitset>

namespace wizrobo { namespace bcu_driver {
using namespace wizrobo::str_ext;

static const int MAX_MOTOR_NUM = 4;
static const int MAX_SONAR_NUM = 12;
static const int MAX_INFRD_NUM = 8;

//// com
enum BcuDriverType
{
    SERIAL_ASCII = 0u,
    SERIAL_HEX = 1u,
    CAN = 2u,
    ETHERNET = 3u,
    SERIAL_KINCO = 4u,
    SERIAL_KEYA = 5u,
    SERIAL_SIM = 6u,
    SERIAL_NBOX = 7u,
    SERIAL_FDK = 8u,
    SERIAL_CSG = 9u,    
    SERIAL_FAKE = 10u,
    SERIAL_ZXFDK = 11u,
    SERIAL_AMPS = 12u
};

// control
enum BcuControlMode
{
    MOTOR_CONTROL_UNIT    = 1u,
    DATA_ACQUISITION_UNIT = 2u,
    INTEGRATED            = 3u,
};

// aux
enum IoAuxMode
{
    AUX_ALM = 0u,
    AUX_DIR = 1u,
};
//workerror
enum BcuWorkErrorType
{
    NO_ERROR = 0u,
    EMERGENCY_STOP = 1u,
    BATTERY_LOW = 2u,
    PUR_WATER_HIGH = 3u,
    PUR_WATER_LOW = 4u,
    SEW_WATER_HIGH = 5u,
};

}}// namespace

namespace wizrobo { namespace enum_ext {
using namespace wizrobo::bcu_driver;

// string support of BcuDriverType
BEGIN_ENUM_STRING(BcuDriverType)
{
    ENUM_STRING(SERIAL_ASCII);
    ENUM_STRING(SERIAL_HEX);
    ENUM_STRING(CAN);
    ENUM_STRING(ETHERNET);
    ENUM_STRING(SERIAL_KINCO);
    ENUM_STRING(SERIAL_KEYA);
    ENUM_STRING(SERIAL_SIM);
    ENUM_STRING(SERIAL_NBOX);
    ENUM_STRING(SERIAL_FDK);
    ENUM_STRING(SERIAL_CSG);
    ENUM_STRING(SERIAL_FAKE);
    ENUM_STRING(SERIAL_ZXFDK);
    ENUM_STRING(SERIAL_AMPS);
}
END_ENUM_STRING;

// string support for BcuControlMode
BEGIN_ENUM_STRING(BcuControlMode)
{
    ENUM_STRING(MOTOR_CONTROL_UNIT);
    ENUM_STRING(DATA_ACQUISITION_UNIT);
    ENUM_STRING(INTEGRATED);
}
END_ENUM_STRING;

// string support of BcuWorkErrorType
BEGIN_ENUM_STRING(BcuWorkErrorType)
{
    ENUM_STRING(NO_ERROR);
    ENUM_STRING(EMERGENCY_STOP);
    ENUM_STRING(BATTERY_LOW);
    ENUM_STRING(PUR_WATER_HIGH)
    ENUM_STRING(PUR_WATER_LOW);
    ENUM_STRING(SEW_WATER_HIGH);
}
END_ENUM_STRING;

}}// namespace

namespace wizrobo { namespace bcu_driver {

// com param
struct ComParam
{
    ComParam()
    {
#if WR_WINNT
        port_id = 0;
#elif WR_UBUNTU
        port_id = "/dev/bcu";
#endif
        cmd_retry_num = 3;
        cmd_delay_ms = 5;
        ans_retry_num = 20;
        ans_delay_ms = 5;
        enb_com_data_display = false;
        baudrate = B115200;
    }
    std::string ToStr()
    {
        std::stringstream ss;
        ss << "\n-------- ComParam --------\n"
           << "- port_id: " << port_id << "\n"
           << "- cmd_retry_num: " << cmd_retry_num << "\n"
           << "- cmd_delay_ms: " << cmd_delay_ms << "\n"
           << "- ans_retry_num: " << ans_retry_num << "\n"
           << "- ans_delay_ms: " << ans_delay_ms << "\n"
           << "- baudrate: " << baudrate << "\n"
           << "- enb_com_data_display: " << BoolToStr(enb_com_data_display) << "\n";
        return ss.str();
    }
    ComPortIdType port_id;
    int cmd_retry_num;
    int cmd_delay_ms;
    int ans_retry_num;
    int ans_delay_ms;
    bool enb_com_data_display;
    int baudrate;
};

// motor param
struct MotorParam
{
    MotorParam()
    {
        motor_num = 2;
        motor_dir_str = "--";
        motor_max_spd_rpm = 1500;
        motor_rdc_ratio = 10.0;
        motor_enc_res_ppr = 500;
        motor_pwm_frq_hz = 1000;
        motor_brk_type = wizrobo_npu::SOFT_BRK;
        enb_vol = wizrobo_npu::LOW_VALID;
        dir_vol = wizrobo_npu::LOW_VALID;
        pwm_vol = wizrobo_npu::HIGH_VALID;
        brk_vol = wizrobo_npu::LOW_VALID;
        enb_auxdir_mode = false;
        enb_throttle_mode = false;
        throttle_zero_pos_fac = 0.125f;
        throttle_zero_neg_fac = 0.115f;
        max_acc_time_s = 3.0f;
        max_dec_time_s = 1.0f;
        end_left_right_switch = false;
        enc_pos_scales.resize(motor_num, 1.0f);
        enc_neg_scales.resize(motor_num, 1.0f);
        motor_hall_spd_scale = 1.0f;

        enc_dir_str = "++";
        enb_slow_launch = false;
        motor_start_rpm = 30;
        motor_acc_rpm = -1;
        dac_bit_width = 12;
        dac_ref_v = 0.0;
        dac_offset_v_left = 0.0;
        dac_offset_v_right = 0.0;
    }

    MotorParam(const wizrobo_npu::MotorParam& npu_motor_param)
    {
        motor_num = npu_motor_param.motor_num;
        motor_dir_str = npu_motor_param.motor_dir_str;
        motor_max_spd_rpm = npu_motor_param.motor_max_spd_rpm;
        motor_rdc_ratio = npu_motor_param.motor_rdc_ratio;
        motor_enc_res_ppr = npu_motor_param.motor_enc_res_ppr;
        motor_pwm_frq_hz = npu_motor_param.motor_pwm_frq_hz;
        motor_brk_type = npu_motor_param.motor_brk_type;
        enb_vol = npu_motor_param.enb_vol;
        dir_vol = npu_motor_param.dir_vol;
        pwm_vol = npu_motor_param.pwm_vol;
        brk_vol = npu_motor_param.brk_vol;
        enb_auxdir_mode = npu_motor_param.enb_auxdir_mode;
        enb_throttle_mode = npu_motor_param.enb_throttle_mode;
        throttle_zero_pos_fac = npu_motor_param.throttle_zero_pos_fac;
        throttle_zero_neg_fac = npu_motor_param.throttle_zero_neg_fac;
        end_left_right_switch = npu_motor_param.end_left_right_switch;

        enc_dir_str = "++";
        enb_slow_launch = false;
        motor_start_rpm = 30;
        motor_acc_rpm = -1;
        dac_bit_width = 12;
        dac_ref_v = 0.0;
        dac_offset_v_left = 0.0;
        dac_offset_v_right = 0.0;
    }

    std::string ToStr()
    {
        std::stringstream ss;
        ss << "\n-------- MotorParam --------\n"
           << "- motor_num: " << motor_num << "\n"
           << "- motor_dir_str: " << motor_dir_str << "\n"
           << "- motor_max_spd_rpm: " << motor_max_spd_rpm << "\n"
           << "- motor_rdc_ratio: " << motor_rdc_ratio << "\n"
           << "- motor_enc_res_ppr: " << motor_enc_res_ppr << "\n"
           << "- motor_pwm_frq_hz: " << motor_pwm_frq_hz << "\n"
           << "- motor_brk_type: "
           << enum_ext::EnumString<wizrobo_npu::BrakeType>::EnumToStr(motor_brk_type) << "\n"
           << "- enb_vol: "
           << enum_ext::EnumString<wizrobo_npu::ValidOutputLevel>::EnumToStr(enb_vol) << "\n"
           << "- dir_vol: "
           << enum_ext::EnumString<wizrobo_npu::ValidOutputLevel>::EnumToStr(dir_vol) << "\n"
           << "- pwm_vol: "
           << enum_ext::EnumString<wizrobo_npu::ValidOutputLevel>::EnumToStr(pwm_vol) << "\n"
           << "- brk_vol: "
           << enum_ext::EnumString<wizrobo_npu::ValidOutputLevel>::EnumToStr(brk_vol) << "\n"
           << "- enb_auxdir_mode: " << str_ext::BoolToStr(enb_auxdir_mode) << "\n"
           << "- enb_throttle_mode: " << str_ext::BoolToStr(enb_throttle_mode) << "\n"
           << "- throttle_zero_pos_fac: " << throttle_zero_pos_fac << "\n"
           << "- throttle_zero_neg_fac: " << throttle_zero_neg_fac << "\n"
           << "- max_acc_time_s: " << max_acc_time_s << "\n"
           << "- max_dec_time_s: " << max_dec_time_s << "\n"
           << "- end_left_right_switch: " << str_ext::BoolToStr(end_left_right_switch) << "\n"
           << "- enc_pos_scales: [" << str_ext::FloatVecToStr(enc_pos_scales, ", ") << "]\n"
           << "- enc_neg_scales: [" << str_ext::FloatVecToStr(enc_neg_scales, ", ") << "]\n"
           << "- enc_dir_str: " << enc_dir_str << "\n"
           << "- enb_slow_launch: " << str_ext::BoolToStr(enb_slow_launch) << "\n"
           << "- motor_start_rpm: " << motor_start_rpm << "\n"
           << "- motor_acc_rpm: " << motor_acc_rpm << "\n"
           << "- dac_bit_width: " << dac_bit_width  << "\n"
           << "- dac_ref_v: " << dac_ref_v  << "\n"
           << "- dac_offset_v_left: " << dac_offset_v_left << "\n"
           << "- dac_offset_v_right: " << dac_offset_v_right << "\n";
        return ss.str();
    }
    static std::string DirByteToStr(const byte& dir_byte, int motor_num)
    {
        std::string dir_str(motor_num, ' ');
        byte b = dir_byte;
        for (int i = 0; i < motor_num; i++)
        {
            dir_str[i] = (b & 0x80) != 0 ? '+' : '-';
            b <<= 1;
        }
        return dir_str;
    }
    static byte DirStrToByte(const std::string& dir_str, int motor_num)
    {
        byte dir_byte = 0x00;
        if (!dir_str.empty())
        {
            int num = MIN(dir_str.size(), motor_num);
            byte mask = 0x80;
            for (int i = 0; i < num; i++)
            {
                if (dir_str[i] == '-')
                {
                    dir_byte &= ~mask;
                }
                else
                {
                    dir_byte |= mask;
                }
                mask >>= 1;
            }
        }
        return dir_byte;
    }

    inline int VoltageToControlWord(float voltage, int bit_width, float ref_v)
    {
        int max = 0xFFFFFFFF >> (32-bit_width) + 1;
        return (int)(voltage / ref_v * max);
    }
    inline float ControlWordToVoltage(int control_word, int bit_width, float ref_v)
    {
        int max = 0xFFFFFFFF >> (32-bit_width) + 1;
        return ref_v * control_word / max;
    }

    inline int Dav2w(float voltage)
    {
        return VoltageToControlWord(voltage, dac_bit_width, dac_ref_v);
    }
    inline float Daw2v(int control_word)
    {
        return ControlWordToVoltage(control_word, dac_bit_width, dac_ref_v);
    }

    // motor
    int motor_num;//
    std::string motor_dir_str;// "++--++--"
    std::string enc_dir_str;// "++--++--"
    int motor_max_spd_rpm;// [Rotation Per Minute]
    float motor_rdc_ratio;
    int motor_enc_res_ppr;// [Pulse Per Rotation]
    int motor_pwm_frq_hz;// [Hz]
    wizrobo_npu::BrakeType motor_brk_type;
    // io valid level
    wizrobo_npu::ValidOutputLevel enb_vol;
    wizrobo_npu::ValidOutputLevel dir_vol;
    wizrobo_npu::ValidOutputLevel pwm_vol;
    wizrobo_npu::ValidOutputLevel brk_vol;
    // aux mode
    bool enb_auxdir_mode;
    // throttle mode
    bool enb_throttle_mode;
    // throttle fac
    float throttle_zero_pos_fac;
    float throttle_zero_neg_fac;
    // acc & dec
    float max_acc_time_s;
    float max_dec_time_s;
    /// npu-only
    // left-right switch
    bool end_left_right_switch;
    std::vector<float> enc_pos_scales;
    std::vector<float> enc_neg_scales;

    float motor_hall_spd_scale;

    int dac_bit_width;
    float dac_ref_v;
    float dac_offset_v_left;
    float dac_offset_v_right;

    bool enb_slow_launch;
    float motor_start_rpm;
    float motor_acc_rpm;
};

struct AdcSensorParam
{
    float k;
    float offset;
    float threshold;
};

// motor param
struct SensorParam
{
    SensorParam()
    {
        bmp_vol = wizrobo_npu::HIGH_VALID;
        esb_vol = wizrobo_npu::HIGH_VALID;
        sw_vol  = wizrobo_npu::HIGH_VALID;
        brk_vol = wizrobo_npu::HIGH_VALID;
        dead_time = 15;

        sonar_threshold.resize(0);
        infrd.resize(0);
        main_power.resize(1);
        battery.resize(1);

        enb_imu = false;
        enb_gps = false;
    }

    SensorParam(const wizrobo_npu::SensorParam& param)
    {
        SensorParam();
    }

    std::string ToStr()
    {
        std::stringstream ss;
        ss << "\n-------- SensorParam --------\n"
           << "- bmp_vol: " << enum_ext::EnumString<wizrobo_npu::ValidOutputLevel>::EnumToStr(bmp_vol)
           << "\n"
           << "- esb_vol: " << enum_ext::EnumString<wizrobo_npu::ValidOutputLevel>::EnumToStr(esb_vol)
           << "\n"
           << "- brk_vol: " << enum_ext::EnumString<wizrobo_npu::ValidOutputLevel>::EnumToStr(brk_vol)
           << "\n";
        for(int i;i<infrd.size();i++)
        {
            ss << "- infrd[" << i << "](k/offset/threshold): "
               << infrd[i].k << "/" << infrd[i].offset << "/" << infrd[i].threshold
               << "\n";
        }
        ss << "- main_power(k/offset/threshold): "
           << main_power[0].k << "/" << main_power[0].offset << "/" << main_power[0].threshold << "\n"
           << "- battery   (k/offset/threshold): "
           << battery[0].k << "/" << battery[0].offset << "/" << battery[0].threshold << "\n"
           << "- enb_imu: " << enb_imu << "\n"
           << "- enb_gps: " << enb_gps << "\n";
        return ss.str();
    }

    wizrobo_npu::ValidOutputLevel bmp_vol;
    wizrobo_npu::ValidOutputLevel esb_vol;
    wizrobo_npu::ValidOutputLevel sw_vol;
    wizrobo_npu::ValidOutputLevel brk_vol;
    int dead_time;
    bool enb_imu;
    bool enb_gps;
    std::vector<float> sonar_threshold;
    std::vector<AdcSensorParam> infrd;
    std::vector<AdcSensorParam> battery;
    std::vector<AdcSensorParam> main_power;
};

// pid param
struct PidParam
{
    PidParam()
    {
        enb_pid = false;
        kp_acc = 0;
        kp_dec = 0;
        ki_acc = 0;
        ki_dec = 0;
        kd_acc = 0;
        kd_dec = 0;
    }
    std::string ToStr()
    {
        std::stringstream ss;
        ss << "\n-------- PidParam --------\n"
           << "- enb_pid: " << str_ext::BoolToStr(enb_pid) << "\n"
           << "- kp_acc: " << kp_acc << "\n"
           << "- kp_dec: " << kp_dec << "\n"
           << "- ki_acc: " << ki_acc << "\n"
           << "- ki_dec: " << ki_dec << "\n"
           << "- ki_acc: " << kd_acc << "\n"
           << "- ki_dec: " << kd_dec << "\n";
        return ss.str();
    }
    bool enb_pid;
    float kp_acc;
    float kp_dec;
    float ki_acc;
    float ki_dec;
    float kd_acc;
    float kd_dec;
};

struct BcuWorkMode
{
    bool is_battery_low;
    bool is_emergency_stop;
    bool is_purwater_high;
    bool is_purwater_low;
    bool is_sewwater_high;

    BcuWorkMode()
    {
        is_battery_low = false;
        is_emergency_stop = false;
        is_purwater_high = false;
        is_purwater_low = false;
        is_sewwater_high = false;
    }

    std::string ToStr()
    {
        std::stringstream ss;
        ss << "\n-------- BcuWorkMode --------\n"
           << "- is_emergency_stop : " << str_ext::BoolToStr(is_battery_low) << "\n"
           << "- is_emergency_stop : " << str_ext::BoolToStr(is_emergency_stop) << "\n"
           << "- is_purwater_high  : " << str_ext::BoolToStr(is_purwater_high) << "\n"
           << "- is_purwater_low   : " << str_ext::BoolToStr(is_purwater_low) << "\n"
           << "- is_sewwater_high  : " << str_ext::BoolToStr(is_sewwater_high) << "\n";
        return ss.str();
    }
};

struct BcuParam
{
    ComParam com;
    MotorParam motor;
    SensorParam sensor;
    PidParam pid;
};

typedef BcuParam* BcuParamPtr;
}}
#endif // WIZROBO_BCU_BCU_DRIVER_PARAM_INTERFACE_H
