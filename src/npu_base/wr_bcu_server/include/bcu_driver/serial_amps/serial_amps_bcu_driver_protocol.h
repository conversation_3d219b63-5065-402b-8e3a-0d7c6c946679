#ifndef WIZROBO_BCU_DRIVER_SERIAL_AMPS_BCU_DRIVER_PROTOCOL_H
#define WIZROBO_BCU_DRIVER_SERIAL_AMPS_BCU_DRIVER_PROTOCOL_H

#include "bcu_driver_interface.h"

namespace wizrobo { namespace bcu_driver { namespace serial_amps{
const unsigned char LEFT_MOTOR_ADD       = 0x03;
const unsigned char RIGHT_MOTOR_ADD      = 0x02;
const int DATALENGTH = 9;
const int TOTALLENGTH = 10;
//READ
const unsigned char READ_CMD             = 0xA0;
const unsigned char READ_ANS_8BIT        = 0xA1;
const unsigned char READ_ANS_16BIT       = 0xA2;
const unsigned char READ_ANS_32BIT       = 0xA4;
const unsigned char READ_ADD_NOTFOUND    = 0x5F;
const unsigned char READ_ERROR_CODE      = 0x00;

const unsigned char READ_DATA[4]         = {0x00, 0x00, 0x00, 0x00};

const unsigned char GET_L_MTR_SPD_ADD[10] = {LEFT_MOTOR_ADD, READ_CMD, 0x70, 0x75, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x88};
const unsigned char GET_R_MTR_SPD_ADD[10] = {RIGHT_MOTOR_ADD, READ_CMD, 0x70, 0x75, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x87};
const unsigned char GET_L_MTR_ENC_ADD[10] = {LEFT_MOTOR_ADD, READ_CMD, 0x70, 0x71, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x84};
const unsigned char GET_R_MTR_ENC_ADD[10] = {RIGHT_MOTOR_ADD, READ_CMD, 0x70, 0x71, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x83};
const unsigned char GET_L_MTR_VOL_ADD[10] = {LEFT_MOTOR_ADD, READ_CMD, 0x50, 0x01, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0xF4};
const unsigned char GET_R_MTR_VOL_ADD[10] = {RIGHT_MOTOR_ADD, READ_CMD, 0x50, 0x01, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0xF3};
const unsigned char GET_L_MTR_CUR_ADD[10] = {LEFT_MOTOR_ADD, READ_CMD, 0x70, 0x07, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x1A};
const unsigned char GET_R_MTR_CUR_ADD[10] = {RIGHT_MOTOR_ADD, READ_CMD, 0x70, 0x07, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x19};

const unsigned char GET_L_MTR_ERR_STE[10] = {LEFT_MOTOR_ADD, READ_CMD, 0x70, 0x11, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x24};
const unsigned char GET_R_MTR_ERR_STE[10] = {RIGHT_MOTOR_ADD, READ_CMD, 0x70, 0x11, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x23};


//SET
const unsigned char SET_CMD_8BIT         = 0x51;
const unsigned char SET_CMD_16BIT        = 0x52;
const unsigned char SET_CMD_32BIT        = 0x54;
const unsigned char SET_MTR_SPD[2]       = {0x70, 0xB1};
const unsigned char CLR_ENC_DATA[4]      = {0x00, 0x00, 0x00, 0x01};

const unsigned char SET_L_MTR_SPD[10]     = {LEFT_MOTOR_ADD, SET_CMD_16BIT, 0x70, 0xB1, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x00};
const unsigned char SET_R_MTR_SPD[10]     = {RIGHT_MOTOR_ADD, SET_CMD_16BIT, 0x70, 0xB1, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x00, 0x00};
const unsigned char CLR_L_MTR_ENC_ADD[10] = {LEFT_MOTOR_ADD, SET_CMD_8BIT, 0x70, 0xAC, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x01, 0x00};
const unsigned char CLR_R_MTR_ENC_ADD[10] = {RIGHT_MOTOR_ADD, SET_CMD_8BIT, 0x70, 0xAC, READ_ERROR_CODE, 0x00, 0x00, 0x00, 0x01, 0x00};

const unsigned char SET_L_MOTOR_ENB[10] = {LEFT_MOTOR_ADD,0x52,0x70,0x19,0x00,0x00,0x00,0x00,0x0F,0xED};
const unsigned char SET_R_MOTOR_ENB[10] = {RIGHT_MOTOR_ADD,0x52,0x70,0x19,0x00,0x00,0x00,0x00,0x0F,0xEC};

const unsigned char SET_L_MOTOR_PARAM_1[10] = {LEFT_MOTOR_ADD,0x52,0x70,0xE8,0x00,0x00,0x00,0x00,0x14,0xC1};  //64better 64 noise
const unsigned char SET_L_MOTOR_PARAM_2[10] = {LEFT_MOTOR_ADD,0x52,0x70,0xE7,0x00,0x00,0x00,0x01,0x2c,0xD9};  //01f4better 01f4 noise

const unsigned char SET_R_MOTOR_PARAM_1[10] = {RIGHT_MOTOR_ADD,0x52,0x70,0xE8,0x00,0x00,0x00,0x00,0x14,0xC0};  //64better 64 noise
const unsigned char SET_R_MOTOR_PARAM_2[10] = {RIGHT_MOTOR_ADD,0x52,0x70,0xE7,0x00,0x00,0x00,0x01,0x2c,0xD8};  //01f4better 01f4 noise

const unsigned char SET_L_MOTOR_P[10]       = {LEFT_MOTOR_ADD,0x52,0x70,0xB3,0x00,0x00,0x00,0x00,0x0a,0x00}; //03better 0c noise
const unsigned char SET_L_MOTOR_I[10]       = {LEFT_MOTOR_ADD,0x52,0x70,0xB4,0x00,0x00,0x00,0x00,0x14,0x00};  //1430niu 14ok 20niu 07 better 07 noise
const unsigned char SET_L_MOTOR_DEC[10]     = {LEFT_MOTOR_ADD,0x54,0x70,0x9a,0x00,0x00,0x00,0x00,0x20,0x00};
const unsigned char SET_L_MOTOR_ACC[10]     = {LEFT_MOTOR_ADD,0x54,0x70,0x99,0x00,0x00,0x00,0x00,0x20,0x00};

const unsigned char SET_R_MOTOR_P[10]       = {RIGHT_MOTOR_ADD,0x52,0x70,0xB3,0x00,0x00,0x00,0x00,0x0a,0x00}; //03better 0c noise
const unsigned char SET_R_MOTOR_I[10]       = {RIGHT_MOTOR_ADD,0x52,0x70,0xB4,0x00,0x00,0x00,0x00,0x14,0x00};  //1430niu 14ok 20niu 07 better 07 noise
const unsigned char SET_R_MOTOR_DEC[10]     = {RIGHT_MOTOR_ADD,0x54,0x70,0x9a,0x00,0x00,0x00,0x00,0x20,0x00};
const unsigned char SET_R_MOTOR_ACC[10]     = {RIGHT_MOTOR_ADD,0x54,0x70,0x99,0x00,0x00,0x00,0x00,0x20,0x00};

struct SpdChar
{
    SpdChar() {
    }
    unsigned char spd_cmd_1;
    unsigned char spd_cmd_2;
};

struct U16Char
{
    U16Char() {
    }
    unsigned char data_1;
    unsigned char data_2;
};

struct U32Char
{
    U32Char() {
    }
    unsigned char data_1;
    unsigned char data_2;
    unsigned char data_3;
    unsigned char data_4;
};


typedef struct _AmpsFrame
{
    unsigned char motor_address;
    unsigned char cmd;
    unsigned char cmd_address_1;
    unsigned char cmd_address_2;
    unsigned char error_code;
    unsigned char data[4];
    unsigned char check_sum;
}AmpsFrame;

const std::string PARITY("EVEN");
const int BAUDRATE = B9600;
const int DATABITS = 7;

const int MAX_MOTOR_SPD = 1500;
const int MIN_MOTOR_SPD = -1500;
const int CMD_LEN = 3;
const int MIN_ANS_LEN = 9;
const int MOTOR_ID_MAP[2][2] = {
    { 0, 1},
    { 1, 0},
};


typedef struct _RtuMotorStatus
{
    _RtuMotorStatus()
    {
        left_motor_pose = 0;
        right_motor_pose = 0;
        left_motor_spd = 0;
        right_motor_spd = 0;
        left_motor_current = 0;
        right_motor_current = 0;
        left_motor_voltage = 0;
        right_motor_voltage = 0;
        left_motor_state_signal = 0;
        right_motor_state_signal = 0;
        left_motor_error_signal = 0;
        right_motor_error_signal = 0;
        motor_driver_temp = 0;
    }
    void PrintData()
    {
        printf("left_motor_pose is %d\n", left_motor_pose);
        printf("right_motor_pose is %d\n", right_motor_pose);
        printf("left_motor_spd is %d\n", left_motor_spd);
        printf("right_motor_spd is %d\n", right_motor_spd);
        printf("left_motor_current is %.3f\n", left_motor_current);
        printf("right_motor_current is %.3f\n", right_motor_current);
        printf("left_motor_state_signal is %d\n", left_motor_state_signal);
        printf("right_motor_state_signal %d\n", right_motor_state_signal);
        printf("left_motor_error_signal %d\n", left_motor_error_signal);
        printf("right_motor_error_signal %d\n", right_motor_error_signal);
        printf("left_motor_voltage %.3f\n", left_motor_voltage);
        printf("right_motor_voltage %.3f\n", right_motor_voltage);
        printf("motor_driver_temp %d\n", motor_driver_temp);
    }
    int left_motor_pose;
    int right_motor_pose;
    short left_motor_spd;
    short right_motor_spd;
    float left_motor_current;
    float right_motor_current;
    float left_motor_voltage;
    float right_motor_voltage;
    unsigned short left_motor_state_signal;
    unsigned short right_motor_state_signal;
    unsigned short left_motor_error_signal;
    unsigned short right_motor_error_signal;
    short motor_driver_temp;
}RtuMotorStatus;

typedef struct _AmpsPidParam
{
    bool enb_pid;
    unsigned short p;
    unsigned short i;
    unsigned short dec;
    unsigned short acc;

}AmpsPidParam;

}}} // namespace

#endif  // WIZROBO_BCU_DRIVER_SERIAL_AMPS_BCU_DRIVER_PROTOCOL_H
