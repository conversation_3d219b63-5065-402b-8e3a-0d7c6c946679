#ifndef WIZROBO_BCU_DRIVER_AMPS_SERIAL_BCU_DRIVER_H
#define WIZROBO_BCU_DRIVER_AMPS_SERIAL_BCU_DRIVER_H

#include "serial_amps_bcu_driver_protocol.h"
#include "wr_npu_msgs/ScrubberGetMotorStatus.h"
#include "wr_npu_msgs/ScrubberWorkingStatus.h"

namespace wizrobo { namespace bcu_driver {

class SerialAmpsBcuDriver : public IBcuDriver
{
public:
    SerialAmpsBcuDriver();
    ~SerialAmpsBcuDriver();

    int Init(BcuParamPtr ptr);
    int Stop();

    int SetPidParam();
    int ClrMotorEnc();
    int GetMotorSpd(MotorSpdArray& rpm); // hall sensor
    int GetMotorEnc(MotorEncArray& ticks);  // enc
    int SetMotorSpd(const MotorSpdArray& spd); // -1000~1000
    int GetMotorStatus(MotorStatus &motor_status);
    int SwpMotorSpd(const MotorSpdArray& cmd_rpm, MotorSpdArray& act_rpm)  {}
private:
    int ReceiveAns(char* ans);
    int CheckAns(unsigned char *ans);
    int WriteCmd(unsigned char* cmd, int cmd_len);
    int SetMotorPid(const serial_amps::AmpsPidParam &param);
    int SendRequest(unsigned char *req, string cmd);
    int SendRequest(unsigned char *req, string cmd, int data);

    unsigned char GetCheckSum(unsigned char* data, int len);
    bool EnbDriver(bool flag);
    bool MotorStatusCallBack(wr_npu_msgs::ScrubberGetMotorStatus::Request &req,
                             wr_npu_msgs::ScrubberGetMotorStatus::Response &res);
    void EmergencyStopStatusCallBack(const wr_npu_msgs::ScrubberWorkingStatusConstPtr &msg);


    void PrintFrame(unsigned char *ans, string func);

private:
    bool emergencystop_state_;
    bool last_emergencystop_state_;
    SerialPort serial_port_;
    serial_amps::RtuMotorStatus motor_status_;

    ros::ServiceServer motor_status_service_;
    ros::Subscriber working_status_sub_;

};

}}  // namespace

#endif  // WIZROBO_BCU_DRIVER_FDK_SERIAL_BCU_DRIVER_H
