#ifndef WIZROBO_BCU_DRIVER_SERIAL_FAKE_BCU_DRIVER_H
#define WIZROBO_BCU_DRIVER_SERIAL_FAKE_BCU_DRIVER_H

#include "bcu_driver_interface.h"

namespace wizrobo { namespace bcu_driver {

class SerialFakeBcuDriver : public IBcuDriver
{
public:
    SerialFakeBcuDriver()
    {
    }

    virtual ~SerialFakeBcuDriver()
    {
    }

    int Init(BcuParamPtr ptr);

    int GetMotorSpd(MotorSpdArray& rpms);
    int GetMotorEnc(MotorEncArray& ticks);
};
}}// namespace
#endif// WIZROBO_BCU_DRIVER_SERIAL_HEX_BCU_DRIVER_H
