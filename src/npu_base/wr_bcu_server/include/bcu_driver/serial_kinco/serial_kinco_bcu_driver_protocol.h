#ifndef WIZROBO_BCU_DRIVER_SERIAL_KINCO_BCU_DRIVER_PROTOCOL_H
#define WIZROBO_BCU_DRIVER_SERIAL_KINCO_BCU_DRIVER_PROTOCOL_H
#include "bcu_driver_interface.h"

/**
 * Version: 1.0
 * Date: 2017-06-18
 * Author: lawrence.han
 */
namespace wizrobo { namespace bcu_driver { namespace serial_kinco{
typedef std::deque<byte> AnsArrayType;
typedef std::vector<byte> CmdArrayType;

enum CmdType// = [CMD]
{
    CLR_MTR_ALM = 0x23406000,
    SET_MTR_ENB = 0x23406000,
    SET_SPD_MOD = 0x23606000,
    SET_MTR_ACC = 0x23608300,
    SET_MTR_DEC = 0x23608400,

    SET_MTR_SPD = 0x23FF6000,

    GET_MTR_SPD = 0x40F96018,
    GET_MTR_ENC = 0x40636000,
};

//// com
const std::string PARITY("NONE");
const int BAUDRATE = B115200;
const int DATABITS = 8;

//// cmd
static const int MAX_CMD_LEN = 50;
static const int CMD_IDX = 1;
static const int CMD_BYTT_LEN = 4;

static const byte MTR_CLR_ALM = 0x86;
static const uchar MTR_PWR_ON = 0x0F;
static const uchar MTR_PWR_OFF = 0x06;

static const uchar MTR_INS_SPD_MOD = 0xFD;
static const uchar MTR_TRP_SPD_MOD = 0x03;
//// ans
static const int ANS_LEN = 10;
static const uchar ANS_ERR_CODE = 0x80;
static const int ANS_DATA_START = 5;
static const int ANS_DATA_LEN = 4;
static const int ANS_BIT_CODE_IDX = 1;
static const uchar ANS_32BIT_CODE = 0x43;
static const uchar ANS_16BIT_CODE = 0x4B;
static const uchar ANS_8BIT_CODE = 0x4F;

enum SpdCtlMode
{
    INSTANT_MODE,
    TRAPEZOID_MODE,
};

const int MOTOR_ID_MAP[2][2] = {
    {1, 2},
    {2, 1}
};

}}}// namespace
#endif// WIZROBO_BCU_DRIVER_SERIAL_KINCO_BCU_DRIVER_PROTOCOL_H
