#ifndef WIZROBO_BCU_DRIVER_KINCO_SERIAL_HEX_BCU_DRIVER_H
#define WIZROBO_BCU_DRIVER_KINCO_SERIAL_HEX_BCU_DRIVER_H

//#include "../serial_hex/serial_hex_bcu_driver.h"
#include "serial_kinco_bcu_driver_protocol.h"

namespace wizrobo { namespace bcu_driver {

class SerialKincoBcuDriver : public IBcuDriver
{
public:
    SerialKincoBcuDriver();
    ~SerialKincoBcuDriver();

    int Init(BcuParamPtr ptr);

    int SetMotorParam();

    int Enable(bool on);
    int Brake(bool on);
    int Stop();

    int GetMotorSpd(MotorSpdArray& rpms);
    int SetMotorSpd(const MotorSpdArray& rpms);
    int SwpMotorSpd(const MotorSpdArray& cmd_rpms, MotorSpdArray& ans_rpms);
    int GetMotorEnc(MotorEncArray& ticks);
    int ClrMotorEnc();

private:
    int InitOnce();
    int CheckPort();
    int ClrAlarm_();
    /// com
    bool Communicate_(const byte* cmd, int cmd_len);
    bool Communicate_(const byte* cmd, int cmd_len, serial_kinco::AnsArrayType& ans);
    /// cmd
    int WriteCmd(const byte* cmd, int cmd_len);
    int FillCmdHead_(byte* cmd, int motor_id, serial_kinco::CmdType type);
    int FillCmdEnd_(byte* cmd, int idx);
    int FillBytesInt(byte* arr, int idx, int len, const int& num);
    int ReverseFillBytesInt(byte* arr, int idx, int len, const int& num);

    byte CalNegChkSum_(byte* cmd, int cmd_len);
    /// ans
    int ReceiveAns_(serial_kinco::AnsArrayType& ans);
    /// com

    int SetSpdCtrlMode_(serial_kinco::SpdCtlMode mode);
    int SetMotorAcc_(float rpss);
    int SetMotorDec_(float rpss);

    int ParseMotorData_(const serial_kinco::AnsArrayType& ans, double& data);
    int ParseMotorSpd_(const serial_kinco::AnsArrayType& ans, float& rpm);
    int ParseMotorEnc_(const serial_kinco::AnsArrayType& ans, int& tick);

private:
    //// setting
    static const bool IS_ALWAYS_CHECK_PORT = true;
    //// com
    SerialPort serial_port_;
    //serial_hex::CmdType last_cmd_type_;
    serial_kinco::SpdCtlMode motor_spd_ctl_mode_;
    float motor_acc_rps2_;
    float motor_dec_rps2_;
    MotorEncArray offset_ticks_;

};
}}// namespace
#endif// WIZROBO_BCU_DRIVER_KINCO_SERIAL_HEX_BCU_DRIVER_H
