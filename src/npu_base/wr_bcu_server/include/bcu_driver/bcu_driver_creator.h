/**************************************************************************
**   WizRobo NPU (Navigation Processing Unit)
**   Date: 4/30/2017
**   Copyright: Shenzhen Linkmiao Robotics Co., Ltd. 2015-2017
**   Creator: lhan
**************************************************************************/

#ifndef WIZROBO_BCU_DRIVER_BCU_DRIVER_CREATOR_H
#define WIZROBO_BCU_DRIVER_BCU_DRIVER_CREATOR_H
//#include <serial_ascii/serial_ascii_bcu_driver.h>
//#include <serial_hex/serial_hex_bcu_driver.h>
#include <serial_kinco/serial_kinco_bcu_driver.h>
#include <serial_keya/serial_keya_bcu_driver.h>
#include <serial_fdk/serial_fdk_bcu_driver.h>
#include <serial_amps/serial_amps_bcu_driver.h>
#include <serial_sim/serial_sim_bcu_driver.h>
#include <serial_nbox/serial_hex_nbox_driver.h>
#include <serial_csg/serial_csg_bcu_driver.h>
#include <serial_fake/serial_fake_bcu_driver.h>
#include <serial_zx/serial_zx_bcu_driver.h>

namespace wizrobo { namespace bcu_driver {

class BcuDriverCreator
{
public:
    IBcuDriver* Create(BcuDriverType type)
    {
        IBcuDriver* p_bcu = NULL;
        switch (type)
        {
        case SERIAL_KINCO:
            p_bcu = new SerialKincoBcuDriver();
            break;
        case SERIAL_KEYA:
            p_bcu = new SerialKeyaBcuDriver();
            break;
        case SERIAL_FDK:
            p_bcu = new SerialFdkBcuDriver();
            break;
        case SERIAL_AMPS:
            p_bcu = new SerialAmpsBcuDriver();
            break;
        case CAN:
            assert(0);
            break;
        case ETHERNET:
            assert(0);
            break;
        case SERIAL_SIM:
            p_bcu = new SerialSimBcuDriver();
            break;
        case SERIAL_NBOX:
            p_bcu = new SerialHexNboxDriver();
            break;
        case SERIAL_CSG:
            p_bcu = new SerialCsgBcuDriver();
            break;            
        case SERIAL_FAKE:
            p_bcu = new SerialFakeBcuDriver();
            break;
        case SERIAL_ZXFDK:
            p_bcu = new SerialZXFdkBcuDriver();
            break;
        default:
            assert(0);
            break;
        }
        return p_bcu;
    }
};
}}//namespace
#endif // WIZROBO_BCU_DRIVER_BCU_DRIVER_CREATOR_H
