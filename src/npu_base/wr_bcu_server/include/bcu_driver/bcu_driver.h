/**************************************************************************
** WizRobo BCU (Basement Control Unit) SDK
**	Version: Alpha 3.1
**	Date: 2015.10 - 2017.05
**	Creator: <EMAIL>
**	Copyright: Shenzhen Linkmiao Robotics Co., Ltd. All rights reserved.
** Supported OS: WINXP, WIN7, UBUNTU
** Supported Architecture: Win32, MFC, Linux API
** Supported Interface: serial(asiic), serial(hex)
** Code Style: Google-like
**************************************************************************/
#ifndef WIZROBO_BCU_DRIVER_BCU_DRIVER_H
#define WIZROBO_BCU_DRIVER_BCU_DRIVER_H
#include "npu.h"

/// Head Files
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include <cmath>
#include <iostream>
#include <iomanip>
#include <string>
#include <sstream>
#include <vector>
#include <deque>
#include <bitset>
#include <fstream>
#include <algorithm>

#if WR_UBUNTU
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <termios.h>
#include <time.h>
#include <errno.h>
#include <pthread.h>    // Linux multi-thread support
#ifndef MAKE_DIR
#define MAKE_DIR(str) mkdir(str,S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH)
#endif
#endif

#if WR_WINNT
#if WR_WIN32
#include <windows.h>
#elif WR_MFC
#include <afx.h>
#endif
#include "3dparty/dirent.h"
#ifndef MAKE_DIR
#define MAKE_DIR(str) CreateDirectoryA(str,NULL)
#endif
#endif

namespace wizrobo { namespace bcu_driver {

#if WR_WINNT
typedef int ComPortIdType;
#elif WR_UBUNTU
typedef std::string ComPortIdType;
#endif

#if WR_UBUNTU
const int TRUE = 1;
const int FALSE = 0;
#endif

//const int FAILED = -1;
//const float PI = 3.14159f;
}}// namespace
#endif// WIZROBO_BCU_DRIVER_BCU_DRIVER_H
