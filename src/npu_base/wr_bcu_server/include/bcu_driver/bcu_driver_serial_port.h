#ifndef BCU_DERVER_SERIAL_PORT_H
#define BCU_DERVER_SERIAL_PORT_H
#include "bcu_driver.h"

//////////////////////////////////////////////////////////////////////////
#if WR_WINNT
#define MIN_PORT_ID 1

//////////////////////////////////////////////////////////////////////////
#if WR_MFC
#include "serial_port/serial_port_mfc.h"
//////////////////////////////////////////////////////////////////////////
#elif WR_WIN32
#include "serial_port/serial_port_win32.h"
#endif

//////////////////////////////////////////////////////////////////////////
#elif WR_UBUNTU
#define MIN_PORT_ID 0
#include "serial_port/serial_port_ubuntu.h"
#endif
#endif// BCU_DERVER_SERIAL_PORT_H
