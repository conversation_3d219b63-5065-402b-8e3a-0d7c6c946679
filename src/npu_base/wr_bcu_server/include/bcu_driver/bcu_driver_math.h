#ifndef WIZROBO_BCU_DRIVER_BCU_DRIVER_MATH_H
#define WIZROBO_BCU_DRIVER_BCU_DRIVER_MATH_H
#include "bcu_driver.h"

namespace wizrobo { namespace bcu_driver {

#ifndef MAX
#define MAX(x,y) ((x)>=(y)?(x):(y))
#endif

#ifndef MIN
#define MIN(x,y) ((x)<(y)?(x):(y))
#endif

#ifndef SIGN
#define SIGN(x)  ((x==0)?0:((x)>0?1:(-1)))
#endif

#ifndef SQU
#define SQU(x) ((x)*(x))
#endif

#ifndef AVG
#define AVG(x,y) (((x)+(y))/2)
#endif

#ifndef ROUND_UP
#define ROUND_UP(d) ((((d) - static_cast<int>(d)) == 0) ? 1.0 * static_cast<int>(d) : 1.0 * (static_cast<int>(d) + SIGN(d)))
#endif

#ifndef ROUND_DOWN
#define ROUND_DOWN(d) (static_cast<int>(d))
#endif

#ifndef ROUND
#define ROUND(d) (static_cast<int>(d + 0.5*SIGN(d)))
#endif

#ifndef UNIFY360
#define UNIFY360(deg) (((static_cast<int>(deg))%360==0)?0:(((deg)>360)?((deg) - 360*ROUND_DOWN((deg)/360)):(((deg)<0)?((deg) - 360*ROUND_UP((deg)/360)):(deg))))
#endif

#ifndef UNIFY180
#define UNIFY180(deg) ((UNIFY360(deg) >= 180)?(UNIFY360(deg) - 360):UNIFY360(deg))
#endif

#ifndef SIND
#define SIND(d) (sin((d)*3.14159/180))
#endif

#ifndef COSD
#define COSD(d) (cos((d)*3.14159/180))
#endif

#ifndef ASIND
#define ASIND(x) (asin(x)/3.14159*180)
#endif

#ifndef ACOSD
#define ACOSD(x) (acos(x)/3.14159*180)
#endif
}}// namespace
#endif// WIZROBO_BCU_DRIVER_BCU_DRIVER_MATH_H
