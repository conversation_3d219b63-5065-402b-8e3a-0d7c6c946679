#ifndef WIZROBO_BCU_DRIVER_SERIAL_FDK_BCU_DRIVER_PROTOCOL_H
#define WIZROBO_BCU_DRIVER_SERIAL_FDK_BCU_DRIVER_PROTOCOL_H

#include "bcu_driver_interface.h"

#define ENDIAN_EXCHANGE_HALFWORD(x) ((((x)&0xFF00)>>8)+(((x)&0x00FF)<<8))
#define ENDIAN_EXCHANGE_WORD(x) ((((x)&0xFF000000)>>24)+(((x)&0x00FF0000)>>8)+(((x)&0x0000FF00)<<8)+(((x)&0x000000FF)<<24))


namespace wizrobo { namespace bcu_driver { namespace serial_fdk{

const std::string MTR_SOFT_START(":0106105A000A85\r\n");
const std::string MTR_SOFT_STOP(":0106105A00018E\r\n");
const std::string SET_MTR_SPD("0110115C000208");
//const std::string SET_MTR_SPD("011011AA000208");

const std::string SET_LEFT_MTR_SPD("100D");
const std::string SET_RIGHT_MTR_SPD("10E5");
const std::string MTR_EMERGENCY_SHUTDOWN("!EX ");
const std::string MTR_RELEASE_SHUTDOWN("!MG ");
const std::string CLR_MTR_ENC("011011EA0004100000000000000000E0");

const std::string GET_MTR_CURRENT("?A");
const std::string GET_BATTERY_CURRENT("?BA");
const std::string GET_MTR_SPD("010311EE0002FB");
const std::string GET_MTR_STATUS("010311EA000EF3");
const std::string GET_LEFT_MTR_SPD("100C");
const std::string GET_RIGHT_MTR_SPD("10E4");

const std::string GET_MTR_ENC("010311EA0004");
const std::string GET_LEFT_MTR_ENC("1008");
const std::string GET_RIGHT_MTR_ENC("10E0");

const std::string GET_FEEDBACK("?F");
const std::string COLON(":");
const std::string CMD_ADDRESS("01");
const std::string CMD_READ("03");
const std::string CMD_WRITE("06");
const std::string CMD_END("\r\n");

const std::string PARITY("EVEN");
const int BAUDRATE = B9600;
const int DATABITS = 7;

const int MAX_MOTOR_SPD = 1500;
const int MIN_MOTOR_SPD = -1500;
const int CMD_LEN = 3;
const int MIN_ANS_LEN = 9;
const int MOTOR_ID_MAP[2][2] = {
    { 0, 1},
    { 1, 0},
};

const unsigned char GET_MTR_STATUS_RTU[8] = {0x01, 0x03, 0x11, 0xEA, 0x00, 0x0E, 0xE0, 0xC6};

typedef struct _RtuMotorStatus
{
    _RtuMotorStatus()
    {
    }
    void PrintData()
    {
        printf("left_motor_pose is %d\n", left_motor_pose);
        printf("right_motor_pose is %d\n", right_motor_pose);
        printf("left_motor_spd is %d\n", left_motor_spd);
        printf("right_motor_spd is %d\n", right_motor_spd);
        printf("left_motor_current is %d\n", left_motor_current);
        printf("right_motor_current is %d\n", right_motor_current);
        printf("left_motor_state_signal is %d\n", left_motor_state_signal);
        printf("right_motor_state_signal %d\n", right_motor_state_signal);
        printf("left_motor_error_signal %d\n", left_motor_error_signal);
        printf("right_motor_error_signal %d\n", right_motor_error_signal);
        printf("motor_bus_voltage %d\n", motor_bus_voltage);
        printf("motor_driver_temp %d\n", motor_driver_temp);
    }
    void EndianExchange()
    {
//        _RtuMotorStatus status;
         this->left_motor_pose           = ENDIAN_EXCHANGE_WORD(this->left_motor_pose);
         this->right_motor_pose          = ENDIAN_EXCHANGE_WORD(this->right_motor_pose);
         this->left_motor_spd            = ENDIAN_EXCHANGE_HALFWORD(this->left_motor_spd);
         this->right_motor_spd           = ENDIAN_EXCHANGE_HALFWORD(this->right_motor_spd);
         this->left_motor_state_signal   = ENDIAN_EXCHANGE_HALFWORD(this->left_motor_state_signal);
         this->right_motor_state_signal  = ENDIAN_EXCHANGE_HALFWORD(this->right_motor_state_signal);
         this->left_motor_error_signal   = ENDIAN_EXCHANGE_HALFWORD(this->left_motor_error_signal);
         this->right_motor_error_signal  = ENDIAN_EXCHANGE_HALFWORD(this->right_motor_error_signal);
         this->motor_bus_voltage         = ENDIAN_EXCHANGE_HALFWORD(this->motor_bus_voltage);
         this->motor_driver_temp         = ENDIAN_EXCHANGE_HALFWORD(this->motor_driver_temp);
         this->left_motor_current        = ENDIAN_EXCHANGE_HALFWORD(this->left_motor_current);
         this->right_motor_current       = ENDIAN_EXCHANGE_HALFWORD(this->right_motor_current);
        return;
    }
    int left_motor_pose;
    int right_motor_pose;
    short left_motor_spd;
    short right_motor_spd;
    short left_motor_current;
    short right_motor_current;
    unsigned short left_motor_state_signal;
    unsigned short right_motor_state_signal;
    unsigned short left_motor_error_signal;
    unsigned short right_motor_error_signal;
    short motor_bus_voltage;
    short motor_driver_temp;
}RtuMotorStatus;


}}} // namespace

#endif  // WIZROBO_BCU_DRIVER_SERIAL_FDK_BCU_DRIVER_PROTOCOL_H
