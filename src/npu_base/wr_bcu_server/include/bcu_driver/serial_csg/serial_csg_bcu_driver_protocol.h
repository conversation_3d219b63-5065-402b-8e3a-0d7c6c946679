#ifndef WIZROBO_BCU_DRIVER_SERIAL_CSG_BCU_DRIVER_PROTOCOL_H
#define WIZROBO_BCU_DRIVER_SERIAL_CSG_BCU_DRIVER_PROTOCOL_H

#include "bcu_driver_interface.h"

namespace wizrobo { namespace bcu_driver { namespace serial_csg{

const std::string PARITY("NONE");
const int BAUDRATE = B115200;
const int DATABITS = 8;

const unsigned short FLAG_AAAA = 0xAAAA;
const unsigned short FLAG_FAAA = 0xAAFA;

struct Header {
    unsigned short flag_aaaa;
    unsigned char function;
    unsigned char data_length;
    unsigned char data_first_char;
    unsigned char reserved[3];
};
const unsigned int HEADER_LENGTH = 4;

struct ControlInfo {
    unsigned char mode;
    char angle;
    short speed;
};
const unsigned int CONTROL_INFO_LENGTH = 4;

struct AngleData {
    short angle_deg[4];    /* ANGLE_SENSOR1/100<deg> */
};
const unsigned int ANGLE_DATA_LENGTH = 16;

struct MotorData {
    short current[4];      /* CURRENT*0.01<A> */
    short speed_rpm[4];    /* SPEED<RPM> */
};

struct EncoderData {
    int ticks[4];
};

struct SonarData {
    unsigned char sonar[8];
    unsigned char obstacle;
    unsigned char keeping_distence;
    unsigned char reserved[2];
};

struct BatteryVoltageData {
    unsigned short cell[12];
};

struct BatteryManufacturerInfo {
    unsigned short max_voltage;
    unsigned short min_voltage;
    unsigned short max_voltage_pos;
    unsigned short min_voltage_pos;
    unsigned short voltage_diff;
    unsigned short avrg_voltage;
    unsigned short total_voltage;
    unsigned short charge_current;
    unsigned short discharge_current;
    unsigned short soc;
};

struct BatteryTemperatureData {
    unsigned short temperature[6];
    unsigned short max_temperature;
    unsigned short min_temperature;
    unsigned short avrg_temperature;
    unsigned short envirment_temperature;
};

struct MotionData {
    unsigned short rc;
    unsigned char _wr[4];
    unsigned char reserved[2];
    int wr()
    {
        int *ptr = (int *)(&(_wr[0]));
        return (*ptr);
    }
};

struct ErrorCode {
    unsigned char code;
};

const int MOTION_CARLIKE_LENGTH = 3;
struct MotionCarlike {
    unsigned char angle;
    unsigned char _speed[2];
    unsigned char reserved;
    void set_speed (short speed)
    {
        unsigned char *ptr = (unsigned char *)(&speed);
        _speed[0] = ptr[1];
        _speed[1] = ptr[0];
    }
};

const int MOTION_SPINAROUND_LENGTH = 2;
struct MotionSpinAround {
    unsigned short speed;
};

struct KeepingDistence {
    unsigned char distence;
};

enum ReceiveFunctionWord {
    CONTROL_INFO = 0,
    ANGLE_DATA,
    MOTOR_DATA,
    ENCODER_DATA,
    SONAR_DATA,
    BATTERY_VOLTAGE_DATA,
    BATTERY_MANUFACTURER_INFO,
    BATTERY_TEMPERATURE_DATA,
    MOTION_DATA,
    ERROR_CODE,
    RFW_THE_END
};

enum ControlFunctionWord {
    MOTION_CARLIKE = 0,
    MOTION_SPIN_AROUND,
    MOTION_PARK,
    MOTION_KEEP_DISTENCE,
    MOTION_FWD_CHANNEL,
    CFW_THE_END
};

}}} // namespace

#endif  // WIZROBO_BCU_DRIVER_SERIAL_CSG_BCU_DRIVER_PROTOCOL_H
