#ifndef WIZROBO_BCU_DRIVER_CSG_SERIAL_BCU_DRIVER_H
#define WIZROBO_BCU_DRIVER_CSG_SERIAL_BCU_DRIVER_H

#include "serial_csg_bcu_driver_protocol.h"
#include "wr_npu_msgs/CsgBatterySonar.h"
#include "wr_npu_msgs/CsgMode.h"
#include <std_msgs/Bool.h>
#include <std_msgs/Float32MultiArray.h>

namespace wizrobo { namespace bcu_driver {

enum controlmode{
    DRIVING = 0u,
    ROUND   = 1u,
    STOP    = 3u
};

class SerialCsgBcuDriver : public IBcuDriver
{
public:
    SerialCsgBcuDriver();
    ~SerialCsgBcuDriver();

    int Init(BcuParamPtr ptr);
    int Stop();

    int GetMotorSpd(MotorSpdArray& rpm); // hall sensor
    int GetSteerAngle(float& angle, wizrobo_npu::SteerEncLocation& location);
    int SetMotorSpd(const MotorSpdArray& spd); // -1000~1000
    int SwpMotorSpd(const MotorSpdArray& cmd_rpm, MotorSpdArray& act_rpm)  {}
    int GetMotorEnc(MotorEncArray& ticks);  // enc

private:
    int MakeFrame(int func_index, unsigned char* ptr_data, int data_length);
    int WriteCmd(unsigned char* ptr, int length);
    unsigned char CheckSum(unsigned char *ptr, int length);
    void ProcessBufferData();
    int ReceiveLoop();

    void FreshControlInfo(unsigned char *ptr);
    void FreshAngleData(unsigned char *ptr);
    void FreshMotorData(unsigned char *ptr);
    void FreshEncoderData(unsigned char *ptr);
    void FreshSonarData(unsigned char *ptr);
    void FreshBatteryVoltageData(unsigned char *ptr);
    void FreshBatteryManufacturerInfo(unsigned char *ptr);
    void FreshBatteryTemperatureData(unsigned char *ptr);
    void FreshMotionData(unsigned char *ptr);
    void FreshErrorCode(unsigned char *ptr);
    void ObstacleCallback(const std_msgs::BoolConstPtr &msg);
    bool StopMode(wr_npu_msgs::CsgMode::Request &req,
                  wr_npu_msgs::CsgMode::Response &res);

private:
    int obstacle_callback_count_;
    int csg_msg_pub_count_;
    bool is_stopmode_;
    ros::Time obstacle_callback_;
    ros::Time csg_msg_pub_;
    double bias_;
    SerialPort serial_port_;
    boost::thread* ptr_thread_recv_loop_;
    static const int receive_buffer_length_ = 64;
    static const int send_buffer_length_ = 64;
    static const int motor_cnt_ = 4;
    unsigned char receive_buffer_[receive_buffer_length_];
    int receive_buffer_rear_;
    unsigned char send_buffer_[send_buffer_length_];
    float angle_array[4];

    serial_csg::ControlInfo control_info_;
    serial_csg::AngleData angle_data_;
    serial_csg::MotorData motor_data_;
    serial_csg::EncoderData encoder_data_;
    serial_csg::SonarData sonar_data_;
    serial_csg::BatteryVoltageData battery_voltage_data_;
    serial_csg::BatteryManufacturerInfo battery_man_info_;
    serial_csg::BatteryTemperatureData battery_temp_;
    serial_csg::MotionData motion_data_;
    serial_csg::ErrorCode error_code_;

    typedef boost::function<void(unsigned char *)> RfwFunction;
    RfwFunction rfw_function_map_[11];
    wizrobo_npu::SteerEncLocation steer_location_;

    static const int front_right_i = 0;
    static const int front_left_i = 1;
    static const int back_left_i = 2;
    static const int back_right_i = 3;

    wr_npu_msgs::CsgBatterySonar csgmsg_;
    std_msgs::Float32MultiArray csg_angle_;
    ros::ServiceServer service_;
    ros::Publisher csgpub_;
    ros::Publisher csg_angle_pub_;
    ros::Subscriber obstacle_sub_;

    controlmode mode_;
};

}}  // namespace

#endif  // WIZROBO_BCU_DRIVER_CSG_SERIAL_BCU_DRIVER_H
