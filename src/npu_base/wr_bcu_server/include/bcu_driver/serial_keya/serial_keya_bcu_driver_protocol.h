#ifndef WIZROBO_BCU_DRIVER_SERIAL_KEYA_BCU_DRIVER_PROTOCOL_H
#define WIZROBO_BCU_DRIVER_SERIAL_KEYA_BCU_DRIVER_PROTOCOL_H

#include "bcu_driver_interface.h"

namespace wizrobo { namespace bcu_driver { namespace serial_keya{

const std::string MTR_SOFT_START("!AC ");
const std::string MTR_SOFT_STOP("!DC ");
const std::string SET_MTR_SPD("!M ");
const std::string MTR_EMERGENCY_SHUTDOWN("!EX ");
const std::string MTR_RELEASE_SHUTDOWN("!MG ");

const std::string GET_MTR_CURRENT("?A");
const std::string GET_BATTERY_CURRENT("?BA");
const std::string GET_MTR_SPD("?S");
const std::string GET_MTR_ENC("?C");
const std::string GET_FEEDBACK("?F");
const std::string COLON(":");
const std::string CMD_END("\r");

const std::string PARITY("NONE");
const int BAUDRATE = B115200;
const int DATABITS = 8;

const int MAX_MOTOR_SPD = 1000;
const int MIN_MOTOR_SPD = -1000;
const int CMD_LEN = 3;
const int MIN_ANS_LEN = 9;
const int MOTOR_ID_MAP[2][2] = {
    { 0, 1},
    { 1, 0},
};

}}} // namespace

#endif  // WIZROBO_BCU_DRIVER_SERIAL_KEYA_BCU_DRIVER_PROTOCOL_H
