#ifndef WIZROBO_BCU_DRIVER_KEYA_SERIAL_BCU_DRIVER_H
#define WIZROBO_BCU_DRIVER_KEYA_SERIAL_BCU_DRIVER_H

#include "serial_keya_bcu_driver_protocol.h"

namespace wizrobo { namespace bcu_driver {

class SerialKeyaBcuDriver : public IBcuDriver
{
public:
    SerialKeyaBcuDriver();
    ~SerialKeyaBcuDriver();

    int Init(BcuParamPtr ptr);
    int Stop();

    int GetMotorSpd(MotorSpdArray& rpm); // hall sensor
    int SetMotorSpd(const MotorSpdArray& spd); // -1000~1000
    int SwpMotorSpd(const MotorSpdArray& cmd_rpm, MotorSpdArray& act_rpm)  {}
    int GetMotorEnc(MotorEncArray& ticks);  // enc

private:
    int WriteCmd(const char* cmd, uint len);
    int ReceiveAns(std::string& ans);
    int ParseMotorSpd(const std::string& ans, MotorSpdArray& rpm);
    int ParseEncData(const std::string& ans, MotorEncArray& rpm);
    int CheckAns(const std::string& ans);
    void PrintAns(const std::string& ans);

private:
    SerialPort serial_port_;
};

}}  // namespace

#endif  // WIZROBO_BCU_DRIVER_KEYA_SERIAL_BCU_DRIVER_H
