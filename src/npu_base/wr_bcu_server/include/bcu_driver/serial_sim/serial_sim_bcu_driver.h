#ifndef WIZROBO_BCU_DRIVER_SERIAL_SIM_BCU_DRIVER_H
#define WIZROBO_BCU_DRIVER_SERIAL_SIM_BCU_DRIVER_H
#include <deque>
#include <iostream>
#include <iomanip>

#include <ros/ros.h>

#include "bcu_driver_interface.h"
#include "bcu_driver_serial_port.h"

namespace wizrobo { namespace bcu_driver {

class SerialSimBcuDriver : public IBcuDriver
{
public:
    SerialSimBcuDriver()
    {
    }
    virtual ~SerialSimBcuDriver()
    {
        if (is_inited_)
        {
            Stop();
            Enable(false);
        }
    }

public:
    //// IBcuDriver interfaces
    inline int Init(BcuParamPtr ptr)
    {
        is_inited_ = false;
        bcu_param_ptr = ptr;
        spd_rpms_.resize(MAX_MOTOR_NUM, 0);
        enc_ticks.resize(MAX_MOTOR_NUM, 0);
        start_time_ = ros::Time::now();

        is_inited_ = true;
        return TRUE;
    }

    inline int SetMotorParam()
    {
        if (spd_rpms_.size() != bcu_param_ptr->motor.motor_num)
        {
            spd_rpms_.resize(bcu_param_ptr->motor.motor_num, 0);
            enc_ticks.resize(bcu_param_ptr->motor.motor_num, 0);
        }
        return TRUE;
    }

    inline int Enable(bool on)
    {
        flag_enabled_ = on;
        return TRUE;
    }


    inline int Brake(bool on)
    {
        return Stop();
    }

    inline int Stop()
    {
        WR_DEBUG("SerialSimBcuDriver::Stop()...");
        MotorSpdArray rpms(bcu_param_ptr->motor.motor_num, 0);
        return SetMotorSpd(rpms);
    }

    inline int GetMotorSpd(MotorSpdArray& rpms)
    {
        rpms = spd_rpms_;
        return TRUE;
    }
    inline int SetMotorSpd(const MotorSpdArray& rpms)
    {
        spd_rpms_ = rpms;
        return TRUE;
    }
    inline int SwpMotorSpd(const MotorSpdArray& cmd_rpms, MotorSpdArray& ans_rpms)
    {
        spd_rpms_ = cmd_rpms;
        ans_rpms = spd_rpms_;
        return TRUE;
    }

    inline int GetMotorEnc(MotorEncArray& ticks)
    {
        ros::Duration itv = ros::Time::now() - start_time_;
        for (int i = 0; i < bcu_param_ptr->motor.motor_num; i++)
        {
            int diff_tick = itv.toSec() * spd_rpms_[i] / 60 * bcu_param_ptr->motor.motor_enc_res_ppr * 4;
            enc_ticks[i] += diff_tick;
//            ROS_DEBUG("#%d: itv = %.2f [s], spd = %.2f [rpm], enc_res = %d [ppr],  diff_tick = %d [tick]"
//                      , i, itv.toSec(), spd_rpms_[i], motor_param_.motor_enc_res_ppr, diff_tick);
        }
        ticks = enc_ticks;
        start_time_ = ros::Time::now();
        return TRUE;
    }
    inline int ClrMotorEnc()
    {
        enc_ticks.resize(enc_ticks.size(), 0);
        return TRUE;
    }

protected:
    int InitOnce()
    {
        spd_rpms_.resize(MAX_MOTOR_NUM, 0);
        enc_ticks.resize(MAX_MOTOR_NUM, 0);
        start_time_ = ros::Time::now();
        return true;
    }

    int CheckPort()
    {
        return TRUE;
    }
private:
    //// constant
    static const int MAX_MOTOR_NUM = 2;
    //// functional members
    bool flag_enabled_;
    MotorEncArray enc_ticks;
    MotorSpdArray spd_rpms_;
    ros::Time start_time_;
};
}}// namespace
#endif// WIZROBO_BCU_DRIVER_SERIAL_SIM_BCU_DRIVER_H
