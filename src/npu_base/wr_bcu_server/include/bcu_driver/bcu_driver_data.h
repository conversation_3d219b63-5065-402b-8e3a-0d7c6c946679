#ifndef WIZROBO_BCU_DRIVER_BCU_DRIVER_DATA_H
#define WIZROBO_BCU_DRIVER_BCU_DRIVER_DATA_H
#include <wr_npu_msgs/MotorEnc.h>

namespace wizrobo { namespace bcu_driver {
/// bcu data
// motor spd
typedef std::vector<float> MotorSpdArray;
typedef std::vector<float> MotorPwmArray;
// motor enc
typedef std::vector<int> MotorEncArray;
// motor acc
typedef std::vector<float> MotorAccArray;
// sonar
typedef std::vector<float> SonarDataArray;
typedef std::vector<float> InfrdDataArray;
typedef std::vector<unsigned char> BumperDataArray;// TODO

/***************************************************************/
//battery
struct BatteryDataArray
{
    float vol;
    float temp;
    float battery_capacity;
    float battery_level;  //0~100%
};

// com status
struct ComStatusType
{
    ComStatusType()
    {
        freq_hz = 0;
        delay_ms = 0;
        cnt = 0;
        err_cnt = 0;
        err_rate = 0;
    }
    float freq_hz;
    float delay_ms;
    long cnt;
    long err_cnt;
    float err_rate;
    std::string cmd_str;
    std::string ans_str;
};

// sensor: imu
//struct ImuDataElm
//{
//    ImuDataElm()
//    {
//        x = 0;
//        y = 0;
//        z = 0;
//        memset(cov, 0, 9);
//    }
//    double x;
//    double y;
//    double z;
//    double cov[9];
//};
//struct ImuData
//{
//    ImuDataElm mag;
//    ImuDataElm gyr;
//    ImuDataElm acc;
//};

typedef struct Axis
{
    float x;
    float y;
    float z;
}Axis;

typedef struct Pose
{
    float roll;
    float pitch;
    float yaw;
}Pose;

typedef struct Quaternion
{
    float x;
    float y;
    float z;
    float w;
}Quaternion;

typedef struct ImuData
{
    Axis acc;
    Axis gyro;
    Axis mgg;
    Pose pose;
    float yaw_raw;
}ImuData;

typedef struct _BmsData
{
    int vol;
    int cur;
    int temp;
    int res_cap;
    int capacity;
    unsigned char level;  //0~100%
    int protect;
}BmsData;

typedef struct _MotorState
{
    short rpm;
    short cur;
    short vol;
    short temp;
    unsigned short error;
}MotorState;
typedef std::vector<MotorState> MotorStatus;

typedef struct _WaterLevel
{
    unsigned char clean;
    unsigned char dirty;
}WaterLevel;

typedef struct _UsageTime
{
    int roller_brush;
    int fan_carbon_brush;
    int filter;
    int hanging;
}UsageTime;


// sensor: gps
struct GpsDatasensor_msgs
{
//    GpsData()
//    {
//        latitude = 0;
//        longitude = 0;
//        altitude = 0;
//        memset(position_covariance, 0, 9);
//    }
    double latitude;
    double longitude;
    double altitude;
    double position_covariance[9];
};

struct IoData
{
    IoData()
    {
        bumper = 0;
        esb = 0;
        brake = 0;
        user_io = 0;
    }
    unsigned char bumper;
    unsigned char esb;
    unsigned char brake;
    unsigned char user_io;
};

typedef enum OutputMode
{
    OUTPUT_MODE_KEEP_HIGH = 0,
    OUTPUT_MODE_KEEP_LOW = 1,
    OUTPUT_MODE_BLINK_1 = 2,
    OUTPUT_MODE_BLINK_2 = 3,
    OUTPUT_MODE_BLINK_3 = 4
}OutputMode;

typedef enum _LampChannel
{
    LAMP_FRONT_LEFT  = 0,
    LAMP_FRONT_RIGHT = 1,
    LAMP_BACK_LEFT   = 2,
    LAMP_BACK_RIGHT  = 3
}LampChannel;

typedef enum _BlnStatus
{
    ALLCLOSE = 0,
    ALLOPEN =  1,
    WHITECLOSE = 2,
    WHITEOPEN = 3,
    REDCLOSE = 4,
    REDOPEN = 5,
    RED1HZ = 6,
    RED2HZ = 7,
    REDALON = 8,
    ALLON = 9
}BlnStatus;

typedef enum _BrushChannel
{
    BRUSHCHANNEL = 0
}BrushChannel;

typedef enum _FanChannel
{
    FANCHANNEL = 0
}FanChannel;

typedef enum _BrushRcc
{
    BRUSHRCC_0 = 0,
    BRUSHRCC_500 = 500,
    BRUSHRCC_1000 = 1000,
    BRUSHRCC_1500 = 1500,
    BRUSHRCC_2000 = 2000,
}BrushRcc;

typedef enum _FanRcc
{
    FANRCC_STOP    = 0,
    FANRCC_LOW     = 180,
    FANRCC_MEDIUM  = 260,
    FANRCC_HIGH    = 490,
    FANRCC_MAX     = 490,
}FanRcc;

typedef enum _SbStatus
{
    SBRAW = 0,
    SBCHANGE = 1
}SbStatus;

typedef enum _InputChannel
{
    INPUT_USER_0 = 0,
    INPUT_USER_1,
    INPUT_USER_2,
    INPUT_USER_3,

    INPUT_USER_4,
    INPUT_USER_5,
    INPUT_USER_6,
    INPUT_USER_7
}InputChannel;

typedef enum _OutputChannel
{
    OUTPUT_USER_0 = 0,
    OUTPUT_USER_1,
    OUTPUT_USER_2,
    OUTPUT_USER_3,

    OUTPUT_USER_4,
    OUTPUT_USER_5,
    OUTPUT_USER_6,
    OUTPUT_USER_7
}OutputChannel;

typedef struct _SensorData
{
    std::vector<float> sonar;
    std::vector<float> infrd;

    float power_vol;
    float battery_vol;

    int esb;
    int sw;
    int bumper;
    int clean_level;
    int dirty_level;
    int io_input;

    void PrintData(){
        printf("sonar num is : %d", static_cast<int>(sonar.size()));
        printf("sonar data :");
        for(int i = 0;i < static_cast<int>(sonar.size());i++){
            printf(" (%d)%.2f", i, sonar[i]);
        }
        printf("\n");
        printf("infrd num is : %d", static_cast<int>(infrd.size()));
        printf("infrd data :");
        for(int i = 0;i < static_cast<int>(infrd.size());i++){
            printf(" (%d)%.2f", i, infrd[i]);
        }
        printf("\n");
        printf(" power_vol is : %.2f\n", power_vol);
        printf(" battery_vol is : %.2f\n", battery_vol);
        printf(" esb is : %d\n", esb);
        printf(" sw is : %d\n", sw);
        printf(" bumper is : %d\n", bumper);
        printf(" clean_level is : %d\n", clean_level);
        printf(" dirty_level is : %d\n", dirty_level);
        printf(" io_input is : %d\n", io_input);
    }
}SensorData;

typedef struct _Sht3xData
{
    float temp;
    float humi;
}Sht3xData;

}}// namespace
#endif // WIZROBO_BCU_DRIVER_BCU_DRIVER_DATA_H
