#ifndef WIZROBO_BCU_DRIVER_SERIAL_PORT_WIN32_H
#define WIZROBO_BCU_DRIVER_SERIAL_PORT_WIN32_H
#include "serial_port_interface.h"
#include "../3dparty/CnComm.h"
#define MAX_BUF_LEN 1024

namespace wizrrobo { namespace bcu_driver {
class SerialPort: public SerialPortInterface {
public:
    SerialPort();
    ~SerialPort();
    virtual inline bool Open(std::string port_name, int baud_rate,int data_bits, std::string parity)
    {
        if (IsOpen())
        {
            Close();
        }
        return m_com.Open(port_name, baud_rate,data_bits,parity);
    }
    virtual inline bool IsOpen() const
    {
        return m_com.IsOpen();
    }
    virtual inline void Close()
    {
        return m_com.Close();
    }

    virtual inline int Write(const char* p_buf, int buf_len)
    {
        int couter_before = m_com.GetCounter(false);
        m_com.Write((LPCVOID)p_buf,(DWORD)buf_len);
        int couter_after = m_com.GetCounter(false);
        return couter_after - couter_before;
    }
    virtual inline int WriteByte(char ch)
    {
        return Write(&ch,1);
    }

    virtual inline int BytesWaiting()
    {
        m_buf_len = m_com.Read(m_buf,MAX_BUF_LEN);
        if (m_buf_len > MAX_BUF_LEN)
        {
            cout << "[FATAL ERROR] Read buffer overflowed!!!." << endl;
            return 0;
        }
        return m_buf_len;
    }
    virtual inline int Read(char* p_buf, int buf_len)
    {
        if (buf_len > MAX_BUF_LEN)
        {
            cout << "[FATAL ERROR] Required byte number overflowed read buffer!!!." << endl;
            return 0;
        }
        buf_len = MIN(buf_len,m_buf_len);
        memcpy(p_buf,m_buf,buf_len);
        return buf_len;
    }
    virtual inline int ReadByte(char& ch)
    {
        return m_com.ReadPort((LPVOID)&ch,1);
    }
    virtual inline void ClearReadBuffer()
    {
        m_com.Purge(PURGE_RXABORT | PURGE_RXCLEAR);
    }
private:
    CnComm m_com;
    char m_buf[MAX_BUF_LEN];
    int m_buf_len;
};
}}// namespace
#endif// WIZROBO_BCU_DRIVER_SERIAL_PORT_WIN32_H
