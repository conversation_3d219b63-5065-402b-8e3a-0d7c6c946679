#ifndef WIZROBO_BCU_DRIVER_SERIAL_PORT_MFC_H
#define WIZROBO_BCU_DRIVER_SERIAL_PORT_MFC_H
#include "serial_port_interface.h"
#include "../3dparty/naughter_serial_port.h"

namespace wizrobo { namespace bcu_driver {
class SerialPort: public ISerialPort {
public:
    SerialPort();
    ~SerialPort();
    virtual inline bool Open(std::string port_name, int baud_rate,int data_bits, std::string parity)
    {
        if (IsOpen())
        {
            Close();
        }
        return m_com.Open(port_name, baud_rate,data_bits,parity);
    }
    virtual inline bool IsOpen() const
    {
        return (bool)m_port.IsOpen();
    }
    virtual inline void Close()
    {
        return m_port.Close();
    }
    virtual inline int Write(const char* p_buf, int buf_len)
    {
        return (int)m_port.Write((void*)p_buf,(DWORD)buf_len);
    }
    virtual inline int WriteByte(char ch)
    {
        return (int)m_port.Write((void*)&ch,(DWORD)1);
    }
    virtual inline int BytesWaiting()
    {
        return (int)m_port.BytesWaiting();
    }
    virtual inline int Read(char* p_buf, int buf_len)
    {
        return (int)m_port.Read((void*)p_buf,(DWORD)buf_len);
    }
    virtual inline int ReadByte(char& ch)
    {
        return (int)m_port.Read((void*)&ch,1);
    }
    virtual inline void ClearReadBuffer()
    {
        return m_port.ClearReadBuffer();
    }
private:
    CSerialPort m_port;
};
}}// namespace
#endif// WIZROBO_BCU_DRIVER_SERIAL_PORT_MFC_H
