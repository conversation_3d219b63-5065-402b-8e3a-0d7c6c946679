#ifndef WIZROBO_BCU_DRIVER_SERIAL_PORT_UBUNTU_H
#define WIZROBO_BCU_DRIVER_SERIAL_PORT_UBUNTU_H
#include "serial_port_interface.h"

namespace wizrobo { namespace bcu_driver {

class SerialPort: public ISerialPort {
public:
    SerialPort();
    ~SerialPort();
    virtual bool Open(std::string port_name, int baud_rate,int data_bits, std::string parity);
    virtual inline bool IsOpen() const
    {
        return is_open;
    }
    virtual void Close();

    virtual int Write(const char* p_buf, int buf_len);
    virtual int WriteByte(char ch);

    virtual int BytesWaiting();
    virtual int Read(char* p_buf, int buf_len);
    virtual int ReadByte(char& ch);
    virtual inline void ClearReadBuffer()
    {
        if (file_dev > -1)
        {
            tcflush(file_dev, TCIOFLUSH);
        }
    }
private:
    void PrintOption(const struct termios &opt) const;
    inline int GetBaud(int define_baud) const
    {
        if (define_baud == B9600)
        {
            return 9600;
        }
        if (define_baud == B115200)
        {
            return 115200;
        }
        return 0;
    }
private:
    bool is_open;
    int file_dev;
};
}}// namespace
#endif// WIZROBO_BCU_DRIVER_SERIAL_PORT_UBUNTU_H
