#ifndef WIZROBO_BCU_DRIVER_SERIAL_PORT_INTERFACE_H
#define WIZROBO_BCU_DRIVER_SERIAL_PORT_INTERFACE_H
#include "../bcu_driver.h"

namespace wizrobo { namespace bcu_driver {

#define SERIAL_DEBUG 0

enum SerialComMode
{
    ASIIC_MODE = 0u,
    HEX_MODE = 1u
};

class ISerialPort
{
public:
    virtual ~ISerialPort()
    {}
    virtual bool Open(ComPortIdType port_id, int baud_rate,int data_bits,std::string parity) = 0;
    virtual bool IsOpen() const = 0;
    virtual void Close() = 0;
    virtual int Write(const char* p_buf, int buf_len) = 0;
    inline int Write(const byte* p_buf, int buf_len)
    {
        return this->Write((const char*)p_buf, buf_len);
    }
    virtual int WriteByte(char ch) = 0;
    inline int WriteByte(byte ch)
    {
        return this->WriteByte((char)ch);
    }
    virtual int BytesWaiting() = 0;
    virtual int Read(char* p_buf, int buf_len) = 0;
    inline int Read(byte* p_buf, int buf_len)
    {
        return this->Read((char*)p_buf, buf_len);
    }
    virtual int ReadByte(char& ch) = 0;
    inline int ReadByte(byte& ch)
    {
        //char tmp_char;
        return this->ReadByte((char&)ch);
    }

    virtual void ClearReadBuffer() = 0;

protected:
    ISerialPort()
    {}
};
}}// namespace
#endif// WIZROBO_BCU_DRIVER_SERIAL_PORT_INTERFACE_H
