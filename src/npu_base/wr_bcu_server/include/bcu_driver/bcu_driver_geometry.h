#ifndef WIZROBO_BCU_DRIVER_BCU_DRIVER_GEOMETRY_H
#define WIZROBO_BCU_DRIVER_BCU_DRIVER_GEOMETRY_H
#include "bcu_driver.h"
#include "bcu_driver_math.h"

namespace wizrobo { namespace bcu_driver {
/// <summary>
/// Unify to [0,360)
/// </summary>
#ifndef UNIFY360
#define UNIFY360(deg) (((deg)%360==0)?0:(((deg)>360)?((deg) - 360*ROUND_DOWN((deg)/360)):(((deg)<0)?((deg) - 360*ROUND_UP((deg)/360)):(deg))))
#endif
/// <summary>
/// Unify to [-180,180)
/// </summary>
#ifndef UNIFY180
#define UNIFY180(deg) ((((deg)%360==0)?0:(((deg)>360)?((deg) - 360*ROUND_DOWN((deg)/360)):(((deg)<0)?((deg) - 360*ROUND_UP((deg)/360)):(deg)))) - 180)
#endif
/// <summary>
/// Sine of deg
/// </summary>
#ifndef SIND
#define SIND(d) (sin((d)*3.14159/180))
#endif
/// <summary>
/// Cosine of deg
/// </summary>
#ifndef COSD
#define COSD(d) (cos((d)*3.14159/180))
#endif
/// <summary>
/// Arc-sine of x
/// </summary>
#ifndef ASIND
#define ASIND(x) (asin(x)/3.14159*180)
#endif
/// <summary>
/// Arc-cosine of x
/// </summary>
#ifndef ACOSD
#define ACOSD(x) (acos(x)/3.14159*180)
#endif
/// <summary>
/// [Coordinates]
///				/|\ [+x] [0 deg]	
///				 |
///				 |
///	 [+y]     [front]	[270 deg]	
///	<---------------------------
///	[90 deg]	 |
///				 |
///				 |
///				 | [180 deg]	
/// </summary>
typedef struct Position {
    /// <summary>
    /// X coordinate. [m]
    /// </summary>
    float x;
    /// <summary>
    /// Y coordinate. [m]
    /// </summary>
    float y;
    /// <summary>
    /// [deg]
    /// </summary>
    float deg;
    inline Position() : x(0),y(0),deg(90) {}
    inline Position(float x_in, float y_in, float deg_in) : x(x_in),y(y_in),deg(deg_in) {}
    inline Position operator += (const Position& pos)
    {
        x += pos.x;
        y += pos.y;
        deg += pos.deg;
        return *this;
    }
    inline bool operator == (const Position& pos) const
    {
        if ((x == pos.x) && (y == pos.y) && (deg == pos.deg))
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    inline bool operator < (const Position& pos) const
    {
        if (x < pos.x)
        {
            return true;
        }
        else if (x > pos.x)
        {
            return false;
        }
        else// x == pos.x
        {
            if (y < pos.y)
            {
                return true;
            }
            else if (y > pos.y)
            {
                return false;
            }
            else// y == pos.y
            {
                if (deg < pos.deg)
                {
                    return true;
                }
                else if (deg > pos.deg)
                {
                    return false;
                }
                else// deg == pos.deg
                {
                    return false;
                }
            }
        }

    }
    //inline bool operator < (const Position pos)
    //{
    //	if (x < pos.x)
    //	{
    //		return true;
    //	}
    //	else if (y < pos.y)
    //	{
    //		return true;
    //	}
    //	else if (deg < pos.deg)
    //	{
    //		return true;
    //	}
    //	else
    //	{
    //		return false;
    //	}
    //}
    inline void Reset()
    {
        x = 0;
        y = 0;
        deg = 90;
    }
    inline void Unify()
    {
        if (deg > 360)
        {
            deg = deg - 360 * ROUND_DOWN(deg/ 360);
        }
        else if (deg < 0)
        {
            deg = deg - 360 * ROUND_UP(deg/ 360);
        }
        if (deg == 360)
        {
            deg = 0;
        }
    }
    static inline float Dist(const Position& pos1, const Position& pos2)
    {
        return sqrt(SQU(pos1.x - pos2.x) + SQU(pos1.y - pos2.y));
    }
    static inline float Deg(const Position& pos1, const Position& pos2)
    {
        float deg = pos1.deg - pos2.deg;
        /// [0,360)
        if (deg > 360)
        {
            deg = deg - 360 * ROUND_DOWN(deg/ 360);
        }
        else if (deg < 0)
        {
            deg = deg - 360 * ROUND_UP(deg/ 360);
        }
        if (deg == 360)
        {
            deg = 0;
        }
        if (deg > 180)
        {
            deg -= 360;
        }
        return deg;
    }
}Position;
class Line
{
public:
    Line(void) {}
    ~Line(void) {}
};
typedef struct Force {
    float x;
    float y;
    inline Force() : x(0),y(0) {}
    inline Force(float fx, float fy) : x(fx),y(fy) {}
    inline Force& operator = (const Force& force)
    {
        x = force.x;
        y = force.y;
        return *this;
    }
    inline Force& operator + (const Force& force)
    {
        x += force.x;
        y += force.y;
        return *this;
    }
    inline Force& operator - (const Force& force)
    {
        x -= force.x;
        y -= force.y;
        return *this;
    }
    inline Force& operator += (const Force& force)
    {
        x += force.x;
        y += force.y;
        return *this;
    }
    inline Force& operator -= (const Force& force)
    {
        x -= force.x;
        y -= force.y;
        return *this;
    }
    inline bool operator == (const Force& force) const
    {
        return (x == force.x && y == force.y);
    }
    inline float deg() const//deg
    {
        float deg = atan2(y,x)/M_PI*180;
        if (deg > 360)
        {
            deg = deg - 360 * ROUND_DOWN(deg/ 360);
        }
        else if (deg < 0)
        {
            deg = deg - 360 * ROUND_UP(deg/ 360);
        }
        if (deg == 360)
        {
            deg = 0;
        }
        return deg;
    }
    inline float angle() const//radian
    {
        return atan2(y,x);
    }
    inline float norm() const
    {
        return sqrt(x*x + y*y);
    }
}Force;
typedef struct Obstacle
{
    // Relative to the robot, always greater than 0. [m]
    float dist;
    // Relative to the heading direction of the robot.
    // CCW is positive and CW is negative;
    float deg;
    /// <summary>
    /// Confidence rate
    /// </summary>
    float conf;
    Obstacle()
    {
        dist = 0;
        deg = 0;
        conf = 0;
    }
    Obstacle(float deg_in, float dist_in, float conf_in)
    {
        dist = dist_in;
    }
    inline void Unify()/// [0,360)
    {
        deg = UNIFY360(deg);
    }
}Obstacle;
}}// namespace
#endif// WIZROBO_BCU_BCU_GEOMETRY_H
