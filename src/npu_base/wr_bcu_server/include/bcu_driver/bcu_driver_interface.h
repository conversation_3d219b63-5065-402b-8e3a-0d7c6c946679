#ifndef WIZROBO_BCU_DRIVER_BCU_DRIVER_INTERFACE_H
#define WIZROBO_BCU_DRIVER_BCU_DRIVER_INTERFACE_H
#include <boost/thread.hpp>
#include "bcu_driver.h"
#include "bcu_driver_geometry.h"
#include "bcu_driver_param.h"
#include "bcu_driver_data.h"
#include "bcu_driver_serial_port.h"

//#include "bcu_server.h"

namespace wizrobo { namespace bcu_driver {

class IBcuDriver
{
protected:
    static const int MAX_WHEEL_NUM = 4;

protected:
    IBcuDriver()
        : is_inited_(FALSE)
    {}

public:
    virtual ~IBcuDriver()
    {}

public:
    inline int IsInited()
    {
        return is_inited_;
    }

    virtual int Init(BcuParamPtr ptr)
    {
        bcu_param_ptr = ptr;
        return 0;
    }

    virtual int GetMotorParam()
    {
        WR_DEBUG("IBcuDriver(TODO)::GetMotorParam()");
        return TRUE;
    }
    virtual int GetMotorStatus(MotorStatus& motor_status)
    {
        WR_DEBUG("IBcuDriver(TODO)::GetMotorStatus()");
        return TRUE;
    }
    virtual int SetMotorParam()
    {
        WR_DEBUG("IBcuDriver(TODO)::SetMotorParam()");
        return TRUE;
    }
    virtual int GetSensorParam()
    {
        WR_DEBUG("IBcuDriver(TODO)::GetSensorParam()");
        return TRUE;
    }

    virtual int SetSensorParam()
    {
        WR_DEBUG("IBcuDriver(TODO)::SetSensorParam()");
        return TRUE;
    }

    virtual int GetPidParam()
    {
        WR_DEBUG("IBcuDriver(TODO)::GetPidParam()");
        return TRUE;
    }
    virtual int SetPidParam()
    {
        WR_DEBUG("IBcuDriver(TODO)::SetPidParam()");
        return TRUE;
    }

    virtual int Enable(bool on)
    {
        WR_DEBUG("IBcuDriver(TODO)::Enable()");
        return TRUE;
    }
    virtual int Brake(bool on)
    {
        WR_DEBUG("IBcuDriver(TODO)::Brake()");
        return TRUE;
    }
    //virtual int Direction(bool on)
    virtual int Stop()
    {
        WR_DEBUG("IBcuDriver(TODO)::Stop()");
        return TRUE;
    }

    virtual int SetMotorSpd(const MotorSpdArray& rpms) // motor spd
    {
        WR_DEBUG("IBcuDriver(TODO)::SetMotorSpd()");
        return TRUE;
    }
    virtual int GetMotorSpd(MotorSpdArray& rpms) // motor spd
    {
        WR_DEBUG("IBcuDriver(TODO)::GetMotorSpd()");
        return TRUE;
    }
    virtual int GetSteerAngle(float& angle, wizrobo_npu::SteerEncLocation& location)
    {
        WR_DEBUG("IBcuDriver(TODO)::GetSteerAngle()");
        return TRUE;
    }
    virtual int SwpMotorSpd(const MotorSpdArray& cmd_rpms, MotorSpdArray& act_rpms) // motor spd
    {
        WR_DEBUG("IBcuDriver(TODO)::SwpMotorSpd()");
        return TRUE;
    }
    virtual int GetMotorEnc(MotorEncArray& arr) // enc
    {
        WR_DEBUG("IBcuDriver(TODO)::GetMotorEnc()");
        return TRUE;
    }
    virtual int ClrMotorEnc()
    {
        WR_DEBUG("IBcuDriver(TODO)::ClrMotorEnc()");
        return TRUE;
    }

    virtual int SetUserIo(InputChannel input_channel, OutputMode mode, int loop_cnt)
    {
        WR_DEBUG("IBcuDriver(TODO)::SetUserIo()");
        return TRUE;
    }

    virtual int GetImuData(ImuData& imu)
    {
        WR_DEBUG("IBcuDriver(TODO)::GetImuData()");
        return TRUE;
    }

    virtual int SetBeepStatus(OutputMode mode, int loop_cnt)
    {
        WR_DEBUG("IBcuDriver(TODO)::SetBeepStatus()");
        return TRUE;
    }
    virtual int SetLampStatus(LampChannel lamp_channel, OutputMode mode, int loop_cnt)
    {
        WR_DEBUG("IBcuDriver(TODO)::SetLampStatus()");
        return TRUE;
    }
    virtual int SetBlnStatus(BlnStatus bln_status, OutputMode mode)
    {
        WR_DEBUG("IBcuDriver(TODO)::SetBlnStatus()");
        return TRUE;
    }
    virtual int SetBrushStatus(BrushChannel brush_channel,BrushRcc brush_rcc,int loop_cnt)
    {
        WR_DEBUG("IBcuDriver(TODO)::SetBrushStatus()");
        return TRUE;
    }
    virtual int SetFanStatus(FanChannel fan_channel,FanRcc fan_rcc,int loop_cnt)
    {
        WR_DEBUG("IBcuDriver(TODO)::SetBrushStatus()");
        return TRUE;
    }
    virtual int SetSbStatus(SbStatus mode, int loop_cnt)
    {
        WR_DEBUG("IBcuDriver(TODO)::SetSbStatus()");
        return TRUE;
    }
    virtual int GetBmsData(BmsData& bms)
    {
        WR_DEBUG("IBcuDriver(TODO)::GetBmsData()");
        return TRUE;
    }
    virtual int GetSensorData(SensorData& sensor_data)
    {
        WR_DEBUG("IBcuDriver(TODO)::GetSensorData()");
        return TRUE;
    }
    virtual int GetSht3xData(Sht3xData& sht3x_data)
    {
        WR_DEBUG("IBcuDriver(TODO)::GetSht3xData()");
        return TRUE;
    }
    virtual int GetWaterLevel(WaterLevel& water_level)
    {
        WR_DEBUG("IBcuDriver(TODO)::GetWaterLevel()");
        return TRUE;
    }
    virtual int GetUsageTime(UsageTime& usage_time)
    {
        WR_DEBUG("IBcuDriver(TODO)::GetUsageTime()");
        return TRUE;
    }

public:
    BcuParamPtr bcu_param_ptr;

protected:
    int is_inited_;
};
}}// namespace
#endif// WIZROBO_BCU_DRIVER_BCU_DRIVER_INTERFACE_H
