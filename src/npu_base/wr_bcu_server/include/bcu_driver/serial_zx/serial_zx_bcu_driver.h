#ifndef WIZROBO_BCU_DRIVER_ZXFDK_SERIAL_BCU_DRIVER_H
#define WIZROBO_BCU_DRIVER_ZXFDK_SERIAL_BCU_DRIVER_H

#include "serial_zx_bcu_driver_protocol.h"

namespace wizrobo { namespace bcu_driver {

class SerialZXFdkBcuDriver : public IBcuDriver
{
public:
    SerialZXFdkBcuDriver();
    ~SerialZXFdkBcuDriver();

    int Init(BcuParamPtr ptr);
    int Stop();

    int GetMotorSpd(MotorSpdArray& rpm); // hall sensor
    int SetMotorSpd(const MotorSpdArray& spd); // -1000~1000
    int SwpMotorSpd(const MotorSpdArray& cmd_rpm, MotorSpdArray& act_rpm)  {}
    int GetMotorEnc(MotorEncArray& ticks);  // enc
//    int ClrMotorEnc();

private:
    int WriteCmd(const char* cmd, uint len);
    int ReceiveAns(std::string& ans);
    int ParseMotorSpd(const std::string& ans, int &rpm);
    int ParseEncData(const std::string& ans, int &enc);
    int CheckAns(const std::string& ans);
    void PrintAns(const std::string& ans);
    bool EnbDriver(bool flag);
    void ConvertUncharToStr(char* str, unsigned char* Unchar, int ucLen);
    void ConvertStrToUnChar(const std::string&  str, unsigned char* UnChar,int len);
    void ConvertShortToStr(char* str,short data);
    void GetCheckSum(char* str, unsigned char* UnChar,int len);

private:
    SerialPort serial_port_;
};

}}  // namespace

#endif  // WIZROBO_BCU_DRIVER_ZXFDK_SERIAL_BCU_DRIVER_H
