#ifndef WIZROBO_BCU_DRIVER_SERIAL_ZXFDK_BCU_DRIVER_PROTOCOL_H
#define WIZROBO_BCU_DRIVER_SERIAL_ZXFDK_BCU_DRIVER_PROTOCOL_H

#include "bcu_driver_interface.h"

namespace wizrobo { namespace bcu_driver { namespace serial_zxfdk{

const std::string MTR_SOFT_START(":0106105A000A85\r\n");
const std::string MTR_SOFT_STOP(":0106105A00018E\r\n");
const std::string SET_MTR_SPD("011011AA000208");
const std::string SET_LEFT_MTR_SPD("0106100D");
const std::string SET_RIGHT_MTR_SPD("010610E5");
const std::string MTR_EMERGENCY_SHUTDOWN("!EX ");
const std::string MTR_RELEASE_SHUTDOWN("!MG ");

const std::string GET_MTR_CURRENT("?A");
const std::string GET_BATTERY_CURRENT("?BA");
const std::string GET_MTR_SPD("010311EE0002FB");
const std::string GET_LEFT_MTR_SPD("100C");
const std::string GET_RIGHT_MTR_SPD("10E4");

const std::string GET_MTR_ENC("010311EA0004");
const std::string GET_LEFT_MTR_ENC(":010310080002E2\r\n");
const std::string GET_RIGHT_MTR_ENC(":010310E000020A\r\n");

//const std::string GET_LEFT_MTR_ENC("1008");
//const std::string GET_RIGHT_MTR_ENC("10E0");

const std::string GET_FEEDBACK("?F");
const std::string COLON(":");
const std::string CMD_ADDRESS("01");
const std::string CMD_READ("03");
const std::string CMD_WRITE("06");
const std::string CMD_END("\r\n");

const std::string PARITY("EVEN");
const int BAUDRATE = B9600;
const int DATABITS = 7;

const int MAX_MOTOR_SPD = 1500;
const int MIN_MOTOR_SPD = -1500;
const int CMD_LEN = 3;
const int MIN_ANS_LEN = 9;
const int MOTOR_ID_MAP[2][2] = {
    { 0, 1},
    { 1, 0},
};

}}} // namespace

#endif  // WIZROBO_BCU_DRIVER_SERIAL_ZXFDK_BCU_DRIVER_PROTOCOL_H
