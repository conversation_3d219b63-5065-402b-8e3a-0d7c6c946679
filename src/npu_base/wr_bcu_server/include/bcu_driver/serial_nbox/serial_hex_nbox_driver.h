#ifndef WIZROBO_BCU_DRIVER_SERIAL_HEX_NBOX_DRIVER_H
#define WIZROBO_BCU_DRIVER_SERIAL_HEX_NBOX_DRIVER_H
//#include <deque>
//#include <iostream>
//#include <iomanip>

#include "bcu_driver_interface.h"
#include "bcu_driver_serial_port.h"
#define UPPER
#include "bcu_protocol.h"

namespace wizrobo { namespace bcu_driver { namespace serial_nbox {

const std::string PARITY("NONE");
const int BAUDRATE = B115200;
const int DATABITS = 8;

typedef struct _BcuDataBuffer
{
    unsigned char * cur_cell;
    unsigned int    status;
    unsigned int    length;
    unsigned int    check_sum;
    unsigned char   buffer[bcup::RECV_BUFFER_LEN];
    _BcuDataBuffer()
    {
        for (int i=0;i<bcup::RECV_BUFFER_LEN;i++)
        {
            buffer[i] = 0x00;
        }
        cur_cell = &(buffer[0]);
        length = 0;
        status = 0;
        check_sum = 0;
    }

    int Push(unsigned char b)
    {
        *cur_cell = b;
        cur_cell++;
        length++;
        check_sum += b;
        return 0;
    }

    int Reset()
    {
        for (int i=0;i<length;i++)
        {
            buffer[i] = 0x00;
        }
        cur_cell = &(buffer[0]);
        length = 0;
        status = 0;
        check_sum = 0;
    }

    int CheckFrame()
    {
        int rtn = 0;
        unsigned char target_length;
        unsigned char t_check_sum;
        bcup::BcuProtocolHead *p_head = (bcup::BcuProtocolHead *)(&(buffer[0]));
        switch (status)
        {
        case 0:
            if (p_head->flag_a == bcup::FRAME_FLAGA)
            {
                status = 1;
            }
            else
            {
                rtn = -1;
            }
            break;
        case 1:
            if (p_head->flag_b == bcup::FRAME_FLAGB)
            {
                status = 2;
            }
            else
            {
                rtn = -2;
            }
            break;
        case 2:
            if (length >= bcup::HEAD_LEN)
            {
                if (length < bcup::MAX_FRAME_LEN)
                {
                    status = 3;
                }
                else
                {
                    rtn = -3;
                }
            }
            break;
        case 3:
            target_length = p_head->length + bcup::HEAD_LEN + 1;
            if (length >= target_length)
            {
                t_check_sum = buffer[length - 1];
                check_sum = (check_sum - t_check_sum ) & 0x000000FF;
                if (t_check_sum == check_sum)
                {
                    status = 0;
                    rtn = 1;
                }
                else
                {
                    rtn = -4;
                }
            }
            break;
        default:
            rtn = -6;
            break;
        }
        return rtn;
    }

}BcuDataBuffer;

struct AnalogConverter
{
    int bit_width_;
    int max_digital_value;
    float ref_value_;
    AnalogConverter()
    {
        AnalogConverter(8, 5.0);
    }
    AnalogConverter(int bit_width, float ref_value)
    {
        bit_width_ = bit_width;
        max_digital_value = 0xFFFFFFFF >> (32-bit_width);
        ref_value_ = ref_value;
    }

    int ToDigitalValue(float analog_value)
    {
        int digital_value = static_cast<int>((max_digital_value+1) * analog_value / ref_value_);
        digital_value = RANGE(digital_value, 0, max_digital_value);
        return digital_value;
    }

    float ToAnalogValue(int digital_value)
    {
        float analog_value = ref_value_ * digital_value / (max_digital_value+1);
        analog_value = RANGE(analog_value, 0, ref_value_);
        return analog_value;
    }
};
typedef AnalogConverter* AnalogConverterPtr;

struct AnalogDevice
{
    float k_;
    float offset_;
    AnalogConverterPtr converter_ptr_;

    AnalogDevice()
    {
        AnalogDevice(NULL, 1.0, 0.0);
    }

    AnalogDevice(AnalogConverterPtr converter_ptr, float k, float offset)
    {
        k_ = k;
        offset_ = offset;
        converter_ptr_ = converter_ptr;
    }

    float ToDigitalValue(float real_value)
    {
        float analog_value = (real_value - offset_) / k_;
        int digital_value = converter_ptr_->ToDigitalValue(analog_value);
        return digital_value;
    }

    float ToRealValue(int digital_value)
    {
        float analog_value = converter_ptr_->ToAnalogValue(digital_value);
        float real_value = (analog_value * k_) + offset_;
        return real_value;
    }
};
}}}

namespace wizrobo { namespace bcu_driver {

class SerialHexNboxDriver : public IBcuDriver
{
public:
    SerialHexNboxDriver()
    {
    }

    virtual ~SerialHexNboxDriver()
    {
        if (is_inited_)
        {
            Stop();
            Enable(false);
        }
        if (serial_port_.IsOpen() == TRUE)
        {
            serial_port_.Close();
        }
    }

public:
    //// IBcuDriver interfaces
    int Init(BcuParamPtr ptr);

    int GetMotorParam();
    int SetMotorParam();
    int GetSensorParam();
    int SetSensorParam();
    int GetPidParam();
    int SetPidParam();

    int Enable(bool on);
    int Brake(bool on);
    int Stop();

    int GetMotorSpd(MotorSpdArray& rpms);
    int SetMotorSpd(const MotorSpdArray& rpms);

    int GetMotorEnc(MotorEncArray& ticks);
    int ClrMotorEnc();

    int SetUserIo(wizrobo::bcu_driver::InputChannel input_channel, wizrobo::bcu_driver::OutputMode mode, int loop_cnt);
    int GetImuData(ImuData& imu);

    int SetBeepStatus(wizrobo::bcu_driver::OutputMode mode, int loop_cnt);
    int SetLampStatus(wizrobo::bcu_driver::LampChannel lamp_channel, wizrobo::bcu_driver::OutputMode mode, int loop_cnt);
    int GetSensorData(SensorData& sensor_data);

private:
    //// init
    int CheckPort()
    {
        if (serial_port_.IsOpen() == FALSE)
        {
            WR_ERROR("Serial Port Opening failed.");
            return FALSE;
        }
        return TRUE;
    }
    //// cmd
    int WriteCmd(const byte* cmd, int cmd_len);
    int ReceiveAns();

private:
    //// init
    int SyncParam();
    int InitOnce();
    int GetVersionId(int& id);
    int SendRequest(unsigned char cmd, unsigned char *p_data, unsigned char length);
    int RecvResponse(unsigned char cmd, unsigned char *p_data, unsigned char length);
private:
    //// setting
    static const bool IS_ALWAYS_CHECK_PORT = true;
    //// com
    SerialPort serial_port_;
    serial_nbox::BcuDataBuffer bcu_data_buffer_;
    serial_nbox::AnalogConverterPtr ad_cvt_ptr_;
    serial_nbox::AnalogConverterPtr da_cvt_ptr_;
    std::vector<serial_nbox::AnalogDevice> main_power_;
    std::vector<serial_nbox::AnalogDevice> battery_;
    std::vector<serial_nbox::AnalogDevice> infrd_;
    std::vector<serial_nbox::AnalogDevice> dac_left_;
    std::vector<serial_nbox::AnalogDevice> dac_right_;

    int adc_bit_width_;
    float adc_ref_v_;
    int dac_bit_width_;
    float dac_ref_v_;
};
}}// namespace
#endif// WIZROBO_BCU_DRIVER_SERIAL_HEX_BCU_DRIVER_H
