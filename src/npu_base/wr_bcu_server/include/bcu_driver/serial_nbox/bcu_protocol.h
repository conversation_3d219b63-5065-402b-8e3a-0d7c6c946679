#ifndef BCU_PROTOCOL_H
#define BCU_PROTOCOL_H

#ifdef UPPER
namespace wizrobo { namespace bcu_driver { namespace serial_nbox { namespace bcup {
#else
#include "stm32f4xx.h"
#endif

typedef float           F32;
typedef char            S8;
typedef unsigned char   U8;
typedef short           S16;
typedef unsigned short  U16;
typedef int             S32;
typedef unsigned int    U32;

#ifdef UPPER
static const int VERSION_MAIN = 2;
static const int VERSION_SUB  = 0;

static const int FRAME_FLAGA = 0x55;
static const int FRAME_FLAGB = 0xAA;

static const int HEAD_LEN        = 4;
static const int MAX_FRAME_LEN   = 32;
static const int RECV_BUFFER_LEN = 100;

static const int MAX_MOTOR_NUM = 4;
static const int ACT_MOTOR_NUM = 2;

static const int MAX_SONAR_NUM = 12;
static const int ACT_SONAR_NUM = 12;

static const int MAX_BMP_NUM = 8;
static const int ACT_BMP_NUM = 8;

static const int MAX_INFRD_NUM = 8;
static const int ACT_INFRD_NUM = 8;

static const int MAX_CARLAMP_NUM = 4;
static const int ACT_CARLAMP_NUM = 4;

static const int MAX_GPIO_INPUT_NUM = 8;
static const int ACT_GPIO_INPUT_NUM = 8;

static const int MAX_GPIO_OUTPUT_NUM = 8;
static const int ACT_GPIO_OUTPUT_NUM = 8;
#else
#define VERSION_MAIN (2)
#define VERSION_SUB  (0)

#define FRAME_FLAGA (0x55)
#define FRAME_FLAGB (0xAA)

#define HEAD_LEN        (4)
#define MAX_FRAME_LEN   (32)
#define RECV_BUFFER_LEN (100)

#define MAX_MOTOR_NUM (4)
#define ACT_MOTOR_NUM (2)

#define MAX_SONAR_NUM (12)
#define ACT_SONAR_NUM (12)

#define MAX_BMP_NUM (8)
#define ACT_BMP_NUM (8)

#define MAX_INFRD_NUM (8)
#define ACT_INFRD_NUM (8)

#define MAX_CARLAMP_NUM (4)
#define ACT_CARLAMP_NUM (4)

#define MAX_GPIO_INPUT_NUM (8)
#define ACT_GPIO_INPUT_NUM (8)

#define MAX_GPIO_OUTPUT_NUM (8)
#define ACT_GPIO_OUTPUT_NUM (8)
#endif

typedef enum _BrakeType
{
    SOFT_BRK = 0x00,
    HARD_BRK
}BrakeType;

#ifdef UPPER
typedef enum _EnableType
{
    DISABLE = 0x00,
    ENABLE
}EnableType;
#else
typedef FunctionalState EnableType;
#endif
typedef enum _DirType
{
    BACKWORD = 0x00,
    FORWARD
}DirType;

typedef enum _ValidLevelType
{
    LOW_VALID = 0x00,
    HIGH_VALID
}ValidLevelType;

typedef enum _SyntropyType
{
    REVERSE = 0x00,
    SYNTROPY
}SyntropyType;


typedef enum OutputMode
{
    OUTPUT_MODE_KEEP_HIGH = 0,
    OUTPUT_MODE_KEEP_LOW,
    OUTPUT_MODE_BLINK_1,
    OUTPUT_MODE_BLINK_2,
    OUTPUT_MODE_BLINK_3
}OutputMode;

typedef enum _BumperChannel
{
    BUMPER_0 = 0,
    BUMPER_1,
    BUMPER_2,
    BUMPER_3,

    BUMPER_4,
    BUMPER_5,
    BUMPER_6,
    BUMPER_7
}BumperChannel;

typedef enum _InputChannel
{
    INPUT_USER_0 = 0,
    INPUT_USER_1,
    INPUT_USER_2,
    INPUT_USER_3,

    INPUT_USER_4,
    INPUT_USER_5,
    INPUT_USER_6,
    INPUT_USER_7
}InputChannel;

typedef enum _OutputChannel
{
    OUTPUT_USER_0 = 0,
    OUTPUT_USER_1,
    OUTPUT_USER_2,
    OUTPUT_USER_3,

    OUTPUT_USER_4,
    OUTPUT_USER_5,
    OUTPUT_USER_6,
    OUTPUT_USER_7
}OutputChannel;

typedef enum _LampChannel
{
    LAMP_FRONT_LEFT  = 0,
    LAMP_FRONT_RIGHT = 1,
    LAMP_BACK_LEFT   = 2,
    LAMP_BACK_RIGHT  = 3
}LampChannel;

typedef struct _BcuProtocolHead
{
    U8 flag_a;
    U8 flag_b;
    U8 length;
    U8 cmd;

    U8 first_byte;
    U8 reserve[3];
}BcuProtocolHead;

typedef struct _BeepStatus
{
    U8 mode;
    S8 repeat;
}BeepStatus;

typedef struct _OutputStatus
{
    U8 channel;
    U8 mode;
    S8 repeat;
}OutputStatus;

typedef OutputStatus LampStatus;

typedef struct _ControlParam
{
    U8  motor_dir[MAX_MOTOR_NUM]; /* [4] */
    U8  enc_dir[MAX_MOTOR_NUM];   /* [4] */

    U8  motor_num;
    U8  motor_brk_type;
    U8  enb_auxdir;
    U8  enb_slow_launch;

    U8  enb_vol;
    U8  dir_vol;
    U8  pwm_vol;
    U8  brk_vol;

    U16 motor_enc_res_ppr;
    U16 motor_pwm_frq_hz;

    U16 dac_offset_v_left;
    U16 dac_offset_v_right;

    F32 motor_max_rpm;
    F32 motor_start_rpm;
    F32 motor_acc_rpm;
    F32 motor_rdc_ratio;
}ControlParam;

typedef struct _SensorParam
{
    U8  bmp_vol;
    U8  esb_vol;
    U8  brk_vol;
    U8  dead_time;
    U16 infrd_threshold[MAX_INFRD_NUM];
}SensorParam;

typedef struct _PidParam
{
    F32 kp_acc;
    F32 kp_dec;
    F32 ki_acc;
    F32 ki_dec;
    F32 kd_acc;
    F32 kd_dec;
    U8  enb_pid;
    U8  reserve[3];
}PidParam;

typedef struct _MotorSpd
{
    F32 rpm[MAX_MOTOR_NUM];
    U8  motor_num;
    U8  reserve[3];
}MotorSpd;

typedef struct _MotorEnc
{
    S32 enc[MAX_MOTOR_NUM];
    U8  motor_num;
    U8  reserve[3];
}MotorEnc;

typedef struct _SensorData
{
    U16 sonar[MAX_SONAR_NUM];   /* [12] */
    U16 infrd[MAX_INFRD_NUM];   /* [8] */

    U16 power_vol;
    U16 battery_vol;

    U8 esb;
    U8 bumper;
    U8 io_input;
    U8 reserve;
}SensorData;

typedef struct _ImuGyrData
{
    S16 x;
    S16 y;
    S16 z;
    U8  reserve[2];
}ImuGyrData;

typedef struct _ImuAccData
{
    S16 x;
    S16 y;
    S16 z;
    U8  reserve[2];
}ImuAccData;

typedef struct _ImuMagData
{
    S16 x;
    S16 y;
    S16 z;
    U8  reserve[2];
}ImuMagData;

typedef struct _ImuRpyData
{
    F32 roll;
    F32 pitch;
    F32 yaw;
}ImuRpyData;

typedef struct _ImuData
{
    ImuGyrData gyr;
    ImuAccData acc;
    ImuMagData mag;
    ImuRpyData rpy;
}ImuData;

typedef struct _BmsData
{
    F32 vol;
    F32 temp;
    F32 capacity;
    F32 level;  //0~100%
}BmsData;

typedef enum _CmdType
{
    GET_VER_ID  = 0x0A,

    SET_CTL_PARA = 0xFB,
    GET_CTL_PARA = 0x0B,
    SET_PID_PARA = 0xFC,
    GET_PID_PARA = 0x0C,
    SET_SNR_PARA = 0xFD,
    GET_SNR_PARA = 0x0D,

    SET_MTR_ENB = 0xF0,
    SET_MTR_BRK = 0xF8,
    SET_MTR_DIR = 0xF9,
    SET_MTR_SPD = 0xF1,
    GET_MTR_SPD = 0x01,
    SWP_MTR_SPD = 0xA1,
    GET_MTR_ENC = 0x02,
    CLR_MTR_ENC = 0xA2,

    GET_IO_STATUS = 0x05,
    SET_USER_IO   = 0xF5,
    GET_AD_DATA   = 0x06,
    GET_IMU_DATA  = 0x10,

    SET_BEEP_STATUS = 0xF4,
    SET_LAMP_STATUS = 0xF6,
    GET_BMS_DATA    = 0x0E,  //Battery
    GET_SNR_DATA    = 0x03,  //Sensor
    SET_RESET       = 0xFE,
}CmdType;

#ifdef UPPER
}}}}
#endif
#endif// BCU_PROTOCOL_H
