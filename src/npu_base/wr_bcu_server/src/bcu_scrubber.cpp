#include "bcu_scrubber.h"

#include <exception>
#include <climits>
#include <npu_enum_ext.h>

#define ENB_BEEP 1
#define SNSR_DEBUG 0
#define OPT_CB_DEBUG 0
#define WATER_LV_DEBUG 0
#define EXEC_CLEANING_STATE_DEBUG 0
#define UPDATE_CLEANING_STATE_BY_NEW_TOPIC 1

namespace wizrobo {

using namespace wizrobo::enum_ext;

BcuScrubber::BcuScrubber(int argc, char **argv)
    : BcuServer(argc, argv)
    , lamp_status_()
    , working_error_()
    , beep_loop_(1)
    , valve_loop_(1)
    , lift_status_(UP)
    , working_status_()
    , scrb_opt_topic_("")
    , npu_idle("IDLE_STATE")
    , npu_navi("NAVI_STATE")
    , npu_slam("SLAM_STATE")
    , scrb_str_opt_topic_("")
    , low_power_threshold_(1.0)
    , fan_lv_(SpeedLevel::STOP)
    , enb_error_reacting_(true)
    , is_new_cleaning_cmd_(true)
    , scrb_usage_time_topic_("")
    , brush_lv_(SpeedLevel::STOP)
    , valve_lv_(SpeedLevel::STOP)
    , scrb_water_level_topic_("")
    , scrb_motor_status_topic_("")
    , scrb_system_error_topic_("")
    , scrb_switch_status_topic_("")
    , scrb_battery_status_topic_("")
    , scrb_working_status_topic_("")
    , scrb_inner_tmp_data_topic_("")
    , enb_pub_battery_status_(false)
    , scrb_inner_hmdt_data_topic_("")
    , scrb_water_valve_status_topic_("")
    , infrd_warning_threshold_(0.2)
    , sonar_warning_threshold_(0.2)
    , beep_status_(OUTPUT_MODE_KEEP_HIGH)
    , bl_status_(BreathLightStatus::BLN_WHITE_BREATHE)
{
    working_error_.system_report.resize(1);
    working_error_.system_report[0] = wizrobo_npu_scrubber::SystemStatusReminder::BATTERY_NORMAL;
}

BcuScrubber::~BcuScrubber()
{
    SetBlnStatus(REDCLOSE);
    SetBlnStatus(WHITECLOSE);
    SetFanStatus(SpeedLevel::STOP);
    SetBrushStatus(SpeedLevel::STOP);
    SetValveStatus(SpeedLevel::STOP, 1);
    SetSbStatus(LiftStatus::UP);
}

void BcuScrubber::OnInit()
{
    LM_DEBUG("");
    ros::NodeHandle& nh = (*p_nh_);
    ros::NodeHandle& prv_nh = (*p_prv_nh_);

    prv_nh.param<int>("main_loop_frq", main_loop_frq_, 100);
    if (main_loop_frq_ != 100) {
        LM_WARN("main_loop_frq_(%d) is NOT 100", main_loop_frq_);
    }

    prv_nh.param<bool>("enb_pub_battery_status", enb_pub_battery_status_, true);
    prv_nh.param<bool>("enb_error_reacting", enb_error_reacting_, true);
    prv_nh.param<float>("low_power_threshold", low_power_threshold_, 30);
    prv_nh.param<std::string>("battery_status_topic", scrb_battery_status_topic_, SCRB_BATTERY_STATUS_TOPIC);
    prv_nh.param<std::string>("switch_status_topic", scrb_switch_status_topic_, SCRB_SWITCH_STATUS_TOPIC);
    prv_nh.param<std::string>("inner_tmp_data_topic", scrb_inner_tmp_data_topic_, SCRB_INNER_TMP_DATA_TOPIC);
    prv_nh.param<std::string>("inner_hmdt_data_topic", scrb_inner_hmdt_data_topic_, SCRB_INNER_HMDT_DATA_TOPIC);
    prv_nh.param<std::string>("working_status_topic", scrb_working_status_topic_, SCRB_WORKING_STATUS_TOPIC);
    prv_nh.param<std::string>("opt_topic", scrb_opt_topic_, SCRB_OPT_TOPIC);
    prv_nh.param<std::string>("str_opt_topic", scrb_str_opt_topic_, SCRB_STR_OPT_TOPIC);
    prv_nh.param<std::string>("system_error_topic", scrb_system_error_topic_, SCRB_SYSTEM_ERROR_TOPIC);
    prv_nh.param<std::string>("scrb_motor_status_topic", scrb_motor_status_topic_, SCRB_MOTOR_STATE_TOPIC);
    prv_nh.param<std::string>("scrb_water_valve_status_topic", scrb_water_valve_status_topic_, SCRB_WATER_VALVE_STATUS_TOPIC);
    prv_nh.param<std::string>("scrb_water_level_topic", scrb_water_level_topic_, SCRB_WATER_LEVEL_TOPIC);
    prv_nh.param<std::string>("scrb_water_level_asta_topic_", scrb_water_level_asta_topic_, SCRB_WATER_LEVEL_TOPIC_ASTA);
    prv_nh.param<std::string>("scrb_clean_water_level_topic_", scrb_clean_water_level_topic_, SCRB_CLEAR_WATER_LEVEL_TOPIC);
    prv_nh.param<std::string>("scrb_dirty_water_level_topic_", scrb_dirty_water_level_topic_, SCRB_DIRTY_WATER_LEVEL_TOPIC);
    prv_nh.param<std::string>("scrb_usage_time_topic", scrb_usage_time_topic_, SCRB_USAGE_LEVEL_TOPIC);

    battery_status_pub_ = nh.advertise<wr_npu_msgs::BatteryStatus>(scrb_battery_status_topic_, 1);
    switch_status_pub_ = nh.advertise<wr_npu_msgs::EmergencyStopData>(scrb_switch_status_topic_, 1);
    inner_tmp_data_pub_ = nh.advertise<std_msgs::Float32>(scrb_inner_tmp_data_topic_, 1);
    inner_hmdt_data_pub_ = nh.advertise<std_msgs::Float32>(scrb_inner_hmdt_data_topic_, 1);
    working_status_pub_ = nh.advertise<wr_npu_msgs::ScrubberWorkingStatus>(scrb_working_status_topic_, 1);
    motor_state_pub_ = nh.advertise<wr_npu_msgs::ScrubberMotorStateALL>(scrb_motor_status_topic_, 1);
    water_valve_pub_ = nh.advertise<wr_npu_msgs::ScrubberWaterValveStatus>(scrb_water_valve_status_topic_, 1);
    water_level_asta_pub_ = nh.advertise<wr_npu_msgs::ScrubberWaterLevel>(scrb_water_level_asta_topic_, 1);
    clean_water_level_pub_ = nh.advertise<std_msgs::Int32>(scrb_clean_water_level_topic_, 1);
    dirty_water_level_pub_ = nh.advertise<std_msgs::Int32>(scrb_dirty_water_level_topic_, 1);
    water_level_pub_ = nh.advertise<wr_npu_msgs::WaterTank>(scrb_water_level_topic_, 1);
    usage_time_pub_ = nh.advertise<wr_npu_msgs::ScrubberUsageTime>(scrb_usage_time_topic_, 1);
    security_status_pub_ = nh.advertise<wr_npu_msgs::ScrubberSecurityStatus>(SCRB_SECURITY_STATUS_TOPIC, 1);

    //sub
    scrubber_opt_sub_ = nh.subscribe<wr_npu_msgs::ScrubberOpt>(scrb_opt_topic_, 10, &BcuScrubber::ScrubberOptCallback, this);
    scrubber_str_opt_sub_ = nh.subscribe<wr_npu_msgs::ScrubberStrOpt>(scrb_str_opt_topic_, 10, &BcuScrubber::ScrubberStrOptCallback, this);
    system_error_sub_ = nh.subscribe<wr_npu_msgs::SystemStatus>(scrb_system_error_topic_, 10, &BcuScrubber::SystemErrorCallback, this);
    is_em_stop_restoring_ = nh.subscribe<std_msgs::Bool>(SCRB_RESTORE_STATUS_TOPIC, 1, &BcuScrubber::ObstacleRestoreCallBack, this);

    //servive client
    wheel_motor_status_client_ = nh.serviceClient<wr_npu_msgs::ScrubberGetMotorStatus>("/scrubber_get_motor_status");
#if ENB_BEEP
    SetBeepStatus(BeepStatus::BEEP_CLOSE, 1);
#endif
    return;
}

void BcuScrubber::SensorRunOnce()
{
    static int cnt = 0;
    ++cnt;
    int time_piece = cnt % 100;

    if (time_piece == 1) { /* 1Hz */
        UpdateBmsData();
    }
    if (time_piece == 2) { /* 1Hz */
        UpdateSht3xData();
    }
    if (time_piece % 10 == 3) { /* 10Hz */
        UpdateSensorData();
    }
    if (time_piece % 10 == 4) { /* 10Hz */
        UpdateCleaningState();
    }
    if (bcu_param_.sensor.enb_imu && time_piece % 5 == 0) { /* 20Hz */
        UpdateImuData();
    }
#if 0
    if (time_piece % 10 == 6) { /* 10Hz */
        UpdateWaterLevel();
    }
#endif
    if (time_piece % 10 == 7) { /* 10Hz */
        UpdateMotorState();
    }
    if (time_piece % 10 == 8) { /* 10Hz */
        UpdateUsageTime();
    }
    if (time_piece % 10 == 9) { /* 10Hz */
        UpdateCleaningState();
    }
    if (time_piece % 10 == 0) { /* 10Hz */
    }
#if 0
    if (time_piece % 10 == 5) { /* 10Hz */
        UpdateLampAndIoState();
    }
#endif
    if (enb_error_reacting_) {
        ProcessWorkErr();
    }
}

void BcuScrubber::ScrubberOptCallback(const wr_npu_msgs::ScrubberOpt::ConstPtr &msg)
{
    is_new_cleaning_cmd_ = true;
    if (msg->bl_status.size() == 1) {
        bl_status_ = BreathLightStatus(msg->bl_status[0]);
        LM_DEBUG("bl_status_: %d", bl_status_);
#if OPT_CB_DEBUG
        LM_INFO("bl_status_: %s", EnumString<BreathLightStatus>::EnumToStr(BreathLightStatus(bl_status_)).c_str());
#endif
    }
    if (msg->lift_status.size() == 1) {
        lift_status_ = LiftStatus(msg->lift_status[0]);
        LM_DEBUG("lift_status_: %d", lift_status_);
#if OPT_CB_DEBUG
        LM_INFO("lift_status_: %s", EnumString<LiftStatus>::EnumToStr(LiftStatus(lift_status_)).c_str());
#endif
    }
    if (msg->fan_lv.size() == 1) {
        fan_lv_ = SpeedLevel(msg->fan_lv[0]);
        LM_DEBUG("fan_lv_: %d", fan_lv_);
#if OPT_CB_DEBUG
        LM_INFO("fan_lv_: %s", EnumString<SpeedLevel>::EnumToStr(SpeedLevel(fan_lv_)).c_str());
#endif
    }
    if (msg->brush_lv.size() == 1) {
        brush_lv_ = SpeedLevel(msg->brush_lv[0]);
#if OPT_CB_DEBUG
        LM_INFO("brush_lv_: %s",EnumString<SpeedLevel>::EnumToStr(SpeedLevel(brush_lv_)).c_str());
#endif
    }
    if (msg->valve_lv.size() == 1) {
        valve_lv_   = SpeedLevel(msg->valve_lv[0]);
        valve_loop_ = SpeedLevel(RANGE(msg->valve_lv[0]%4,1,4));
        LM_DEBUG("valve_lv_: %d", valve_lv_);
#if OPT_CB_DEBUG
        LM_INFO("valve_lv_: %s",EnumString<SpeedLevel>::EnumToStr(SpeedLevel(valve_lv_)).c_str());
#endif
    }
    if (msg->beep_status.size() == 1) {
        beep_status_ = BeepStatus(msg->beep_status[0]);
        LM_DEBUG("valve_lv_: %d", valve_lv_);
#if OPT_CB_DEBUG
        LM_INFO("beep_status_: %s",EnumString<BeepStatus>::EnumToStr(BeepStatus(beep_status_)).c_str());
#endif
    }
}

void BcuScrubber::ScrubberStrOptCallback(const wr_npu_msgs::ScrubberStrOpt::ConstPtr &msg)
{
    LM_DEBUG("ecu: %s -> action: %s", msg->ecu.c_str(), msg->action.c_str());
    try {
        if (msg->ecu == "BL") {
            bl_status_ = EnumString<BreathLightStatus>::StrToEnum(msg->action);
            LM_DEBUG("bl_status_: %d", bl_status_);
        } else if (msg->ecu == "LIFT") {
            lift_status_ = EnumString<LiftStatus>::StrToEnum(msg->action);
            LM_DEBUG("lift_status_: %d", lift_status_);
        } else if (msg->ecu == "FAN") {
            fan_lv_ = EnumString<SpeedLevel>::StrToEnum(msg->action);
            LM_DEBUG("fan_lv_: %d", fan_lv_);
        } else if (msg->ecu == "BRUSH") {
            brush_lv_ = EnumString<SpeedLevel>::StrToEnum(msg->action);
            LM_DEBUG("brush_lv_: %d", brush_lv_);
        } else if (msg->ecu == "VALVEON") {
            valve_lv_ = SpeedLevel::LOW;
            valve_loop_ = strtoul(msg->action.c_str(),0,10);
            LM_DEBUG("valve_lv_: %d valve_loop_: %d", valve_lv_, valve_loop_);
        } else if (msg->ecu == "VALVEOFF") {
            valve_lv_ = SpeedLevel::STOP;
            valve_loop_ = strtoul(msg->action.c_str(),0,10);
            LM_DEBUG("valve_lv_: %d valve_loop_: %d", valve_lv_, valve_loop_);
        } else if (msg->ecu == "ALLCLOSE") {
            bl_status_ = REDOPEN;
            lift_status_ = UP;
            fan_lv_ = brush_lv_ = valve_lv_ = SpeedLevel::STOP;
            LM_DEBUG("Close All!!");
        } else {
            return;
        }
    } catch (std::exception& e ){
        LM_ERROR("Exception: %s", e.what());
    }
    return;
}

void BcuScrubber::SystemErrorCallback(const wr_npu_msgs::SystemStatus::ConstPtr &msg)
{
    working_error_.system_report.clear();
    //TODO
#if 0
    for (auto it : msg->battery) {
        working_error_.system_report.push_back((wizrobo_npu_scrubber::SystemStatusReminder)it);
    }
#endif
}

void BcuScrubber::ObstacleRestoreCallBack(const std_msgs::BoolConstPtr &msg)
{
    is_em_stop_restoring_ = msg->data;
}

int BcuScrubber::UpdateSensorData()
{
    SensorData sensor_data;

    if (!is_inited_) {
        return FALSE;
    }

    if (!p_bcu_->GetSensorData(sensor_data)) {
        LM_WARN("GetSensorData failed!!!");
        return FALSE;
    }

    if(sensor_data.sw) {
        int ret = system("/home/<USER>/test/shutdown.sh");
        if(WIFSIGNALED(ret) && (WTERMSIG(ret) == SIGINT || WTERMSIG(ret) == SIGQUIT)) {
            return TRUE;
        }
    }

    wr_npu_msgs::ScrubberSecurityStatus status;
    status.infrd_threshold = infrd_warning_threshold_;
    status.sonar_threshold = sonar_warning_threshold_;

    int length = static_cast<int>(sensor_data.infrd.size());
    status.infrd_warning.resize(length);
    for(int i = 0;i < length;i++){
        if(sensor_data.infrd[i] > infrd_warning_threshold_){
            if(is_em_stop_restoring_){
                status.infrd_warning[i] = wr_npu_msgs::ScrubberSecurityStatus::RESTORING;
            } else{
                status.infrd_warning[i] = wr_npu_msgs::ScrubberSecurityStatus::WARNING;
            }
            status.infrd_warning[i] = wr_npu_msgs::ScrubberSecurityStatus::NORMAL;
        }
    }
    length = static_cast<int>(sensor_data.sonar.size());
    status.infrd_warning.resize(length);
    for(int i = 0;i < length;i++){
        if(sensor_data.sonar[i] > sonar_warning_threshold_){
            if(is_em_stop_restoring_){
                status.sonar_warning[i] = wr_npu_msgs::ScrubberSecurityStatus::RESTORING;
            } else{
                status.sonar_warning[i] = wr_npu_msgs::ScrubberSecurityStatus::WARNING;
            }
            status.sonar_warning[i] = wr_npu_msgs::ScrubberSecurityStatus::NORMAL;
        }
    }
    length = 1;
    status.infrd_warning.resize(length);
    for(int i = 0;i < length;i++){
        if(sensor_data.bumper > 0){
            if(is_em_stop_restoring_){
                status.bumper_warning[i]
                        = wr_npu_msgs::ScrubberSecurityStatus::RESTORING;
            } else{
                status.bumper_warning[i] = wr_npu_msgs::ScrubberSecurityStatus::WARNING;
            }
            status.bumper_warning[i] = wr_npu_msgs::ScrubberSecurityStatus::NORMAL;
        }
    }
    security_status_pub_.publish(status);

#if SNSR_DEBUG
    sensor_data.PrintData();
#endif
    PubRangeData(sensor_data);
    PubBumperData(sensor_data);
    PubSwitchStatus(sensor_data);
    FreshWorkingStatus(sensor_data);
    PubEmergencyButtonStatus(sensor_data);
    UpdateWaterLevel(sensor_data);
    return TRUE;
}

int BcuScrubber::UpdateSht3xData()
{
    Sht3xData sht3x_data;
    LM_DEBUG("");
    if(!is_inited_) {
        return FALSE;
    }
    if(!p_bcu_->GetSht3xData(sht3x_data)) {
        LM_WARN("GetSht3xData failed!!!");
        return FALSE;
    }
    std_msgs::Float32 inner_tmp_msg, inner_hmdt_msg;
    inner_tmp_msg.data = sht3x_data.temp;
    inner_hmdt_msg.data = sht3x_data.humi;
    inner_tmp_data_pub_.publish(inner_tmp_msg);
    inner_hmdt_data_pub_.publish(inner_hmdt_msg);
    return TRUE;
}

int BcuScrubber::UpdateBmsData()
{
    LM_DEBUG("");
    if(!is_inited_) {
        return FALSE;
    }
    BmsData bms_data;
    sensor_msgs::BatteryState bms_msg;
    wr_npu_msgs::BatteryStatus battery_msg;
    if(!p_bcu_->GetBmsData(bms_data)) {
        LM_WARN("GetBmsData failed!!!");
        return FALSE;
    }
    bms_msg.voltage = (float)bms_data.vol;
    bms_msg.current = (float)bms_data.cur;
    bms_msg.charge = 0;
    bms_msg.capacity = (float)bms_data.res_cap;
    bms_msg.design_capacity = (float)bms_data.capacity;
    bms_msg.percentage = (float)bms_data.level;

    battery_msg.state = bms_msg;
    battery_msg.abnormal_state = 0;
    battery_msg.temperature = bms_data.temp;
    PubBmsData(battery_msg);
    working_status_.is_battery_low = ((float)bms_data.vol < low_power_threshold_) ? true:false;
    return TRUE;
}

int BcuScrubber::UpdateMotorState()
{
    if(!is_inited_) {
        return FALSE;
    }
    bcu_driver::MotorStatus motor_status;
    wr_npu_msgs::ScrubberMotorStateALL motor_status_msg;
    if(p_bcu_->GetMotorStatus(motor_status) == FALSE || motor_status.size() != 2){
        LM_WARN("BcuScrubber::UpdateFanMotorState() failed to get motor status.");
        return FALSE;
    }
    //fan motor
    motor_status_msg.status.resize(2);
    motor_status_msg.status[0].id = "fan motor";
    motor_status_msg.status[0].index = 0;
    //brush motor
    motor_status_msg.status[1].id = "brush motor";
    motor_status_msg.status[1].index = 0;
    for(int i = 0;i < 2;i++){
        motor_status_msg.status[i].current =
                static_cast<float>(motor_status[i].cur) * 0.1;
        motor_status_msg.status[i].voltage =
                static_cast<float>(motor_status[i].vol);
        motor_status_msg.status[i].power =
                motor_status_msg.status[i].current * motor_status_msg.status[i].voltage;
        motor_status_msg.status[i].rpm =
                static_cast<float>(motor_status[i].rpm);
        motor_status_msg.status[i].tempture =
                static_cast<float>(motor_status[i].temp);
    }
    wr_npu_msgs::ScrubberGetMotorStatus srv;
    srv.request.num = bcu_param_.motor.motor_num;
    if(wheel_motor_status_client_.call(srv)){
        //left wheel motor
        motor_status_msg.status.push_back(srv.response.status[0]);
        //right wheel motor
        motor_status_msg.status.push_back(srv.response.status[1]);
        MotorSpdArray rpms;
        rpms.push_back(srv.response.status[0].rpm);
        rpms.push_back(srv.response.status[1].rpm);
        if(!p_bcu_->SetMotorSpd(rpms)) {
            LM_WARN("SetMotorSpd failed!!!");
            return FALSE;
        }
    }
    PubMotorStatus(motor_status_msg);
    return TRUE;
}

int BcuScrubber::UpdateWaterLevel(const SensorData &sensor_data)
{
    if(!is_inited_) {
        return FALSE;
    }
    WaterLevel level;
    wr_npu_msgs::ScrubberWaterLevel level_msg;
#if 0
    if(p_bcu_->GetWaterLevel(level) == FALSE){
        LM_WARN("BcuScrubber::UpdateWaterLevel() failed to get water level.");
        return FALSE;
    }
#endif
#if WATER_LV_DEBUG
    LM_INFO("level.clean is %02X, &0x03 = %02X", sensor_data.clean_level, (sensor_data.clean_level & 0x03));
#endif
    switch (sensor_data.clean_level & 0x03) {
    case 0:
        level_msg.clean_level = wr_npu_msgs::ScrubberWaterLevel::FULL;
        working_status_.is_purwater_high = true;
        working_status_.is_purwater_low = false;
        break;
    case 1:
        level_msg.clean_level = wr_npu_msgs::ScrubberWaterLevel::LOW;
        working_status_.is_purwater_low = true;
        break;
    case 2:
        level_msg.clean_level = wr_npu_msgs::ScrubberWaterLevel::ERROR;
        working_status_.is_purwater_low = false;
        working_status_.is_purwater_high = false;
        break;
    case 3:
        level_msg.clean_level = wr_npu_msgs::ScrubberWaterLevel::EMPTY;
        working_status_.is_purwater_high = false;
        break;
    default:
        break;
    }
#if WATER_LV_DEBUG
    LM_INFO("level.dirty is %02X, &0x03 = %02X", sensor_data.dirty_level, (sensor_data.dirty_level & 0x03));
#endif
    switch (sensor_data.dirty_level & 0x03) {
    case 0:
        level_msg.dirty_level = wr_npu_msgs::ScrubberWaterLevel::FULL;
        working_status_.is_sewwater_high = true;
        break;
    case 1:
        level_msg.dirty_level = wr_npu_msgs::ScrubberWaterLevel::LOW;
        working_status_.is_sewwater_high = false;
        break;
    case 2:
        level_msg.dirty_level = wr_npu_msgs::ScrubberWaterLevel::ERROR;
        working_status_.is_sewwater_high = false;
        break;
    case 3:
        level_msg.dirty_level = wr_npu_msgs::ScrubberWaterLevel::EMPTY;
        working_status_.is_sewwater_high = false;
        break;
    default:
        break;
    }

    PubWaterLevel(level_msg);
}

int BcuScrubber::UpdateUsageTime()
{
    static double s_filter = 0.0;
    static double s_hanging = 0.0;
    static double s_roller_brush = 0.0;
    static double s_fan_carbon_brush = 0.0;
    static double last_filter = 0.0;
    static double last_hanging = 0.0;
    static double last_roller_brush = 0.0;
    static double last_fan_carbon_brush = 0.0;
    if(!is_inited_) {
        return FALSE;
    }
    UsageTime time;
    wr_npu_msgs::ScrubberUsageTime msg;
    if(p_bcu_->GetUsageTime(time) == FALSE){
        LM_WARN("BcuScrubber::UpdateUsageTime() failed to get usage time.");
        return FALSE;
    }
    if(time.filter > last_filter && last_filter > 1e-5){
        s_filter+=(time.filter - last_filter);
    }
    if(time.hanging > last_hanging && last_hanging > 1e-5){
        s_hanging+=(time.hanging-last_hanging);
    }
    if(time.roller_brush > last_roller_brush && last_roller_brush > 1e-5){
        s_roller_brush+=(time.roller_brush - last_roller_brush);
    }
    if(time.fan_carbon_brush > last_fan_carbon_brush && last_fan_carbon_brush > 1e-5){
        s_fan_carbon_brush+=(time.fan_carbon_brush - last_fan_carbon_brush);
    }
    last_filter = time.filter;
    last_hanging = time.hanging;
    last_roller_brush = time.roller_brush;
    last_fan_carbon_brush = time.fan_carbon_brush;

    msg.filter = s_filter;
    msg.hanging = s_hanging;
    msg.roller_brush = s_roller_brush;
    msg.fan_carbon_brush = s_fan_carbon_brush;
    PubUsageTime(msg);
}

int BcuScrubber::UpdateVoltageData()
{
    if(!is_inited_) {
        return FALSE;
    }
    vol_msg_.battery_voltage = bms_data_.vol;
    return TRUE;
}

int BcuScrubber::UpdateCleaningState()
{
    static int i = 0;
    ++i;
#if UPDATE_CLEANING_STATE_BY_NEW_TOPIC
    LM_INFO("is_new_cleaning_cmd_ is %d ", is_new_cleaning_cmd_);
#else
    static int last_brush_lv_ = 0;
    static int last_lift_status_ = 1;
    static int last_fan_lv_ = 0;
    static int last_valve_lv_ = 0;
    static int last_bl_status_ = 0;
    static int last_beep_status_ = 0;
#endif

    if(!is_inited_) {
        return FALSE;
    }

    if(!is_new_cleaning_cmd_){
        return FALSE;
    }
    switch (i%6) {
    case 0:
#if UPDATE_CLEANING_STATE_BY_NEW_TOPIC
        SetBlnStatus(bl_status_);
#else
#if EXEC_CLEANING_STATE_DEBUG
        LM_INFO("bl_status_ is %d , last_bl_status_ is %d", bl_status_, last_bl_status_);
#endif
        if(bl_status_ != last_bl_status_){
#if EXEC_CLEANING_STATE_DEBUG
            LM_WARN("exec SetBlnStatus(%d)", bl_status_);
#endif
            SetBlnStatus(bl_status_);
        }
        last_bl_status_ = bl_status_;
#endif
        break;
    case 1:
#if UPDATE_CLEANING_STATE_BY_NEW_TOPIC
        SetBeepStatus(beep_status_, beep_loop_);
#else
#if EXEC_CLEANING_STATE_DEBUG
        LM_INFO("beep_status_ is %d , last_beep_status_ is %d", beep_status_, last_beep_status_);
#endif
        if(beep_status_ != last_beep_status_){
#if EXEC_CLEANING_STATE_DEBUG
            LM_WARN("exec SetBeepStatus(%d ,%d)", beep_status_, beep_loop_);
#endif
            SetBeepStatus(beep_status_, beep_loop_);
        }
        last_beep_status_ = beep_status_;
#endif
        break;
    case 2:
#if UPDATE_CLEANING_STATE_BY_NEW_TOPIC
        SetSbStatus(lift_status_);
#else
#if EXEC_CLEANING_STATE_DEBUG
        LM_INFO("lift_status_ is %d , last_lift_status_ is %d", lift_status_, last_lift_status_);
#endif
        if(lift_status_ != last_lift_status_){
#if EXEC_CLEANING_STATE_DEBUG
            LM_WARN("exec SetSbStatus(%d)", lift_status_);
#endif
            SetSbStatus(lift_status_);
        }
        last_lift_status_ = lift_status_;
#endif
        break;
    case 3:
#if UPDATE_CLEANING_STATE_BY_NEW_TOPIC
        SetBrushStatus(brush_lv_);
#else
#if EXEC_CLEANING_STATE_DEBUG
        LM_INFO("brush_lv_ is %d , last_brush_lv_ is %d", brush_lv_, last_brush_lv_);
#endif
        if(brush_lv_ != last_brush_lv_){
#if EXEC_CLEANING_STATE_DEBUG
            LM_WARN("exec SetBrushStatus(%d)", brush_lv_);
#endif
            SetBrushStatus(brush_lv_);
        }
        last_brush_lv_ = brush_lv_;
#endif
        break;
    case 4:
#if UPDATE_CLEANING_STATE_BY_NEW_TOPIC
        SetFanStatus(fan_lv_);
#else
#if EXEC_CLEANING_STATE_DEBUG
        LM_INFO("fan_lv_ is %d , last_fan_lv_ is %d", fan_lv_, last_fan_lv_);
#endif
        if(fan_lv_ != last_fan_lv_){
#if EXEC_CLEANING_STATE_DEBUG
            LM_WARN("exec SetFanStatus(%d)", fan_lv_);
#endif
            SetFanStatus(fan_lv_);
        }
        last_fan_lv_ = fan_lv_;
#endif
        break;
    case 5:
#if UPDATE_CLEANING_STATE_BY_NEW_TOPIC
        SetFanStatus(fan_lv_);
#else
#if EXEC_CLEANING_STATE_DEBUG
        LM_INFO("valve_lv_ is %d , last_valve_lv_ is %d", valve_lv_, last_valve_lv_);
#endif
        if(valve_lv_ != last_valve_lv_){
#if EXEC_CLEANING_STATE_DEBUG
            LM_WARN("exec PubWaterValveStatus(%d, %d)", valve_lv_, valve_loop_);
#endif
            PubWaterValveStatus(valve_lv_, valve_loop_);
        }
        last_valve_lv_ = valve_lv_;
#endif
        break;
    default:
        break;
    }
    is_new_cleaning_cmd_ = false;
    return TRUE;
}

int BcuScrubber::UpdateLampAndIoState()
{
    if (cmd_angular_vel_ > float_threshold_2_
            || (ABS(cmd_linear_vel_) < float_threshold_1_)
            && cmd_angular_vel_ > float_threshold_1_) {         // turn left
        SetLampStatus(LAMP_OFF, LAMP_ON, LAMP_OFF, LAMP_ON);
    } else if (cmd_angular_vel_ < -float_threshold_2_
             || (ABS(cmd_linear_vel_) < float_threshold_1_)
                && cmd_angular_vel_ < (-float_threshold_1_)) {  // turn right
        SetLampStatus(LAMP_ON, LAMP_OFF, LAMP_ON, LAMP_OFF);
    } else if (cmd_linear_vel_ > float_threshold_1_) {          // move forward
        SetLampStatus(LAMP_OFF, LAMP_OFF, LAMP_ON, LAMP_ON);
    } else if (cmd_linear_vel_ < (-float_threshold_1_)) {       // move backward
        SetLampStatus(LAMP_ON, LAMP_ON, LAMP_OFF, LAMP_OFF);
    } else {                                                    // stop
        SetLampStatus(LAMP_ON, LAMP_ON, LAMP_ON, LAMP_ON);
    }
//    beep_status_ = LAMP_OFF;
}

void BcuScrubber::ProcessWorkErr()
{
}

void BcuScrubber::FreshWorkingStatus(const SensorData& sensor_data)
{
    working_status_.is_emergency_stop = (!sensor_data.esb)?true:false;
#if 0
    working_status_.is_purwater_high = false;
    working_status_.is_purwater_low = false;
    working_status_.is_sewwater_high = false;
#endif
    wr_npu_msgs::ScrubberWorkingStatus status_msg;
    status_msg.header.frame_id = " ";
    status_msg.header.stamp = ros::Time::now();
    status_msg.is_battery_low = working_status_.is_battery_low;
    status_msg.is_emergency_stop = working_status_.is_emergency_stop;
    status_msg.is_purwater_high = working_status_.is_purwater_high;
    status_msg.is_purwater_low = working_status_.is_purwater_low;
    status_msg.is_sewwater_high = working_status_.is_sewwater_high;
    status_msg.bl_status = bl_status_;
    status_msg.lift_status = lift_status_;
    status_msg.fan_lv = fan_lv_;
    status_msg.brush_lv = brush_lv_;
    status_msg.valve_lv = valve_lv_;
    working_status_pub_.publish(status_msg);
}

void BcuScrubber::PubMotorStatus(const wr_npu_msgs::ScrubberMotorStateALL &motor_status)
{
    wr_npu_msgs::ScrubberMotorStateALL motor_status_msg = motor_status;
    motor_status_msg.header.frame_id = "scrb_motor_status";
    motor_status_msg.header.stamp = ros::Time::now();
    motor_state_pub_.publish(motor_status_msg);
}

void BcuScrubber::PubEmergencyButtonStatus(const SensorData &sensor_data)
{
    wr_npu_msgs::EmergencyStopData ems_msg;
    ems_msg.status = sensor_data.esb;
    ems_msg.header.stamp = ros::Time::now();
    ems_msg.header.frame_id = base_frame_id_;
    emergency_stop_status_pub_.publish(ems_msg);
}

void BcuScrubber::PubSwitchStatus(const SensorData &sensor_data)
{
    wr_npu_msgs::EmergencyStopData sw_msg;
    sw_msg.status = sensor_data.sw;
    sw_msg.header.stamp = ros::Time::now();
    sw_msg.header.frame_id = base_frame_id_;
    switch_status_pub_.publish(sw_msg);
}

void BcuScrubber::PubBumperData(const SensorData &sensor_data)
{
    wr_npu_msgs::BumperData io_msg;
    io_msg.bumper_num = 8;
    io_msg.states.resize(8);
    io_msg.header.frame_id = base_frame_id_;
    io_msg.header.stamp = ros::Time::now();

    for (int i=0;i<8;i++) {
        io_msg.states[i] = (sensor_data.io_input>>i)&0x01;
    }
    input_io_pub_.publish(io_msg);

    for (int i=0;i<8;i++) {
        io_msg.states[i] = (sensor_data.bumper>>i)&0x01;
    }
    bumper_data_pub_.publish(io_msg);
}

void BcuScrubber::PubRangeData(const SensorData &sensor_data)
{
    bool is_entrp_detected = false;
    for(int i=0;i<sonar_range_pubs_.size();++i) {
        // for navi
        sonars_range_msg_[i].header.stamp = ros::Time::now();
        sonars_range_msg_[i].range = sensor_data.sonar[i];
        sonar_range_pubs_[i].publish(sonars_range_msg_[i]);
        if (bcu_param_.sensor.sonar_threshold[i] > 0
                && sonars_range_msg_[i].range < bcu_param_.sensor.sonar_threshold[i]) {
            LM_DEBUG("i: %d range: %.2f / %.2f", i, sonars_range_msg_[i].range, bcu_param_.sensor.sonar_threshold[i]);
            is_entrp_detected = true;
        }
    }

    for(int i=0;i<infrd_range_pubs_.size();++i) {
        // for navi
        infrds_range_msg_[i].header.stamp = ros::Time::now();
        /*2~15cm Infrd Sensor*/
        infrds_range_msg_[i].range =
                - 0.0259 * pow(sensor_data.infrd[i], 3)
                + 0.1388 * pow(sensor_data.infrd[i], 2)
                - 0.2558 * sensor_data.infrd[i]+0.1857;
        infrd_range_pubs_[i].publish(infrds_range_msg_[i]);
    }

    wr_npu_msgs::EmergencyStopData entrp_detect_msg;
    if (is_entrp_detected) {
        entrp_detect_msg.header.stamp = ros::Time::now();
        entrp_detect_msg.header.frame_id = base_frame_id_;
        entrp_detect_msg.status = 1;
        entrp_detect_pub_.publish(entrp_detect_msg);
    }
}

void BcuScrubber::PubVoltageData(const wr_npu_msgs::VoltageData &voltage_msg)
{
    wr_npu_msgs::VoltageData voltage = voltage_msg;
    voltage.header.stamp = ros::Time::now();
    voltage.header.frame_id = base_frame_id_;
    voltage_data_pub_.publish(voltage);
}

void BcuScrubber::PubBmsData(const wr_npu_msgs::BatteryStatus &msg)
{
    wr_npu_msgs::BatteryStatus bms_msg = msg;
    bms_msg.state.header.stamp = ros::Time::now();
    bms_msg.state.header.frame_id = battery_frame_id_;
    battery_status_pub_.publish(bms_msg);
}

void BcuScrubber::PubWaterValveStatus(int status, int loop_cnt)
{
    wr_npu_msgs::ScrubberWaterValveStatus status_msg;
    LM_DEBUG("status: %d, loop_cnt: %d", status, loop_cnt);
    SetValveStatus(status, loop_cnt);
    status_msg.header.frame_id = "water_valve";
    status_msg.header.stamp = ros::Time::now();
    status_msg.status = status;
    status_msg.frequence = loop_cnt;
    water_valve_pub_.publish(status_msg);
}

void BcuScrubber::PubWaterLevel(const wr_npu_msgs::ScrubberWaterLevel &level)
{
    wr_npu_msgs::ScrubberWaterLevel asta_msg = level;
    wr_npu_msgs::WaterTank tank_msg;
    asta_msg.header.frame_id = "scrubber_water_level";
    asta_msg.header.stamp = ros::Time::now();
    water_level_asta_pub_.publish(asta_msg);
    tank_msg.pure_water_tank_level     = asta_msg.clean_level;
    tank_msg.polluted_water_tank_level = asta_msg.dirty_level;
    water_level_pub_.publish(tank_msg);
}

void BcuScrubber::PubUsageTime(const wr_npu_msgs::ScrubberUsageTime &time)
{
    wr_npu_msgs::ScrubberUsageTime msg = time;
    msg.header.frame_id = "scrubber_usage_time";
    msg.header.stamp = ros::Time::now();
    usage_time_pub_.publish(msg);
}

}// namespace
