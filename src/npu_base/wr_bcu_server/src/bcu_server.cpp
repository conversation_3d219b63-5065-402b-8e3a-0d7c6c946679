#include <climits>
#include "bcu_server.h"

#ifdef _LM_NAME_PREFIX_
#  undef  _LM_NAME_PREFIX_
#endif
#define _LM_NAME_PREFIX_ "BcuServer"

namespace wizrobo {

BcuServer::BcuServer(int argc, char **argv)
    : p_nh_(NULL)
    , p_prv_nh_(NULL)
    , is_inited_(false)
    , main_loop_frq_(30)
    , is_dyncfg_inited_(false)
    , p_bcu_dyncfg_(NULL)
    , p_pid_dyncfg_(NULL)
    , p_bcu_(NULL)
    , roll_(0)
    , pitch_(0)
    , yaw_(0)
    , auto_em_stop_(0)
    , steer_angle_deg_(0.0)
    , left_spd_(0.0)
    , right_spd_(0.0)
    , steer_location_(wizrobo_npu::CENTER)
    , is_emergency_stop_(false)
    , enb_slave_mode_(false)
    , infrd_em_stop_(false)
    , sonar_em_stop_(false)
    , bumper_em_stop_(false)
    , is_em_stop_restoring_(false)
    , control_mode_(INTEGRATED)
{
    p_nh_ = new ros::NodeHandle();
    p_prv_nh_ = new ros::NodeHandle("~");
}

BcuServer::~BcuServer()
{
    RELEASE_POINTER(p_bcu_);
    RELEASE_POINTER(p_bcu_dyncfg_);
    RELEASE_POINTER(p_pid_dyncfg_);
    RELEASE_POINTER(p_prv_nh_);
    RELEASE_POINTER(p_nh_);
}

void BcuServer::Init()
{
    is_inited_ = false;

    ros::NodeHandle& nh = (*p_nh_);
    ros::NodeHandle& prv_nh = (*p_prv_nh_);

    // logger level
    LoggerLevel logger_level;
    GetEnumParam<LoggerLevel>(prv_nh, "logger_level", logger_level, DEBUG);
    SetLoggerLevel(logger_level);

    SyncAllParam();

    if (p_bcu_ && (MOTOR_CONTROL_UNIT & control_mode_)) {
        p_bcu_->Stop();
        p_bcu_->Brake(true);
    }
    RELEASE_POINTER(p_bcu_);
    BcuDriverCreator creator;
    p_bcu_ = creator.Create(driver_type_);

    if(!p_bcu_->Init(&bcu_param_)) {
        LM_ERROR("p_bcu_->Init() return failed, pls check com param: %s", bcu_param_.com.ToStr().c_str());
        return;
    }

    brake_srv = prv_nh.advertiseService("brake", &BcuServer::BrakeSrvCallback, this);
    if ((MOTOR_CONTROL_UNIT&control_mode_) && !CommanderInit()) {
        return;
    }
    if ((DATA_ACQUISITION_UNIT&control_mode_) && !SensorInit()) {
        return;
    }
#if 0
    RELEASE_POINTER(p_bcu_dyncfg_);
    p_bcu_dyncfg_ = new BcuDynamicConfigServer(prv_nh);
    BcuDynamicConfigServer::CallbackType bcu_cb =
            boost::bind(&BcuServer::BcuDynamicConfigCallback, this, _1, _2);
    p_bcu_dyncfg_->setCallback(bcu_cb);
#endif

    RELEASE_POINTER(p_pid_dyncfg_);
    p_pid_dyncfg_ = new PidDynamicConfigServer(prv_nh);
    PidDynamicConfigServer::CallbackType pid_cb = boost::bind(&BcuServer::PidDynamicConfigCallback, this, _1, _2);
    p_pid_dyncfg_->setCallback(pid_cb);
    exception_info_sub_ = nh.subscribe<wr_npu_msgs::ExceptionInformation>(exception_info_topic_, 1, &BcuServer::ExceptionInfoCallback, this);
    com_status_pub_ = nh.advertise<wr_npu_msgs::ComStatus>(bcu_com_status_topic_, 1000);
    OnInit();
    is_inited_ = true;
    return;
}

void BcuServer::Run()
{
    if (!is_inited_) {
        return;
    }
    ros::Rate loop_rater(main_loop_frq_);
    unsigned int loop_cnt = 0;
    ros::Time start_time = ros::Time::now();
    wr_npu_msgs::ComStatus com_status;
    com_status.err_cnt = 0;
    com_status.err_rate = 0;

    while (ros::ok()) {
        ros::spinOnce();
        loop_rater.sleep();
        loop_cnt++;
        double duration = (ros::Time::now() - start_time).toSec();
        start_time = ros::Time::now();
        com_status.freq_hz = 1.0 / duration;
        com_status.delay_ms = duration * 1000;
        com_status.cnt = loop_cnt;
        com_status_pub_.publish(com_status);

        if (DATA_ACQUISITION_UNIT & control_mode_) {
            SensorRunOnce();
        }

        if (MOTOR_CONTROL_UNIT & control_mode_) {
            CommanderRunOnce();
        }
    }
    if (MOTOR_CONTROL_UNIT & control_mode_) {
        p_bcu_->Stop();
    }
    return;
}
void BcuServer::SensorRunOnce()
{
    auto _set_lamp_func = [this] (OutputMode m1, OutputMode m2, OutputMode m3, OutputMode m4) {
        p_bcu_->SetLampStatus(LAMP_FRONT_LEFT, m1, -1);
        p_bcu_->SetLampStatus(LAMP_FRONT_RIGHT, m2, -1);
        p_bcu_->SetLampStatus(LAMP_BACK_LEFT, m3, -1);
        p_bcu_->SetLampStatus(LAMP_BACK_RIGHT, m4, -1);
        return;
    };

    UpdateSensorData();

    if (bcu_param_.sensor.enb_imu) {
        UpdateImuData();
    }

    if (is_emergency_stop_) {
        p_bcu_->SetBeepStatus(OUTPUT_MODE_BLINK_3, -1);
        _set_lamp_func(OUTPUT_MODE_BLINK_2, OUTPUT_MODE_BLINK_2, OUTPUT_MODE_BLINK_2, OUTPUT_MODE_BLINK_2);
        return;
    }

    const float threshold_1 = 0.01;
    const float threshold_2 = 0.2;
    if (cmd_angular_vel_ > threshold_2
            || (ABS(cmd_linear_vel_) < threshold_1 && cmd_angular_vel_ > threshold_1)) {
        // turn left
        _set_lamp_func(OUTPUT_MODE_KEEP_HIGH, OUTPUT_MODE_KEEP_LOW, OUTPUT_MODE_KEEP_HIGH, OUTPUT_MODE_KEEP_LOW);
    } else if (cmd_angular_vel_ < -threshold_2 || (ABS(cmd_linear_vel_) < threshold_1 && cmd_angular_vel_ < (-threshold_1))) {
        // turn right
        _set_lamp_func(OUTPUT_MODE_KEEP_LOW, OUTPUT_MODE_KEEP_HIGH, OUTPUT_MODE_KEEP_LOW, OUTPUT_MODE_KEEP_HIGH);
    } else if (cmd_linear_vel_ > threshold_1) {
        // move forward
        _set_lamp_func(OUTPUT_MODE_KEEP_HIGH, OUTPUT_MODE_KEEP_HIGH, OUTPUT_MODE_KEEP_LOW, OUTPUT_MODE_KEEP_LOW);
    } else if (cmd_linear_vel_ < (-threshold_1)) {
        // move backward
        _set_lamp_func(OUTPUT_MODE_KEEP_LOW, OUTPUT_MODE_KEEP_LOW, OUTPUT_MODE_KEEP_HIGH, OUTPUT_MODE_KEEP_HIGH);
    } else {
        // stop
        _set_lamp_func(OUTPUT_MODE_KEEP_LOW, OUTPUT_MODE_KEEP_LOW, OUTPUT_MODE_KEEP_LOW, OUTPUT_MODE_KEEP_LOW);
    }
    p_bcu_->SetBeepStatus(OUTPUT_MODE_KEEP_LOW, -1);
}

void BcuServer::CommanderRunOnce()
{
    UpdateActMotorSpd();
    UpdateMotorEnc();
    //UpdateImuData();

    if (is_emergency_stop_) {
        LM_ERROR("Emergency Stop!!!");
        if(fabs(left_spd_) > 0 || fabs(right_spd_) > 0 || fabs(steer_angle_deg_) > 0) {
            p_bcu_->Stop();
            p_bcu_->Brake(true);
            return;
        }
    }
    if ((fabs(left_spd_) + fabs(right_spd_) + fabs(steer_angle_deg_) < 1e-5)) {
        p_bcu_->Stop();
        return;
    }
    bcu_driver::MotorSpdArray cmd_rpms;
    if (model_type_ == wizrobo_npu::DIFFDRV) {
        cmd_rpms.push_back(left_spd_);
        cmd_rpms.push_back(right_spd_);
    } else if (model_type_ == wizrobo_npu::CSGDRV) {
        if (steer_location_ == wizrobo_npu::SteerEncLocation::CENTER) {
            cmd_rpms.push_back(-1.0);
        } else {
            cmd_rpms.push_back(1.0);
        }
        if (left_spd_ > 0) {
            cmd_rpms.push_back(-(steer_angle_deg_));
        } else {
            cmd_rpms.push_back(steer_angle_deg_);
        }
        cmd_rpms.push_back(left_spd_);
    } else {
        p_bcu_->Stop();
        p_bcu_->Brake(true);
        return;
    }

    if(sonar_em_stop_ || bumper_em_stop_ || infrd_em_stop_){
        if(!is_em_stop_restoring_){
            for(int i = 0;i < cmd_rpms.size();i++){
                cmd_rpms[i] = 0;
            }
        }
    }
    if(auto_em_stop_ == 1){
        p_bcu_->Stop();
    } else{
        p_bcu_->SetMotorSpd(cmd_rpms);
    }
}

int BcuServer::ComInit()
{
    return TRUE;
}

int BcuServer::CommanderInit()
{
    ros::NodeHandle& nh = (*p_nh_);
    ros::NodeHandle& prv_nh = (*p_prv_nh_);

    if (!p_bcu_->SetMotorParam() || !p_bcu_->SetPidParam()) {
        LM_ERROR("Failed to set motor param, pls check motor drivers and motors");
        return FALSE;
    }

    if (!p_bcu_->ClrMotorEnc()) {
        LM_ERROR("Failed to clear motor encoders, pls check motor drivers and motors");
        return FALSE;
    }
    LM_INFO("motor param: %s", bcu_param_.motor.ToStr().c_str());
    LM_INFO("pid param: %s", bcu_param_.pid.ToStr().c_str());

    cmd_motor_spd_sub_ = nh.subscribe<wr_npu_msgs::MotorSpd>(cmd_motor_spd_topic_, 1, &BcuServer::CmdMotorSpdCallback, this);
    security_status_sub_ = nh.subscribe<wr_npu_msgs::ScrubberSecurityStatus>("/scrubber_security_status", 1, &BcuServer::SecurityStatusCallback,this);
    restore_state_sub_ = nh.subscribe<std_msgs::Bool>("/obstacle_stop_restore", 1, &BcuServer::RestoreStateCallback, this);
    soft_emergency_stop_sub_ = nh.subscribe<wr_npu_msgs::EmergencyStopData>("/auto_emergency_stop", 1, &BcuServer::AutoEmStopCallback, this);
    act_motor_spd_pub_ = nh.advertise<wr_npu_msgs::MotorSpd>(act_motor_spd_topic_, 1);
    motor_enc_pub_ = nh.advertise<wr_npu_msgs::MotorEnc>(motor_enc_topic_, 1);
    raw_motor_enc_pub_ = nh.advertise<wr_npu_msgs::MotorEnc>(motor_enc_topic_ + "_raw", 1);

    motor_dyn_param_srv_ = nh.advertiseService("/dyn_param/motor/bcu_server", &BcuServer::MotorDynParamSrvCallback, this);
    pid_dyn_param_srv_ = nh.advertiseService("/dyn_param/pid/bcu_server", &BcuServer::PidDynParamSrvCallback, this);
    cme_srv_ = prv_nh.advertiseService("clear_motor_enc", &BcuServer::ClearMotorEncCallback, this);

    // motor test
    prv_nh.param<double>("left_spd", left_spd_, 0);
    prv_nh.param<double>("right_spd", right_spd_, 0);

    last_raw_ticks.resize(bcu_param_.motor.motor_num, 0);
    last_ticks.resize(bcu_param_.motor.motor_num, 0);

    motor_enc_msg_.motor_num = 0;
    pthread_spin_init(&motor_enc_msg_lock_, PTHREAD_PROCESS_PRIVATE);
    pub_thread_ = new boost::thread(boost::bind(&BcuServer::PubLoopThread, this));

    OnCommanderInit();

    return TRUE;
}

int BcuServer::SensorInit()
{
    LM_WARN("");
    ros::NodeHandle& nh = (*p_nh_);
    ros::NodeHandle& prv_nh = (*p_prv_nh_);

    if (!p_bcu_->SetSensorParam()) {
        LM_ERROR("Failed to set sensor param, pls check motor drivers and motors");
        return FALSE;
    }
    emergency_stop_status_pub_ =
            nh.advertise<wr_npu_msgs::EmergencyStopData>(emergency_stop_status_topic_, 1);
    entrp_detect_pub_ = nh.advertise<wr_npu_msgs::EmergencyStopData>(entrp_detect_topic_, 1);

    int _num;
    double _min_range, _max_range, _fov_deg;

    nh.param<int>(STD_SENSOR_PARAM_NS + "/sonar_num", _num, 0);
    _num = MIN(_num, MAX_SONAR_NUM);
    sonar_range_pubs_.resize(_num);
    sonars_range_msg_.resize(_num);
    for(int i=0;i<_num;i++) {
        nh.param(STD_SENSOR_PARAM_NS+"/sonar_params/sonar_"+std::to_string(i)+"/min_range_m", _min_range, 0.1);
        nh.param(STD_SENSOR_PARAM_NS+"/sonar_params/sonar_"+std::to_string(i)+"/max_range_m", _max_range, 1.0);
        nh.param(STD_SENSOR_PARAM_NS+"/sonar_params/sonar_"+std::to_string(i)+"/fov_deg", _fov_deg, 60.0);
        nh.param(STD_SENSOR_PARAM_NS+"/sonar_params/sonar_"+std::to_string(i)+"/fov_deg", _fov_deg, 60.0);
        // for navi
        sonars_range_msg_[i].header.frame_id = "sonar_frame_" + std::to_string(i);
        sonars_range_msg_[i].max_range = _max_range;
        sonars_range_msg_[i].min_range = _min_range;
        sonars_range_msg_[i].field_of_view = DEG2RAD(_fov_deg);
        sonars_range_msg_[i].radiation_type = sensor_msgs::Range::ULTRASOUND;
        sonar_range_pubs_[i] = nh.advertise<sensor_msgs::Range>(sonar_data_topic_ + std::to_string(i), 1);
    }

    nh.param<int>(STD_SENSOR_PARAM_NS + "/infrd_num", _num, 0);
    _num = MIN(_num, MAX_SONAR_NUM);
    infrd_range_pubs_.resize(_num);
    infrds_range_msg_.resize(_num);
    for(int i=0;i<_num;i++) {
        nh.param(STD_SENSOR_PARAM_NS+"/infrd_params/infrd_"+std::to_string(i)+"/min_range_m", _min_range, 0.01);
        nh.param(STD_SENSOR_PARAM_NS+"/infrd_params/infrd_"+std::to_string(i)+"/max_range_m", _max_range, 0.20);
        nh.param(STD_SENSOR_PARAM_NS+"/infrd_params/infrd_"+std::to_string(i)+"/fov_deg", _fov_deg, 5.0);
        // for navi
        infrds_range_msg_[i].header.frame_id = "infrd_frame_" + std::to_string(i);
        infrds_range_msg_[i].max_range = _max_range;
        infrds_range_msg_[i].min_range = _min_range;
        infrds_range_msg_[i].field_of_view = DEG2RAD(_fov_deg);
        infrds_range_msg_[i].radiation_type = sensor_msgs::Range::INFRARED;
        infrd_range_pubs_[i] = nh.advertise<sensor_msgs::Range>(infrd_data_topic_ + std::to_string(i), 1);
    }

    input_io_pub_ = nh.advertise<wr_npu_msgs::BumperData>(input_io_topic_, 1);
    output_io_pub_ = nh.advertise<wr_npu_msgs::BumperData>(output_io_topic_, 1);
    bumper_data_pub_ = nh.advertise<wr_npu_msgs::BumperData>(bumper_data_topic_, 1);
    battery_data_pub_ = nh.advertise<sensor_msgs::BatteryState>(battery_data_topic_, 1);
    voltage_data_pub_ = nh.advertise<wr_npu_msgs::VoltageData>(voltage_data_topic_, 1);

    if (bcu_param_.sensor.enb_imu) {
        imu_data_pub_ = nh.advertise<sensor_msgs::Imu>(imu_data_topic_, 1);
        rpy_data_pub_ = nh.advertise<geometry_msgs::Vector3Stamped>(rpy_data_topic_, 1);
        all_imu_data_pub_ = nh.advertise<sensor_msgs::Imu>(all_imu_data_topic_, 1);
    }
    cmd_vel_sub_ = nh.subscribe<geometry_msgs::Twist>(cmd_vel_topic_, 1, &BcuServer::CmdVelCallback, this);
    cmd_motor_spd_sub_ = nh.subscribe<wr_npu_msgs::MotorSpd>(cmd_motor_spd_topic_, 1, &BcuServer::CmdMotorSpdCallback, this);

    //cmd_motor_spd_sub_ = nh.subscribe<wr_npu_msgs::MotorSpd>(cmd_motor_spd_topic_, 1, &BcuServer::CmdMotorSpdCallback, this);
    sensor_dyn_param_srv_ = nh.advertiseService("/dyn_param/sensor/bcu_server", &BcuServer::SensorDynParamSrvCallback, this);

    // Defaults obtained experimentally from hardware, no device spec exists
    prv_nh.param<double>("linear_acceleration_stdev", linear_acceleration_stdev_, (4.0 * 1e-3f * 9.80665));
    prv_nh.param<double>("angular_velocity_stdev", angular_velocity_stdev_, (0.06 * M_PI / 180.0));
    prv_nh.param<double>("orientation_x_stdev", orientation_x_stdev_, (0.15 * M_PI / 180.0));
    prv_nh.param<double>("orientation_y_stdev", orientation_y_stdev_, (0.15 * M_PI / 180.0));
    prv_nh.param<double>("orientation_z_stdev", orientation_z_stdev_, (0.15 * M_PI / 180.0));
    linear_acceleration_cov_ = linear_acceleration_stdev_ * linear_acceleration_stdev_;
    angular_velocity_cov_ = angular_velocity_stdev_ * angular_velocity_stdev_;
    orientation_x_covar_ = orientation_x_stdev_ * orientation_x_stdev_;
    orientation_y_covar_ = orientation_y_stdev_ * orientation_y_stdev_;
    orientation_z_covar_ = orientation_z_stdev_ * orientation_z_stdev_;

    OnSensorInit();

    return TRUE;
}

void BcuServer::SyncAllParam()
{
    LM_WARN("");
    ros::NodeHandle& prv_nh = (*p_prv_nh_);

    prv_nh.param<int>("main_loop_frq", main_loop_frq_, 100);
    prv_nh.param<int>("msg_pub_frq", msg_pub_frq_, 30);

    prv_nh.param<std::string>("odom_frame_id",      odom_frame_id_,      "odom_frame");
    prv_nh.param<std::string>("base_frame_id",      base_frame_id_,      "base_frame");
    prv_nh.param<std::string>("sonar_frame_id",     sonar_frame_id_,     "sonar_frame");
    prv_nh.param<std::string>("infrd_frame_id",     infrd_frame_id_,     "infrd_frame");
    prv_nh.param<std::string>("bumper_frame_id",    bumper_frame_id_,    "bumper_frame");
    prv_nh.param<std::string>("battery_frame_id",   battery_frame_id_,   "battery_frame");
    prv_nh.param<std::string>("exception_frame_id", exception_frame_id_, "exception_frame");
    prv_nh.param<std::string>("imu_frame_id",       imu_frame_id_,       "imu_frame");
    prv_nh.param<std::string>("gps_frame_id",       gps_frame_id_,       "gps_frame");

    prv_nh.param<std::string>("emergency_stop_status_topic", emergency_stop_status_topic_, "/emergency_stop_status");
    prv_nh.param<std::string>("entrp_detect_topic",     entrp_detect_topic_,   "/entrp_detect");
    prv_nh.param<std::string>("cmd_vel_topic",          cmd_vel_topic_,        "/cmd_vel");
    prv_nh.param<std::string>("cmd_motor_spd_topic",    cmd_motor_spd_topic_,  "/cmd_motor_spd");
    prv_nh.param<std::string>("act_motor_spd_topic",    act_motor_spd_topic_,  "/act_motor_spd");
    prv_nh.param<std::string>("motor_enc_topic",        motor_enc_topic_,      "/motor_enc");
    prv_nh.param<std::string>("sonar_data_topic",       sonar_data_topic_,     "/sonar_data");
    prv_nh.param<std::string>("infrd_data_topic",       infrd_data_topic_,     "/infrd_data");
    prv_nh.param<std::string>("voltage_data_topic_",    voltage_data_topic_,   "/voltage_data");
    prv_nh.param<std::string>("input_io_topic",         input_io_topic_,       "/input_io");
    prv_nh.param<std::string>("output_io_topic",        output_io_topic_,      "/output_io");
    prv_nh.param<std::string>("bumper_data_topic",      bumper_data_topic_,    "/bumper_data");
    prv_nh.param<std::string>("battery_data_topic",     battery_data_topic_,   "/battery_data");
    prv_nh.param<std::string>("exception_info_topic_",  exception_info_topic_, "/exception_info");
    prv_nh.param<std::string>("imu_data_topic",         imu_data_topic_,       "/imu/data_raw");
    prv_nh.param<std::string>("rpy_data_topic",         rpy_data_topic_,       "/imu/rpy_raw");
    prv_nh.param<std::string>("rpy_data_topic",         all_imu_data_topic_,   "/imu/data_all");
    prv_nh.param<std::string>("gps_data_topic",         gps_data_topic_,       "/gps_data");
    prv_nh.param<std::string>("bcu_com_status_topic",   bcu_com_status_topic_, "/bcu_com_status");

    ComParam& com_param = bcu_param_.com;
    prv_nh.param<ComPortIdType>("port_id",     com_param.port_id,              "/dev/bcu");
    prv_nh.param<int>("cmd_retry_num",         com_param.cmd_retry_num,        com_param.cmd_retry_num);
    prv_nh.param<int>("cmd_delay_ms",          com_param.cmd_delay_ms,         com_param.cmd_delay_ms);
    prv_nh.param<int>("ans_retry_num",         com_param.ans_retry_num,        com_param.ans_retry_num);
    prv_nh.param<int>("ans_delay_ms",          com_param.ans_delay_ms,         com_param.ans_delay_ms);
    prv_nh.param<bool>("enb_com_data_display", com_param.enb_com_data_display, com_param.enb_com_data_display);
    prv_nh.param<int>("baudrate",              com_param.baudrate,             com_param.baudrate);
    LM_INFO("port_id = %s", com_param.port_id.c_str());
    LM_DEBUG("%s", com_param.ToStr().c_str());

    GetEnumParam<wizrobo_npu::ChassisModelType>(prv_nh, "model_type", model_type_, wizrobo_npu::DIFFDRV);
    GetEnumParam<BcuControlMode>(prv_nh, "control_mode", control_mode_, control_mode_);
    GetEnumParam<BcuDriverType>(prv_nh, "driver_type", driver_type_, BcuDriverType::SERIAL_FDK);

    LM_INFO("control_mode = %d", control_mode_);
    LM_INFO("model_type = %s", EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type_).c_str());
    LM_INFO("control_mode = %s", EnumString<BcuControlMode>::EnumToStr(control_mode_).c_str());
    LM_INFO("driver_type = %s", EnumString<BcuDriverType>::EnumToStr(driver_type_).c_str());

    SyncMotorParam();
    SyncSensorParam();

    OnSyncAllParam();

    return;
}

void BcuServer::SyncMotorParam()
{
    ros::NodeHandle& nh = *p_nh_;
    MotorParam& mp = bcu_param_.motor;

    nh.param(STD_MOTOR_PARAM_NS + "/motor_num", mp.motor_num, mp.motor_num);
    nh.param<std::string>(STD_MOTOR_PARAM_NS + "/motor_dir_str", mp.motor_dir_str, mp.motor_dir_str);
    nh.param(STD_MOTOR_PARAM_NS + "/motor_max_spd_rpm", mp.motor_max_spd_rpm, mp.motor_max_spd_rpm);
    GetFloatParam(nh, STD_MOTOR_PARAM_NS + "/motor_rdc_ratio", mp.motor_rdc_ratio, mp.motor_rdc_ratio);
    nh.param(STD_MOTOR_PARAM_NS + "/motor_enc_res_ppr", mp.motor_enc_res_ppr, mp.motor_enc_res_ppr);
    nh.param(STD_MOTOR_PARAM_NS + "/motor_pwm_frq_hz",  mp.motor_pwm_frq_hz, mp.motor_pwm_frq_hz);
    GetEnumParam<BrakeType>(nh, STD_MOTOR_PARAM_NS + "/motor_brk_type", mp.motor_brk_type, mp.motor_brk_type);
    GetEnumParam<ValidOutputLevel>(nh, STD_MOTOR_PARAM_NS + "/enb_vol", mp.enb_vol, mp.enb_vol);
    GetEnumParam<ValidOutputLevel>(nh, STD_MOTOR_PARAM_NS + "/dir_vol", mp.dir_vol, mp.dir_vol);
    GetEnumParam<ValidOutputLevel>(nh, STD_MOTOR_PARAM_NS + "/pwm_vol", mp.pwm_vol, mp.pwm_vol);
    GetEnumParam<ValidOutputLevel>(nh, STD_MOTOR_PARAM_NS + "/brk_vol", mp.brk_vol, mp.brk_vol);
    nh.param<bool>(STD_MOTOR_PARAM_NS + "/enb_auxdir_mode", mp.enb_auxdir_mode, mp.enb_auxdir_mode);
    nh.param<bool>(STD_MOTOR_PARAM_NS + "/enb_throttle_mode", mp.enb_throttle_mode, mp.enb_throttle_mode);
    GetFloatParam(nh, STD_MOTOR_PARAM_NS + "/throttle_zero_pos_fac", mp.throttle_zero_pos_fac, mp.throttle_zero_pos_fac);
    GetFloatParam(nh, STD_MOTOR_PARAM_NS + "/throttle_zero_neg_fac", mp.throttle_zero_neg_fac, mp.throttle_zero_neg_fac);
    GetFloatParam(nh, STD_MOTOR_PARAM_NS + "/max_acc_time_s", mp.max_acc_time_s, mp.max_acc_time_s);
    GetFloatParam(nh, STD_MOTOR_PARAM_NS + "/max_dec_time_s", mp.max_dec_time_s, mp.max_dec_time_s);
    nh.param<bool>(STD_MOTOR_PARAM_NS + "/end_left_right_switch", mp.end_left_right_switch, mp.end_left_right_switch);
    GetFloatVecParam(nh, STD_MOTOR_PARAM_NS + "/enc_pos_scales", mp.enc_pos_scales, mp.motor_num, 1.0f);
    GetFloatVecParam(nh, STD_MOTOR_PARAM_NS + "/enc_neg_scales", mp.enc_neg_scales, mp.motor_num, 1.0f);
    nh.param<float>(STD_MOTOR_PARAM_NS + "/hall_spd_scale", mp.motor_hall_spd_scale, 1.0f);
    nh.param<std::string>(STD_MOTOR_PARAM_NS + "/enc_dir_str", mp.enc_dir_str, mp.enc_dir_str);
    nh.param<bool>(STD_MOTOR_PARAM_NS + "/enb_slow_launch", mp.enb_slow_launch, mp.enb_slow_launch);
    nh.param<float>(STD_MOTOR_PARAM_NS + "/motor_start_rpm", mp.motor_start_rpm, mp.motor_start_rpm);
    nh.param<float>(STD_MOTOR_PARAM_NS + "/motor_acc_rpm", mp.motor_acc_rpm, mp.motor_acc_rpm);
    nh.param<float>(STD_MOTOR_PARAM_NS + "/dac_offset_v_left", mp.dac_offset_v_left, mp.dac_offset_v_left);
    nh.param<float>(STD_MOTOR_PARAM_NS + "/dac_offset_v_right", mp.dac_offset_v_right, mp.dac_offset_v_right);

    LM_DEBUG("Modifed motor param: %s", mp.ToStr().c_str());

    PidParam& pp = bcu_param_.pid;

    nh.param<bool>(STD_PID_PARAM_NS + "/enb_pid",  pp.enb_pid, pp.enb_pid);
    GetFloatParam(nh, STD_PID_PARAM_NS + "/kp_acc", pp.kp_acc, pp.kp_acc);
    GetFloatParam(nh, STD_PID_PARAM_NS + "/kp_dec", pp.kp_dec, pp.kp_dec);
    GetFloatParam(nh, STD_PID_PARAM_NS + "/ki_acc", pp.ki_acc, pp.ki_acc);
    GetFloatParam(nh, STD_PID_PARAM_NS + "/ki_dec", pp.ki_dec, pp.ki_dec);
    GetFloatParam(nh, STD_PID_PARAM_NS + "/kd_acc", pp.kd_acc, pp.kd_acc);
    GetFloatParam(nh, STD_PID_PARAM_NS + "/kd_dec", pp.kd_dec, pp.kd_dec);

    LM_DEBUG("Modified pid param: %s", pp.ToStr().c_str());
}


void BcuServer::SyncSensorParam()
{
    ros::NodeHandle& nh = *p_nh_;
    ros::NodeHandle& prv_nh = (*p_prv_nh_);
    SensorParam& sp = bcu_param_.sensor;

    GetEnumParam<wizrobo_npu::ValidOutputLevel>(nh, STD_MOTOR_PARAM_NS + "/bmp_vol", sp.bmp_vol, sp.bmp_vol);
    GetEnumParam<wizrobo_npu::ValidOutputLevel>(nh, STD_MOTOR_PARAM_NS + "/esb_vol", sp.esb_vol, sp.esb_vol);
    GetEnumParam<wizrobo_npu::ValidOutputLevel>(nh, STD_MOTOR_PARAM_NS + "/brk_vol", sp.brk_vol, sp.brk_vol);
    nh.param<int>(STD_MOTOR_PARAM_NS + "/dead_time", sp.dead_time, sp.dead_time);
    if(prv_nh.getNamespace() == "/bcu_scrubber_node"){
        prv_nh.param<bool>("enb_imu", sp.enb_imu, sp.enb_imu);
    } else if(prv_nh.getNamespace() == "/bcu_server_node"){
        nh.param<bool>(STD_MOTOR_PARAM_NS+"/enb_imu", sp.enb_imu, sp.enb_imu);
    }
    nh.param<bool>(STD_MOTOR_PARAM_NS+"/enb_gps", sp.enb_gps, sp.enb_gps);
    int infrd_num;
    nh.param<int>(STD_SENSOR_PARAM_NS + "/infrd_num", infrd_num, 0);
    if(infrd_num > MAX_INFRD_NUM) {
        LM_ERROR("infrd_num(%d) is greater than MAX_INFRD_NUM(%d)", infrd_num, MAX_INFRD_NUM);
    }
    sp.infrd.resize(MAX_INFRD_NUM);
    for(int i=0;i<MAX_INFRD_NUM;i++) {
        std::stringstream ss;
        ss << STD_SENSOR_PARAM_NS << "/infrd_params/infrd_" << i;
        nh.param<float>(ss.str()+"/k_voltage_to_range", sp.infrd[i].k, 1.0);
        nh.param<float>(ss.str()+"/convert_offset", sp.infrd[i].offset, 0.0);
        nh.param<float>(ss.str()+"/trigger_level_1_range_m", sp.infrd[i].threshold, -1.0);
    }
    sp.sonar_threshold.resize(MAX_SONAR_NUM);
    for(int i=0;i<MAX_SONAR_NUM;i++) {
        std::stringstream ss;
        ss << STD_SENSOR_PARAM_NS << "/sonar_params/sonar_" << i;
        nh.param<float>(ss.str()+"/trigger_level_1_range_m", sp.sonar_threshold[i], -1.0);
    }
    nh.param<float>(STD_SENSOR_PARAM_NS+"/bcu_power/k_voltage_to_range", sp.main_power[0].k, 1.0);
    nh.param<float>(STD_SENSOR_PARAM_NS+"/bcu_power/convert_offset", sp.main_power[0].offset, 0.0);
    nh.param<float>(STD_SENSOR_PARAM_NS+"/battery/k_voltage_to_range", sp.battery[0].k, 1.0);
    nh.param<float>(STD_SENSOR_PARAM_NS+"/battery/convert_offset", sp.battery[0].offset, 0.0);

    LM_DEBUG("Modified sensor param: %s", sp.ToStr().c_str());
}

bool BcuServer::MotorDynParamSrvCallback(wr_npu_msgs::DynParam::Request &req, wr_npu_msgs::DynParam::Response &res)
{
    SyncMotorParam();
    return true;
}

bool BcuServer::PidDynParamSrvCallback(wr_npu_msgs::DynParam::Request &req, wr_npu_msgs::DynParam::Response &res)
{
    SyncMotorParam();
    return true;
}

bool BcuServer::SensorDynParamSrvCallback(wr_npu_msgs::DynParam::Request &req, wr_npu_msgs::DynParam::Response &res)
{
    SyncSensorParam();
    return true;
}

bool BcuServer::BrakeSrvCallback(wr_npu_msgs::FuncRtnBool::Request &req, wr_npu_msgs::FuncRtnBool::Response &res)
{
    LM_INFO("");
    if (!is_inited_) {
        return false;
    }
    res.rlt = (p_bcu_->Brake(true)==TRUE)?true:false;
    return res.rlt;
}

bool BcuServer::ClearMotorEncCallback(wr_npu_msgs::ClearMotorEncRequest &rqst, wr_npu_msgs::ClearMotorEncResponse &resp)
{
    LM_INFO("");
    if (!is_inited_) {
        return false;
    }
    if (!p_bcu_->ClrMotorEnc()) {
        return false;
    }
    last_ticks.resize(last_ticks.size(), 0);
    last_raw_ticks.resize(last_raw_ticks.size(), 0);
    return true;
}

void BcuServer::BcuDynamicConfigCallback(BcuDynamicConfig& config, uint32_t level)
{
    LM_DEBUG("TODO");
    return;
}

void BcuServer::PidDynamicConfigCallback(PidDynamicConfig& config, uint32_t level)
{
    static bool is_init = false;
    if(!is_init) {
        config.enb_pid = bcu_param_.pid.enb_pid;
        config.kp_acc = bcu_param_.pid.kp_acc;
        config.ki_acc = bcu_param_.pid.ki_acc;
        config.kd_acc = bcu_param_.pid.kd_acc;
        config.kp_dec = bcu_param_.pid.kp_dec;
        config.ki_dec = bcu_param_.pid.ki_dec;
        config.kd_dec = bcu_param_.pid.kd_dec;
        is_init = true;
    } else {
        bcu_param_.pid.enb_pid = config.enb_pid;
        bcu_param_.pid.kp_acc = config.kp_acc;
        bcu_param_.pid.ki_acc = config.ki_acc;
        bcu_param_.pid.kd_acc = config.kd_acc;
        bcu_param_.pid.kp_dec = config.kp_dec;
        bcu_param_.pid.ki_dec = config.ki_dec;
        bcu_param_.pid.kd_dec = config.kd_dec;
    }
    p_bcu_->SetPidParam();
    return;
}

void BcuServer::ExceptionInfoCallback(const wr_npu_msgs::ExceptionInformation::ConstPtr& msg)
{
    const wr_npu_msgs::ExceptionInformation& exception = *msg;
    LM_DEBUG("state_index: %d", exception.state_index);
    if (!is_inited_) {
        return;
    }

    switch (exception.state_index) {
    case wr_npu_msgs::ExceptionInformation::EXCEPTION_RESET:
        is_emergency_stop_ = false;
        //beeper_mode_ = BEEPER_MUTE;
        //led_mode_ = LED_OFF;
        break;
    case wr_npu_msgs::ExceptionInformation::BUMMPER_TRIGGERED:
        is_emergency_stop_ = true;
        //beeper_mode_ = BEEPER_MODE_1;
        //led_mode_ = LED_ALL;
        break;
    case wr_npu_msgs::ExceptionInformation::EM_STP_TRIGGERED:
        is_emergency_stop_ = true;
        //beeper_mode_ = BEEPER_MODE_2;
        //led_mode_ = LED_ALL;
        break;
    case wr_npu_msgs::ExceptionInformation::INFRD_TRIGGERED:
        is_emergency_stop_ = true;
        //beeper_mode_ = BEEPER_MODE_3;
        //led_mode_ = LED_ALL;
        break;
    default:
        break;
    }
    return;
}

void BcuServer::CmdMotorSpdCallback(const wr_npu_msgs::MotorSpd::ConstPtr& msg)
{
    LM_DEBUG("");
    if (!is_inited_) {
        return;
    }
    const wr_npu_msgs::MotorSpd& cmd_spd = *msg;
    MotorSpdArray rpms;
    rpms.resize(cmd_spd.motor_num);
    for (int i = 0; i < rpms.size(); i++) {
        rpms[i] = cmd_spd.rdc_rpss[i] * bcu_param_.motor.motor_rdc_ratio * 60.0;
    }
    wizrobo_npu::ChassisModelType model_type =
            EnumString<wizrobo_npu::ChassisModelType>::StrToEnum(cmd_spd.model_type);
    switch (model_type) {
    case wizrobo_npu::CARLIKE:
        LM_DEBUG("[CARLIKE][MOTOR_SPD] steer_angle = %.2f [deg], spds = (%.2f) [rpm]"
                  , cmd_spd.carlike_steer_angle_deg, rpms[0]);
        break;
    case wizrobo_npu::DIFFDRV:
        LM_DEBUG("[DIFFDRV][MOTOR_SPD] wheel_num = %d, spds = (%.2f, %.2f) [rpm]"
                  , cmd_spd.motor_num, rpms[0], rpms[1]);
        left_spd_ = rpms[0];
        right_spd_ = rpms[1];
        break;
    case wizrobo_npu::UNIVWHEEL:
        LM_DEBUG("[UNIVWHEEL][MOTOR_SPD] wheel_num = %d, spds = (%.2f, %.2f, %.2f) [rpm]"
                  , cmd_spd.motor_num, rpms[0], rpms[1], rpms[2]);
        break;
    case wizrobo_npu::OMNIWHEEL:
        LM_DEBUG("[OMNIWHEEL][MOTOR_SPD] wheel_num = %d, spds = (%.2f, %.2f, %.2f, %.2f) [rpm]"
                  , cmd_spd.motor_num
                  , rpms[0], rpms[1], rpms[2], rpms[3]);
    case wizrobo_npu::CSGDRV:
        LM_DEBUG("[CSGDRV][MOTOR_SPD] wheel_num = %d, angle = %.4f, spd = %.4f",
                  cmd_spd.motor_num, cmd_spd.carlike_steer_angle_deg, rpms[0]);
        steer_location_ =
                EnumString<wizrobo_npu::SteerEncLocation>::StrToEnum(cmd_spd.carlike_steer_enc_location);
        steer_angle_deg_ = cmd_spd.carlike_steer_angle_deg;
        left_spd_ = rpms[0];
        right_spd_ = rpms[0];
        break;
    default:
        LM_FATAL("Illegal value of cmd_spd.model_type value");
        break;
    }
}

void BcuServer::CmdVelCallback(const geometry_msgs::Twist::ConstPtr& msg)
{
    LM_DEBUG("");
    cmd_linear_vel_ = msg->linear.x;
    cmd_angular_vel_ = msg->angular.z;
    return;
}

void BcuServer::SecurityStatusCallback(const wr_npu_msgs::ScrubberSecurityStatusConstPtr& msg)
{
    int sensor_num = static_cast<int>(msg->infrd_warning.size());
    infrd_status_.resize(sensor_num);
    for(int i = 0;i < sensor_num ;i++){
        infrd_status_[i] = msg->infrd_warning[i];
        if(msg->infrd_warning[i] == 1){
            infrd_em_stop_ = true;
            break;
        }
    }
    sensor_num = static_cast<int>(msg->sonar_warning.size());
    infrd_status_.resize(sensor_num);
    for(int i = 0;i < sensor_num ;i++){
        sonar_status_[i] = msg->sonar_warning[i];
        if(msg->sonar_warning[i] == 1){
            sonar_em_stop_ = true;
            break;
        }
    }
    sensor_num = static_cast<int>(msg->bumper_warning.size());
    infrd_status_.resize(sensor_num);
    for(int i = 0;i < sensor_num ;i++){
        bumper_status_[i] = msg->bumper_warning[i];
        if(msg->bumper_warning[i] == 1){
            bumper_em_stop_ = true;
            break;
        }
    }
    return;
}

void BcuServer::RestoreStateCallback(const std_msgs::Bool::ConstPtr &msg)
{
    is_em_stop_restoring_ =  msg->data;
}

void BcuServer::AutoEmStopCallback(const wr_npu_msgs::EmergencyStopData::ConstPtr &msg)
{
    auto_em_stop_ = msg->status;
}

int BcuServer::UpdateActMotorSpd(bool pub_only, const MotorSpdArray& act_rpms)
{
    LM_DEBUG("");
    if (!is_inited_) {
        return FALSE;
    }
    bcu_driver::MotorSpdArray rpms;
    float angle = 0.0;
    wizrobo_npu::SteerEncLocation location = wizrobo_npu::SteerEncLocation::CENTER;
    if (pub_only) {
        rpms = act_rpms;
    } else {
        if (!p_bcu_->GetMotorSpd(rpms)) {
            return FALSE;
        }
        if (model_type_ == wizrobo_npu::ChassisModelType::CSGDRV) {
            if (!p_bcu_->GetSteerAngle(angle, location)) {
                return FALSE;
            }
        }
    }
    ROS_ASSERT(rpms.size() == bcu_param_.motor.motor_num);
#if 0
    for (int i = 0; i < rpms.size(); i++) {
        if (rpms[i] > 0) {
            rpms[i] *= bcu_param_.motor.enc_pos_scales[i];
        } else if (rpms[i] < 0) {
            rpms[i] *= bcu_param_.motor.enc_neg_scales[i];
        }
    }
#endif
    wr_npu_msgs::MotorSpd motor_spd;
    motor_spd.model_type = EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type_);
    motor_spd.motor_num = rpms.size();
    motor_spd.rpms.resize(rpms.size());
    motor_spd.rpss.resize(rpms.size());
    /*
    motor_spd.carlike_steer_angle_deg;
    motor_spd.carlike_steer_enc_location;
    */
    motor_spd.rdc_rpss.resize(rpms.size());
    for (int i = 0; i < rpms.size(); i++) {
        motor_spd.rpms[i] = rpms[i];
        motor_spd.rpss[i] = TrimFloat(rpms[i] / 60.0, 2);
        motor_spd.rdc_rpss[i] = TrimFloat(
                    motor_spd.rpss[i] / bcu_param_.motor.motor_rdc_ratio, 2);
    }
    motor_spd.carlike_steer_angle_deg = angle;
    motor_spd.carlike_steer_enc_location =
            EnumString<wizrobo_npu::SteerEncLocation>::EnumToStr(location);
    //TODO: CARLIKE chassis support
    motor_spd.header.stamp = ros::Time::now();
    motor_spd.header.frame_id = base_frame_id_;
    act_motor_spd_pub_.publish(motor_spd);
    return TRUE;
}

int BcuServer::UpdateMotorEnc()
{
    LM_DEBUG("");
    if (!is_inited_) {
        return FALSE;
    }
    bcu_driver::MotorEncArray raw_ticks;
    float angle = 0.0;
    wizrobo_npu::SteerEncLocation location = wizrobo_npu::SteerEncLocation::CENTER;

    if (!p_bcu_->GetMotorEnc(raw_ticks)) {
        return FALSE;
    }
    if (model_type_ == wizrobo_npu::ChassisModelType::CSGDRV) {
        if (!p_bcu_->GetSteerAngle(angle, location)) {
            return FALSE;
        }
    }
    ROS_ASSERT(raw_ticks.size() == bcu_param_.motor.motor_num);
    bcu_driver::MotorEncArray ticks = raw_ticks;
    for (int i = 0; i < raw_ticks.size(); i++) {
        if (raw_ticks[i] - last_raw_ticks[i] > 0) {
            ticks[i] = (ticks[i] - last_raw_ticks[i])
#if 0
                    * bcu_param_.motor.enc_pos_scales[i]
#endif
                    + last_ticks[i];
        } else if (raw_ticks[i] - last_raw_ticks[i] < 0) {
            ticks[i] = (ticks[i] - last_raw_ticks[i])
#if 0
                    * bcu_param_.motor.enc_neg_scales[i]
#endif
                    + last_ticks[i];
        } else {
            ticks[i] = last_ticks[i];
        }
    }
    last_raw_ticks = raw_ticks;
    last_ticks = ticks;

    pthread_spin_lock(&motor_enc_msg_lock_);
    motor_enc_msg_.model_type = EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type_);
    motor_enc_msg_.motor_num = ticks.size();
    motor_enc_msg_.ticks = ticks;
    motor_enc_msg_.degs.resize(ticks.size());
    motor_enc_msg_.rdc_degs.resize(ticks.size());
    for (int i = 0; i < ticks.size(); i++) {
        motor_enc_msg_.degs[i] = TrimFloat(360.0 * ticks[i] / bcu_param_.motor.motor_enc_res_ppr / 4, 2);// (AB_CHNL)
        motor_enc_msg_.rdc_degs[i] = TrimFloat(motor_enc_msg_.degs[i] / bcu_param_.motor.motor_rdc_ratio, 2);
    }
    //TODO: CARLIKE chassis support
    motor_enc_msg_.header.stamp = ros::Time::now();
    motor_enc_msg_.header.frame_id = odom_frame_id_;
    motor_enc_msg_.child_frame_id = base_frame_id_;
    motor_enc_msg_.carlike_steer_angle_deg = angle;
    motor_enc_msg_.carlike_steer_enc_location = EnumString<wizrobo_npu::SteerEncLocation>::EnumToStr(location);

    pthread_spin_unlock(&motor_enc_msg_lock_);
    wr_npu_msgs::MotorEnc raw_motor_enc = motor_enc_msg_;
    raw_motor_enc.ticks = raw_ticks;
    raw_motor_enc.degs.resize(raw_ticks.size());
    raw_motor_enc.rdc_degs.resize(raw_ticks.size());
    for (int i = 0; i < raw_ticks.size(); i++) {
        raw_motor_enc.degs[i] = TrimFloat(
                    360.0 * raw_ticks[i] / bcu_param_.motor.motor_enc_res_ppr / 4, 2);// (AB_CHNL)
        raw_motor_enc.rdc_degs[i] = TrimFloat(
                    raw_motor_enc.degs[i] / bcu_param_.motor.motor_rdc_ratio, 2);
    }
    raw_motor_enc.carlike_steer_angle_deg = angle;
    raw_motor_enc.carlike_steer_enc_location =
            EnumString<wizrobo_npu::SteerEncLocation>::EnumToStr(location);
    raw_motor_enc_pub_.publish(raw_motor_enc);
    return TRUE;
}

int BcuServer::UpdateImuData()
{

    bcu_driver:: ImuData data;
    if (!p_bcu_->GetImuData(data)) {
        return false;
    }
    sensor_msgs::Imu imu_msg;
    sensor_msgs::Imu imu_all_msg;

    imu_msg.header.stamp = ros::Time::now();
    imu_msg.header.frame_id = imu_frame_id_;

    imu_msg.linear_acceleration_covariance[0] = linear_acceleration_cov_;
    imu_msg.linear_acceleration_covariance[4] = linear_acceleration_cov_;
    imu_msg.linear_acceleration_covariance[8] = linear_acceleration_cov_;

    imu_msg.angular_velocity_covariance[0] = angular_velocity_cov_;
    imu_msg.angular_velocity_covariance[4] = angular_velocity_cov_;
    imu_msg.angular_velocity_covariance[8] = angular_velocity_cov_;

    imu_msg.orientation_covariance[0] = orientation_x_covar_;
    imu_msg.orientation_covariance[4] = orientation_y_covar_;
    imu_msg.orientation_covariance[8] = orientation_z_covar_;

    imu_msg.angular_velocity.x = 0.0;
    imu_msg.angular_velocity.y = 0.0;
    imu_msg.angular_velocity.z = data.gyro.z;
    imu_msg.linear_acceleration.x = 0.0;
    imu_msg.linear_acceleration.y = 0.0;
    imu_msg.linear_acceleration.z = 9.8;
    imu_msg.orientation = tf::createQuaternionMsgFromRollPitchYaw(
          0.0,
          0.0,
          DEG2RAD(data.pose.yaw));
    if (imu_data_pub_.getNumSubscribers() != 0) {
        imu_data_pub_.publish(imu_msg);
    }
    imu_all_msg = imu_msg;
    imu_all_msg.linear_acceleration.x = data.acc.x;
    imu_all_msg.linear_acceleration.y = data.acc.y;
    imu_all_msg.linear_acceleration.z = data.acc.z;
    imu_all_msg.angular_velocity.x = data.gyro.x;
    imu_all_msg.angular_velocity.y = data.gyro.y;
    imu_all_msg.angular_velocity.z = data.gyro.z;

    if (all_imu_data_pub_.getNumSubscribers() != 0) {
        all_imu_data_pub_.publish(imu_all_msg);
    }

    geometry_msgs::Vector3Stamped rpy_msg;
    rpy_msg.header.stamp = ros::Time::now();
    rpy_msg.header.frame_id = imu_frame_id_;
    rpy_msg.vector.x = 0.0;
    rpy_msg.vector.y = 0.0;
    rpy_msg.vector.z = data.pose.yaw;
    if (rpy_data_pub_.getNumSubscribers() != 0) {
        rpy_data_pub_.publish(rpy_msg);
    }
    return TRUE;
}

int BcuServer::UpdateSensorData()
{
    ros::NodeHandle nh;
    SensorData sensor_data;
    bool is_entrp_detected = false;

    LM_DEBUG("");
    if (!is_inited_) {
        return FALSE;
    }
    if (!p_bcu_->GetSensorData(sensor_data)) {
        LM_WARN("GetSensorData failed!!!");
        return FALSE;
    }

    wr_npu_msgs::EmergencyStopData ems_msg;
    ems_msg.status = sensor_data.esb;
    ems_msg.header.stamp = ros::Time::now();
    ems_msg.header.frame_id = base_frame_id_;
    emergency_stop_status_pub_.publish(ems_msg);

    wr_npu_msgs::BumperData io_msg;
    io_msg.bumper_num = 8;
    io_msg.states.resize(8);
    io_msg.header.frame_id = base_frame_id_;
    io_msg.header.stamp = ros::Time::now();

    for (int i=0;i<8;i++) {
        io_msg.states[i] = (sensor_data.io_input>>i)&0x01;
    }
    input_io_pub_.publish(io_msg);

    //output_io_pub_.publish(io_msg);
    for (int i=0;i<8;i++) {
        io_msg.states[i] = (sensor_data.bumper>>i)&0x01;
    }
    bumper_data_pub_.publish(io_msg);

    for(int i=0;i<sonars_range_msg_.size();++i) {
        // for navi
        sonars_range_msg_[i].header.stamp = ros::Time::now();
        sonars_range_msg_[i].range = sensor_data.sonar[i];
        sonar_range_pubs_[i].publish(sonars_range_msg_[i]);
        if (bcu_param_.sensor.sonar_threshold[i] > 0
                && sonars_range_msg_[i].range < bcu_param_.sensor.sonar_threshold[i]) {
            is_entrp_detected = true;
        }
    }

    for(int i=0;i<infrds_range_msg_.size();++i) {
        // for navi
        infrds_range_msg_[i].header.stamp = ros::Time::now();
        infrds_range_msg_[i].range = sensor_data.infrd[i];
        infrd_range_pubs_[i].publish(infrds_range_msg_[i]);
        if ( bcu_param_.sensor.infrd[i].threshold > 0
             && infrds_range_msg_[i].range < bcu_param_.sensor.infrd[i].threshold) {
            is_entrp_detected = true;
        }
    }

    wr_npu_msgs::EmergencyStopData entrp_detect_msg;
    if (is_entrp_detected) {
        entrp_detect_msg.header.stamp = ros::Time::now();
        entrp_detect_msg.header.frame_id = base_frame_id_;
        entrp_detect_msg.status = 1;
        entrp_detect_pub_.publish(entrp_detect_msg);
    }

    wr_npu_msgs::VoltageData voltage_msg;
    voltage_msg.power_voltage = sensor_data.power_vol;
    voltage_msg.battery_voltage = sensor_data.battery_vol;
    voltage_msg.header.stamp = ros::Time::now();
    voltage_msg.header.frame_id = infrd_frame_id_;
    voltage_data_pub_.publish(voltage_msg);
    return TRUE;
}

void BcuServer::PubLoopThread()
{
    ros::Rate loop_rater(msg_pub_frq_);
    while (ros::ok()) {
        loop_rater.sleep();
        if (pthread_spin_trylock(&motor_enc_msg_lock_) != 0) {
            continue;
        }
        if (motor_enc_msg_.motor_num != 0) {
            motor_enc_pub_.publish(motor_enc_msg_);
        }
        pthread_spin_unlock(&motor_enc_msg_lock_);
    }
}

}// namespace
