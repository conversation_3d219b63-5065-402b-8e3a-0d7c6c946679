#include "serial_keya/serial_keya_bcu_driver.h"
#include <string>
#include <ros/ros.h>

namespace wizrobo { namespace bcu_driver {
using namespace serial_keya;

SerialKeyaBcuDriver::SerialKeyaBcuDriver()
{

}
SerialKeyaBcuDriver::~SerialKeyaBcuDriver()
{
    if(serial_port_.IsOpen())
        serial_port_.Close();
}

int SerialKeyaBcuDriver::Init(BcuParamPtr ptr)
{
    bcu_param_ptr = ptr;
    if(serial_port_.Open(bcu_param_ptr->com.port_id, serial_keya::BAUDRATE,serial_keya::DATABITS,serial_keya::PARITY))
    {
        is_inited_ = TRUE;
        return TRUE;
    }
    return FALSE;
}

int SerialKeyaBcuDriver::Stop()
{
    WR_DEBUG("SerialKeyaBcuDriver::Stop()");
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    MotorSpdArray rpms(bcu_param_ptr->motor.motor_num, 0);
    SetMotorSpd(rpms);
    return TRUE;
}

int SerialKeyaBcuDriver::GetMotorSpd(MotorSpdArray& rpm)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    std::string cmd(serial_keya::GET_MTR_SPD + serial_keya::CMD_END);
    std::string ans;

    serial_port_.ClearReadBuffer();

    if(!WriteCmd(cmd.c_str(), cmd.size()))
    {
        return FALSE;
    }

    rpm.resize(bcu_param_ptr->motor.motor_num);

    if(ReceiveAns(ans) && ParseMotorSpd(ans, rpm))
    {
//        for(int i=0;i<rpm.size();i++)
//        {
//            //r = r * bcu_param_ptr->motor.motor_hall_spd_scale;
//            rpm[i] = rpm[i] * (bcu_param_ptr->motor.enc_dir_str[i] == '+' ? 1 : (-1));
//        }
        return TRUE;
    }
    return FALSE;
}

int SerialKeyaBcuDriver::SetMotorSpd(const MotorSpdArray& spd)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    int motor_spd[2];    
    for(int i = 0; i < 2; ++i)
    {
        motor_spd[i] = static_cast<int>(spd[i] / bcu_param_ptr->motor.motor_max_spd_rpm * serial_keya::MAX_MOTOR_SPD);
        motor_spd[i] *= (bcu_param_ptr->motor.motor_dir_str[i] == '+' ? 1 : (-1));

        if(motor_spd[i] > serial_keya::MAX_MOTOR_SPD)
        {
            motor_spd[i] = serial_keya::MAX_MOTOR_SPD;
        }
        else if(motor_spd[i] < serial_keya::MIN_MOTOR_SPD)
        {
            motor_spd[i] = serial_keya::MIN_MOTOR_SPD;
        }
    }

    std::string cmd;
    std::string ans;

    int index = (bcu_param_ptr->motor.end_left_right_switch==false)?0:1;
    const int *id_map = serial_keya::MOTOR_ID_MAP[index];

    cmd.append(serial_keya::SET_MTR_SPD);
    std::string spd_s;
    spd_s = std::to_string(motor_spd[id_map[0]]);
    cmd.append(spd_s);
    cmd.append(serial_keya::COLON);
    spd_s = std::to_string(motor_spd[id_map[1]]);
    cmd.append(spd_s);
    cmd.append(serial_keya::CMD_END);

    if(!WriteCmd(cmd.c_str(), cmd.size()))
    {
        return FALSE;
    }

    if(ReceiveAns(ans))
    {
        return TRUE;
    }
    else
    {
        WR_ERROR("keya driver: set speed ans error.");
        PrintAns(ans);
        return FALSE;
    }
}

int SerialKeyaBcuDriver::GetMotorEnc(MotorEncArray& ticks)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    std::string cmd(serial_keya::GET_MTR_ENC + serial_keya::CMD_END);
    std::string ans;

    ticks.resize(bcu_param_ptr->motor.motor_num);
    serial_port_.ClearReadBuffer();

    if(!WriteCmd(cmd.c_str(), cmd.size()))
    {
        return FALSE;
    }

    if(!ReceiveAns(ans) || !ParseEncData(ans, ticks))
    {
        return FALSE;
    }
    for(int i = 0; i < bcu_param_ptr->motor.motor_num; ++i)
    {
        ticks[i] *= ((bcu_param_ptr->motor.enc_dir_str[i]=='+')?1:(-1));
    }
    return TRUE;

}

int SerialKeyaBcuDriver::WriteCmd(const char* cmd, uint len)
{
    serial_port_.ClearReadBuffer();

    if (serial_port_.Write(cmd, len) < len)
    {
        WR_ERROR("Writing command failed. Please check serial port.");
        while(!serial_port_.Open(bcu_param_ptr->com.port_id, serial_keya::BAUDRATE,serial_keya::DATABITS,serial_keya::PARITY))
        {
            WR_DEBUG("retrying...");
            WR_SLEEP_MS(bcu_param_ptr->com.cmd_delay_ms);
        }
        WR_INFO("keya driver reconnected");
        return FALSE;
    }

    return TRUE;
}

int SerialKeyaBcuDriver::ReceiveAns(std::string& ans)
{
    uint retry_cnt = 0;

    while(retry_cnt < bcu_param_ptr->com.ans_retry_num)
    {
        WR_SLEEP_MS(bcu_param_ptr->com.ans_delay_ms);
        if(serial_port_.BytesWaiting() > 0)
        {
            uint len = serial_port_.BytesWaiting();
            char *buf = new char[len];

            if(serial_port_.Read(buf, len) < len)
            {
                WR_ERROR("serial port read failed.");
            }
            else
            {
                ans.append(buf, len);
                if(CheckAns(ans))
                      break;
            }
        }
        retry_cnt++;
    }

    if (retry_cnt < bcu_param_ptr->com.ans_retry_num)
    {
        serial_port_.ClearReadBuffer();
        return TRUE;
    }
    else
    {
        WR_ERROR("ReceiveAns() failed: TIMEOUT");
        return FALSE;
    }
}

int SerialKeyaBcuDriver::ParseMotorSpd(const std::string& ans, MotorSpdArray& rpm)
{
    int index = (bcu_param_ptr->motor.end_left_right_switch==false)?0:1;
    const int *id_map = serial_keya::MOTOR_ID_MAP[index];
    if(ans[3] == 'S' && ans[4] == '=')
    {
        auto idx = ans.find_first_of("-0123456789");
        if(idx != ans.npos)
        {
            try
            {
                rpm[id_map[0]] = -stoi(ans.substr(idx));
            }

            catch(std::invalid_argument)
            {
                WR_ERROR("invalid_argument");
                return FALSE;
            }
        }
        else
        {
            WR_ERROR("keya driver: wrong spd data 01.");
            PrintAns(ans);
            return FALSE;
        }

        idx = ans.find(':');
        if(idx != ans.npos)
        {
            try
            {
                rpm[id_map[1]] = -stoi(ans.substr(idx + 1));
            }

            catch(std::invalid_argument)
            {
                WR_ERROR("invalid_argument");
                return FALSE;
            }
        }
        else
        {
            WR_ERROR("keya driver: wrong spd data 02.");
            PrintAns(ans);
            return FALSE;
        }
        return TRUE;
    }
    else
    {
        WR_ERROR("keya driver: wrong spd data 03.");
        PrintAns(ans);
        return FALSE;
    }
    return TRUE;
}

int SerialKeyaBcuDriver::ParseEncData(const std::string& ans, MotorEncArray& rpm)
{
    int index = (bcu_param_ptr->motor.end_left_right_switch==false)?0:1;
    const int *id_map = serial_keya::MOTOR_ID_MAP[index];

    if(ans[3] == 'C' && ans[4] == '=')
    {
        auto idx = ans.find_first_of("-0123456789");
        if(idx != ans.npos)
        {
            try
            {
                rpm[id_map[0]] = -stoi(ans.substr(idx));
            }

            catch(std::invalid_argument)
            {
                WR_ERROR("invalid_argument");
                return FALSE;
            }
        }
        else
        {
            WR_ERROR("keya driver: wrong enc data.");
            PrintAns(ans);
            return FALSE;
        }

        idx = ans.find(':');
        if(idx != ans.npos)
        {
            try
            {
                rpm[id_map[1]] = -stoi(ans.substr(idx + 1));
            }
            catch(std::invalid_argument)
            {
                WR_ERROR("invalid_argument");
                return FALSE;
            }
        }
        else
        {
            WR_ERROR("keya driver: wrong enc data.");
            PrintAns(ans);
            return FALSE;
        }
        return TRUE;
    }
    else
    {
        WR_ERROR("keya driver: wrong enc data.");
        PrintAns(ans);
        return FALSE;
    }
    return TRUE;
}

int SerialKeyaBcuDriver::CheckAns(const std::string& ans)
{
    if(ans.size() < serial_keya::MIN_ANS_LEN)
        return FALSE;

    int cnt = 0;
    for(const char& c: ans)
    {
        if(c == '\r')
            cnt++;
    }

    if(ans.back() == '\r' && cnt == 2)
        return TRUE;
    else
        return FALSE;
}

void SerialKeyaBcuDriver::PrintAns(const std::string& ans)
{
    std::string temp = ans;
    for(char &c : temp)
    {
        if(c == '\r')
        {
            c = '.';
        }
    }
    WR_DEBUG("len = %d, ans = %s", static_cast<int>(temp.size()), temp.c_str());
}

}}  // namespace
