﻿#include "serial_fdk/serial_fdk_bcu_driver.h"
#include <string>
#include <ros/ros.h>
#include <algorithm>
#include <vector>

namespace wizrobo { namespace bcu_driver {
using namespace serial_fdk;
using namespace std;
SerialFdkBcuDriver::SerialFdkBcuDriver()
{
    ros::NodeHandle nh;
    motor_status_service_ = nh.advertiseService("/scrubber_get_motor_status", &SerialFdkBcuDriver::MotorStatusCallBack, this);
}
SerialFdkBcuDriver::~SerialFdkBcuDriver()
{
    if(serial_port_.IsOpen())
        serial_port_.Close();
}

int SerialFdkBcuDriver::Init(BcuParamPtr ptr)
{
    bcu_param_ptr = ptr;
    if(serial_port_.Open(bcu_param_ptr->com.port_id, serial_fdk::BAUDRATE,serial_fdk::DATABITS,serial_fdk::PARITY))
    {
        is_inited_ = TRUE;
        EnbDriver(TRUE);
        return TRUE;
    }
    return FALSE;
}


bool SerialFdkBcuDriver::MotorStatusCallBack(wr_npu_msgs::ScrubberGetMotorStatus::Request &req,
                                             wr_npu_msgs::ScrubberGetMotorStatus::Response &res)
{
    res.status.resize(2);
    for(int i = 0;i < 2;i++){
        res.status[i].id = "wheel_motor";
        res.status[i].index = i;
        res.status[i].tempture = motor_status_.motor_driver_temp;
        res.status[i].voltage = motor_status_.motor_bus_voltage;
    }
    res.status[0].current = static_cast<float>(motor_status_.left_motor_current)/10;
    res.status[1].current = static_cast<float>(motor_status_.right_motor_current)/10;
    res.status[0].rpm = motor_status_.left_motor_spd;
    res.status[1].rpm = motor_status_.right_motor_spd;
    res.status[0].motor_pose = motor_status_.left_motor_pose;
    res.status[1].motor_pose = motor_status_.right_motor_pose;
    res.status[0].state_signal = motor_status_.left_motor_state_signal;
    res.status[1].state_signal = motor_status_.right_motor_state_signal;
    res.status[0].error_signal = motor_status_.left_motor_error_signal;
    res.status[1].error_signal = motor_status_.right_motor_error_signal;
    res.status[0].power = res.status[0].current
                        * motor_status_.motor_bus_voltage;
    res.status[1].power = res.status[1].current
                        * motor_status_.motor_bus_voltage;
    return true;
}

int SerialFdkBcuDriver::Stop()
{
    WR_DEBUG("SerialFdkBcuDriver::Stop()");
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    MotorSpdArray rpms(bcu_param_ptr->motor.motor_num, 0);
    SetMotorSpd(rpms);
    return TRUE;
}

int SerialFdkBcuDriver::ClrMotorEnc()
{
    WR_DEBUG("SerialFdkBcuDriver::Stop()");
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }

    std::string clear_enc(serial_fdk::COLON + serial_fdk::CLR_MTR_ENC + serial_fdk::CMD_END);
    if(!WriteCmd(clear_enc.c_str(), clear_enc.size())){
        return FALSE;
    }
    return TRUE;
}

int SerialFdkBcuDriver::GetMotorSpd(MotorSpdArray& rpm)
{
    if(!is_inited_){
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    if(bcu_param_ptr->motor.motor_num != 2){
        WR_ERROR("motor num is %d", bcu_param_ptr->motor.motor_num);
        return FALSE;
    }

    int length = 0;
    long int_frame = 0;
    unsigned char frame[64];
    std::string motor_status_ans;
    std::string motor_status_cmd(serial_fdk::COLON + serial_fdk::GET_MTR_STATUS + serial_fdk::CMD_END);

    serial_port_.ClearReadBuffer();
    if(!WriteCmd(motor_status_cmd.c_str(), motor_status_cmd.size())){
        return FALSE;
    }
    if(ReceiveAns(motor_status_ans)){
        string data_buf = motor_status_ans.substr(1, length-1);
        length = data_buf.size()-2;
        for(int i = 0; i < length/2;i++){
            int_frame = strtoul(data_buf.substr(i * 2,2).c_str(),0,16);
            frame[i] = *(unsigned char*)&int_frame;
        }
#if 0
        printf("buffer is :");
        for(int i = 0; i < length/2;i++){
            printf("%02X ", frame[i]);
        }
        printf("\n");
#endif
        RtuMotorStatus *motor_status = (RtuMotorStatus*)(&frame[3]);
#if 1
        motor_status->EndianExchange();
#endif
        motor_status_ = *motor_status;
        motor_status_.left_motor_spd = motor_status_.left_motor_spd * (bcu_param_ptr->motor.motor_dir_str[0] == '+' ? 1 : (-1));
        motor_status_.right_motor_spd = motor_status_.right_motor_spd * (bcu_param_ptr->motor.motor_dir_str[1] == '+' ? 1 : (-1));
        rpm.resize(2);
        rpm[0] = motor_status_.left_motor_spd;
        rpm[1] = motor_status_.right_motor_spd;
        return TRUE;
    } else{
        WR_ERROR("failed to get wheel motor status at ReceiveAns()");
        return FALSE;
    }
}

int SerialFdkBcuDriver::SetMotorSpd(const MotorSpdArray& spd)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }

    short motor_spd[2];
    for(int i = 0; i < 2; ++i)
    {
//        motor_spd[i] = (short)(spd[i] / bcu_param_ptr->motor.motor_max_spd_rpm * serial_fdk::MAX_MOTOR_SPD);
        motor_spd[i] = (short)spd[i];
        motor_spd[i] *= (bcu_param_ptr->motor.motor_dir_str[i] == '+' ? 1 : (-1));

        if(motor_spd[i] > serial_fdk::MAX_MOTOR_SPD)
        {
            motor_spd[i] = serial_fdk::MAX_MOTOR_SPD;
        }
        else if(motor_spd[i] < serial_fdk::MIN_MOTOR_SPD)
        {
            motor_spd[i] = serial_fdk::MIN_MOTOR_SPD;
        }

    }

    std::string cmd_data;
    std::string ans;

    int index = (bcu_param_ptr->motor.end_left_right_switch==false)?0:1;
    const int *id_map = serial_fdk::MOTOR_ID_MAP[index];

    cmd_data.append(serial_fdk::COLON);
    cmd_data.append(serial_fdk::SET_MTR_SPD);
    char spd_l[4];
    ConvertShortToStr(spd_l,motor_spd[id_map[0]]);
    cmd_data.append(spd_l);

    char spd_r[4];
    ConvertShortToStr(spd_r,motor_spd[id_map[1]]);
    cmd_data.append(spd_r);
    unsigned char dst[11];

    ConvertStrToUnChar(cmd_data.substr(1), dst,11);
    char check_sum[2];
    GetCheckSum(check_sum,dst,11);
    cmd_data.append(check_sum);
    transform(cmd_data.begin(), cmd_data.end(), cmd_data.begin(), ::toupper);
    cmd_data.append(serial_fdk::CMD_END);

    if(!WriteCmd(cmd_data.c_str(), cmd_data.size()))
    {
        return FALSE;
    }

    if(ReceiveAns(ans))
    {
        return TRUE;
    }
    else
    {
        WR_ERROR("fdk driver: set speed ans error.");
        PrintAns(ans);
        return FALSE;
    }
}

int SerialFdkBcuDriver::GetMotorEnc(MotorEncArray& ticks)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    std::string cmd_data(serial_fdk::COLON+serial_fdk::GET_MTR_ENC);
    std::string ans;

    ticks.resize(bcu_param_ptr->motor.motor_num);
    serial_port_.ClearReadBuffer();
    unsigned char dst[6];
    ConvertStrToUnChar(cmd_data.substr(1), dst,6);
    char check_sum[2];
    GetCheckSum(check_sum,dst,6);
    cmd_data.append(check_sum);
    transform(cmd_data.begin(), cmd_data.end(), cmd_data.begin(), ::toupper);
    cmd_data.append(serial_fdk::CMD_END);

    if(!WriteCmd(cmd_data.c_str(), cmd_data.size()))
    {
        return FALSE;
    }

    if(!ReceiveAns(ans) || !ParseEncData(ans, ticks))
    {
        return FALSE;
    }
    for(int i = 0; i < bcu_param_ptr->motor.motor_num; ++i)
    {
        ticks[i] *= ((bcu_param_ptr->motor.motor_dir_str[i]=='+')?1:(-1));
    }
    return TRUE;
}

int SerialFdkBcuDriver::WriteCmd(const char* cmd, uint len)
{
    serial_port_.ClearReadBuffer();
    //ROS_INFO(cmd);
    //std::cout << cmd << std::endl;

    if (serial_port_.Write(cmd, len) < len)
    {
        WR_ERROR("Writing command failed. Please check serial port.");
        while(!serial_port_.Open(bcu_param_ptr->com.port_id, serial_fdk::BAUDRATE,serial_fdk::DATABITS,serial_fdk::PARITY))
        {
            WR_DEBUG("retrying...");
            WR_SLEEP_MS(bcu_param_ptr->com.cmd_delay_ms);
        }
        WR_INFO("fdk driver reconnected");
        return FALSE;
    }

    return TRUE;
}

int SerialFdkBcuDriver::ReceiveAns(std::string& ans)
{
    uint retry_cnt = 0;

    while(retry_cnt < bcu_param_ptr->com.ans_retry_num)
    {
        WR_SLEEP_MS(bcu_param_ptr->com.ans_delay_ms);
        if(serial_port_.BytesWaiting() > 0)
        {
            uint len = serial_port_.BytesWaiting();
            char *buf = new char[len];

            if(serial_port_.Read(buf, len) < len)
            {
                WR_ERROR("serial port read failed.");
            }
            else
            {
                ans.append(buf, len);
                if(CheckAns(ans))
                {
                    break;
                }
            }
        }
        retry_cnt++;
    }

    //WR_INFO("retry_cnt:%d,retry_num:%d",retry_cnt,bcu_param_ptr->com.ans_retry_num);


    if (retry_cnt < bcu_param_ptr->com.ans_retry_num)
    {
        serial_port_.ClearReadBuffer();
        //WR_INFO("GET ANSWER DONE!");
        return TRUE;
    }
    else
    {
        WR_ERROR("ReceiveAns() failed: TIMEOUT");
        return FALSE;
    }
}

int SerialFdkBcuDriver::ParseMotorSpd(const std::string& ans, MotorSpdArray& rpm)
{

    //WR_INFO(ans.c_str());
    int index = (bcu_param_ptr->motor.end_left_right_switch==false)?0:1;
    const int *id_map = serial_fdk::MOTOR_ID_MAP[index];
    if(ans[0] == ':' && ans[4] == '3' && ans[6] == '4' )
    {
        std::string left  = ans.substr(7,4);
        std::string right = ans.substr(11,4);
        short left_spd  = 0;
        short right_spd = 0;
        unsigned char left_data[2];
        //WR_INFO(left.c_str());
        //WR_INFO(right.c_str());

        ConvertStrToUnChar(left,left_data,2);
        left_spd = left_spd | left_data[0];
        left_spd = left_spd << 8;
        left_spd = left_spd | left_data[1];

        rpm[id_map[0]] = left_spd*1.0;

        unsigned char right_data[2];

        ConvertStrToUnChar(right,right_data,2);

        right_spd = right_spd | right_data[0];
        right_spd = right_spd << 8;
        right_spd = right_spd | right_data[1];
        rpm[id_map[1]] = right_spd*1.0;
        //WR_INFO("left spd:%d, right spd:%d",(int)left_spd,(int)right_spd);
        return TRUE;
    }
    else
    {
        WR_ERROR("fdk driver: wrong spd data 03.");
        PrintAns(ans);
        return FALSE;
    }
    return TRUE;
}

int SerialFdkBcuDriver::ParseEncData(const std::string& ans, MotorEncArray& enc)
{
    int index = (bcu_param_ptr->motor.end_left_right_switch==false)?0:1;
    const int *id_map = serial_fdk::MOTOR_ID_MAP[index];

    //WR_INFO(ans.c_str());

    if(ans[0] == ':' && ans[4] == '3' && ans[6] == '8' )
    {
        std::string left  = ans.substr(7,8);
        std::string right = ans.substr(15,8);
        int left_enc  =0;
        int right_enc =0;
        unsigned char left_data[4];
        ConvertStrToUnChar(left,left_data,4);
        int temp = 0;
        temp = temp | left_data[2];
        temp = temp << 24;
        left_enc = left_enc | temp;
        temp = 0;
        temp = temp | left_data[3];
        temp |= temp << 16;
        left_enc = left_enc | temp;
        temp = 0;
        temp = temp | left_data[0];
        temp = temp << 8;
        left_enc = left_enc | temp;
        temp = 0;
        temp = temp | left_data[1];
        left_enc = left_enc | temp;
        enc[id_map[0]] = left_enc;

        unsigned char right_data[4];

        ConvertStrToUnChar(right,right_data,4);
        temp = 0;
        temp = temp | right_data[2];
        temp = temp << 24;
        right_enc = right_enc | temp;
        temp = 0;
        temp = temp | right_data[3];
        temp |= temp << 16;
        right_enc = right_enc | temp;
        temp = 0;
        temp = temp | right_data[0];
        temp = temp << 8;
        right_enc = right_enc | temp;
        temp = 0;
        temp = temp | right_data[1];
        right_enc = right_enc | temp;
        enc[id_map[1]] = right_enc;

        //WR_INFO("left enc:%d, right enc:%d",left_enc,right_enc);

        return TRUE;
    }
    else
    {
        WR_ERROR("fdk driver: wrong enc data.");
        PrintAns(ans);
        return FALSE;
    }
    return TRUE;
}

int SerialFdkBcuDriver::CheckAns(const std::string& ans)
{
    if(ans.size() < serial_fdk::MIN_ANS_LEN)
    {
        return FALSE;
    }
    int cnt = 0;
    for(const char& c: ans)
    {
        if(c == '\r')
            cnt++;
    }
    if(ans.back() == '\n' && cnt == 1)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

void SerialFdkBcuDriver::PrintAns(const std::string& ans)
{
    std::string temp = ans;
    for(char &c : temp)
    {
        if(c == '\r')
        {
            c = '.';
        }
    }
    WR_DEBUG("len = %d, ans = %s", static_cast<int>(temp.size()), temp.c_str());
}

bool SerialFdkBcuDriver::EnbDriver(bool flag)
{
    if (flag)
    {
       std::string cmd(MTR_SOFT_START);
       serial_port_.ClearReadBuffer();

       if(!WriteCmd(cmd.c_str(), cmd.size()))
       {
           return FALSE;
       }
    }

    else
    {
        std::string cmd(MTR_SOFT_STOP);
        serial_port_.ClearReadBuffer();

        if(!WriteCmd(cmd.c_str(), cmd.size()))
        {
            return FALSE;
        }
    }

    return TRUE;
}

void SerialFdkBcuDriver::ConvertUncharToStr(char *str, unsigned char *Unchar, int ucLen)
{
    for (int var = 0; var < ucLen; ++var)
    {
        sprintf(str+var*2,"%02x",Unchar[var]);
    }
}

void SerialFdkBcuDriver::ConvertStrToUnChar(const std::string&  str, unsigned char* UnChar, int len)
{
    //std::string cmd_ = str.substr(1);  //Delete ":" from string
    // WR_INFO(str.c_str());
    char temp[len*2];
    for (int var = 0; var < len*2; ++var)
    {
        temp[var] = str[var];
    }
    int i = strlen(temp), j = 0, counter = 0;
    char c[2];
    unsigned int bytes[2];

    for (j = 0; j < i; j += 2)
    {
        if(0 == j % 2)
        {
            c[0] = temp[j];
            c[1] = temp[j + 1];
            sscanf(c, "%02x" , &bytes[0]);
            UnChar[counter] = bytes[0];
            counter++;
        }
    }
    return;
}


void SerialFdkBcuDriver::ConvertShortToStr(char* str,short data)
{
    unsigned char left_spd[2];
    memcpy(&left_spd,&data,2);
    unsigned char left_spd1[2]={left_spd[1],left_spd[0]};
    ConvertUncharToStr(str,left_spd1,2);
}

void SerialFdkBcuDriver::GetCheckSum(char* str, unsigned char* UnChar, int len)
{
    vector<unsigned char> cmd;
    for (int var = 0; var < len; ++var)
    {
        cmd.push_back(UnChar[var]);
    }

    unsigned char sum;
    for (int var = 0; var < len; ++var)
    {
        sum += cmd[var];
    }

    sum = ~sum;
    sum = sum+1;
    unsigned char sum_[1] ={sum};
    sprintf(str,"%02x",sum_[0]);
}

}}  // namespace
