#include "serial_fake/serial_fake_bcu_driver.h"

namespace wizrobo { namespace bcu_driver {

int SerialFakeBcuDriver::Init(BcuParamPtr ptr)
{
    WR_DEBUG("SerialHexNboxDriver::Init()");
    return TRUE;
}

int SerialFakeBcuDriver::GetMotorSpd(MotorSpdArray& rpms)
{
    WR_DEBUG("SerialHexNboxDriver::GetMotorSpd()");
    rpms.resize(2);
    rpms[0] = 0.0;
    rpms[1] = 0.0;
    return TRUE;
}

int SerialFakeBcuDriver::GetMotorEnc(MotorEncArray& ticks)
{
    WR_DEBUG("SerialHexNboxDriver::GetMotorEnc()");
    ticks.resize(2);
    ticks[0] = 0;
    ticks[1] = 0;
    return TRUE;
}

}}// namespace
