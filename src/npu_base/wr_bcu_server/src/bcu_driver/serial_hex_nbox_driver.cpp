#include "serial_nbox/serial_hex_nbox_driver.h"

#include <stdio.h>

namespace wizrobo { namespace bcu_driver {

using namespace serial_nbox;

int SerialHexNboxDriver::Init(BcuParamPtr ptr)
{
    WR_DEBUG("SerialHexNboxDriver::Init()");
    bcu_param_ptr = ptr;
    is_inited_ = TRUE;
    int retry_cnt = bcu_param_ptr->com.cmd_retry_num;
    while (retry_cnt > 0)
    {
        retry_cnt--;
        if (InitOnce() == TRUE)
        {            
            SyncParam();
            return TRUE;
        }
        else
        {
            WR_DEBUG("Try again...(%d)",retry_cnt);
        }
        WR_SLEEP_MS(bcu_param_ptr->com.cmd_delay_ms);
    }
    WR_ERROR("Init() failed: TIMEOUT");

    is_inited_ = FALSE;
    return FALSE;
}

int SerialHexNboxDriver::GetMotorParam()
{
    WR_DEBUG("SerialHexNboxDriver::GetMotorParam()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    MotorParam& motor_param = bcu_param_ptr->motor;
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetMotorParam() failed at CheckPort()");
        return FALSE;
    }
    bcup::ControlParam control_param;
    SendRequest(bcup::GET_CTL_PARA, NULL, 0);
    if (RecvResponse(bcup::GET_CTL_PARA, (unsigned char*)(&control_param), sizeof(bcup::ControlParam)) == FALSE)
    {
        WR_ERROR("GetMotorParam() failed at RecvResponse()");
        return FALSE;
    }
    motor_param.motor_num = control_param.motor_num;
    std::string motor_dir_str(control_param.motor_num, ' ');
    std::string enc_dir_str(control_param.motor_num, ' ');
    for (int i = 0; i < control_param.motor_num; i++)
    {
        motor_dir_str[i] = (control_param.motor_dir[i])==bcup::SYNTROPY?'+':'-';
        enc_dir_str[i] = (control_param.enc_dir[i])==bcup::SYNTROPY?'+':'-';
    }
    motor_param.motor_dir_str = motor_dir_str;
    motor_param.enc_dir_str = enc_dir_str;

    motor_param.motor_brk_type = (control_param.motor_brk_type==bcup::SOFT_BRK)?wizrobo_npu::SOFT_BRK:wizrobo_npu::HARD_BRK;
    motor_param.enb_auxdir_mode = (control_param.enb_auxdir==bcup::ENABLE)?true:false;
    motor_param.enb_slow_launch = (control_param.enb_slow_launch==bcup::ENABLE)?true:false;

    motor_param.enb_vol = (control_param.enb_vol==bcup::LOW_VALID)?wizrobo_npu::LOW_VALID:wizrobo_npu::HIGH_VALID;
    motor_param.dir_vol = (control_param.dir_vol==bcup::LOW_VALID)?wizrobo_npu::LOW_VALID:wizrobo_npu::HIGH_VALID;
    motor_param.pwm_vol = (control_param.pwm_vol==bcup::LOW_VALID)?wizrobo_npu::LOW_VALID:wizrobo_npu::HIGH_VALID;
    motor_param.brk_vol = (control_param.brk_vol==bcup::LOW_VALID)?wizrobo_npu::LOW_VALID:wizrobo_npu::HIGH_VALID;

    motor_param.motor_enc_res_ppr = control_param.motor_enc_res_ppr;
    motor_param.motor_pwm_frq_hz = control_param.motor_pwm_frq_hz;

    motor_param.dac_offset_v_left = dac_left_[0].ToRealValue(control_param.dac_offset_v_left);
    motor_param.dac_offset_v_right = dac_right_[0].ToRealValue(control_param.dac_offset_v_right);

    motor_param.motor_max_spd_rpm = control_param.motor_max_rpm;
    motor_param.motor_start_rpm = control_param.motor_start_rpm;
    motor_param.motor_acc_rpm = control_param.motor_acc_rpm;
    motor_param.motor_rdc_ratio = control_param.motor_rdc_ratio;

    return TRUE;
}

int SerialHexNboxDriver::SetMotorParam()
{
    WR_DEBUG("SerialHexNboxDriver::SetMotorParam()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    MotorParam& motor_param = bcu_param_ptr->motor;
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetMotorParam() failed at CheckPort()");
        return FALSE;
    }
    if (motor_param.motor_num > bcup::MAX_MOTOR_NUM)
    {
        WR_WARN("Only surpport %d motors for now. Reset motor_param.motor_num",
            bcup::MAX_MOTOR_NUM);
        motor_param.motor_num = bcup::MAX_MOTOR_NUM;
    }

    bcup::ControlParam control_param;
    control_param.motor_num = motor_param.motor_num;
    for (int i = 0; i < motor_param.motor_num; i++)
    {
        control_param.motor_dir[i] = (motor_param.motor_dir_str[i])=='+'?bcup::SYNTROPY:bcup::REVERSE;
        control_param.enc_dir[i] = (motor_param.enc_dir_str[i])=='+'?bcup::SYNTROPY:bcup::REVERSE;
    }

    control_param.motor_brk_type = (motor_param.motor_brk_type==wizrobo_npu::SOFT_BRK)?bcup::SOFT_BRK:bcup::HARD_BRK;
    control_param.enb_auxdir = (motor_param.enb_auxdir_mode==true)?bcup::ENABLE:bcup::DISABLE;
    control_param.enb_slow_launch = (motor_param.enb_slow_launch==true)?bcup::ENABLE:bcup::DISABLE;

    control_param.enb_vol = (motor_param.enb_vol==wizrobo_npu::LOW_VALID)?bcup::LOW_VALID:bcup::HIGH_VALID;
    control_param.dir_vol = (motor_param.dir_vol==wizrobo_npu::LOW_VALID)?bcup::LOW_VALID:bcup::HIGH_VALID;
    control_param.pwm_vol = (motor_param.pwm_vol==wizrobo_npu::LOW_VALID)?bcup::LOW_VALID:bcup::HIGH_VALID;
    control_param.brk_vol = (motor_param.brk_vol==wizrobo_npu::LOW_VALID)?bcup::LOW_VALID:bcup::HIGH_VALID;

    control_param.motor_enc_res_ppr = motor_param.motor_enc_res_ppr;
    control_param.motor_pwm_frq_hz = motor_param.motor_pwm_frq_hz;

    control_param.dac_offset_v_left = dac_left_[0].ToDigitalValue(motor_param.dac_offset_v_left);
    control_param.dac_offset_v_right = dac_right_[0].ToDigitalValue(motor_param.dac_offset_v_right);

    control_param.motor_max_rpm = motor_param.motor_max_spd_rpm;
    control_param.motor_start_rpm = motor_param.motor_start_rpm;
    control_param.motor_acc_rpm = motor_param.motor_acc_rpm;
    control_param.motor_rdc_ratio = motor_param.motor_rdc_ratio;

    SendRequest(bcup::SET_CTL_PARA, (unsigned char *)&control_param, sizeof(bcup::ControlParam));
    if (RecvResponse(bcup::SET_CTL_PARA, NULL, 0) == FALSE)
    {
        WR_ERROR("SetMotorParam() failed at RecvResponse()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::GetSensorParam()
{
    WR_DEBUG("SerialHexNboxDriver::GetSensorParam()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    SensorParam& sensor_param = bcu_param_ptr->sensor;
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetSensorParam() failed at CheckPort()");
        return FALSE;
    }
    bcup::SensorParam bcup_sensor_param;
    SendRequest(bcup::GET_SNR_PARA, NULL, 0);
    if (RecvResponse(bcup::GET_SNR_PARA, (unsigned char*)(&bcup_sensor_param), sizeof(bcup::SensorParam)) == FALSE)
    {
        WR_ERROR("GetSensorParam() failed at RecvResponse()");
        return FALSE;
    }

    sensor_param.bmp_vol = (bcup_sensor_param.bmp_vol==bcup::LOW_VALID)?wizrobo_npu::LOW_VALID:wizrobo_npu::HIGH_VALID;
    sensor_param.esb_vol = (bcup_sensor_param.esb_vol==bcup::LOW_VALID)?wizrobo_npu::LOW_VALID:wizrobo_npu::HIGH_VALID;

    for (int i=0;i<bcup::MAX_INFRD_NUM;i++)
    {
        sensor_param.infrd[i].threshold = ad_cvt_ptr_->ToAnalogValue(bcup_sensor_param.infrd_threshold[i]);
    }
    return TRUE;
}

int SerialHexNboxDriver::SetSensorParam()
{
    WR_DEBUG("SerialHexNboxDriver::SetSensorParam()");
    SensorParam& sensor_param = bcu_param_ptr->sensor;
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetSensorParam() failed at CheckPort()");
        return FALSE;
    }

    bcup::SensorParam bcup_sensor_param;

    bcup_sensor_param.bmp_vol = (sensor_param.bmp_vol==wizrobo_npu::LOW_VALID)?bcup::LOW_VALID:bcup::HIGH_VALID;
    bcup_sensor_param.esb_vol = (sensor_param.esb_vol==wizrobo_npu::LOW_VALID)?bcup::LOW_VALID:bcup::HIGH_VALID;

    for (int i=0;i<sensor_param.infrd.size();i++)
    {
        bcup_sensor_param.infrd_threshold[i] = ad_cvt_ptr_->ToDigitalValue(sensor_param.infrd[i].threshold);
    }
    SendRequest(bcup::SET_SNR_PARA, (unsigned char *)&bcup_sensor_param, sizeof(bcup::SensorParam));
    if (RecvResponse(bcup::SET_SNR_PARA, NULL, 0) == FALSE)
    {
        WR_ERROR("SetSensorParam() failed at RecvResponse()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::GetPidParam()
{
    PidParam& pid_param = bcu_param_ptr->pid;
    WR_DEBUG("SerialHexNboxDriver::GetPidParam()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetPidParam() failed at CheckPort()");
        return FALSE;
    }
    bcup::PidParam bcup_pid_param;
    SendRequest(bcup::GET_PID_PARA, NULL, 0);
    if (RecvResponse(bcup::GET_PID_PARA, (unsigned char*)(&bcup_pid_param), sizeof(bcup::PidParam)) == FALSE)
    {
        WR_ERROR("GetPidParam() failed at RecvResponse()");
        return FALSE;
    }
    pid_param.enb_pid = bcup_pid_param.enb_pid==bcup::ENABLE?true:false;
    pid_param.kp_acc = bcup_pid_param.kp_acc;
    pid_param.kp_dec = bcup_pid_param.kp_dec;
    pid_param.ki_acc = bcup_pid_param.ki_acc;
    pid_param.ki_dec = bcup_pid_param.ki_dec;
    pid_param.kd_acc = bcup_pid_param.kd_acc;
    pid_param.kd_dec = bcup_pid_param.kd_dec;
    return TRUE;
}

int SerialHexNboxDriver::SetPidParam()
{
    PidParam& pid_param = bcu_param_ptr->pid;
    WR_DEBUG("SerialHexNboxDriver::SetPidParam()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetPidParam() failed at CheckPort()");
        return FALSE;
    }
    bcup::PidParam bcup_pid_param;
    bcup_pid_param.enb_pid = pid_param.enb_pid==true?bcup::ENABLE:bcup::DISABLE;
    bcup_pid_param.kp_acc = pid_param.kp_acc;
    bcup_pid_param.kp_dec = pid_param.kp_dec;
    bcup_pid_param.ki_acc = pid_param.ki_acc;
    bcup_pid_param.ki_dec = pid_param.ki_dec;
    bcup_pid_param.kd_acc = pid_param.kd_acc;
    bcup_pid_param.kd_dec = pid_param.kd_dec;
    SendRequest(bcup::SET_PID_PARA, (unsigned char*)(&bcup_pid_param), sizeof(bcup::PidParam));
    if (RecvResponse(bcup::SET_PID_PARA, NULL, 0) == FALSE)
    {
        WR_ERROR("SetPidParam() failed at RecvResponse()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::Enable(bool on)
{
    WR_DEBUG("SerialHexNboxDriver::Enable()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }

    if (IS_ALWAYS_CHECK_PORT && CheckPort()==FALSE)
    {
        WR_ERROR("Enable() failed at CheckPort()");
        return FALSE;
    }
    unsigned char enable = (on==true)?bcup::ENABLE:bcup::DISABLE;
    SendRequest(bcup::SET_MTR_ENB, &enable, 1);
    if (RecvResponse(bcup::SET_MTR_ENB, NULL, 0) == FALSE)
    {
        WR_ERROR("Enable() failed at RecvResponse()");
        return FALSE;
    }
    WR_DEBUG("SerialHexNboxDriver::Enable() Return");
    return TRUE;
}

int SerialHexNboxDriver::Brake(bool on)
{
    WR_DEBUG("SerialHexNboxDriver::Brake()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("Brake() failed at CheckPort()");
        return FALSE;
    }
    unsigned char enable = (on==true)?bcup::ENABLE:bcup::DISABLE;
    SendRequest(bcup::SET_MTR_BRK, &enable, 1);
    if (RecvResponse(bcup::SET_MTR_BRK, NULL, 0) == FALSE)
    {
        WR_ERROR("Brake() failed at RecvResponse()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::Stop()
{
    WR_DEBUG("SerialHexNboxDriver::Stop()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    MotorSpdArray rpms(bcu_param_ptr->motor.motor_num, 0);
    SetMotorSpd(rpms);
    //Brake(true);
}

int SerialHexNboxDriver::GetMotorSpd(MotorSpdArray& rpms)
{
    WR_DEBUG("SerialHexNboxDriver::GetMotorSpd()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetMotorSpd() failed at CheckPort()");
        return FALSE;
    }
    bcup::MotorSpd spd;
    SendRequest(bcup::GET_MTR_SPD, NULL, 0);
    if (RecvResponse(bcup::GET_MTR_SPD, (unsigned char *)&spd, sizeof(bcup::MotorSpd)) == FALSE)
    {
        WR_ERROR("GetMotorSpd() failed at RecvResponse()");
        return FALSE;
    }
    rpms.resize(spd.motor_num);
    for (int i=0;i<spd.motor_num;i++)
    {
        rpms[i] = spd.rpm[i];
    }
    return TRUE;
}

int SerialHexNboxDriver::SetMotorSpd(const MotorSpdArray& rpms)
{
    WR_DEBUG("SerialHexNboxDriver::SetMotorSpd()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetMotorSpd() failed at CheckPort()");
        return FALSE;
    }
    bcup::MotorSpd spd;
    spd.motor_num = rpms.size();
    for (int i=0;i<spd.motor_num;i++)
    {
        spd.rpm[i] = rpms[i];
    }
    SendRequest(bcup::SET_MTR_SPD, (unsigned char *)&spd, sizeof(bcup::MotorSpd));
    if (RecvResponse(bcup::SET_MTR_SPD, NULL, 0) == FALSE)
    {
        WR_ERROR("SetMotorSpd() failed at RecvResponse()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::GetMotorEnc(MotorEncArray& ticks)
{
    WR_DEBUG("SerialHexNboxDriver::GetMotorEnc()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetMotorEnc() failed at CheckPort()");
        return FALSE;
    }

    bcup::MotorEnc enc;
    SendRequest(bcup::GET_MTR_ENC, NULL, 0);
    if (RecvResponse(bcup::GET_MTR_ENC, (unsigned char *)&enc, sizeof(bcup::MotorEnc)) == FALSE)
    {
        WR_ERROR("GetMotorEnc() failed at RecvResponse()");
        return FALSE;
    }
    ticks.resize(enc.motor_num);
    for (int i=0;i<enc.motor_num;i++)
    {
        ticks[i] = enc.enc[i];
    }
    return TRUE;
}

int SerialHexNboxDriver::ClrMotorEnc()
{
    WR_DEBUG("SerialHexNboxDriver::ClrMototnc()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("ClrMotorEnc() failed at CheckPort()");
        return FALSE;
    }
    SendRequest(bcup::CLR_MTR_ENC, NULL, 0);
    if (RecvResponse(bcup::CLR_MTR_ENC, NULL, 0) == FALSE)
    {
        WR_ERROR("ClrMotorEnc() failed at RecvResponse()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::SetUserIo(InputChannel input_channel, OutputMode mode, int loop_cnt)
{
    WR_DEBUG("SerialHexNboxDriver::SetUserIo()");
    if (!is_inited_)
    {
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetUserIo() failed at CheckPort()");
        return FALSE;
    }
    bcup::OutputStatus status;
    status.channel = input_channel;
    status.mode = mode;
    status.repeat = loop_cnt;
    SendRequest(bcup::SET_USER_IO, (unsigned char *)(&status), sizeof(bcup::OutputStatus));
    if (RecvResponse(bcup::SET_USER_IO, NULL, 0) == FALSE)
    {
        WR_ERROR("SetUserIo() failed at RecvResponse()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::GetImuData(ImuData& imu)
{

    WR_DEBUG("SerialHexNboxDriver::GetImuData()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
#if 0
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetImuData() failed at CheckPort()");
        return FALSE;
    }

    bcup::ImuData data;
    SendRequest((bcup::GET_IMU_DATA, NULL, 0);
    if (RecvResponse(bcup::GET_IMU_DATA, (unsigned char *)&data, sizeof(bcup::ImuData)) == FALSE)
    {
        WR_ERROR("GetImuData() failed at RecvResponse()");
        return FALSE;
    }

    imu.acc.x=data.ACCX*0.000598;  //Convert to m/s2   Jeremy 2017.12.1
    imu.acc.y=data.ACCY*0.000598;
    imu.acc.z=data.ACCZ*0.000598;
    imu.gyro.x=data.GyrX*0.001064;
    imu.gyro.y=data.GyrY*0.001064;
    imu.gyro.z=data.GyrZ*0.001064; //Convert to rad/s  Jeremy 2017.12.1
    imu.pose.roll=data.roll;
    imu.pose.pitch=data.pitch;
    imu.pose.yaw=data.yaw;
    //ROS_INFO("GetImuData.GyrZ is: %d  yaw is: %f ", data.GyrZ,data.yaw);
#endif

    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetImuData() failed at CheckPort()");
        return FALSE;
    }

    bcup::ImuData data;
    SendRequest(bcup::GET_IMU_DATA, NULL, 0);
    if (RecvResponse(bcup::GET_IMU_DATA, (unsigned char *)&data, sizeof(bcup::ImuData)) == FALSE)
    {
        WR_ERROR("GetImuData() failed at RecvResponse()");
        return FALSE;
    }

    imu.acc.x=data.acc.x;
    imu.acc.y=data.acc.y;
    imu.acc.z=data.acc.z;
    imu.gyro.x=data.gyr.x;
    imu.gyro.y=data.gyr.y;
    imu.gyro.z=data.gyr.z;
    imu.mgg.x=data.mag.x;
    imu.mgg.y=data.mag.y;
    imu.mgg.z=data.mag.z;
    imu.pose.roll=data.rpy.roll;
    imu.pose.pitch=data.rpy.pitch;
    imu.pose.yaw=data.rpy.yaw;
    return TRUE;
}

int SerialHexNboxDriver::SetBeepStatus(wizrobo::bcu_driver::OutputMode mode, int loop_cnt)
{
    WR_DEBUG("SerialHexNboxDriver::SetBeepStatus()");
    if (!is_inited_)
    {
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetBeepStatus() failed at CheckPort()");
        return FALSE;
    }
    bcup::BeepStatus status;
    status.mode = mode;
    status.repeat = loop_cnt;
    SendRequest(bcup::SET_BEEP_STATUS, (unsigned char *)(&status), sizeof(bcup::BeepStatus));
    if (RecvResponse(bcup::SET_BEEP_STATUS, NULL, 0) == FALSE)
    {
        WR_ERROR("SetBeepStatus() failed at RecvResponse()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::SetLampStatus(wizrobo::bcu_driver::LampChannel lamp_channel, wizrobo::bcu_driver::OutputMode mode, int loop_cnt)
{
    WR_DEBUG("SerialHexNboxDriver::SetLampStatus()");
    if (!is_inited_)
    {
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetLedStatus() failed at CheckPort()");
        return FALSE;
    }
    bcup::OutputStatus status;
    status.channel = lamp_channel;
    status.mode = mode;
    status.repeat = loop_cnt;
    SendRequest(bcup::SET_LAMP_STATUS, (unsigned char *)(&status), sizeof(bcup::OutputStatus));
    if (RecvResponse(bcup::SET_LAMP_STATUS, NULL, 0) == FALSE)
    {
        WR_ERROR("SetLampStatus() failed at RecvResponse()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::GetSensorData(SensorData& sensor_data)
{
    WR_DEBUG("SerialHexNboxDriver::GetSensorData()");
    if (!is_inited_)
    {
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetSonarData() failed at CheckPort()");
        return FALSE;
    }
    bcup::SensorData bcu_sensor_data;
    SendRequest(bcup::GET_SNR_DATA, NULL, 0);
    if (RecvResponse(bcup::GET_SNR_DATA, (unsigned char *)(&bcu_sensor_data), sizeof(bcup::SensorData)) == FALSE)
    {
        WR_ERROR("SetLedStatus() failed at RecvResponse()");
        return FALSE;
    }
    if (sensor_data.sonar.size() != bcup::MAX_SONAR_NUM){
        sensor_data.sonar.resize(bcup::MAX_SONAR_NUM);
    }
    for (int i=0;i<sensor_data.sonar.size();i++)
    {
        sensor_data.sonar[i] = 0.001 * bcu_sensor_data.sonar[i];
    }

    if (sensor_data.infrd.size() != bcup::MAX_INFRD_NUM){
        sensor_data.infrd.resize(bcup::MAX_INFRD_NUM);
    }
    for (int i=0;i<sensor_data.infrd.size();i++)
    {
        sensor_data.infrd[i] = infrd_[i].ToRealValue(bcu_sensor_data.infrd[i]);
    }
    sensor_data.battery_vol = battery_[0].ToRealValue(bcu_sensor_data.battery_vol);
    sensor_data.power_vol = main_power_[0].ToRealValue(bcu_sensor_data.power_vol);

    sensor_data.bumper = bcu_sensor_data.bumper;
    sensor_data.esb = bcu_sensor_data.esb;
    sensor_data.io_input = bcu_sensor_data.io_input;
    return TRUE;
}
/******************************************************************************/
int SerialHexNboxDriver::GetVersionId(int& id)
{
    WR_DEBUG("SerialHexNboxDriver::GetVersionId()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetVersionId() failed at CheckPort()");
        return FALSE;
    }
    return TRUE;
}

int SerialHexNboxDriver::SyncParam()
{
    WR_DEBUG("SerialHexNboxDriver::SyncParam()");
    ros::NodeHandle prv_nh("~");
    prv_nh.param<int>("adc_bit_width", adc_bit_width_, adc_bit_width_);
    prv_nh.param<float>("adc_ref_v",   adc_ref_v_, adc_ref_v_);
    prv_nh.param<int>("dac_bit_width", dac_bit_width_, dac_bit_width_);
    prv_nh.param<float>("dac_ref_v",   dac_ref_v_, dac_ref_v_);
    WR_DEBUG("adc_bit_width_: %d", adc_bit_width_);
    WR_DEBUG("    adc_ref_v_: %.2f", adc_ref_v_);
    WR_DEBUG("dac_bit_width_: %d", dac_bit_width_);
    WR_DEBUG("    dac_ref_v_: %.2f", dac_ref_v_);
    ad_cvt_ptr_ = new serial_nbox::AnalogConverter(adc_bit_width_, adc_ref_v_);
    da_cvt_ptr_ = new serial_nbox::AnalogConverter(dac_bit_width_, dac_ref_v_);

    main_power_.resize(0);
    main_power_.push_back(serial_nbox::AnalogDevice(ad_cvt_ptr_, bcu_param_ptr->sensor.main_power[0].k, bcu_param_ptr->sensor.main_power[0].offset));
    battery_.resize(0);
    battery_.push_back(serial_nbox::AnalogDevice(ad_cvt_ptr_, bcu_param_ptr->sensor.battery[0].k, bcu_param_ptr->sensor.battery[0].offset));
    infrd_.resize(0);
    for(int i=0;i<bcu_param_ptr->sensor.infrd.size();i++)
    {
        infrd_.push_back(serial_nbox::AnalogDevice(ad_cvt_ptr_, bcu_param_ptr->sensor.infrd[i].k, bcu_param_ptr->sensor.infrd[i].offset));
    }
    dac_left_.resize(0);
    dac_left_.push_back(serial_nbox::AnalogDevice(da_cvt_ptr_, 1.0, bcu_param_ptr->motor.dac_offset_v_left));
    dac_right_.resize(0);
    dac_right_.push_back(serial_nbox::AnalogDevice(da_cvt_ptr_, 1.0, bcu_param_ptr->motor.dac_offset_v_right));
    return TRUE;
}

int SerialHexNboxDriver::InitOnce()
{

    WR_DEBUG("SerialHexNboxDriver::InitOnce()");
    if (serial_port_.IsOpen() == TRUE)
    {
        Stop();
        Enable(false);
        serial_port_.Close();
    }
    serial_port_.Open( bcu_param_ptr->com.port_id, serial_nbox::BAUDRATE,serial_nbox::DATABITS,serial_nbox::PARITY);
    if (CheckPort() == FALSE)
    {
        WR_ERROR("InitOnce() failed at CheckPort()");
        return FALSE;
    }

    int id;
    if (!GetVersionId(id))
    {
        WR_ERROR("InitOnce() failed at GetVersionId()");
        return FALSE;
    }

    else if (!Enable(true))
    {
        WR_ERROR("InitOnce() failed at Enable()");
        return FALSE;
    }

    return TRUE;
}

int SerialHexNboxDriver::WriteCmd(const byte* cmd, int cmd_len)
{
    ComParam& com_param = bcu_param_ptr->com;
    int retry_cnt = 0;
    int len = 0;
    while(retry_cnt < com_param.cmd_retry_num)
    {
        len += serial_port_.Write((char*)(cmd + len), cmd_len - len);
        if (len == 0)
        {
            WR_INFO("WriteCmd(): Port lost, try to re-open");
            int lost_retry_cnt  = 0;
            while (lost_retry_cnt < 3)
            {
                WR_SLEEP_MS(200);
                serial_port_.Close();
                serial_port_.Open(com_param.port_id, serial_nbox::BAUDRATE,serial_nbox::DATABITS,serial_nbox::PARITY);
                if (serial_port_.IsOpen())
                {
                    WR_INFO("WriteCmd(): Re-open succeeded.");
                    break;
                }
            }
            if (lost_retry_cnt >= 3)
            {
                WR_ERROR("WriteCmd(): Re-open failed");
            }
        }
        else if (len >= cmd_len)
        {
            break;
        }
        WR_SLEEP_MS(com_param.cmd_delay_ms);
        retry_cnt++;
    }

    if (retry_cnt < com_param.cmd_retry_num)
    {
        //serial_port_.ClearReadBuffer();
        return TRUE;
    }
    else
    {
        WR_ERROR("WriteCmd() failed: TIMEOUT (len = %d, cmd_len = %d, [CMD] = %s)"
                 , len, cmd_len, str_ext::ByteArrToStr(cmd, cmd_len).c_str());
    }
    return FALSE;
}

int SerialHexNboxDriver::ReceiveAns()
{
    int retry_cnt = 0;
    int len = 0;
    while(retry_cnt <  bcu_param_ptr->com.ans_retry_num)
    {
        WR_SLEEP_MS(bcu_param_ptr->com.ans_delay_ms);
        len = serial_port_.BytesWaiting();
        while (len > 0)
        {
            unsigned char _bype;
            serial_port_.Read((char *)(&_bype), 1);

            bcu_data_buffer_.Push(_bype);
            int rtn = bcu_data_buffer_.CheckFrame();
            if ( rtn < 0)
            {
                WR_ERROR("ReceiveAns() CheckFrame return: %d", rtn);
                return FALSE;
            }
            else if( rtn == 1 )
            {
                return TRUE;
            }
            else
            {
                len --;
            }
        }
        retry_cnt++;
    }
    WR_ERROR("ReceiveAns() bcu_data_buffer_.status: %d", bcu_data_buffer_.status);
    WR_ERROR("ReceiveAns() bcu_data_buffer_.length: %d", bcu_data_buffer_.length);
    WR_ERROR("ReceiveAns() buffer:");
    for (int i=0;i<bcup::RECV_BUFFER_LEN;i++)
    {
        printf("%02X ", bcu_data_buffer_.buffer[i]);
        //WR_ERROR("ReceiveAns() buffer: 0x%02X", bcu_data_buffer_.buffer[i]);
    }
    printf("\n");
    WR_ERROR("ReceiveAns() failed: TIMEOUT");
    return FALSE;
}

int SerialHexNboxDriver::SendRequest(
    unsigned char cmd, unsigned char *p_data, unsigned char data_length)
{
    serial_port_.ClearReadBuffer();
    const unsigned char frame_flaga = bcup::FRAME_FLAGA;
    const unsigned char frame_flagb = bcup::FRAME_FLAGB;
    unsigned int check_sum_ = 0;
    unsigned char check_sum = 0;
    int req_length = data_length
            + sizeof(frame_flaga) + sizeof(frame_flagb)
            + sizeof(data_length) + sizeof(cmd) + sizeof(check_sum);
    unsigned char *req_buf = new unsigned char [req_length];
    req_buf[0] = bcup::FRAME_FLAGA;
    req_buf[1] = bcup::FRAME_FLAGB;
    req_buf[2] = data_length;
    req_buf[3] = cmd;
    check_sum_ = bcup::FRAME_FLAGA + bcup::FRAME_FLAGB + data_length + cmd;
    for (int i=0;p_data!=NULL&&i<data_length;i++)
    {
        req_buf[4+i] = p_data[i];
        check_sum_ += p_data[i];
    }
    check_sum = check_sum_ & 0x000000FF;
    req_buf[req_length-1] = check_sum;
    WriteCmd(req_buf, req_length);
    delete req_buf;
    return TRUE;
}

int SerialHexNboxDriver::RecvResponse(unsigned char cmd, unsigned char *p_data, unsigned char length)
{
    bcu_data_buffer_.Reset();
    if (ReceiveAns() == FALSE)
    {
        return FALSE;
    }
    bcup::BcuProtocolHead *p_head = (bcup::BcuProtocolHead *)(&(bcu_data_buffer_.buffer[0]));
    if (p_head->cmd != cmd)
    {
        return FALSE;
    }
    for (int i=0;i<length;i++)
    {
        *(p_data+i) = *(&(p_head->first_byte) + i);
    }
    return TRUE;
}
}}// namespace
