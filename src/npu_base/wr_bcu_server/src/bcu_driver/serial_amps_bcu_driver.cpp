#include "serial_amps/serial_amps_bcu_driver.h"
#include <string>
#include <ros/ros.h>
#include <algorithm>
#include <vector>
#include <stdio.h>
#include "math.h"

#define RECEIVE_DEBUG 0
#define WRITE_CMD_DEBUG 0
#define CHECK_SUM_DEBUG 0
#define SET_MTR_SPD_DEBUG 0
#define GET_MTR_SPD_DEBUG 0
#define GET_MTR_ENC_DEBUG 0
#define ENB_SET_PID_DEBUG 0
#define GET_MTR_STATE_DEBUG 0
#define GET_MTR_ERROR_DEBUG 0


#define ENDIAN_EXCHANGE_HALFWORD(x) ((((x)&0xFF00)>>8)+(((x)&0x00FF)<<8))
#define ENDIAN_EXCHANGE_WORD(x) ((((x)&0xFF000000)>>24)+(((x)&0x00FF0000)>>8)+(((x)&0x0000FF00)<<8)+(((x)&0x000000FF)<<24))


namespace wizrobo { namespace bcu_driver {
using namespace serial_amps;
using namespace std;
bool toSetSpd_i = false;
int pid_i_index = 0;
unsigned char setSpdI[7]= {0,0,0,0,0,0,0};

SerialAmpsBcuDriver::SerialAmpsBcuDriver():
     emergencystop_state_(false)
    ,last_emergencystop_state_(false)
{
    ros::NodeHandle nh;
    motor_status_service_ = nh.advertiseService(
                "/scrubber_get_motor_status", &SerialAmpsBcuDriver::MotorStatusCallBack, this);
    working_status_sub_ = nh.subscribe<wr_npu_msgs::ScrubberWorkingStatus>(
                "/scrubber_working_status", 10, &SerialAmpsBcuDriver::EmergencyStopStatusCallBack, this);;
}
SerialAmpsBcuDriver::~SerialAmpsBcuDriver()
{
    if(serial_port_.IsOpen())
        serial_port_.Close();
    Stop();
}

int SerialAmpsBcuDriver::Init(BcuParamPtr ptr)
{
    LM_INFO("");
    bcu_param_ptr = ptr;
    bcu_driver::PidParam& pid_param = bcu_param_ptr->pid;
    serial_amps::AmpsPidParam bcup_pid_param;
    bcup_pid_param.enb_pid = pid_param.enb_pid;
    bcup_pid_param.p = pid_param.kp_acc;
    bcup_pid_param.i = pid_param.ki_acc;
    bcup_pid_param.dec = pid_param.kd_dec;
    bcup_pid_param.acc = pid_param.kd_acc;

    LM_INFO("p is : %d", bcup_pid_param.p);
    LM_INFO("p is : %d", bcup_pid_param.i);
    LM_INFO("p is : %d", bcup_pid_param.dec);
    LM_INFO("p is : %d", bcup_pid_param.acc);
    LM_INFO("p is : %d", bcup_pid_param.enb_pid);

    if(serial_port_.Open(bcu_param_ptr->com.port_id, B38400,8,"NONE"))
    {
        EnbDriver(TRUE);
        if(bcup_pid_param.enb_pid){
            LM_INFO("enb to set pid");
            SetMotorPid(bcup_pid_param);
        } else{
            LM_INFO("disable to set pid");
        }
        is_inited_ = TRUE;
        return TRUE;
    }
    return FALSE;
}

bool SerialAmpsBcuDriver::MotorStatusCallBack(wr_npu_msgs::ScrubberGetMotorStatus::Request &req,
                                             wr_npu_msgs::ScrubberGetMotorStatus::Response &res)
{
    res.status.resize(2);
    for(int i = 0;i < 2;i++){
        res.status[i].id = "wheel_motor";
        res.status[i].index = i;
        res.status[i].tempture = motor_status_.motor_driver_temp;
    }
    res.status[0].current = motor_status_.left_motor_current;
    res.status[1].current = motor_status_.right_motor_current;
    res.status[0].rpm = motor_status_.left_motor_spd;
    res.status[1].rpm = motor_status_.right_motor_spd;
    res.status[0].motor_pose = motor_status_.left_motor_pose;
    res.status[1].motor_pose = motor_status_.right_motor_pose;
    res.status[0].state_signal = motor_status_.left_motor_state_signal;
    res.status[1].state_signal = motor_status_.right_motor_state_signal;
    res.status[0].error_signal = motor_status_.left_motor_error_signal;
    res.status[1].error_signal = motor_status_.right_motor_error_signal;
    res.status[0].voltage = motor_status_.left_motor_voltage;
    res.status[1].voltage = motor_status_.right_motor_voltage;
    res.status[0].power = res.status[0].current
                        * motor_status_.left_motor_voltage;
    res.status[1].power = res.status[1].current
                        * motor_status_.right_motor_voltage;
    return true;
}

void SerialAmpsBcuDriver::EmergencyStopStatusCallBack(const wr_npu_msgs::ScrubberWorkingStatusConstPtr &msg)
{
    emergencystop_state_ = (msg->is_emergency_stop == 1)?true:false;
    LM_DEBUG("msg->is_emergency_stop is :%d , emergencystop_state_ is %d",
             msg->is_emergency_stop, emergencystop_state_);
}

int SerialAmpsBcuDriver::SetPidParam()
{
    bcu_driver::PidParam& pid_param = bcu_param_ptr->pid;
    WR_DEBUG("SerialAmpsBcuDriver::SetPidParam()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    serial_amps::AmpsPidParam bcup_pid_param;
    bcup_pid_param.enb_pid = (pid_param.enb_pid==true)?true:false;
    bcup_pid_param.p = pid_param.kp_acc;
    bcup_pid_param.i = pid_param.ki_acc;
    bcup_pid_param.dec = pid_param.kd_dec;
    bcup_pid_param.acc = pid_param.kd_acc;

    LM_INFO("p is : %d", bcup_pid_param.p);
    LM_INFO("i is : %d", bcup_pid_param.i);
    LM_INFO("dec is : %d", bcup_pid_param.dec);
    LM_INFO("acc is : %d", bcup_pid_param.acc);
    LM_INFO("enb is : %d", bcup_pid_param.enb_pid);

    if(bcup_pid_param.enb_pid){
        LM_INFO("enb to set pid");
        SetMotorPid(bcup_pid_param);
    } else{
        LM_INFO("disable to set pid");
    }

    return TRUE;
}

int SerialAmpsBcuDriver::ClrMotorEnc()
{
    unsigned char cmd[TOTALLENGTH];
    for(int i = 0; i < DATALENGTH;i++){
        cmd[i] = serial_amps::CLR_L_MTR_ENC_ADD[i];
    }

    cmd[9] = GetCheckSum((unsigned char*)&cmd[0], DATALENGTH);

    if(WriteCmd((unsigned char*)(&cmd), TOTALLENGTH) == FALSE){
        LM_WARN("clear motor enc failed at WriteCmd()");
        return FALSE;
    }

    for(int i = 0; i < DATALENGTH;i++){
        cmd[i] = serial_amps::CLR_R_MTR_ENC_ADD[i];
    }

    cmd[9] = GetCheckSum((unsigned char*)&cmd[0], DATALENGTH);

    if(WriteCmd((unsigned char*)(&cmd), TOTALLENGTH) == FALSE){
        LM_WARN("clear motor enc failed at WriteCmd()");
        return FALSE;
    }
    return TRUE;
}

int SerialAmpsBcuDriver::Stop()
{
    WR_DEBUG("SerialAmpsBcuDriver::Stop()");
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    MotorSpdArray rpms(bcu_param_ptr->motor.motor_num, 0);
    SetMotorSpd(rpms);
    return TRUE;
}

int SerialAmpsBcuDriver::GetMotorSpd(MotorSpdArray& rpm)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }

    rpm.resize(2);
    int left_rpm;
    int right_rpm;
    unsigned char left_spd[10];
    unsigned char right_spd[10];

//get left spd
    if(WriteCmd((unsigned char*)serial_amps::GET_L_MTR_SPD_ADD, TOTALLENGTH) == FALSE){
        LM_WARN("get left motor spd failed at WriteCmd()");
        return FALSE;
    }
    if(ReceiveAns((char *)(&left_spd[0])) == FALSE){
        LM_WARN("get left motor spd failed at ReceiveAns()");
        return FALSE;
    }

    AmpsFrame *p_left_spd_frame = (AmpsFrame*)(&left_spd[0]);
    left_rpm = *((int *)(&p_left_spd_frame->data[0]));
    left_rpm = ENDIAN_EXCHANGE_WORD(left_rpm);
    //left_rpm =(int) (left_rpm*1875*1.0/512/4096);
#if GET_MTR_SPD_DEBUG
    PrintFrame(left_spd, __FUNCTION__);
    LM_INFO("left spd is : %d", left_rpm);
#endif
    rpm[0] = left_rpm * (bcu_param_ptr->motor.motor_dir_str[0] == '+' ? (-1):1);

//get right spd
    if(WriteCmd((unsigned char*)serial_amps::GET_R_MTR_SPD_ADD, TOTALLENGTH) == FALSE){
        LM_WARN("get right motor spd failed at WriteCmd()");
        return FALSE;
    }
    if(ReceiveAns((char *)(&right_spd[0])) == FALSE){
        LM_WARN("get right motor spd failed at ReceiveAns()");
        return FALSE;
    }

    AmpsFrame *p_right_spd_frame = (AmpsFrame*)(&right_spd[0]);
    right_rpm = *((int *)(&p_right_spd_frame->data[0]));
    right_rpm = ENDIAN_EXCHANGE_WORD(right_rpm);
    //right_rpm =(int) (right_rpm*1875*1.0/512/4096);

#if GET_MTR_SPD_DEBUG
    PrintFrame(right_spd, __FUNCTION__);
    LM_INFO("right spd is : %d", right_rpm);
#endif

    rpm[1] = right_rpm * (bcu_param_ptr->motor.motor_dir_str[1] == '+' ? (-1):1);
    rpm[1] = -rpm[1];

    motor_status_.left_motor_spd = rpm[0];
    motor_status_.right_motor_spd = rpm[1];

//get motor status
    MotorStatus motor_status;
    if(GetMotorStatus(motor_status) == FALSE){
        LM_WARN("get motor status failed at GetMotorStatus()");
    }
    return TRUE;
}

int SerialAmpsBcuDriver::SetMotorSpd(const MotorSpdArray& spd)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    if(emergencystop_state_){
        LM_ERROR("Emergency stop was taken");
        last_emergencystop_state_ = emergencystop_state_;
        return FALSE;
    }
    if(last_emergencystop_state_){
        LM_ERROR("Emergency stop has been restored");
        EnbDriver(true);
        last_emergencystop_state_ = emergencystop_state_;
    }
    MotorSpdArray cmd = spd;

#if 0
    short left_cmd_spd = (bcu_param_ptr->motor.end_left_right_switch==false)?static_cast<short>(cmd[1]):static_cast<short>(cmd[0]);
    short right_cmd_spd = (bcu_param_ptr->motor.end_left_right_switch==false)?static_cast<short>(cmd[0]):static_cast<short>(cmd[1]);

    left_cmd_spd = (bcu_param_ptr->motor.motor_dir_str[0] == '+' ? 1 : (-1)) * left_cmd_spd;
    right_cmd_spd = (bcu_param_ptr->motor.motor_dir_str[1] == '+' ? 1 : (-1)) * left_cmd_spd;
#endif

    short left_cmd_spd = static_cast<short>(-cmd[0]);
    short right_cmd_spd = static_cast<short>(cmd[1]);
    for(int i = 0; i < 2; ++i){
        cmd[i] = static_cast<int>(spd[i] * (bcu_param_ptr->motor.motor_dir_str[i] == '+' ? 1 : (-1)));
        cmd[i] = static_cast<int>(RANGE(spd[i], serial_amps::MIN_MOTOR_SPD, serial_amps::MAX_MOTOR_SPD));
    }

    unsigned char set_left_spd_frame[TOTALLENGTH];
    unsigned char set_right_spd_frame[TOTALLENGTH];

    for(int i = 0;i < TOTALLENGTH;i++){
        set_left_spd_frame[i] = serial_amps::SET_L_MTR_SPD[i];
    }
    for(int i = 0;i < TOTALLENGTH;i++){
        set_right_spd_frame[i] = serial_amps::SET_R_MTR_SPD[i];
    }

#if SET_MTR_SPD_DEBUG
    LM_WARN("spd is (%.2f|%.2f)",spd[0],spd[1]);
    LM_WARN("cmd is (%d|%d)",left_cmd_spd,right_cmd_spd);
#endif

    SpdChar *left_spd_char = (SpdChar*)(&left_cmd_spd);
    set_left_spd_frame[7] = left_spd_char->spd_cmd_2;
    set_left_spd_frame[8] = left_spd_char->spd_cmd_1;
    set_left_spd_frame[9] = GetCheckSum(set_left_spd_frame, DATALENGTH);

#if SET_MTR_SPD_DEBUG
    LM_WARN("left spd char is (%.2f|%.2f)",left_spd_char->spd_cmd_2, left_spd_char->spd_cmd_1);
#endif

    SpdChar *right_spd_char = (SpdChar*)(&right_cmd_spd);
    set_right_spd_frame[7] = right_spd_char->spd_cmd_2;
    set_right_spd_frame[8] = right_spd_char->spd_cmd_1;
    set_right_spd_frame[9] = GetCheckSum(set_right_spd_frame, DATALENGTH);

#if SET_MTR_SPD_DEBUG
    LM_WARN("right spd char is (%.2f|%.2f)",right_spd_char->spd_cmd_2, right_spd_char->spd_cmd_1);

    PrintFrame((unsigned char*)&set_left_spd_frame, "set_left_spd_frame");
#endif

    if(WriteCmd((unsigned char*)(&set_left_spd_frame), TOTALLENGTH) == FALSE){
        LM_WARN("set left motor spd failed at WriteCmd()");
        return FALSE;
    }

    if(ReceiveAns((char*)(&set_left_spd_frame)) == FALSE){
        LM_WARN("set left motor spd failed at ReceiveAns()");
        return FALSE;
    }

#if SET_MTR_SPD_DEBUG
    PrintFrame((unsigned char*)&set_right_spd_frame, "set_right_spd_frame");
#endif

    if(WriteCmd((unsigned char*)(&set_right_spd_frame), TOTALLENGTH) == FALSE){
        LM_WARN("set right motor spd failed at WriteCmd()");
        return FALSE;
    }

    if(ReceiveAns((char*)(&set_right_spd_frame)) == FALSE){
        LM_WARN("set left motor spd failed at ReceiveAns()");
        return FALSE;
    }
    return TRUE;
}

int SerialAmpsBcuDriver::GetMotorEnc(MotorEncArray& ticks)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    ticks.resize(2);
    int left_enc;
    int right_enc;
    unsigned char left_enc_frame[0];
    unsigned char right_enc_frame[0];

//left enc
    if(WriteCmd((unsigned char*)serial_amps::GET_L_MTR_ENC_ADD, TOTALLENGTH) == FALSE){
        LM_WARN("get left motor enc failed at WriteCmd()");
        return FALSE;
    }

    if(ReceiveAns((char *)(&left_enc_frame[0])) == FALSE){
        LM_WARN("get left motor enc failed at ReceiveAns()");
        return FALSE;
    }

    AmpsFrame *p_left_enc_frame = (AmpsFrame*)(&left_enc_frame);
    left_enc = *((int *)(&p_left_enc_frame->data[0]));
    left_enc = ENDIAN_EXCHANGE_WORD(left_enc);

#if GET_MTR_ENC_DEBUG
    PrintFrame(left_enc_frame, __FUNCTION__);
    LM_INFO("left enc is %d", left_enc);
#endif

    ticks[0] = left_enc * (bcu_param_ptr->motor.enc_dir_str[0] == '+' ? (-1):1);

//right enc
    if(WriteCmd((unsigned char*)serial_amps::GET_R_MTR_ENC_ADD, TOTALLENGTH) == FALSE){
        LM_WARN("get right motor enc failed at WriteCmd()");
        return FALSE;
    }

    if(ReceiveAns((char *)(&right_enc_frame[0])) == FALSE){
        LM_WARN("get right motor enc failed at ReceiveAns()");
        return FALSE;
    }

    AmpsFrame *p_right_enc_frame = (AmpsFrame*)(&right_enc_frame);
    right_enc = *((int *)(&p_right_enc_frame->data[0]));
    right_enc = ENDIAN_EXCHANGE_WORD(right_enc);

#if GET_MTR_ENC_DEBUG
    PrintFrame(right_enc_frame, __FUNCTION__);
    LM_INFO("right enc is %d", right_enc);
#endif

    ticks[1] = right_enc * (bcu_param_ptr->motor.enc_dir_str[1] == '+' ? (-1):1);
    return TRUE;
}

int SerialAmpsBcuDriver::GetMotorStatus(MotorStatus &motor_status)
{
    if(!is_inited_){
        WR_DEBUG("Not inited.");
        return FALSE;
    }
////get current
/// left
    unsigned char left_current_frame[TOTALLENGTH];
    unsigned char right_current_frame[TOTALLENGTH];

    unsigned short left_cur;
    unsigned short right_cur;

#if GET_MTR_STATE_DEBUG
    PrintFrame((unsigned char*)serial_amps::GET_L_MTR_CUR_ADD, "GET_L_MTR_CUR_ADD");
#endif

    if(WriteCmd((unsigned char*)serial_amps::GET_L_MTR_CUR_ADD, TOTALLENGTH) == FALSE){
        LM_WARN("get left motor spd failed at WriteCmd()");
        return FALSE;
    }
    if(ReceiveAns((char *)(&left_current_frame[0])) == FALSE){
        LM_WARN("get left motor current failed at ReceiveAns()");
        return FALSE;
    }
    AmpsFrame *p_left_cur_frame = (AmpsFrame*)(&left_current_frame);
    left_cur = *((short *)(&p_left_cur_frame->data[2]));
    left_cur = ENDIAN_EXCHANGE_HALFWORD(left_cur);
    motor_status_.left_motor_current = static_cast<float>(left_cur) * 0.001;

#if GET_MTR_STATE_DEBUG
    LM_WARN("left_cur is %d", left_cur);
    LM_WARN("p_left_cur_frame->data[2](%02X|%02X)", p_left_cur_frame->data[2], p_left_cur_frame->data[3]);
    LM_WARN("left_current_frame %.4f", motor_status_.left_motor_current);
    PrintFrame(left_current_frame, "left_current_frame");
#endif


/// right
#if GET_MTR_STATE_DEBUG
    PrintFrame((unsigned char*)serial_amps::GET_R_MTR_CUR_ADD, "GET_R_MTR_CUR_ADD");
#endif

    if(WriteCmd((unsigned char*)serial_amps::GET_R_MTR_CUR_ADD, TOTALLENGTH) == FALSE){
        LM_WARN("get right motor current failed at WriteCmd()");
        return FALSE;
    }
    if(ReceiveAns((char *)(&right_current_frame)) == FALSE){
        LM_WARN("get right motor current failed at ReceiveAns()");
        return FALSE;
    }
    AmpsFrame *p_right_cur_frame = (AmpsFrame*)(&right_current_frame);
    right_cur = *((short *)(&p_right_cur_frame->data[2]));
    right_cur = ENDIAN_EXCHANGE_HALFWORD(right_cur);
    motor_status_.right_motor_current = static_cast<float>(right_cur) * 0.001;

#if GET_MTR_STATE_DEBUG
    LM_WARN("right_cur is %d", right_cur);
    LM_WARN("p_right_cur_frame->data[2](%02X|%02X)", p_right_cur_frame->data[2], p_right_cur_frame->data[3]);
    LM_WARN("right_motor_current %.4f", motor_status_.right_motor_current);
    PrintFrame(right_current_frame, "right_current_frame");
#endif


////get voltage
    unsigned char left_voltage_frame[TOTALLENGTH];
    unsigned char right_voltage_frame[TOTALLENGTH];

    short left_vol;
    short right_vol;

/// left
#if GET_MTR_STATE_DEBUG
    PrintFrame((unsigned char*)serial_amps::GET_L_MTR_VOL_ADD, "GET_L_MTR_VOL_ADD");
#endif

    if(WriteCmd((unsigned char*)serial_amps::GET_L_MTR_VOL_ADD, TOTALLENGTH) == FALSE){
        LM_WARN("get left motor voltage failed at WriteCmd()");
        return FALSE;
    }
    if(ReceiveAns((char *)(&left_voltage_frame)) == FALSE){
        LM_WARN("get left motor voltage failed at ReceiveAns()");
        return FALSE;
    }
    AmpsFrame *p_left_vol_frame = (AmpsFrame*)(&left_voltage_frame);
    left_vol = *((short *)(&p_left_vol_frame->data[2]));
    left_vol = ENDIAN_EXCHANGE_HALFWORD(left_vol);
    motor_status_.left_motor_voltage = static_cast<float>(left_vol);

#if GET_MTR_STATE_DEBUG
    LM_WARN("left_vol is %d", left_vol);
    LM_WARN("p_left_vol_frame->data[2](%02X|%02X)", p_left_vol_frame->data[2], p_left_vol_frame->data[3]);
    LM_WARN("left_motor_voltage %.4f", motor_status_.left_motor_voltage);
    PrintFrame(left_voltage_frame, "left_voltage_frame");
#endif

///right
#if GET_MTR_STATE_DEBUG
    PrintFrame((unsigned char*)serial_amps::GET_R_MTR_VOL_ADD, "GET_R_MTR_VOL_ADD");
#endif
    if(WriteCmd((unsigned char*)serial_amps::GET_R_MTR_VOL_ADD, TOTALLENGTH) == FALSE){
        LM_WARN("get right motor voltage failed at WriteCmd()");
        return FALSE;
    }
    if(ReceiveAns((char *)(&right_voltage_frame)) == FALSE){
        LM_WARN("get right motor voltage failed at ReceiveAns()");
        return FALSE;
    }

    AmpsFrame *p_right_vol_frame = (AmpsFrame*)(&right_voltage_frame);
    right_vol = *((short *)(&p_right_vol_frame->data[2]));
    right_vol = ENDIAN_EXCHANGE_HALFWORD(right_vol);
    motor_status_.right_motor_voltage = static_cast<float>(right_vol);

#if GET_MTR_STATE_DEBUG
    LM_WARN("right_vol is %d", right_vol);
    LM_WARN("p_right_vol_frame->data[2](%02X|%02X)", p_right_vol_frame->data[2], p_right_vol_frame->data[3]);
    LM_WARN("right_motor_voltage %.4f", motor_status_.right_motor_voltage);
    PrintFrame(right_voltage_frame, "right_voltage_frame");
#endif

////get error code
    unsigned char left_error_frame[TOTALLENGTH];
    unsigned char right_error_frame[TOTALLENGTH];

    short left_error;
    short right_error;

/// left
#if GET_MTR_ERROR_DEBUG
    PrintFrame((unsigned char*)serial_amps::GET_L_MTR_ERR_STE, "GET_L_MTR_ERR_STE");
#endif

    if(WriteCmd((unsigned char*)serial_amps::GET_L_MTR_ERR_STE, TOTALLENGTH) == FALSE){
        LM_WARN("get left motor error failed at WriteCmd()");
        return FALSE;
    }
    if(ReceiveAns((char *)(&left_error_frame)) == FALSE){
        LM_WARN("get left motor error failed at ReceiveAns()");
        return FALSE;
    }
    AmpsFrame *p_left_error_frame = (AmpsFrame*)(&left_error_frame);
    left_error = *((short *)(&p_left_error_frame->data[2]));
    left_error = ENDIAN_EXCHANGE_HALFWORD(left_error);
    motor_status_.left_motor_error_signal = static_cast<unsigned short>(left_error);

#if GET_MTR_ERROR_DEBUG
    LM_WARN("left_error is %d", left_error);
    LM_WARN("p_left_error_frame->data[2](%02X|%02X)", p_left_error_frame->data[2], p_left_error_frame->data[3]);
    LM_WARN("left_motor_error_signal %.4f", motor_status_.left_motor_error_signal);
    PrintFrame(left_error_frame, "left_error_frame");
#endif

///right
#if GET_MTR_ERROR_DEBUG
    PrintFrame((unsigned char*)serial_amps::GET_R_MTR_ERR_STE, "GET_R_MTR_ERR_STE");
#endif
    if(WriteCmd((unsigned char*)serial_amps::GET_R_MTR_ERR_STE, TOTALLENGTH) == FALSE){
        LM_WARN("get right motor error failed at WriteCmd()");
        return FALSE;
    }
    if(ReceiveAns((char *)(&right_error_frame)) == FALSE){
        LM_WARN("get right motor error failed at ReceiveAns()");
        return FALSE;
    }

    AmpsFrame *p_right_error_frame = (AmpsFrame*)(&right_error_frame);
    right_error = *((short *)(&p_right_error_frame->data[2]));
    right_error = ENDIAN_EXCHANGE_HALFWORD(right_error);
    motor_status_.right_motor_error_signal = static_cast<unsigned short>(right_error);

#if GET_MTR_ERROR_DEBUG
    LM_WARN("right_error is %d", right_error);
    LM_WARN("p_right_error_frame->data[2](%02X|%02X)", p_right_error_frame->data[2], p_right_error_frame->data[3]);
    LM_WARN("right_motor_error_signal %.4f", motor_status_.right_motor_error_signal);
    PrintFrame(right_error_frame, "right_error_frame");
#endif

    return TRUE;
}

int SerialAmpsBcuDriver::WriteCmd(unsigned char* cmd, int cmd_len)
{
#if WRITE_CMD_DEBUG
    PrintFrame(cmd,__FUNCTION__);
#endif
    ComParam& com_param = bcu_param_ptr->com;
    serial_port_.ClearReadBuffer();
    int retry_cnt = 0;
    int len = 0;
    while(retry_cnt < com_param.cmd_retry_num)
    {
        len += serial_port_.Write((char*)(cmd), cmd_len - len);
        if (len == 0)
        {
            WR_INFO("WriteCmd(): Port lost, try to re-open");
            int lost_retry_cnt  = 0;
            while (lost_retry_cnt < 3)
            {
                WR_SLEEP_MS(200);
                serial_port_.Close();
                serial_port_.Open(bcu_param_ptr->com.port_id, B38400,8,"NONE");
                if (serial_port_.IsOpen())
                {
                    WR_INFO("WriteCmd(): Re-open succeeded.");
                    break;
                }
            }
            if (lost_retry_cnt >= 3)
            {
                WR_ERROR("WriteCmd(): Re-open failed");
            }
        }
        else if (len >= cmd_len)
        {
            break;
        }
        WR_SLEEP_MS(com_param.cmd_delay_ms);
        retry_cnt++;
    }

    if (retry_cnt < com_param.cmd_retry_num)
    {
        return TRUE;
    }
    else
    {
        WR_ERROR("WriteCmd() failed: TIMEOUT (len = %d, cmd_len = %d, [CMD] = %s)"
                 , len, cmd_len, str_ext::ByteArrToStr(cmd, cmd_len).c_str());
    }
    return FALSE;
}

int SerialAmpsBcuDriver::ReceiveAns(char* ans)
{
    uint retry_cnt = 0;

    while(retry_cnt < bcu_param_ptr->com.ans_retry_num)
    {
        WR_SLEEP_MS(bcu_param_ptr->com.ans_delay_ms);
        uint len = serial_port_.BytesWaiting();
        if(len >= TOTALLENGTH)
        {
#if RECEIVE_DEBUG
            LM_INFO("len is : %d", len);
#endif
            for(int i = 0;i < TOTALLENGTH;i++){
                if(serial_port_.ReadByte(ans[i]) == 0){
                    LM_ERROR("serial port read failed.");
                }
            }
#if RECEIVE_DEBUG
            PrintFrame((unsigned char*)ans, __FUNCTION__);
#endif
            if(CheckAns((unsigned char*)ans)){
#if RECEIVE_DEBUG
                PrintFrame((unsigned char*)ans, __FUNCTION__);
#endif
                serial_port_.ClearReadBuffer();
                return TRUE;
            }
            serial_port_.ClearReadBuffer();
            return FALSE;
        }
        retry_cnt++;
    }
    PrintFrame((unsigned char*)ans, __FUNCTION__);
    WR_ERROR("ReceiveAns() failed: TIMEOUT");
    return FALSE;
}

int SerialAmpsBcuDriver::CheckAns(unsigned char* ans)
{
    unsigned char sum = GetCheckSum(ans,DATALENGTH);
    if(sum == ans[9]){
        return TRUE;
    }
    LM_WARN("Checksum error cal/org sum is : (%02X|%02X)", sum, ans[9]);
    return FALSE;
}

void SerialAmpsBcuDriver::PrintFrame(unsigned char *ans, string func)
{
    printf("%s frame is :", func.c_str());
    for(int i = 0;i < TOTALLENGTH;i++){
        printf(" %02X",ans[i]);
    }
    printf("\n");
}

bool SerialAmpsBcuDriver::EnbDriver(bool flag)
{
    LM_INFO("enb set enb %d", static_cast<int>(flag));
    char receiv_buf[TOTALLENGTH];
    unsigned char sum = 0x00;
    if(flag){
        ////left
        /// enb
        if(WriteCmd((unsigned char*)serial_amps::SET_L_MOTOR_ENB, TOTALLENGTH) == FALSE){
            LM_WARN("set left motor enb failed at WriteCmd()");
            return FALSE;
        }
        if(ReceiveAns(receiv_buf) == FALSE){
            LM_WARN("SET_L_MOTOR_ENB failed at ReceiveAns()");
            return FALSE;
        }
        ///param 1
        if(WriteCmd((unsigned char*)serial_amps::SET_L_MOTOR_PARAM_1, TOTALLENGTH) == FALSE){
            LM_WARN("get left motor param 1 failed at WriteCmd()");
            return FALSE;
        }
        if(ReceiveAns(receiv_buf) == FALSE){
            LM_WARN("SET_L_MOTOR_PARAM_1 failed at ReceiveAns()");
            return FALSE;
        }
        ///param 2
        if(WriteCmd((unsigned char*)serial_amps::SET_L_MOTOR_PARAM_2, TOTALLENGTH) == FALSE){
            LM_WARN("get left motor param 2 failed at WriteCmd()");
            return FALSE;
        }
        if(ReceiveAns(receiv_buf) == FALSE){
            LM_WARN("SET_L_MOTOR_PARAM_2 failed at ReceiveAns()");
            return FALSE;
        }
        ////right
        /// enb
        if(WriteCmd((unsigned char*)serial_amps::SET_R_MOTOR_ENB, TOTALLENGTH) == FALSE){
            LM_WARN("get right motor enb failed at WriteCmd()");
//            return FALSE;
        }
        if(ReceiveAns(receiv_buf) == FALSE){
            LM_WARN("SET_R_MOTOR_ENB failed at ReceiveAns()");
            return FALSE;
        }
        ///param 1
        if(WriteCmd((unsigned char*)serial_amps::SET_R_MOTOR_PARAM_1, TOTALLENGTH) == FALSE){
            LM_WARN("get right motor param 1 failed at WriteCmd()");
            return FALSE;
        }
        if(ReceiveAns(receiv_buf) == FALSE){
            LM_WARN("SET_R_MOTOR_PARAM_1 failed at ReceiveAns()");
            return FALSE;
        }
        ///param 2
        if(WriteCmd((unsigned char*)serial_amps::SET_R_MOTOR_PARAM_2, TOTALLENGTH) == FALSE){
            LM_WARN("get right motor param 2 failed at WriteCmd()");
            return FALSE;
        }
        if(ReceiveAns(receiv_buf) == FALSE){
            LM_WARN("SET_R_MOTOR_PARAM_2 failed at ReceiveAns()");
            return FALSE;
        }
    }
    return TRUE;
}

int SerialAmpsBcuDriver::SetMotorPid(const serial_amps::AmpsPidParam &param)
{
    if(!is_inited_){
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    unsigned char cmd_frame[TOTALLENGTH];

    //// set P
    /// left
    U16Char *p_set_p_char = (U16Char*)(&param.p);

    for(int i = 0;i < TOTALLENGTH;i++){
        cmd_frame[i] = serial_amps::SET_L_MOTOR_P[i];
    }
    cmd_frame[7] = p_set_p_char->data_2;
    cmd_frame[8] = p_set_p_char->data_1;
    if(SendRequest(cmd_frame, "set left p:") == FALSE){
        LM_ERROR("set left p failed at SendRequest");
        return FALSE;
    }
    /// right
    for(int i = 0;i < TOTALLENGTH;i++){
        cmd_frame[i] = serial_amps::SET_R_MOTOR_P[i];
    }
    cmd_frame[7] = p_set_p_char->data_2;
    cmd_frame[8] = p_set_p_char->data_1;
    if(SendRequest(cmd_frame, "set right p:") == FALSE){
        LM_ERROR("set right p failed at SendRequest");
        return FALSE;
    }

    //// set I
    /// left
    U16Char *p_set_i_char = (U16Char*)(&param.i);
    for(int i = 0;i < TOTALLENGTH;i++){
        cmd_frame[i] = serial_amps::SET_L_MOTOR_I[i];
    }
    cmd_frame[7] = p_set_i_char->data_2;
    cmd_frame[8] = p_set_i_char->data_1;
    if(SendRequest(cmd_frame, "set left i:") == FALSE){
        LM_ERROR("set left i failed at SendRequest");
        return FALSE;
    }
    /// right
    for(int i = 0;i < TOTALLENGTH;i++){
        cmd_frame[i] = serial_amps::SET_R_MOTOR_I[i];
    }
    cmd_frame[7] = p_set_i_char->data_2;
    cmd_frame[8] = p_set_i_char->data_1;
    if(SendRequest(cmd_frame, "set right i:") == FALSE){
        LM_ERROR("set right i failed at SendRequest");
        return FALSE;
    }

    //// set Dec
    /// left
    U16Char *p_set_dec_char = (U16Char*)(&param.dec);
    for(int i = 0;i < TOTALLENGTH;i++){
        cmd_frame[i] = serial_amps::SET_L_MOTOR_DEC[i];
    }
    cmd_frame[5] = 0x00;
    cmd_frame[6] = 0x00;
    cmd_frame[7] = p_set_dec_char->data_2;
    cmd_frame[8] = p_set_dec_char->data_1;
    if(SendRequest(cmd_frame, "set left dec:") == FALSE){
        LM_ERROR("set left dec failed at SendRequest");
        return FALSE;
    }
    /// right
    for(int i = 0;i < TOTALLENGTH;i++){
        cmd_frame[i] = serial_amps::SET_R_MOTOR_DEC[i];
    }
    cmd_frame[5] = 0x00;
    cmd_frame[6] = 0x00;
    cmd_frame[7] = p_set_dec_char->data_2;
    cmd_frame[8] = p_set_dec_char->data_1;
    if(SendRequest(cmd_frame, "set right dec:") == FALSE){
        LM_ERROR("set right dec failed at SendRequest");
        return FALSE;
    }

    //// set Acc
    /// left
    for(int i = 0;i < TOTALLENGTH;i++){
        cmd_frame[i] = serial_amps::SET_L_MOTOR_ACC[i];
    }
    cmd_frame[5] = param.acc >> 24;
    cmd_frame[6] = param.acc >> 16;
    cmd_frame[7] = param.acc >> 8;
    cmd_frame[8] = param.acc;
    if(SendRequest(cmd_frame, "set left acc:") == FALSE){
        LM_ERROR("set left acc failed at SendRequest");
        return FALSE;
    }
    /// right
    for(int i = 0;i < TOTALLENGTH;i++){
        cmd_frame[i] = serial_amps::SET_R_MOTOR_ACC[i];
    }
    cmd_frame[5] = param.acc >> 24;
    cmd_frame[6] = param.acc >> 16;
    cmd_frame[7] = param.acc >> 8;
    cmd_frame[8] = param.acc;
    if(SendRequest(cmd_frame, "set right acc:") == FALSE){
        LM_ERROR("set right acc failed at SendRequest");
        return FALSE;
    }

    return TRUE;
}


int SerialAmpsBcuDriver::SendRequest(unsigned char *req, string cmd)
{
    req[9] = GetCheckSum(req, DATALENGTH);
#if ENB_SET_PID_DEBUG
    LM_WARN("sum is %02X", req[9]);
    cmd = cmd + "write";
    PrintFrame(req, cmd);
#endif
    if(WriteCmd((unsigned char*)req, TOTALLENGTH) == FALSE){
        LM_WARN("%s failed at WriteCmd()", cmd.c_str());
        return FALSE;
    }
    if(ReceiveAns((char*)req) == FALSE){
        LM_WARN("%s failed at ReceiveAns()", cmd.c_str());
        return FALSE;
    }
#if ENB_SET_PID_DEBUG
    cmd = cmd + "receive";
    PrintFrame(req, cmd);
#endif
    return TRUE;
}

int SerialAmpsBcuDriver::SendRequest(unsigned char *req, string cmd, int data)
{
//TODO
}

unsigned char SerialAmpsBcuDriver::GetCheckSum(unsigned char* data, int len)
{
    unsigned char sum = 0x00;
#if CHECK_SUM_DEBUG
    printf("check sum is:");
#endif
    for(int i =0;i<len;i++)
    {
       sum+=data[i];
#if CHECK_SUM_DEBUG
       printf(" [%d](%02X),s(%02X)",i, data[i], sum);
#endif
    }
#if CHECK_SUM_DEBUG
    printf("\n");
#endif
    return sum;
}

}}  // namespace
