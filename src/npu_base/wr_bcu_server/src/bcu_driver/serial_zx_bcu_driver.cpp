﻿#include "serial_zx/serial_zx_bcu_driver.h"
#include <string>
#include <ros/ros.h>
#include <algorithm>
#include <vector>

namespace wizrobo { namespace bcu_driver {
using namespace serial_zxfdk;
using namespace std;
SerialZXFdkBcuDriver::SerialZXFdkBcuDriver()
{
    std::cout<<"========================="<<std::endl;
    std::cout<<"========================="<<std::endl;
    std::cout<<"this is zx fdk bcu driver"<<std::endl;
    std::cout<<"========================="<<std::endl;
    std::cout<<"========================="<<std::endl;
}
SerialZXFdkBcuDriver::~SerialZXFdkBcuDriver()
{
    if(serial_port_.IsOpen())
        serial_port_.Close();
}

int SerialZXFdkBcuDriver::Init(BcuParamPtr ptr)
{
    bcu_param_ptr = ptr;
    if(serial_port_.Open(bcu_param_ptr->com.port_id, serial_zxfdk::BAUDRATE,serial_zxfdk::DATABITS,serial_zxfdk::PARITY))
    {
        is_inited_ = TRUE;
        EnbDriver(TRUE);
        WR_SLEEP_MS(20);
//        ClrMotorEnc();
        return TRUE;
    }
    return FALSE;
}

//int SerialFdkBcuDriver::ClrMotorEnc()
//{
//    const std::string CLR_LEFT_MTR_ENC(":0110100800020800000000CD\r\n");
//    const std::string CLR_RIGHT_MTR_ENC(":011010E000020800000000F5\r\n");

//    std::string clr_left_mtr_enc(CLR_LEFT_MTR_ENC);
//    std::string clr_right_mtr_enc(CLR_RIGHT_MTR_ENC);
//    std::string l_ans;
//    std::string r_ans;

//    serial_port_.ClearReadBuffer();

//    if(!WriteCmd(clr_left_mtr_enc.c_str(), clr_left_mtr_enc.size()))
//    {
//        return FALSE;
//    }

//    if(!ReceiveAns(l_ans))
//    {
//        ROS_ERROR("clr left motor enc timeout");
//        PrintAns(l_ans);
//        return FALSE;
//    }


//    WR_SLEEP_MS(10);

//    if(!WriteCmd(clr_right_mtr_enc.c_str(), clr_right_mtr_enc.size()))
//    {
//        return FALSE;
//    }

//    if(!ReceiveAns(r_ans))
//    {
//        ROS_ERROR("clr right motor enc timeout");
//        PrintAns(r_ans);
//        return FALSE;
//    }
//    return TRUE;
//}

int SerialZXFdkBcuDriver::Stop()
{
    WR_DEBUG("SerialFdkBcuDriver::Stop()");
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }
    MotorSpdArray rpms(bcu_param_ptr->motor.motor_num, 0);
    SetMotorSpd(rpms);
    return TRUE;
}

int SerialZXFdkBcuDriver::GetMotorSpd(MotorSpdArray& rpm)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }


    const std::string GET_LEFT_MTR_SPD(":0103100D0001DE\r\n");
    const std::string GET_RIGHT_MTR_SPD(":010310E5000106\r\n");

    std::string get_left_mtr_spd(GET_LEFT_MTR_SPD);
    std::string get_right_mtr_spd(GET_RIGHT_MTR_SPD);
    std::string l_ans;
    std::string r_ans;

    int l_spd = 0;
    int r_spd = 0;

    serial_port_.ClearReadBuffer();

    rpm.clear();
    rpm.resize(2);

    if(!WriteCmd(get_left_mtr_spd.c_str(), get_left_mtr_spd.size()))
    {
        return FALSE;
    }

    if(!ReceiveAns(l_ans) || !ParseMotorSpd(l_ans, l_spd))
    {
        ROS_ERROR("recevive left motor spd timeout");
        PrintAns(l_ans);
        return FALSE;
    }

//    ros::Time set_left_motor_spd_time = ros::Time::now();
//    while (set_left_motor_spd_time + ros::Duration(0.005) > ros::Time::now()) {
//    //delay 0.005 s
//    }

    if(!WriteCmd(get_right_mtr_spd.c_str(), get_right_mtr_spd.size()))
    {
        return FALSE;
    }

    if(!ReceiveAns(r_ans) || !ParseMotorSpd(r_ans, r_spd))
    {
        ROS_ERROR("recevive right motor spd timeout");
        PrintAns(r_ans);
        return FALSE;
    }
    rpm[0] = -l_spd;
    rpm[1] = r_spd;
    return TRUE;
}

int SerialZXFdkBcuDriver::SetMotorSpd(const MotorSpdArray& spd)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }

    short motor_spd[2];
    for(int i = 0; i < 2; ++i)
    {
        motor_spd[i] = (short)(spd[i] / bcu_param_ptr->motor.motor_max_spd_rpm * serial_zxfdk::MAX_MOTOR_SPD);
        motor_spd[i] *= (bcu_param_ptr->motor.motor_dir_str[i] == '+' ? 1 : (-1));

        if(motor_spd[i] > serial_zxfdk::MAX_MOTOR_SPD)
        {
            motor_spd[i] = serial_zxfdk::MAX_MOTOR_SPD;
        }
        else if(motor_spd[i] < serial_zxfdk::MIN_MOTOR_SPD)
        {
            motor_spd[i] = serial_zxfdk::MIN_MOTOR_SPD;
        }
    }

    std::string cmd_data;
    std::string l_ans;
    std::string r_ans;


    int index = (bcu_param_ptr->motor.end_left_right_switch==false)?0:1;
    const int *id_map = serial_zxfdk::MOTOR_ID_MAP[index];

    unsigned char dst[6];
    cmd_data.append(serial_zxfdk::COLON);
    cmd_data.append(serial_zxfdk::SET_LEFT_MTR_SPD);
    char spd_l[4];
    ConvertShortToStr(spd_l,motor_spd[id_map[0]]);
    cmd_data.append(spd_l);
    ConvertStrToUnChar(cmd_data.substr(1), dst,6);
    char check_sum[2];
    GetCheckSum(check_sum,dst,6);
    cmd_data.append(check_sum);
    transform(cmd_data.begin(), cmd_data.end(), cmd_data.begin(), ::toupper);
    cmd_data.append(serial_zxfdk::CMD_END);


    if(!WriteCmd(cmd_data.c_str(), cmd_data.size()))
    {
        return FALSE;
    }

//    ros::Time set_left_motor_spd_time = ros::Time::now();
//    while (set_left_motor_spd_time + ros::Duration(0.005) > ros::Time::now()) {
//    //delay 0.005 s
//    }

    if(ReceiveAns(l_ans))
    {
        //delay  TODO
        cmd_data.clear();
        cmd_data.append(serial_zxfdk::COLON);
        cmd_data.append(serial_zxfdk::SET_RIGHT_MTR_SPD);
        char spd_r[4];
        ConvertShortToStr(spd_r,motor_spd[id_map[1]]);
        cmd_data.append(spd_r);
        ConvertStrToUnChar(cmd_data.substr(1), dst,6);
        char check_sum[2];
        GetCheckSum(check_sum,dst,6);
        cmd_data.append(check_sum);
        transform(cmd_data.begin(), cmd_data.end(), cmd_data.begin(), ::toupper);
        cmd_data.append(serial_zxfdk::CMD_END);

        if(!WriteCmd(cmd_data.c_str(), cmd_data.size()))
        {
            return FALSE;
        }

        if(ReceiveAns(r_ans))
        {
            return TRUE;
        }
        else
        {
            ROS_ERROR("recevive set right motor spd timeout");
            PrintAns(r_ans);
            return FALSE;
        }
    }
    else
    {
        WR_ERROR("fdk driver: set speed ans error.");
        PrintAns(l_ans);
        return FALSE;
    }
}

int SerialZXFdkBcuDriver::GetMotorEnc(MotorEncArray& ticks)
{
    if(!is_inited_)
    {
        WR_DEBUG("Not inited.");
        return FALSE;
    }

    serial_port_.ClearReadBuffer();

    const std::string GET_LEFT_MTR_ENC(":010310080002E2\r\n");
    const std::string GET_RIGHT_MTR_ENC(":010310E000020A\r\n");

    std::string cmd_read_left_enc(GET_LEFT_MTR_ENC);
    std::string cmd_read_right_enc(GET_RIGHT_MTR_ENC);
    std::string ans;
    std::string _ans;
    int l_tick = 0;
    int r_tick = 0;

    if(!WriteCmd(cmd_read_left_enc.c_str(), cmd_read_left_enc.size()))
    {
        ROS_ERROR("read failed left");
        return FALSE;
    }
    if(!ReceiveAns(ans)){
        ROS_ERROR("recevive left motor enc timeout");
        PrintAns(ans);
    }
    ParseEncData(ans,l_tick);
#if 0
    ROS_INFO("GetMotorEnc left");
    ROS_INFO("l tick is %d",l_tick);
#endif
//    ros::Time set_left_motor_spd_time = ros::Time::now();
//    while (set_left_motor_spd_time + ros::Duration(0.005) > ros::Time::now()) {
//    //delay 0.005 s
//    }

    if(!WriteCmd(cmd_read_right_enc.c_str(), cmd_read_right_enc.size()))
    {
        ROS_ERROR("read failed right");
        return FALSE;
    }
    if(!ReceiveAns(_ans)){
        ROS_ERROR("recevive right motor enc timeout");
        PrintAns(_ans);
    }
    ParseEncData(_ans,r_tick);
#if 0
    ROS_INFO("GetMotorEnc right");
    ROS_INFO("r tick is %d",r_tick);
#endif

    ticks.clear();
    ticks.resize(2);
    ticks[0] = -l_tick;
    ticks[1] = r_tick;
    return TRUE;
}

int SerialZXFdkBcuDriver::WriteCmd(const char* cmd, uint len)
{
    serial_port_.ClearReadBuffer();
    //ROS_INFO("%x",cmd);
//    std::cout << cmd << std::endl;

    if (serial_port_.Write(cmd, len) < len)
    {
        WR_ERROR("Writing command failed. Please check serial port.");
        while(!serial_port_.Open(bcu_param_ptr->com.port_id, serial_zxfdk::BAUDRATE,serial_zxfdk::DATABITS,serial_zxfdk::PARITY))
        {
            WR_DEBUG("retrying...");
            WR_SLEEP_MS(bcu_param_ptr->com.cmd_delay_ms);
        }
        WR_INFO("fdk driver reconnected");
        return FALSE;
    }

    return TRUE;
}

int SerialZXFdkBcuDriver::ReceiveAns(std::string& ans)
{
    uint retry_cnt = 0;

    while(retry_cnt < bcu_param_ptr->com.ans_retry_num)
    {
        WR_SLEEP_MS(bcu_param_ptr->com.ans_delay_ms);
        if(serial_port_.BytesWaiting() > 0)
        {
            uint len = serial_port_.BytesWaiting();
            char *buf = new char[len];
            if(serial_port_.Read(buf, len) < len)
            {
                WR_ERROR("serial port read failed.");
            }
            else
            {
                ans.append(buf, len);
                if(CheckAns(ans))
                {
                    break;
                }
            }
        }
        retry_cnt++;
    }
    PrintAns(ans);
    //WR_INFO("retry_cnt:%d,retry_num:%d",retry_cnt,bcu_param_ptr->com.ans_retry_num);

    if (retry_cnt < bcu_param_ptr->com.ans_retry_num)
    {
        serial_port_.ClearReadBuffer();
        //WR_INFO("GET ANSWER DONE!");
        return TRUE;
    }
    else
    {
        WR_ERROR("ReceiveAns() failed: TIMEOUT");
        return FALSE;
    }
}

int SerialZXFdkBcuDriver::ParseMotorSpd(const std::string& ans, int& rpm)
{

    //WR_INFO(ans.c_str());
    if(ans[0] == ':'){
        std::string rpm_string = ans.substr(7,4);

        short spd = 0;
        unsigned char left_data[2];

        ConvertStrToUnChar(rpm_string,left_data,2);
        spd = spd | left_data[0];
        spd = spd << 8;
        spd = spd | left_data[1];

        rpm = spd;
    } else{
        WR_ERROR("ParseMotorSpd():: wrong spd data 03.");
        PrintAns(ans);
        return FALSE;
    }
    return TRUE;
}

int SerialZXFdkBcuDriver::ParseEncData(const std::string& ans, int& enc)
{

    if(ans[0] == ':' && ans[4] == '3' && ans[6] == '4' ){
        std::string motor_enc = ans.substr(7,8);

        unsigned char data[4];
        ConvertStrToUnChar(motor_enc,data,4);

        int temp = 0;
        temp = temp | data[2];
        temp = temp << 24;
        enc = enc | temp;
        temp = 0;
        temp = temp | data[3];
        temp |= temp << 16;
        enc = enc | temp;
        temp = 0;
        temp = temp | data[0];
        temp = temp << 8;
        enc = enc | temp;
        temp = 0;
        temp = temp | data[1];
        enc = enc | temp;

    } else{
        WR_ERROR("ParseEncData():: wrong spd data 03.");
        PrintAns(ans);
        return FALSE;
    }

    return TRUE;

}

int SerialZXFdkBcuDriver::CheckAns(const std::string& ans)
{
    if(ans.size() < serial_zxfdk::MIN_ANS_LEN)
    {
        return FALSE;
    }
    int cnt = 0;
    for(const char& c: ans)
    {
        if(c == '\r')
            cnt++;
    }
    if(ans.back() == '\n' && cnt == 1)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

void SerialZXFdkBcuDriver::PrintAns(const std::string& ans)
{
    std::string temp = ans;
    for(char &c : temp)
    {
        if(c == '\r')
        {
            c = '.';
        }
    }
    WR_DEBUG("len = %d, ans = %s", static_cast<int>(temp.size()), temp.c_str());
}

bool SerialZXFdkBcuDriver::EnbDriver(bool flag)
{
    if (flag)
    {
       std::string cmd(MTR_SOFT_START);
       serial_port_.ClearReadBuffer();

       if(!WriteCmd(cmd.c_str(), cmd.size()))
       {
           return FALSE;
       }
    }

    else
    {
        std::string cmd(MTR_SOFT_STOP);
        serial_port_.ClearReadBuffer();

        if(!WriteCmd(cmd.c_str(), cmd.size()))
        {
            return FALSE;
        }
    }

    return TRUE;
}

void SerialZXFdkBcuDriver::ConvertUncharToStr(char *str, unsigned char *Unchar, int ucLen)
{
    for (int var = 0; var < ucLen; ++var)
    {
        sprintf(str+var*2,"%02x",Unchar[var]);
    }
}

void SerialZXFdkBcuDriver::ConvertStrToUnChar(const std::string&  str, unsigned char* UnChar, int len)
{
    //std::string cmd_ = str.substr(1);  //Delete ":" from string
    // WR_INFO(str.c_str());
    char temp[len*2];
    for (int var = 0; var < len*2; ++var)
    {
        temp[var] = str[var];
    }
    int i = strlen(temp), j = 0, counter = 0;
    char c[2];
    unsigned int bytes[2];

    for (j = 0; j < i; j += 2)
    {
        if(0 == j % 2)
        {
            c[0] = temp[j];
            c[1] = temp[j + 1];
            sscanf(c, "%02x" , &bytes[0]);
            UnChar[counter] = bytes[0];
            counter++;
        }
    }
    return;
}


void SerialZXFdkBcuDriver::ConvertShortToStr(char* str,short data)
{
    unsigned char left_spd[2];
    memcpy(&left_spd,&data,2);
    unsigned char left_spd1[2]={left_spd[1],left_spd[0]};
    ConvertUncharToStr(str,left_spd1,2);
}

void SerialZXFdkBcuDriver::GetCheckSum(char* str, unsigned char* UnChar, int len)
{
    vector<unsigned char> cmd;
    for (int var = 0; var < len; ++var)
    {
        cmd.push_back(UnChar[var]);
    }

    unsigned char sum;
    for (int var = 0; var < len; ++var)
    {
        sum += cmd[var];
    }

    sum = ~sum;
    sum = sum+1;
    unsigned char sum_[1] ={sum};
    sprintf(str,"%02x",sum_[0]);
}

}}  // namespace
