#include "serial_csg/serial_csg_bcu_driver.h"
#include <string>
#include <ros/ros.h>
#include <iostream>
#include <sstream>

#include <stdio.h>
#define BCU_DEBUG(fmt, arg...) ROS_DEBUG_NAMED("driver", "BcuDriverCsg::%s() " fmt, __FUNCTION__,##arg)
#define BCU_INFO(fmt, arg...)  ROS_INFO_NAMED("driver",  "BcuDriverCsg::%s() " fmt, __FUNCTION__,##arg)
#define BCU_WARN(fmt, arg...)  ROS_WARN_NAMED("driver",  "BcuDriverCsg::%s() " fmt, __FUNCTION__,##arg)
#define BCU_ERROR(fmt, arg...) ROS_ERROR_NAMED("driver", "BcuDriverCsg::%s() " fmt, __FUNCTION__,##arg)
#define BCU_FATAL(fmt, arg...) ROS_FATAL_NAMED("driver", "BcuDriverCsg::%s() " fmt, __FUNCTION__,##arg)

#define ENDIAN_EXCHANGE_HALFWORD(x) ((((x)&0xFF00)>>8)+(((x)&0x00FF)<<8))
#define ENDIAN_EXCHANGE_WORD(x) ((((x)&0xFF000000)>>24)+(((x)&0x00FF0000)>>8)+(((x)&0x0000FF00)<<8)+(((x)&0x000000FF)<<24))

namespace wizrobo { namespace bcu_driver {
using namespace serial_csg;

SerialCsgBcuDriver::SerialCsgBcuDriver()
    : obstacle_callback_count_(0)
    , csg_msg_pub_count_(0)
    , is_stopmode_(false)
    , mode_(DRIVING)
{
    ros::NodeHandle nh;
    receive_buffer_rear_ = 0;
    steer_location_ = wizrobo_npu::CENTER;
    csgmsg_.obstacle = 0;
    for (int i=0;i<receive_buffer_length_;i++) {
        receive_buffer_[i] = 0x00;
    }
    rfw_function_map_[CONTROL_INFO] =
            boost::bind(&SerialCsgBcuDriver::FreshControlInfo, this, _1);
    rfw_function_map_[ANGLE_DATA] =
            boost::bind(&SerialCsgBcuDriver::FreshAngleData, this, _1);
    rfw_function_map_[MOTOR_DATA] =
            boost::bind(&SerialCsgBcuDriver::FreshMotorData, this, _1);
    rfw_function_map_[ENCODER_DATA] =
            boost::bind(&SerialCsgBcuDriver::FreshEncoderData, this, _1);
    rfw_function_map_[SONAR_DATA] =
            boost::bind(&SerialCsgBcuDriver::FreshSonarData, this, _1);
    rfw_function_map_[BATTERY_VOLTAGE_DATA] =
            boost::bind(&SerialCsgBcuDriver::FreshBatteryVoltageData, this, _1);
    rfw_function_map_[BATTERY_MANUFACTURER_INFO] =
            boost::bind(&SerialCsgBcuDriver::FreshBatteryManufacturerInfo, this, _1);
    rfw_function_map_[BATTERY_TEMPERATURE_DATA] =
            boost::bind(&SerialCsgBcuDriver::FreshBatteryTemperatureData, this, _1);
    rfw_function_map_[MOTION_DATA] =
            boost::bind(&SerialCsgBcuDriver::FreshMotionData, this, _1);
    rfw_function_map_[ERROR_CODE] =
            boost::bind(&SerialCsgBcuDriver::FreshErrorCode, this, _1);
    rfw_function_map_[RFW_THE_END] = NULL;

    service_ = nh.advertiseService("stopmode", &SerialCsgBcuDriver::StopMode, this);
    csgpub_ = nh.advertise<wr_npu_msgs::CsgBatterySonar>("/csgbatterysonar", 100);
    csg_angle_pub_ = nh.advertise<std_msgs::Float32MultiArray>("/csg_angle_data", 100);
    obstacle_sub_ = nh.subscribe<std_msgs::Bool>("/pathfollower_obstacle",1,&SerialCsgBcuDriver::ObstacleCallback,this);
    nh.param(STD_BASE_PARAM_NS + "/csg_bcu_driver/zero_bias", bias_, 0.0);
}

SerialCsgBcuDriver::~SerialCsgBcuDriver()
{
    if(serial_port_.IsOpen()) {
        serial_port_.Close();
    }
}

bool SerialCsgBcuDriver::StopMode(wr_npu_msgs::CsgMode::Request &req,
                                  wr_npu_msgs::CsgMode::Response &res)
{
    if(!is_inited_) {
        BCU_DEBUG("Not inited.");
        res.return_data = 0;
        return false;
    }
    if(req.mode == 0){
        mode_ = DRIVING;
        BCU_DEBUG("set driving mode");
    } else if(req.mode == 1){
        mode_ = ROUND;
        BCU_DEBUG("set spin mode");
    } else if(req.mode == 2){
        mode_ = STOP;
        BCU_DEBUG("set stop mode");
    }
    res.return_data = 1;
    return true;
}
void SerialCsgBcuDriver::ObstacleCallback(const std_msgs::BoolConstPtr &msg)
{
    BCU_DEBUG("ObstacleCallback()::\ndata is %d",static_cast<int>(msg->data));
    if(msg->data){
        csgmsg_.obstacle = 1;
    } else{
        csgmsg_.obstacle = 0;
    }
    obstacle_callback_count_++;
    obstacle_callback_ = ros::Time::now();
    BCU_DEBUG("obstacle_callback_count_ is : %d, obstacle_callback_time is %.4f",
             obstacle_callback_count_, obstacle_callback_.toSec());
    csg_msg_pub_count_ = obstacle_callback_count_;
    csg_msg_pub_ = obstacle_callback_;
    BCU_DEBUG("csg_msg_pub_count_ is : %d, csg_msg_pub_time is %.4f",
             csg_msg_pub_count_, csg_msg_pub_.toSec());
}

int SerialCsgBcuDriver::Init(BcuParamPtr ptr)
{
    bcu_param_ptr = ptr;
    if(!serial_port_.Open(bcu_param_ptr->com.port_id,
                          serial_csg::BAUDRATE,
                          serial_csg::DATABITS,
                          serial_csg::PARITY)) {
        is_inited_ = FALSE;
        return FALSE;
    }
    is_inited_ = TRUE;
    ptr_thread_recv_loop_ =
            new boost::thread(boost::bind(&SerialCsgBcuDriver::ReceiveLoop, this));
    return TRUE;
}

int SerialCsgBcuDriver::Stop()
{
    MotorSpdArray spd;
    spd.push_back(1.0);
    spd.push_back(0.0);
    spd.push_back(0.0);
    SetMotorSpd(spd);
    return TRUE;
}

int SerialCsgBcuDriver::GetMotorSpd(MotorSpdArray& rpm)
{
    if(!is_inited_) {
        BCU_DEBUG("Not inited.");
        return FALSE;
    }
    rpm.resize(motor_cnt_);
    for (int i=0;i<motor_cnt_;i++) {
        rpm[i] = 1.0 * motor_data_.speed_rpm[i];
        BCU_DEBUG("Bcu rpm[%d]: %.2f",i, rpm[i]);
    }
    return TRUE;
}

int SerialCsgBcuDriver::GetSteerAngle(float& angle, wizrobo_npu::SteerEncLocation& location)
{
    if(!is_inited_) {
        BCU_DEBUG("Not inited.");
        return FALSE;
    }
    if (ABS(angle_data_.angle_deg[back_left_i]) > 200 ) {
        angle = 0.0;
        location = wizrobo_npu::SteerEncLocation::CENTER;
    } else if (angle_data_.angle_deg[front_left_i] > 0) {
        angle = angle_data_.angle_deg[front_left_i] * 0.01;
        location = wizrobo_npu::SteerEncLocation::LEFT;
    } else {
        angle = angle_data_.angle_deg[front_right_i] * 0.01;
        location = wizrobo_npu::SteerEncLocation::RIGHT;
    }
    return TRUE;
}

int SerialCsgBcuDriver::SetMotorSpd(const MotorSpdArray& spd)
{
    if(!is_inited_) {
        BCU_DEBUG("Not inited.");
        return FALSE;
    }

    int function_word, data_length;
    unsigned char *ptr_data = NULL;
    MotionSpinAround motion_spin_around;
    MotionCarlike motion_car_like;

    if(spd[1] != 0 && spd[2] != 0){
        mode_ = DRIVING;
    }

    switch (mode_) {
    case DRIVING:
        /* spd[0]: 1.0 means carlike mode, -1.0 means spin_around mode *
         * spd[1]: indicate angle of front wheels                      *
         * spd[2]: indicate rpms of back wheels                        */
        if (spd[0] < 0) {
            /* spin around */
            motion_spin_around.speed = ENDIAN_EXCHANGE_HALFWORD(-static_cast<short>(spd[2]));
            BCU_DEBUG("spin around -> speed: %.2f/%02d.",
                      spd[2], static_cast<short>(spd[2]));
            function_word = MOTION_SPIN_AROUND;
            data_length = MOTION_SPINAROUND_LENGTH;
            ptr_data = (unsigned char*)&motion_spin_around;
            steer_location_ = wizrobo_npu::SteerEncLocation::CENTER;
        } else {
            /* car like */
            motion_car_like.angle = static_cast<char>(RAD2DEG(spd[1]));
            motion_car_like.set_speed(static_cast<short>(spd[2]));
            BCU_DEBUG("car like -> angle: %.2f/%02d speed: %.2f/%02d.",
                      RAD2DEG(spd[1]), motion_car_like.angle,
                    spd[2], static_cast<short>(spd[2]));
            function_word = MOTION_CARLIKE;
            data_length = MOTION_CARLIKE_LENGTH;
            ptr_data = (unsigned char*)&motion_car_like;
            steer_location_ = (spd[1]>0.0)
                    ?wizrobo_npu::SteerEncLocation::RIGHT
                    :wizrobo_npu::SteerEncLocation::LEFT;
        }
        break;
    case ROUND:
        function_word = MOTION_SPIN_AROUND;
        data_length = MOTION_SPINAROUND_LENGTH;
        motion_spin_around.speed = ENDIAN_EXCHANGE_HALFWORD(-static_cast<short>(spd[2]));
        ptr_data = (unsigned char*)&motion_spin_around;
        break;
    case STOP:
        function_word = 2;
        motion_spin_around.speed = ENDIAN_EXCHANGE_HALFWORD(-static_cast<short>(0));
        ptr_data = (unsigned char*)&motion_spin_around;
        data_length = 2;
        break;
    default:
        break;
    }
    int total_length = MakeFrame(function_word, ptr_data, data_length);
    WriteCmd(send_buffer_, total_length);
    for (int i=0;i<total_length;i++) {
        BCU_DEBUG("Sendbuffer: %02X ", send_buffer_[i]);
    }
    return TRUE;
}

int SerialCsgBcuDriver::GetMotorEnc(MotorEncArray& ticks)
{
    if(!is_inited_) {
        BCU_DEBUG("Not inited.");
        return FALSE;
    }
    ticks.resize(motor_cnt_);
    for (int i=0;i<motor_cnt_;i++) {
        ticks[i] = encoder_data_.ticks[i];
    }
    return TRUE;
}

int SerialCsgBcuDriver::WriteCmd(unsigned char* ptr, int length)
{
    serial_port_.ClearReadBuffer();
    if (serial_port_.Write((char *)ptr, length) < length) {
        BCU_ERROR("Writing command failed. Please check serial port.");
        while(!serial_port_.Open(bcu_param_ptr->com.port_id,
                                 serial_csg::BAUDRATE,
                                 serial_csg::DATABITS,
                                 serial_csg::PARITY)) {
            BCU_DEBUG("retrying...");
            WR_SLEEP_MS(bcu_param_ptr->com.cmd_delay_ms);
        }
        BCU_INFO("csg driver reconnected");
        return FALSE;
    }
    return TRUE;
}

int SerialCsgBcuDriver::MakeFrame(int func_index, unsigned char* ptr_data, int data_length)
{
    Header *ptr_header = (Header *)(&(send_buffer_[0]));
    ptr_header->flag_aaaa = FLAG_FAAA;
    ptr_header->function = func_index;
    ptr_header->data_length = data_length;
    unsigned char *ptr_char = &(ptr_header->data_first_char);
    for (int i=0;i<data_length;i++) {
        (*ptr_char) = (*ptr_data);
        ptr_char++;
        ptr_data++;
    }
    int frame_length = HEADER_LENGTH + data_length;
    int total_length = frame_length + 1;
    (*ptr_char) = CheckSum(&(send_buffer_[0]), frame_length);
#if 0
    for (int i=0;i<total_length;i++) {
        printf("%02X ", send_buffer_[i]);
    }
    printf("\n");
#endif
    return total_length;
}

unsigned char SerialCsgBcuDriver::CheckSum(unsigned char *ptr, int length)
{
    unsigned char sum = 0;
    for (int i=0;i<length;i++) {
        sum += ptr[i];
    }
    return sum;
}

void SerialCsgBcuDriver::ProcessBufferData()
{
    int cnt_available_char = receive_buffer_rear_;
    unsigned char *ptr_available_char = &(receive_buffer_[0]);
    Header *ptr_header;

    while (cnt_available_char >= HEADER_LENGTH) {
        ptr_header = (Header *)ptr_available_char;
        if (ptr_header->flag_aaaa != FLAG_AAAA) {
            ptr_available_char++;
            cnt_available_char--;
            continue;
        }
        int frame_length = ptr_header->data_length + HEADER_LENGTH;
        int total_length = frame_length + 1;
        if (cnt_available_char < total_length) {
            break;
        }
        unsigned char org_sum = ptr_available_char[total_length-1];
        unsigned char cal_sum = CheckSum(ptr_available_char, frame_length);
#if 0
        BCU_DEBUG("flag: %04X", ptr_header->flag_aaaa);
        BCU_DEBUG(" fun: %02X", ptr_header->function);
        BCU_DEBUG(" len: %02X", ptr_header->data_length);
        BCU_DEBUG("org_sum/cal_sum: %02X/%02X", org_sum, cal_sum);
        BCU_DEBUG("frame_length: %d", frame_length);

        for (int i=0;i<frame_length;i++) {
            BCU_DEBUG("byte: %02X", ptr_available_char[i]);
        }
#endif
        if (org_sum == cal_sum && ptr_header->function < RFW_THE_END) {
            (rfw_function_map_[ptr_header->function])(&(ptr_header->data_first_char));
        }
        ptr_available_char += total_length;
        cnt_available_char -= total_length;
    }
    unsigned char *_ptr = &(receive_buffer_[0]);
    int offset = ptr_available_char - &(receive_buffer_[0]);
    receive_buffer_rear_ -= offset;
    for (int i=0;i<cnt_available_char;i++) {
        (*_ptr) = (*(_ptr + offset));
        _ptr++;
    }
    (*_ptr) = 0x00;
    csgmsg_.header.stamp = ros::Time::now();
    csgmsg_.header.frame_id = "csg";
    if(csgmsg_.obstacle == 1){/*
        ROS_WARN("ProcessBufferData():: \n csg_msg_pub_count_ is : %d \n obstacle_callback_count_ is : %d",
                 csg_msg_pub_count_, obstacle_callback_count_);
        if(csg_msg_pub_count_ - obstacle_callback_count_ > 100)
        {
    //        csgmsg_.obstacle = 0;
        }
        ROS_WARN("ProcessBufferData()::csg_msg_pub_ is %.3f,\n obstacle_callback_ is %.3f",
                 csg_msg_pub_.toSec(), obstacle_callback_.toSec());*/
        if(csg_msg_pub_ - ros::Duration(1) > obstacle_callback_)
        {
            ROS_ERROR("CALLBACK TIMEOUT csgmsg_.obstacle = 0");
            csgmsg_.obstacle = 0;
        }
    }
    csgpub_.publish(csgmsg_);
    csg_msg_pub_count_++;
    csg_msg_pub_ = ros::Time::now();
}

int SerialCsgBcuDriver::ReceiveLoop()
{
    while (true) {
        WR_SLEEP_MS(bcu_param_ptr->com.ans_delay_ms);
        int byte_cnt = serial_port_.BytesWaiting();
        if(byte_cnt <= 0) {
            continue;
        }
        if ((byte_cnt + receive_buffer_rear_) > receive_buffer_length_ ) {
            byte_cnt = receive_buffer_length_ - receive_buffer_rear_;
        }
        unsigned char *ptr_buffer_rear = &(receive_buffer_[receive_buffer_rear_]);
        byte_cnt = serial_port_.Read((char *)ptr_buffer_rear, byte_cnt);
        if (byte_cnt < 0) {
            BCU_DEBUG("receive_buffer_rear_: %d, byte_cnt: %d.", receive_buffer_rear_, byte_cnt);
            BCU_ERROR("serial port read failed.");
            //continue;
        }
        receive_buffer_rear_ += byte_cnt;
        ProcessBufferData();
    }
    return 0;
}

void SerialCsgBcuDriver::FreshControlInfo(unsigned char *ptr)
{
    control_info_ = *((ControlInfo *)ptr);
#if 1
    BCU_DEBUG("control_info.mode: %d", control_info_.mode);
    BCU_DEBUG("control_info.angle: %d", control_info_.angle);
    BCU_DEBUG("control_info.speed: %d", control_info_.speed);
#endif
    return;
}

void SerialCsgBcuDriver::FreshAngleData(unsigned char *ptr)
{
    angle_data_.angle_deg[0] = ENDIAN_EXCHANGE_HALFWORD(((*((AngleData *)ptr)).angle_deg[0]));
    angle_data_.angle_deg[1] = ENDIAN_EXCHANGE_HALFWORD(((*((AngleData *)ptr)).angle_deg[1]));
    angle_data_.angle_deg[2] = ENDIAN_EXCHANGE_HALFWORD(((*((AngleData *)ptr)).angle_deg[2]));
    angle_data_.angle_deg[3] = ENDIAN_EXCHANGE_HALFWORD(((*((AngleData *)ptr)).angle_deg[3]));
    float angle[4];
    angle[0] = 0.01 * angle_data_.angle_deg[0];
    angle[1] = 0.01 * angle_data_.angle_deg[1];
    angle[2] = 0.01 * angle_data_.angle_deg[2];
    angle[3] = 0.01 * angle_data_.angle_deg[3];
    for(int i=0; i++; i<4)
    {
        csg_angle_.data[i] = angle[i];
    }
//    csg_angle_.data[0] = angle[0];
//    csg_angle_.data[1] = angle[1];
//    csg_angle_.data[2] = angle[2];
//    csg_angle_.data[3] = angle[3];
    csg_angle_pub_.publish(csg_angle_);
#if 1
    BCU_DEBUG("angle_data: %.2f %.2f %.2f %.2f", angle[0], angle[1], angle[2], angle[3]);
#endif
    return;
}

#define ENC_TO_RPM 1

void SerialCsgBcuDriver::FreshMotorData(unsigned char *ptr)
{
    motor_data_.current[0] = ENDIAN_EXCHANGE_HALFWORD(((MotorData *)ptr)->current[0]);
    motor_data_.current[1] = ENDIAN_EXCHANGE_HALFWORD(((MotorData *)ptr)->current[1]);
    motor_data_.current[2] = ENDIAN_EXCHANGE_HALFWORD(((MotorData *)ptr)->current[2]);
    motor_data_.current[3] = ENDIAN_EXCHANGE_HALFWORD(((MotorData *)ptr)->current[3]);

#if !ENC_TO_RPM
    motor_data_.speed_rpm[0] = ENDIAN_EXCHANGE_HALFWORD(((MotorData *)ptr)->speed_rpm[0]);
    motor_data_.speed_rpm[1] = ENDIAN_EXCHANGE_HALFWORD(((MotorData *)ptr)->speed_rpm[1]);
    motor_data_.speed_rpm[2] = ENDIAN_EXCHANGE_HALFWORD(((MotorData *)ptr)->speed_rpm[2]);
    motor_data_.speed_rpm[3] = ENDIAN_EXCHANGE_HALFWORD(((MotorData *)ptr)->speed_rpm[3]);
#endif

#if 1
    BCU_DEBUG("motor_data.current: %d %d %d %d",
              motor_data_.current[0], motor_data_.current[1],
            motor_data_.current[2], motor_data_.current[3]);

    BCU_DEBUG("motor_data.speed_rpm: %d %d %d %d",
              motor_data_.speed_rpm[0], motor_data_.speed_rpm[1],
            motor_data_.speed_rpm[2], motor_data_.speed_rpm[3]);
#endif
    return;
}

void SerialCsgBcuDriver::FreshEncoderData(unsigned char *ptr)
{
    static int static_ticks[4];
#if ENC_TO_RPM
    static ros::Time stamp = ros::Time::now();
    const int ticks_per_cycle = 2048;
    const float _2pi = 3.14159 * 2;
#endif
    encoder_data_ = *((EncoderData *)ptr);
    encoder_data_.ticks[0] = ENDIAN_EXCHANGE_WORD(((EncoderData *)ptr)->ticks[0]);
    encoder_data_.ticks[1] = ENDIAN_EXCHANGE_WORD(((EncoderData *)ptr)->ticks[1]);
    encoder_data_.ticks[2] = ENDIAN_EXCHANGE_WORD(((EncoderData *)ptr)->ticks[2]);
    encoder_data_.ticks[3] = ENDIAN_EXCHANGE_WORD(((EncoderData *)ptr)->ticks[3]);
    BCU_DEBUG("encoder_data: %d %d %d %d", encoder_data_.ticks[0],  encoder_data_.ticks[1], encoder_data_.ticks[2],  encoder_data_.ticks[3]);

#if ENC_TO_RPM
    int ticks_diff[4];
    for (int i=0;i<4;++i) {
        ticks_diff[i] = encoder_data_.ticks[i] - static_ticks[i];
        motor_data_.speed_rpm[i] = 1.0 * ticks_diff[i] / ticks_per_cycle * _2pi / (ros::Time::now() - stamp).toSec();
        BCU_DEBUG("Bcu rpm22222[%d]: %d",i, motor_data_.speed_rpm[i]);
    }
#endif

#if 0
    if (ABS(static_ticks[0]-encoder_data_.ticks[0]) > 10000
            || ABS(static_ticks[1]-encoder_data_.ticks[1]) > 10000
            || ABS(static_ticks[2]-encoder_data_.ticks[2]) > 10000
            || ABS(static_ticks[3]-encoder_data_.ticks[3]) > 10000) {
        int total_length = ((Header *)(ptr - HEADER_LENGTH))->data_length + HEADER_LENGTH + 1;
        std::stringstream data_frame;
        for (int i=0;i<total_length;i++) {
            data_frame << std::hex << std::setw(2) << std::setfill('0')
                       << std::setiosflags(std::ios::uppercase)
                       << static_cast<unsigned int>(receive_buffer_[i])
                       << " ";
        }
        BCU_WARN("|--> %s", data_frame.str().c_str());
    }
#endif



    static_ticks[0] = encoder_data_.ticks[0];
    static_ticks[1] = encoder_data_.ticks[1];
    static_ticks[2] = encoder_data_.ticks[2];
    static_ticks[3] = encoder_data_.ticks[3];



    return;
}

void SerialCsgBcuDriver::FreshSonarData(unsigned char *ptr)
{
    csgmsg_.sonar.resize(0);
    csgmsg_.sonar.clear();
    sonar_data_ = *((SonarData *)ptr);
    for(int i = 0;i < 8 ;i++){
        csgmsg_.sonar.push_back(static_cast<float>(sonar_data_.sonar[i]) * 0.01);
    }
//    csgmsg_.obstacle = static_cast<float>(sonar_data_.obstacle) * 0.1;
    csgmsg_.keep_distance = static_cast<float>(sonar_data_.keeping_distence) * 0.1;
#if 0
    BCU_DEBUG("sonar_data.sonar[0]: %d", sonar_data_.sonar[0]);
    BCU_DEBUG("sonar_data.sonar[1]: %d", sonar_data_.sonar[1]);
    BCU_DEBUG("sonar_data.sonar[2]: %d", sonar_data_.sonar[2]);
    BCU_DEBUG("sonar_data.sonar[3]: %d", sonar_data_.sonar[3]);
    BCU_DEBUG("sonar_data.sonar[4]: %d", sonar_data_.sonar[4]);
    BCU_DEBUG("sonar_data.sonar[5]: %d", sonar_data_.sonar[5]);
    BCU_DEBUG("sonar_data.sonar[6]: %d", sonar_data_.sonar[6]);
    BCU_DEBUG("sonar_data.sonar[7]: %d", sonar_data_.sonar[7]);
    BCU_DEBUG("sonar_data.obstacle: %d", sonar_data_.obstacle);
    BCU_DEBUG("sonar_data.keeping_distence: %d", sonar_data_.keeping_distence);
#endif
    return;
}

void SerialCsgBcuDriver::FreshBatteryVoltageData(unsigned char *ptr)
{
    csgmsg_.vol.resize(0);
    csgmsg_.vol.clear();
    battery_voltage_data_ = *((BatteryVoltageData *)ptr);
    battery_voltage_data_.cell[0] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[0]);
    battery_voltage_data_.cell[1] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[1]);
    battery_voltage_data_.cell[2] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[2]);
    battery_voltage_data_.cell[3] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[3]);
    battery_voltage_data_.cell[4] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[4]);
    battery_voltage_data_.cell[5] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[5]);
    battery_voltage_data_.cell[6] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[6]);
    battery_voltage_data_.cell[7] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[7]);
    battery_voltage_data_.cell[8] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[8]);
    battery_voltage_data_.cell[9] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[9]);
    battery_voltage_data_.cell[10] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[10]);
    battery_voltage_data_.cell[11] = ENDIAN_EXCHANGE_HALFWORD(((BatteryVoltageData *)ptr)->cell[11]);

    for(int i = 0;i < 12;i++){
        csgmsg_.vol.push_back(static_cast<float>(battery_voltage_data_.cell[i]) * 0.001);
    }
#if 0
    BCU_DEBUG("battery_voltage_data.cell[0]: %d", battery_voltage_data_.cell[0]);
    BCU_DEBUG("battery_voltage_data.cell[1]: %d", battery_voltage_data_.cell[1]);
    BCU_DEBUG("battery_voltage_data.cell[2]: %d", battery_voltage_data_.cell[2]);
    BCU_DEBUG("battery_voltage_data.cell[3]: %d", battery_voltage_data_.cell[3]);
    BCU_DEBUG("battery_voltage_data.cell[4]: %d", battery_voltage_data_.cell[4]);
    BCU_DEBUG("battery_voltage_data.cell[5]: %d", battery_voltage_data_.cell[5]);
    BCU_DEBUG("battery_voltage_data.cell[6]: %d", battery_voltage_data_.cell[6]);
    BCU_DEBUG("battery_voltage_data.cell[7]: %d", battery_voltage_data_.cell[7]);
    BCU_DEBUG("battery_voltage_data.cell[8]: %d", battery_voltage_data_.cell[8]);
    BCU_DEBUG("battery_voltage_data.cell[9]: %d", battery_voltage_data_.cell[9]);
    BCU_DEBUG("battery_voltage_data.cell[10]: %d", battery_voltage_data_.cell[10]);
    BCU_DEBUG("battery_voltage_data.cell[11]: %d", battery_voltage_data_.cell[11]);
#endif
    return;
}

void SerialCsgBcuDriver::FreshBatteryManufacturerInfo(unsigned char *ptr)
{
    battery_man_info_ = *((BatteryManufacturerInfo *)ptr);
    battery_man_info_.max_voltage = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->max_voltage);
    battery_man_info_.min_voltage = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->min_voltage);
    battery_man_info_.max_voltage_pos = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->max_voltage_pos);
    battery_man_info_.min_voltage_pos = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->min_voltage_pos);
    battery_man_info_.voltage_diff = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->voltage_diff);
    battery_man_info_.avrg_voltage = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->avrg_voltage);
    battery_man_info_.total_voltage = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->total_voltage);
    battery_man_info_.charge_current = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->charge_current);
    battery_man_info_.discharge_current = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->discharge_current);
    battery_man_info_.soc = ENDIAN_EXCHANGE_HALFWORD(((BatteryManufacturerInfo *)ptr)->soc);

    csgmsg_.max_voltage = static_cast<float>(battery_man_info_.max_voltage) * 0.001;
    csgmsg_.min_voltage = static_cast<float>(battery_man_info_.min_voltage) * 0.001;
    csgmsg_.max_voltage_pos = battery_man_info_.max_voltage_pos;
    csgmsg_.min_voltage_pos = battery_man_info_.min_voltage_pos;
    csgmsg_.voltage_diff = static_cast<float>(battery_man_info_.voltage_diff) * 0.001;
    csgmsg_.avrg_voltage = static_cast<float>(battery_man_info_.avrg_voltage) * 0.001;
    csgmsg_.total_voltage = static_cast<float>(battery_man_info_.total_voltage) * 0.1;
    csgmsg_.charge_current = static_cast<float>(battery_man_info_.charge_current) * 0.1;
    csgmsg_.discharge_current = static_cast<float>(battery_man_info_.discharge_current) * 0.1;
    csgmsg_.soc = static_cast<float>(battery_man_info_.soc) * 0.01;
    LM_DEBUG("soc: %.4f(%d, %04X)", csgmsg_.soc, battery_man_info_.soc, ((BatteryManufacturerInfo *)ptr)->soc);
#if 0
    BCU_DEBUG("battery_man_info.max_voltage: %d", battery_man_info_.max_voltage);
    BCU_DEBUG("battery_man_info.min_voltage: %d", battery_man_info_.min_voltage);
    BCU_DEBUG("battery_man_info.max_voltage_pos: %d", battery_man_info_.max_voltage_pos);
    BCU_DEBUG("battery_man_info.min_voltage_pos: %d", battery_man_info_.min_voltage_pos);
    BCU_DEBUG("battery_man_info.voltage_diff: %d", battery_man_info_.voltage_diff);
    BCU_DEBUG("battery_man_info.avrg_voltage: %d", battery_man_info_.avrg_voltage);
    BCU_DEBUG("battery_man_info.total_voltage: %d", battery_man_info_.total_voltage);
    BCU_DEBUG("battery_man_info.charge_current: %d", battery_man_info_.charge_current);
    BCU_DEBUG("battery_man_info.discharge_current: %d", battery_man_info_.discharge_current);
    BCU_DEBUG("battery_man_info.soc: %d", battery_man_info_.soc);
#endif
    return;
}

void SerialCsgBcuDriver::FreshBatteryTemperatureData(unsigned char *ptr)
{
    csgmsg_.temperature.resize(0);
    csgmsg_.temperature.clear();
    battery_temp_ = *((BatteryTemperatureData *)ptr);
    battery_temp_.temperature[0] = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->temperature[0]);
    battery_temp_.temperature[1] = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->temperature[1]);
    battery_temp_.temperature[2] = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->temperature[2]);
    battery_temp_.temperature[3] = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->temperature[3]);
    battery_temp_.temperature[4] = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->temperature[4]);
    battery_temp_.temperature[5] = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->temperature[5]);
    battery_temp_.max_temperature = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->max_temperature);
    battery_temp_.min_temperature = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->min_temperature);
    battery_temp_.avrg_temperature = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->avrg_temperature);
    battery_temp_.envirment_temperature = ENDIAN_EXCHANGE_HALFWORD(((BatteryTemperatureData *)ptr)->envirment_temperature);

    for(int i = 0; i < 6; i++){
        csgmsg_.temperature.push_back(static_cast<float>(battery_temp_.temperature[i]) * 0.1);
    }
    csgmsg_.max_temperature = static_cast<float>(battery_temp_.max_temperature) * 0.1;
    csgmsg_.min_temperature = static_cast<float>(battery_temp_.min_temperature) * 0.1;
    csgmsg_.avrg_temperature = static_cast<float>(battery_temp_.avrg_temperature) *0.1;
    csgmsg_.envirment_temperature = static_cast<float>(battery_temp_.envirment_temperature) * 0.1;
#if 0
    BCU_DEBUG("battery_temp.temperature[0]: %d", battery_temp_.temperature[0]);
    BCU_DEBUG("battery_temp.temperature[1]: %d", battery_temp_.temperature[1]);
    BCU_DEBUG("battery_temp.temperature[2]: %d", battery_temp_.temperature[2]);
    BCU_DEBUG("battery_temp.temperature[3]: %d", battery_temp_.temperature[3]);
    BCU_DEBUG("battery_temp.temperature[4]: %d", battery_temp_.temperature[4]);
    BCU_DEBUG("battery_temp.temperature[5]: %d", battery_temp_.temperature[5]);
    BCU_DEBUG("battery_temp.max_temperature: %d", battery_temp_.max_temperature);
    BCU_DEBUG("battery_temp.min_temperature: %d", battery_temp_.min_temperature);
    BCU_DEBUG("battery_temp.avrg_temperature: %d", battery_temp_.avrg_temperature);
    BCU_DEBUG("battery_temp.envirment_temperature: %d", battery_temp_.envirment_temperature);
#endif
    return;
}

void SerialCsgBcuDriver::FreshMotionData(unsigned char *ptr)
{
    motion_data_ = *((MotionData *)ptr);
#if 1
    BCU_DEBUG("motion_data.rc: %d", motion_data_.rc);
    BCU_DEBUG("motion_data.wr: %d", motion_data_.wr());
#endif
    return;
}

void SerialCsgBcuDriver::FreshErrorCode(unsigned char *ptr)
{
    error_code_ = *((ErrorCode *)ptr);
#if 1
    BCU_DEBUG("error_code.code: %d", error_code_.code);
#endif
    return;
}

}}  // namespace
