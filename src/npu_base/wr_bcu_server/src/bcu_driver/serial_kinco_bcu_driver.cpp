#include "serial_kinco/serial_kinco_bcu_driver.h"
#include <stdio.h>
namespace wizrobo { namespace bcu_driver {

const int MOTOR_ID_MAP[2][2] = {
    {1, 2},
    {2, 1}
};
const int KINCO_MOTOR_ID_MAP_EXCHANGE[2] = {2, 1};

SerialKincoBcuDriver::SerialKincoBcuDriver()
    : motor_spd_ctl_mode_(serial_kinco::TRAPEZOID_MODE)
    , motor_acc_rps2_(10)
    , motor_dec_rps2_(20)
{

}

SerialKincoBcuDriver::~SerialKincoBcuDriver()
{
    if (is_inited_)
    {
        Stop();
        Enable(false);
    }
    if (serial_port_.IsOpen() == TRUE)
    {
        serial_port_.Close();
    }
}

int SerialKincoBcuDriver::Init(BcuParamPtr ptr)
{
    WR_INFO("SerialKincoBcuDriver::Init()");
    is_inited_ = FALSE;
    bcu_param_ptr = ptr;
    int retry_cnt = bcu_param_ptr->com.cmd_retry_num;
    while (retry_cnt > 0)
    {
        retry_cnt--;
        if (InitOnce() == TRUE)
        {
            is_inited_ = TRUE;
            offset_ticks_.resize(bcu_param_ptr->motor.motor_num);
            for (int i=0;i<bcu_param_ptr->motor.motor_num;i++)
            {
                offset_ticks_[i] = 0;
            }
            return TRUE;
        }
        else
        {
            WR_DEBUG("Try again...(%d)",retry_cnt);
        }
        WR_SLEEP_MS(bcu_param_ptr->com.cmd_delay_ms);
    }
    WR_ERROR("Init() failed: TIMEOUT");
    return FALSE;
}

int SerialKincoBcuDriver::SetMotorParam()
{
    MotorParam& motor_param = bcu_param_ptr->motor;
    WR_DEBUG("SerialKincoBcuDriver::SetMotorParam()");
    offset_ticks_.resize(motor_param.motor_num);
    if ((motor_spd_ctl_mode_ != serial_kinco::TRAPEZOID_MODE))
    {
        WR_ERROR("SetMotorParam() not in TRAPEZOID_MODE");
        return FALSE;
    }
    motor_acc_rps2_ = 1.0 * motor_param.motor_max_spd_rpm / 60 / motor_param.max_acc_time_s;
    motor_dec_rps2_ = 1.0 * motor_param.motor_max_spd_rpm / 60 / motor_param.max_dec_time_s;
    if (!SetMotorAcc_(motor_acc_rps2_))
    {
        WR_ERROR("SetMotorParam() failed at SetMotorAcc_()");
        return FALSE;
    }
    if (!SetMotorDec_(motor_dec_rps2_))
    {
        WR_ERROR("SetMotorParam() failed at SetMotorDec_()");
        return FALSE;
    }
    if (!Stop())
    {
        WR_ERROR("SetMotorParam() failed at Stop()");
        return FALSE;
    }

    return TRUE;
}

int SerialKincoBcuDriver::Enable(bool on)
{
    MotorParam& motor_param = bcu_param_ptr->motor;
    WR_DEBUG("SerialKincoBcuDriver::Enable()");
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("Enable() failed at CheckPort()");
        return FALSE;
    }
    byte cmd[serial_kinco::MAX_CMD_LEN];
    int index = (motor_param.end_left_right_switch==false)?0:1;
    const int *id_map = serial_kinco::MOTOR_ID_MAP[index];
    for (int i = 0; i < motor_param.motor_num; i++)
    {
        int motor_id = id_map[i];
        int idx = FillCmdHead_(cmd, motor_id, serial_kinco::SET_MTR_ENB);
        idx += ReverseFillBytesInt(cmd, idx, 4, static_cast<int>(on ? serial_kinco::MTR_PWR_ON : serial_kinco::MTR_PWR_OFF));
        idx = FillCmdEnd_(cmd, idx);
        if (!Communicate_(cmd, idx + 1))
        {
            WR_ERROR("Enable()#%d failed at Communicate()", motor_id);
            return FALSE;
        }
    }
    return TRUE;
}

int SerialKincoBcuDriver::Brake(bool on)
{
    WR_DEBUG("SerialKincoBcuDriver::Brake()");
    MotorSpdArray rpms(bcu_param_ptr->motor.motor_num, 0);
    return SetMotorSpd(rpms);
}

int SerialKincoBcuDriver::Stop()
{
    WR_DEBUG("SerialKincoBcuDriver::Stop()");
    MotorSpdArray rpms(bcu_param_ptr->motor.motor_num, 0);
    return SetMotorSpd(rpms);
}

int SerialKincoBcuDriver::GetMotorSpd(MotorSpdArray& rpms)
{
    WR_DEBUG_ONCE("SerialKincoBcuDriver::GetMotorSpd()");
    MotorParam& motor_param = bcu_param_ptr->motor;
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetMotorSpd() failed at CheckPort()");
        return FALSE;
    }

    byte cmd[serial_kinco::MAX_CMD_LEN];
    rpms.resize(motor_param.motor_num);
    int index = (motor_param.end_left_right_switch==false)?0:1;
    const int *id_map = serial_kinco::MOTOR_ID_MAP[index];
    for (int i = 0; i < rpms.size(); i++)
    {
        int motor_id = id_map[i];
        int idx = FillCmdHead_(cmd, motor_id, serial_kinco::GET_MTR_SPD);
        idx += ReverseFillBytesInt(cmd, idx, 4, 0);
        idx = FillCmdEnd_(cmd, idx);
        serial_kinco::AnsArrayType ans;
        if (!Communicate_(cmd, idx + 1, ans))
        {
            WR_ERROR("GetMotorSpd()#%d failed at Communicate()", motor_id);
            return FALSE;
        }
        if (ParseMotorSpd_(ans, rpms[i]) == FALSE)
        {
            WR_ERROR("GetMotorSpd()#%d failed at ParseMotorSpd()", motor_id);
            return FALSE;
        }
        int dir = (motor_param.motor_dir_str[i] == '-') ? -1 : 1;
        rpms[i] *= dir;
    }
    return TRUE;
}

int SerialKincoBcuDriver::SetMotorSpd(const MotorSpdArray& rpms)
{
    MotorParam& motor_param = bcu_param_ptr->motor;
    WR_DEBUG_ONCE("SerialKincoBcuDriver::SetMotorSpd()");
    WR_ASSERT(rpms.size() > 0 && rpms.size() == motor_param.motor_num);
    if (!is_inited_)
    {
        bool is_stop = true;
        for (int i = 0; i < rpms.size(); i++)
        {
            if (rpms[i] != 0)
            {
                is_stop = false;
                break;
            }
        }
        if (!is_stop)
        {
            WR_ERROR("Not inited.");
            return FALSE;
        }
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetMotorSpd() failed at CheckPort()");
        return FALSE;
    }

    byte cmd[serial_kinco::MAX_CMD_LEN];
    int index = (motor_param.end_left_right_switch==false)?0:1;
    const int *id_map = serial_kinco::MOTOR_ID_MAP[index];
    for (int i = 0; i < rpms.size(); i++)
    {
        int motor_id = id_map[i];
        int dir = (motor_param.motor_dir_str[i] == '-') ? -1 : 1;
        int idx = FillCmdHead_(cmd, motor_id, serial_kinco::SET_MTR_SPD);
        int ival = dir * rpms[i] * 512 * 4096 / 1857;
        idx += ReverseFillBytesInt(cmd, idx, 4, ival);
        idx = FillCmdEnd_(cmd, idx);
        if (!Communicate_(cmd, idx + 1))
        {
            WR_ERROR("SetMotorSpd()#%d failed at Communicate()", motor_id);
            return FALSE;
        }
    }
    return TRUE;
}
int SerialKincoBcuDriver::SwpMotorSpd(const MotorSpdArray& cmd_rpms, MotorSpdArray& ans_rpms)
{
    WR_DEBUG_ONCE("SerialKincoBcuDriver::SwpMotorSpd()");
    if (!SetMotorSpd(cmd_rpms))
    {
        WR_ERROR("SwpMotorSpd() failed at SetMotorSpd()");
        return FALSE;
    }
    if (!GetMotorSpd(ans_rpms))
    {
        WR_ERROR("SwpMotorSpd() failed at GetMotorSpd()");
        return FALSE;
    }
    return TRUE;
}

int SerialKincoBcuDriver::GetMotorEnc(MotorEncArray& ticks)
{
    MotorParam& motor_param = bcu_param_ptr->motor;
    WR_DEBUG_ONCE("SerialKincoBcuDriver::GetMotorEnc()");
    if (!is_inited_)
    {
        WR_ERROR("Not inited.");
        return FALSE;
    }
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("GetMotorEnc() failed at CheckPort()");
        return FALSE;
    }

    byte cmd[serial_kinco::MAX_CMD_LEN];
    ticks.resize(motor_param.motor_num);
    int index = (motor_param.end_left_right_switch==false)?0:1;
    const int *id_map = serial_kinco::MOTOR_ID_MAP[index];
    for (int i = 0; i < ticks.size(); i++)
    {
        int motor_id = id_map[i];
        int idx = FillCmdHead_(cmd, motor_id, serial_kinco::GET_MTR_ENC);
        idx += ReverseFillBytesInt(cmd, idx, 4, 0);
        idx = FillCmdEnd_(cmd, idx);
        serial_kinco::AnsArrayType ans;
        if (!Communicate_(cmd, idx + 1, ans))
        {
            WR_ERROR("GetMotorEnc()#%d failed at Communicate()", motor_id);
            return FALSE;
        }
        if (ParseMotorEnc_(ans, ticks[i]) == FALSE)
        {
            WR_ERROR("GetMotorEnc()#%d failed at ParseMotorSpd()", motor_id);
            return FALSE;
        }
        int dir = (motor_param.motor_dir_str[i] == '-') ? -1 : 1;
        ticks[i] *= dir;
        ticks[i] -= offset_ticks_[i];
    }
    return TRUE;
}

int SerialKincoBcuDriver::ClrMotorEnc()
{
    WR_DEBUG("SerialKincoBcuDriver::ClrMotorEnc()");
    MotorEncArray ticks;
    if (!GetMotorEnc(ticks))
    {
        WR_ERROR("ClrMotorEnc() failed at GetEnc().");
        return FALSE;
    }
    offset_ticks_ = ticks;
    return TRUE;
}

int SerialKincoBcuDriver::InitOnce()
{
    WR_DEBUG("SerialKincoBcuDriver::InitOnce()");
    if (serial_port_.IsOpen() == TRUE)
    {
        Stop();
        Enable(false);
        serial_port_.Close();
    }
    serial_port_.Open(bcu_param_ptr->com.port_id, serial_kinco::BAUDRATE,serial_kinco::DATABITS,serial_kinco::PARITY);
    if (CheckPort() == FALSE)
    {
        WR_ERROR("InitOnce() failed at CheckPort()");
        return FALSE;
    }
    if(!ClrAlarm_())
    {
        WR_ERROR("ClrAlarm_() failed at ClrAlarm_()");
        return FALSE;
    }
    if (!SetSpdCtrlMode_(motor_spd_ctl_mode_))
    {
        WR_ERROR("InitOnce() failed at SetSpdCtrlMode_()");
        return FALSE;
    }
    if (motor_spd_ctl_mode_ == serial_kinco::TRAPEZOID_MODE)
    {
        if (!SetMotorAcc_(motor_acc_rps2_))
        {
            WR_ERROR("InitOnce() failed at SetMotorAcc_()");
            return FALSE;
        }
        if (!SetMotorDec_(motor_dec_rps2_))
        {
            WR_ERROR("InitOnce() failed at SetMotorDec_()");
            return FALSE;
        }
    }
    if(!Enable(true))
    {
        WR_ERROR("InitOnce() failed at Enable()");
        return FALSE;
    }
    if (!Stop())
    {
        WR_ERROR("InitOnce() failed at Stop()");
        return FALSE;
    }
    return TRUE;
}

int SerialKincoBcuDriver::CheckPort()
{
    if (serial_port_.IsOpen() == FALSE)
    {
        WR_ERROR("Serial Port Opening failed.");
        return FALSE;
    }
    return TRUE;
}

int SerialKincoBcuDriver::ClrAlarm_()
{
    MotorParam& motor_param = bcu_param_ptr->motor;
    WR_DEBUG("SerialKincoBcuDriver::ClrAlarm_()");
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("ClrAlarm_() failed at CheckPort()");
        return FALSE;
    }

    byte cmd[serial_kinco::MAX_CMD_LEN];
    int index = (motor_param.end_left_right_switch==false)?0:1;
    const int *id_map = serial_kinco::MOTOR_ID_MAP[index];
    for (int i = 0; i < motor_param.motor_num; i++)
    {
        int motor_id = id_map[i];
        int idx = FillCmdHead_(cmd, motor_id, serial_kinco::CLR_MTR_ALM);
        idx += ReverseFillBytesInt(cmd, idx, 4, static_cast<int>(serial_kinco::MTR_CLR_ALM));
        idx = FillCmdEnd_(cmd, idx);
        if (!Communicate_(cmd, idx + 1))
        {
            WR_ERROR("ClrAlarm_()#%d failed at Communicate()", motor_id);
            return FALSE;
        }
    }
    return TRUE;
}

bool SerialKincoBcuDriver::Communicate_(const byte* cmd, int cmd_len)
{
    serial_kinco::AnsArrayType ans;
    return Communicate_(cmd, cmd_len, ans);
}

bool SerialKincoBcuDriver::Communicate_(const byte* cmd, int cmd_len, serial_kinco::AnsArrayType& ans)
{
    ComParam& com_param = bcu_param_ptr->com;
    timeval start, end;
    gettimeofday(&start, NULL);
    bool rlt = WriteCmd(cmd, cmd_len);
    if (com_param.enb_com_data_display)
    {
        WR_INFO("[CMD] %s", str_ext::ByteArrToStr(cmd, cmd_len).c_str());
    }
    if (!rlt)
    {
        WR_ERROR("Communicate() failed at WriteCmd()");
    }
    else
    {
        rlt = ReceiveAns_(ans);
        if (com_param.enb_com_data_display)
        {
            WR_INFO("[ANS] %s", str_ext::ByteQueToStr(ans).c_str());
        }
        if (!rlt)
        {
            WR_ERROR("Communicate() failed at ReceiveAns()");
        }
    }
    gettimeofday(&end, NULL);
    if (!rlt)
    {
        WR_ERROR("[CMD] %s", str_ext::ByteArrToStr(cmd, cmd_len).c_str());
        WR_ERROR("[ANS] %s", str_ext::ByteQueToStr(ans).c_str());
    }
    return rlt;
}

/// cmd
int SerialKincoBcuDriver::WriteCmd(const byte* cmd, int cmd_len)
{
    serial_port_.ClearReadBuffer();
    int len = serial_port_.Write((char*)cmd, cmd_len);
    if (len < cmd_len)
    {
        WR_ERROR("Writing command failed: len < cmd_len");
        return FALSE;
    }
    return TRUE;
}

int SerialKincoBcuDriver::FillCmdHead_(byte* cmd, int motor_id, serial_kinco::CmdType type)
{
    int idx = 0;
    cmd[idx++] = motor_id;
    idx += FillBytesInt(cmd, idx, 4, static_cast<int>(type));
    return idx;
}

int SerialKincoBcuDriver::FillCmdEnd_(byte* cmd, int idx)
{
    cmd[idx] = CalNegChkSum_(cmd, idx);// add 0xChkSum
    return idx;
}

int SerialKincoBcuDriver::FillBytesInt(byte* arr, int idx, int len, const int& num)
{
    int tmp_num = num;
    for (int i = 0; i < len; i++)
    {
        arr[idx + len - 1 - i] = tmp_num & 0xFF;
        if (i < len - 1)
        {
            tmp_num >>= 8;
        }
    }
    return len;
}

int SerialKincoBcuDriver::ReverseFillBytesInt(byte* arr, int idx, int len, const int& num)
{
    int tmp_num = num;
    for (int i = 0; i < len; i++)
    {
        arr[idx + i] = tmp_num & 0xFF;
        if (i < len - 1)
        {
            tmp_num >>= 8;
        }
    }
    return len;
}

byte SerialKincoBcuDriver::CalNegChkSum_(byte* cmd, int cmd_len)
{
    byte sum = 0;
    for(int i = 0; i < cmd_len; i++)
    {
        sum += cmd[i];
    }
    sum = (-sum) & 0xFF;
    return sum;
}

/// cmd
/// ans
int SerialKincoBcuDriver::ReceiveAns_(serial_kinco::AnsArrayType& ans)
{
    ComParam& com_param = bcu_param_ptr->com;
    char buf[200];
    ans.clear();
    int len = 0;
    int retry_cnt = 0; ;
    int head_idx, chksum_idx;
    while(retry_cnt < com_param.ans_retry_num)
    {
        WR_SLEEP_MS(com_param.ans_delay_ms);
        if ((len = serial_port_.BytesWaiting()))
        {
            int read_len = serial_port_.Read(buf,len);
            for (int i = 0; i < read_len; i++)
            {
                ans.push_back(static_cast<byte>(buf[i] & 0xFF));
            }
            if (ans.size() >= serial_kinco::ANS_LEN)// received enough bytes
            {
                break;

            }
        }
        retry_cnt++;
    }
    if (retry_cnt < com_param.ans_retry_num)
    {
        serial_port_.ClearReadBuffer();
        return TRUE;
    }
    else
    {
        WR_ERROR("ReceiveAns() failed: TIMEOUT");
        return FALSE;
    }
}

int SerialKincoBcuDriver::SetSpdCtrlMode_(serial_kinco::SpdCtlMode mode)
{
    MotorParam& motor_param = bcu_param_ptr->motor;
    WR_DEBUG("SerialKincoBcuDriver::SetSpdCtrlMode_()");
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetMotorCtrlMode() failed at CheckPort()");
        return FALSE;
    }
    byte cmd[serial_kinco::MAX_CMD_LEN];
    int index = (motor_param.end_left_right_switch==false)?0:1;
    const int *id_map = serial_kinco::MOTOR_ID_MAP[index];
    for (int i = 0; i < motor_param.motor_num; i++)
    {
        int motor_id = id_map[i];
        int idx = FillCmdHead_(cmd, motor_id, serial_kinco::SET_SPD_MOD);
        if (mode == serial_kinco::INSTANT_MODE)
        {
            idx += ReverseFillBytesInt(cmd, idx, 4, static_cast<int>(serial_kinco::MTR_INS_SPD_MOD));
        }
        else if (mode == serial_kinco::TRAPEZOID_MODE)
        {
            idx += ReverseFillBytesInt(cmd, idx, 4, static_cast<int>(serial_kinco::MTR_TRP_SPD_MOD));
        }
        idx = FillCmdEnd_(cmd, idx);
        if (!Communicate_(cmd, idx + 1))
        {
            WR_ERROR("SetSpdCtrlMode_()#%d failed at Communicate()", motor_id);
            return FALSE;
        }
    }
    return TRUE;
}

int SerialKincoBcuDriver::SetMotorAcc_(float rps2)
{
    MotorParam& motor_param = bcu_param_ptr->motor;
    WR_DEBUG("SerialKincoBcuDriver::SetMotorAcc_()");
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetMotorAcc_() failed at CheckPort()");
        return FALSE;
    }

    byte cmd[serial_kinco::MAX_CMD_LEN];
    int index = (motor_param.end_left_right_switch==false)?0:1;
    const int *id_map = serial_kinco::MOTOR_ID_MAP[index];
    for (int i = 0; i < motor_param.motor_num; i++)
    {
        int motor_id = id_map[i];
        int idx = FillCmdHead_(cmd, motor_id, serial_kinco::SET_MTR_SPD);
        int ival = rps2 * 65536 * 4096 / 4000000;
        idx += ReverseFillBytesInt(cmd, idx, 4, ival);
        idx = FillCmdEnd_(cmd, idx);
        if (!Communicate_(cmd, idx + 1))
        {
            WR_ERROR("SetMotorAcc_()%d failed at Communicate()", motor_id);
            return FALSE;
        }
    }
    return TRUE;
}

int SerialKincoBcuDriver::SetMotorDec_(float rps2)
{
    MotorParam& motor_param = bcu_param_ptr->motor;
    WR_DEBUG("SerialKincoBcuDriver::SetMotorDec()");
    if (IS_ALWAYS_CHECK_PORT && !CheckPort())
    {
        WR_ERROR("SetMotorDec() failed at CheckPort()");
        return FALSE;
    }

    byte cmd[serial_kinco::MAX_CMD_LEN];
    int index = (motor_param.end_left_right_switch==false)?0:1;
    const int *id_map = serial_kinco::MOTOR_ID_MAP[index];
    for (int i = 0; i < motor_param.motor_num; i++)
    {
        int motor_id = id_map[i];
        int idx = FillCmdHead_(cmd, motor_id, serial_kinco::SET_MTR_SPD);
        int ival = rps2 * 65536 * 4096 / 4000000;
        idx += ReverseFillBytesInt(cmd, idx, 4, ival);
        idx = FillCmdEnd_(cmd, idx);
        if (!Communicate_(cmd, idx + 1))
        {
            WR_ERROR("SetMotorDec()#%d failed at Communicate()", motor_id);
            return FALSE;
        }
    }
    return TRUE;
}

int SerialKincoBcuDriver::ParseMotorData_(const serial_kinco::AnsArrayType& ans, double& data)
{
    if (ans.size() != serial_kinco::ANS_LEN)
    {
        WR_ERROR("ans.size() != ANS_LEN");
        return FALSE;
    }
    int iVal = 0;
    switch (ans[serial_kinco::ANS_BIT_CODE_IDX])
    {
    case serial_kinco::ANS_32BIT_CODE:
    {
        //WR_DEBUG("[ANS] [Hex32] ");
        int val32 = 0;
        for (int i = serial_kinco::ANS_DATA_LEN - 1; i >= 0; i--)
        {
            val32 |= ans[i + serial_kinco::ANS_DATA_START];
            if (i > 0)
            {
                val32 = val32 << 8;
            }
        }
        iVal = val32;
    }
        break;
    case serial_kinco::ANS_16BIT_CODE:
    {
        //WR_DEBUG("[ANS] [Hex16] ");
        short val16 = 0;
        for (int i = serial_kinco::ANS_DATA_LEN/2 - 1; i >= 0; i--)
        {
            val16 |= ans[i + serial_kinco::ANS_DATA_START];
            if (i > 0)
            {
                val16 = val16 << 8;
            }
        }
        iVal = val16;
    }
        break;
    case serial_kinco::ANS_8BIT_CODE:
    {
        //WR_DEBUG("[ANS] [Hex8] ");
        char val8 = 0;
        for (int i = serial_kinco::ANS_DATA_LEN/4 - 1; i >= 0; i--)
        {
            val8 |= ans[i + serial_kinco::ANS_DATA_START];
            if (i > 0)
            {
                val8 = val8 << 8;
            }
        }
        iVal = val8;
    }
        break;
    default:
        WR_ERROR("Invalid ANS_BIT_CODE_IDX");
        return FALSE;
    }

    //WR_DEBUG("[ANS] [Dec] %d\n", iVal);
    data = iVal;
    return TRUE;
}

int SerialKincoBcuDriver::ParseMotorSpd_(const serial_kinco::AnsArrayType& ans, float& rpm)
{
    double data;
    int rlt = ParseMotorData_(ans, data);
    if (rlt == TRUE)
    {
        rpm = data;
    }
    return rlt;
}

int SerialKincoBcuDriver::ParseMotorEnc_(const serial_kinco::AnsArrayType& ans, int& tick)
{
    double data;
    int rlt = ParseMotorData_(ans, data);
    if (rlt == TRUE)
    {
        tick = static_cast<int>(data);
    }
    return rlt;
}
}}// namespace
