#include <ros/ros.h>
#include <nodelet/nodelet.h>
#include <pluginlib/class_list_macros.h>

#include "bcu_server.h"

namespace wizrobo {

class BcuServerNodelet : public nodelet::Nodelet
{
public:
    BcuServerNodelet()
    {
    }
    ~BcuServerNodelet()
    {
        if(p_namespace_ != nullptr){
            delete p_namespace_;
        }
        if(p_bcu_ != nullptr){
            delete p_bcu_;
        }
    }

    virtual void onInit()
    {
        p_namespace_ = nullptr;
        p_bcu_ = nullptr;

        ros::NodeHandle& private_nh = this->getMTPrivateNodeHandle();
        const std::string& s_prefix = private_nh.getNamespace();
        int length = s_prefix.size() + 1;
        p_namespace_ = new char [length];
        memset(p_namespace_, 0, length);
        memcpy(p_namespace_, s_prefix.c_str(), s_prefix.size());
        int argc = 1;
        char **argv = &p_namespace_;
        LM_INFO("Name Space: %s", p_namespace_);
        p_bcu_ = new wizrobo::BcuServer(argc, argv);

        p_bcu_->Init();
        if (p_bcu_->IsInited()) {
            p_bcu_->Run();
        }
        return;
    }

private:
    char *p_namespace_;

    wizrobo::BcuServer *p_bcu_;
};

}

PLUGINLIB_EXPORT_CLASS(wizrobo::BcuServerNodelet, nodelet::Nodelet);
