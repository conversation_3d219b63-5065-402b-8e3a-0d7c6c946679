cmake_minimum_required(VERSION 2.8.3)
project(wr_bcu_server)

## Find catkin macros and libraries
## if COMPONENTS list like find_package(catkin REQUIRED COMPONENTS xyz)
## is used, also find other catkin packages
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  nodelet
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs 
  dynamic_reconfigure
  tf
  angles
  wr_npu_core
  wr_npu_msgs
  wr_npu_ice
)
find_package(Boost REQUIRED signals)
find_package(Ice REQUIRED Ice IceUtil)

set(CMAKE_CXX_FLAGS "-std=c++0x ${CMAKE_CXX_FLAGS}")

## System dependencies are found with CMake's conventions
# find_package(Boost REQUIRED COMPONENTS system)


## Uncomment this if the package has a setup.py. This macro ensures
## modules and global scripts declared therein get installed
## See http://ros.org/doc/api/catkin/html/user_guide/setup_dot_py.html
# catkin_python_setup()

################################################
## Declare ROS messages, services and actions ##
################################################

## To declare and build messages, services or actions from within this
## package, follow these steps:
## * Let MSG_DEP_SET be the set of packages whose message types you use in
##   your messages/services/actions (e.g. std_msgs, actionlib_msgs, ...).
## * In the file package.xml:
##   * add a build_depend and a run_depend tag for each package in MSG_DEP_SET
##   * If MSG_DEP_SET isn't empty the following dependencies might have been
##     pulled in transitively but can be declared for certainty nonetheless:
##     * add a build_depend tag for "message_generation"
##     * add a run_depend tag for "message_runtime"
## * In this file (CMakeLists.txt):
##   * add "message_generation" and every package in MSG_DEP_SET to
##     find_package(catkin REQUIRED COMPONENTS ...)
##   * add "message_runtime" and every package in MSG_DEP_SET to
##     catkin_package(CATKIN_DEPENDS ...)
##   * uncomment the add_*_files sections below as needed
##     and list every .msg/.srv/.action file to be processed
##   * uncomment the generate_messages entry below
##   * add every package in MSG_DEP_SET to generate_messages(DEPENDENCIES ...)

## Generate messages in the 'msg' folder
# add_message_files(
#   FILES
# )

## Generate services in the 'srv' folder
# add_service_files(
#   FILES
#   Service1.srv
#   Service2.srv
# )

## Generate actions in the 'action' folder
# add_action_files(
#   FILES
#   Action1.action
#   Action2.action
# )

## Generate added messages and services with any dependencies listed here
# generate_messages(
#   DEPENDENCIES
#   geometry_msgs
#   std_msgs
# )

generate_dynamic_reconfigure_options(
    cfg/BcuServer.cfg
    cfg/PidParam.cfg
)

###################################
## catkin specific configuration ##
###################################
## The catkin_package macro generates cmake config files for your package
## Declare things to be passed to dependent projects
## INCLUDE_DIRS: uncomment this if you package contains header files
## LIBRARIES: libraries you create in this project that dependent projects also need
## CATKIN_DEPENDS: catkin_packages dependent projects also need
## DEPENDS: system dependencies of this project that dependent projects also need
catkin_package(
  INCLUDE_DIRS include include/bcu_driver
  LIBRARIES bcu_driver bcu_server bcu_server_nodelet bcu_scrubber_nodelet
  CATKIN_DEPENDS message_runtime roscpp rospy nodelet
            std_msgs sensor_msgs geometry_msgs nav_msgs dynamic_reconfigure
            tf angles wr_npu_core wr_npu_msgs wr_npu_ice
#  DEPENDS system_lib
)

###########
## Build ##
###########

## Specify additional locations of header files
## Your package locations should be listed before other locations
 include_directories(
  include
  include/bcu_driver
  ${WR_LOCK_INCLUDE}
  ${catkin_INCLUDE_DIRS}
  ${ICE_INCLUDE_DIRS}
)

## Declare a cpp library
# add_library(NPU
#   src/${PROJECT_NAME}/NPU.cpp
# )

add_library(bcu_driver
    src/bcu_driver/serial_csg_bcu_driver.cpp
    src/bcu_driver/serial_kinco_bcu_driver.cpp
    src/bcu_driver/serial_keya_bcu_driver.cpp
    src/bcu_driver/serial_fdk_bcu_driver.cpp
    src/bcu_driver/serial_amps_bcu_driver.cpp
    src/bcu_driver/serial_zx_bcu_driver.cpp
    src/bcu_driver/serial_hex_nbox_driver.cpp
    src/bcu_driver/serial_fake_bcu_driver.cpp
    src/bcu_driver/bcu_driver_serial_port.cpp
    src/bcu_driver/serial_port/serial_port_ubuntu.cpp
)

add_library(bcu_server
    src/bcu_server.cpp
)

add_library(bcu_scrubber
    src/bcu_scrubber.cpp
)

add_library(bcu_server_nodelet
    src/bcu_server_nodelet.cpp
)

add_library(bcu_scrubber_nodelet
    src/bcu_scrubber_nodelet.cpp
)

add_executable(bcu_server_node 
    src/bcu_server_node.cpp
)

add_executable(bcu_scrubber_node
    src/bcu_scrubber_node.cpp
)

add_dependencies(bcu_driver
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

add_dependencies(bcu_server
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

add_dependencies(bcu_scrubber
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

add_dependencies(bcu_server_nodelet
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

add_dependencies(bcu_scrubber_nodelet
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

add_dependencies(bcu_server_node
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

add_dependencies(bcu_scrubber_node
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

target_link_libraries(bcu_driver
   ${catkin_LIBRARIES}
)

target_link_libraries(bcu_server
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  ${Ice_LIBRARIES}
  bcu_driver)

target_link_libraries(bcu_scrubber
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  ${Ice_LIBRARIES}
  bcu_driver
  bcu_server)
target_link_libraries(bcu_server_nodelet
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  ${Ice_LIBRARIES}
  bcu_driver
  bcu_server)
target_link_libraries(bcu_scrubber_nodelet
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  ${Ice_LIBRARIES}
  bcu_driver
  bcu_server
  bcu_scrubber)

target_link_libraries(bcu_server_node
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  ${Ice_LIBRARIES}
  bcu_driver
  bcu_server
  bcu_server_nodelet)

target_link_libraries(bcu_scrubber_node
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  ${Ice_LIBRARIES}
  bcu_driver
  bcu_server
  bcu_scrubber_nodelet)
#############
## Install ##
#############

# all install targets should use catkin DESTINATION variables
# See http://ros.org/doc/api/catkin/html/adv_user_guide/variables.html

## Mark executable scripts (Python etc.) for installation
## in contrast to setup.py, you can choose the destination
# install(PROGRAMS
#   scripts/my_python_script
#   DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

### Mark executables and/or libraries for installation
install(TARGETS bcu_driver bcu_server bcu_scrubber bcu_server_node bcu_scrubber_node bcu_scrubber_nodelet bcu_server_nodelet
 ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
 LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
 RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

### Mark cpp header files for installation
install(DIRECTORY include/
  DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
)


install(FILES plugin/bcu_server_nodelet_plugin.xml
   DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)

install(FILES plugin/bcu_scrubber_nodelet_plugin.xml
   DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)

