#!/usr/bin/env python
# bcu_server_node pid paraments dynamic reconfiguration

PACKAGE='wr_bcu_server'
NODE='bcu_server_node'
FILE_NAME='PidParam'

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

#### pid param
gen.add("enb_pid",bool_t,   0, "enb_pid", False)
gen.add("kp_acc", double_t, 0, "kp_acc", 0, 0)
gen.add("kp_dec", double_t, 0, "kp_dec", 0, 0)
gen.add("ki_acc", double_t, 0, "ki_acc", 0, 0)
gen.add("ki_dec", double_t, 0, "ki_dec", 0, 0)
gen.add("kd_acc", double_t, 0, "kd_acc", 0, 0)
gen.add("kd_dec", double_t, 0, "kd_dec", 0, 0)


exit(gen.generate(PACKAGE, NODE, FILE_NAME))
