#!/usr/bin/env python
# bcu_server_node dynamic reconfiguration

PACKAGE='wr_bcu_server'
NODE='bcu_server_node'
FILE_NAME='BcuServer'

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

#### logger level
logger_level_enum = gen.enum([gen.const("DEBUG",  int_t,  0,  "DEBUG"),
                              gen.const("INFO",   int_t,  1,  "INFO"),
                              gen.const("WARN",   int_t,  2,  "WARN"),
                              gen.const("ERROR",  int_t,  3,  "ERROR"),
                              gen.const("FATAL",  int_t,  4,  "FATAL")], "logger_level_enum")
gen.add("logger_level",  int_t,  0,  "logger_level",  0,  0,  4,  edit_method=logger_level_enum)
gen.add("loop_rate",     int_t,  0,  "loop_rate",     100, 1)

##### driver type
#driver_type_enum = gen.enum([gen.const("SERIAL",     int_t,  0,  "SERIAL"),
#                              gen.const("SERIAL_HEX", int_t,  1,  "SERIAL_HEX"),
#                              gen.const("CAN",        int_t,  2,  "CAN"),
#                              gen.const("ETHERNET",   int_t,  3,  "ETHERNET")], "driver_type_enum")
#gen.add("driver_type",  int_t,  0,  "driver_type",  0,  0,  3,  edit_method=driver_type_enum)

#### com param
gen.add("port_id",        str_t,    0,  "port id",        "/dev/bcu")
gen.add("com_freq",       int_t,    0,  "com_freq",       10,   1)
gen.add("cmd_retry_num",  int_t,    0,  "cmd_retry_num",  10,   1)
gen.add("cmd_delay_ms",   int_t,    0,  "cmd_delay_ms",   100,  1)
gen.add("ans_retry_num",  int_t,    0,  "ans_retry_num",  6,    1)
gen.add("ans_delay_ms",   int_t,    0,  "ans_delay_ms",   5,    1)

#### motor param
control_mode_enum = gen.enum([gen.const("INTEGRATED",  int_t,  0,  "INTEGRATED"),
                              gen.const("COMMANDER",  int_t,  1,  "COMMANDER"),
                              gen.const("SENSOR",     int_t,  2,  "SENSOR")], "control_mode_enum")
gen.add("control_mode", int_t,  0,  "control_mode", 0,  0,  2,  edit_method=control_mode_enum)
gen.add("motor_num",          int_t,  0,  "motor_num",          2,      2)
gen.add("motor_dir_str",      str_t,  0,  "motor_dir_str",   "+-")
gen.add("motor_max_spd_rpm",  int_t,  0,  "motor_max_spd_rpm",  5000,   1)
gen.add("motor_rdc_ratio",    double_t,  0,  "motor_rdc_ratio",  10.0,  1,  100)
gen.add("motor_enc_res_ppr",  int_t,  0,  "motor_enc_res_ppr",  2000,   1)
brake_type_enum = gen.enum([gen.const("SOFT_BRK",  int_t,  0,  "SOFT_BRK"),
                                  gen.const("HARD_BRK",  int_t,  1,  "HARD_BRK")], "brake_type_enum")
gen.add("motor_brk_type", int_t,  0,  "motor_brk_type", 0,  0,  0,  edit_method=brake_type_enum)
valid_out_level_enum = gen.enum([gen.const("LOW_VALID",  int_t,  0,  "LOW_VALID"),
                                    gen.const("HIGH_VALID", int_t,  1,  "HIGH_VALID")], "valid_out_level_enum")
gen.add("brk_vol", int_t,  0,  "brk_vol", 0,  0,  0,  edit_method=valid_out_level_enum)
gen.add("enb_vol", int_t,  0,  "enb_vol", 0,  0,  0,  edit_method=valid_out_level_enum)
gen.add("dir_vol", int_t,  0,  "dir_vol", 0,  0,  0,  edit_method=valid_out_level_enum)

#### pid param
gen.add("enb_pid",bool_t,   0, "enb_pid", False)
gen.add("kp_acc", double_t, 0, "kp_acc", 0, 0)
gen.add("kp_dec", double_t, 0, "kp_dec", 0, 0)
gen.add("ki_acc", double_t, 0, "ki_acc", 0, 0)
gen.add("ki_dec", double_t, 0, "ki_dec", 0, 0)
gen.add("kd_acc", double_t, 0, "kd_acc", 0, 0)
gen.add("kd_dec", double_t, 0, "kd_dec", 0, 0)

#### sensor param
gen.add("enb_sonar",        bool_t,   0, "enb_sonar",        False)
gen.add("enb_infrd",           bool_t,   0, "enb_infrd",           False)
gen.add("enb_bumper",       bool_t,   0, "enb_bumper",       False)
gen.add("enb_gyr",          bool_t,   0, "enb_gyr",          False)
gen.add("enb_acc",          bool_t,   0, "enb_acc",          False)
gen.add("enb_mag",          bool_t,   0, "enb_mag",          False)
gen.add("enb_gps",          bool_t,   0, "enb_gps",          False)
gen.add("sonar_num",  int_t,    0,  "sonar_num",  4,    1)
gen.add("infrd_num",     int_t,    0,  "infrd_num",     4,    1)
gen.add("bumper_num", int_t,    0,  "bumper_num", 4,    1)

exit(gen.generate(PACKAGE, NODE, FILE_NAME))
