#!/usr/bin/env python

PACKAGE = "wr_vel_mux"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gen.add("yaml_cfg_file", str_t, 0, "Pathname to a yaml file for re-configuration of the mux", "")
gen.add("yaml_cfg_data", str_t, 0, "Yaml-formatted string for re-configuration of the mux", "")

# Second arg is node name it will run in (doc purposes only), third is generated filename prefix
exit(gen.generate(PACKAGE, "wr_vel_mux_reload", "reload"))
