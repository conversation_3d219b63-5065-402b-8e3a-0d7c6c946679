# Another example configuration file, though this one is used to test the dynamic reconfiguration
# of the yocs_cmd_vel_mux. Used with reconfigure.launch.

subscribers:
  - name:        "Default input"
    topic:       "input/default"
    timeout:     0.1
    priority:    0
    short_desc:  "The default cmd_vel, controllers unaware that we are multiplexing cmd_vel should come here"
  - name:        "Navigation stack"
    topic:       "input/navigation"
    timeout:     0.5
    priority:    1
    short_desc:  "Navigation stack controller"
  - name:        "Safety controller"
    topic:       "input/safety"
    timeout:     0.1
    priority:    10
    short_desc:  "Used with the reactive velocity control provided by the bumper/cliff sensor safety controller"
