<package>
  <name>wr_vel_mux</name>
  <version>0.7.0</version>
  <description>
     A multiplexer for command velocity inputs.
  </description>
  <author><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>
  <license>BSD</license>
  
  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>roscpp</build_depend>
  <build_depend>nodelet</build_depend>
  <build_depend>dynamic_reconfigure</build_depend>
  <build_depend>pluginlib</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>yaml-cpp</build_depend>

  <run_depend>roscpp</run_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>pluginlib</run_depend>
  <run_depend>nodelet</run_depend>
  <run_depend>dynamic_reconfigure</run_depend>
  <run_depend>yaml-cpp</run_depend>

  <export>
    <nodelet plugin="${prefix}/plugins/nodelets.xml" />
  </export>
</package>
