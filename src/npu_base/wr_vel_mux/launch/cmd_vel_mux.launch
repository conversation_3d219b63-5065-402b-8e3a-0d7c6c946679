<?xml version="1.0"?>
<!-- v1.0 created by tommy @2017-04-20 
Update: 2017-04-20 
-->
<launch>
  <arg name="nodelet_manager_name"  default="nodelet_manager"/>
  <arg name="config_file"           default="$(find wr_vel_mux)/param/wizrobo.yaml"/>

  <node pkg="nodelet" type="nodelet" respawn="true" name="wr_vel_mux"
        args="load wr_vel_mux/WRCmdVelMuxNodelet $(arg nodelet_manager_name)">
    <param name="yaml_cfg_file" value="$(arg config_file)"/>
  </node>
</launch>
