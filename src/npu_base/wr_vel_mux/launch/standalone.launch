<?xml version='1.0'?>
<launch>
  <arg name="nodelet_manager_name"  value="base_nodelet_manager"/>
  <arg name="config_file"           value="$(find wr_vel_mux)/param/wizrobo.yaml"/>
  
  <!-- nodelet manager -->
  <node pkg="nodelet" type="nodelet" name="$(arg nodelet_manager_name)" args="manager"/>
  
  <!-- velocity mulitplexer -->
  <include file="$(find wr_vel_mux)/launch/cmd_vel_mux.launch">
    <arg name="nodelet_manager_name"  value="$(arg nodelet_manager_name)"/>
    <arg name="config_file"           value="$(arg config_file)"/>
  </include>
</launch>
