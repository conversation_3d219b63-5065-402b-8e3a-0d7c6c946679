#include <ros/ros.h>
#include <nodelet/nodelet.h>
#include <pluginlib/class_list_macros.h>

#include "chassis_server.h"

namespace wizrobo_chassis {

class ChassisServerNodelet : public nodelet::Nodelet
{
public:
    ChassisServerNodelet()
    {
    }
    ~ChassisServerNodelet()
    {
        if(p_namespace_ != nullptr){
            delete p_namespace_;
        }
        if(p_cs_ != nullptr){
            delete p_cs_;
        }
    }

    virtual void onInit()
    {
        p_namespace_ = nullptr;
        p_cs_ = nullptr;
        ros::NodeHandle& private_nh = this->getMTPrivateNodeHandle();
        const std::string& s_prefix = private_nh.getNamespace();
        int length = s_prefix.size() + 1;
        p_namespace_ = new char [length];
        memset(p_namespace_, 0, length);
        memcpy(p_namespace_, s_prefix.c_str(), s_prefix.size());
        int argc = 1;
        char **argv = &p_namespace_;
        LM_INFO("Name Space: %s", p_namespace_);
        p_cs_ = new wizrobo::ChassisServer(argc, argv);

        google::InitGoogleLogging(argv[0]);
        FLAGS_log_dir = "/log/npu";
        FLAGS_alsologtostderr = true;
        FLAGS_colorlogtostderr = true;
        google::ParseCommandLineFlags(&argc, &argv, true);
        p_cs_->Init();
        if (p_cs_->IsInited()) {
            p_cs_->Run();
        }
        return;
    }

private:
    char *p_namespace_;

    wizrobo::ChassisServer *p_cs_;
};

}

PLUGINLIB_EXPORT_CLASS(wizrobo_chassis::ChassisServerNodelet, nodelet::Nodelet);
