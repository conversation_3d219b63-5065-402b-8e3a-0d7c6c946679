#include "chassis_server.h"

#ifdef _LM_NAME_PREFIX_
#  undef  _LM_NAME_PREFIX_
#endif

#define _LM_NAME_PREFIX_ "ChassisServer"

namespace wizrobo {

void ChassisServer::Init()
{
    is_inited_ = false;

    LM_INFO("ChassisServer::Init()");
    ros::NodeHandle& nh = (*p_nh_);
    ros::NodeHandle& prv_nh = (*p_prv_nh_);

    // logger level & loop rate
    LoggerLevel logger_level;
    GetEnumParam<LoggerLevel>(prv_nh, "logger_level", logger_level, INFO);
    SetLoggerLevel(logger_level);
    prv_nh.param("loop_rate", loop_rate_, 100);
    prv_nh.param<bool>("enb_imu", enb_imu_, false);

    // dyn param
    prv_nh.param<bool>("enb_dyn_param", enb_dyn_param_, true);

    // frame id
    prv_nh.param<std::string>("odom_frame_id", odom_frame_id_,STD_ODOM_FRAME_ID);
    prv_nh.param<std::string>("base_frame_id", base_frame_id_,STD_BASE_FTP_FRAME_ID);
    prv_nh.param<std::string>("lidar_frame_id", lidar_frame_id_,STD_LIDAR_FRAME_ID);
    prv_nh.param<std::string>("sonar_frame_id", sonar_frame_id_,"sonar_frame");
    prv_nh.param<std::string>("infrd_frame_id", infrd_frame_id_,"infrd_frame");
    prv_nh.param<std::string>("bumper_frame_id", bumper_frame_id_,"bumper_frame");
    prv_nh.param<std::string>("imu_frame_id", imu_frame_id_,STD_IMU_FRAME_ID);
    prv_nh.param<std::string>("gps_frame_id", gps_frame_id_,"gps_frame");

    // topic
    prv_nh.param<std::string>("motor_enc_topic", motor_enc_topic_, "/motor_enc");
    prv_nh.param<std::string>("odom_topic", odom_topic_, "/odom");
    prv_nh.param<std::string>("cmd_vel_topic", cmd_vel_topic_, "/cmd_vel");
    prv_nh.param<std::string>("cmd_motor_spd_topic", cmd_motor_spd_topic_, "/cmd_motor_spd");
    prv_nh.param<std::string>("act_motor_spd_topic", act_motor_spd_topic_, "/act_motor_spd");
    prv_nh.param<std::string>("act_vel_topic", act_vel_topic_, "/act_vel");
    prv_nh.param<std::string>("act_stmp_vel_topic", act_stamp_vel_topic_, "act_stamp_vel");
    std::string imu_topic;
    prv_nh.param<std::string>("imu_topic", imu_topic, "/imu/data_raw");

    //// chassis model
    // create chassis model
    GetEnumParam<wizrobo_npu::ChassisModelType>(nh, STD_CHASSIS_PARAM_NS + "/model_type", model_type_, wizrobo_npu::DIFFDRV);
//    model_type_ = wizrobo_npu::STAGEDIFF;
    LM_WARN("model_type = %s", EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type_).c_str());

    cmd_vel_sub_ = nh.subscribe<geometry_msgs::Twist>(cmd_vel_topic_, 1, &ChassisServer::CmdVelCallback, this);
    bool use_sim_time;
    nh.param<bool>("/use_sim_time", use_sim_time, false);
    if (use_sim_time) {
        odom_pub_ = nh.advertise<nav_msgs::Odometry>(odom_topic_ + "_sim", 1);
    } else {
        odom_pub_ = nh.advertise<nav_msgs::Odometry>(odom_topic_, 1);
    }
    act_vel_pub_ = nh.advertise<geometry_msgs::Twist>(act_vel_topic_, 1);

    RELEASE_POINTER(p_chassis_);

    if (model_type_ != wizrobo_npu::ChassisModelType::STAGEDIFF) {
        ChassisModelCreator creator;
        LM_WARN("model_type_: %s", EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type_).c_str());
        p_chassis_ = creator.Create(model_type_);
        // chassis param
        if (!p_chassis_->GetParam(chassis_param_)) {
            LM_ERROR("ChassisModel(%s)::GetParam() failed, pls check param: %s",
                      EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type_).c_str(), chassis_param_.ToStr().c_str());
            return;
        }
        LM_INFO("Original chassis param: %s", chassis_param_.ToStr().c_str());
        SetChassisParam(enb_dyn_param_);

        // sub & pub
        act_motor_spd_sub_ = nh.subscribe<wr_npu_msgs::MotorSpd>(act_motor_spd_topic_, 1, &ChassisServer::ActMotorSpdCallback, this);
        motor_enc_sub_ = nh.subscribe<wr_npu_msgs::MotorEnc>(motor_enc_topic_, 1, &ChassisServer::MotorEncCallback, this);
        if (enb_imu_) {
            imu_sub_ = nh.subscribe<sensor_msgs::Imu>(imu_topic, 1, &ChassisServer::ImuCallback, this);
        }

        cmd_motor_spd_pub_ = nh.advertise<wr_npu_msgs::MotorSpd>(cmd_motor_spd_topic_, 1);
        act_stmp_vel_pub_ = prv_nh.advertise<geometry_msgs::TwistStamped>(act_stamp_vel_topic_, 1);

        // dyn param
        chassis_dyn_param_srv_ = nh.advertiseService("/dyn_param/chassis/chassis_server", &ChassisServer::ChassisDynParamCallback, this);

        /// srv
        cme_srv_ = prv_nh.advertiseService("clear_motor_enc", &ChassisServer::ClearMotorEncCallback, this);

        // dyn config
        if (!enb_dyn_param_) {
            RELEASE_POINTER(p_dyncfg_);
            p_dyncfg_ = new DynamicConfigServer(prv_nh);
            DynamicConfigServer::CallbackType cb = boost::bind(&ChassisServer::DynamicConfigCallback, this, _1, _2);
            p_dyncfg_->setCallback(cb);
        }
    } else {
        stage_odom_sub_ = nh.subscribe<nav_msgs::Odometry>("/stage_odom", 1, &ChassisServer::StageOdomCallback, this);
        stage_cmd_vel_pub_ = nh.advertise<geometry_msgs::Twist>("/stage_cmd_vel", 1);
        p_odom_thread_ = new boost::thread(boost::bind(&ChassisServer::ZeroOdomPubThread, this));
    }

    is_inited_ = true;
}

void ChassisServer::Run()
{
    LM_INFO("ChassisServer::Run()");
    if (!is_inited_) {
        return;
    }
    ros::spin();
}

void ChassisServer::SetChassisParam(bool enb_dyn_param)
{
    if (enb_dyn_param) {
        ros::NodeHandle& nh = *p_nh_;
        GetEnumParam<wizrobo_npu::ChassisModelType>(nh, STD_CHASSIS_PARAM_NS + "/model_type", chassis_param_.model_type, DIFFDRV);
        GetFloatParam(nh, STD_CHASSIS_PARAM_NS + "/wheel_rdc_ratio", chassis_param_.wheel_rdc_ratio, chassis_param_.wheel_span_m);
        GetFloatParam(nh, STD_CHASSIS_PARAM_NS + "/wheel_radius_m",  chassis_param_.wheel_radius_m,  chassis_param_.wheel_radius_m);
        GetFloatParam(nh, STD_CHASSIS_PARAM_NS + "/wheel_span_m",    chassis_param_.wheel_span_m,    chassis_param_.wheel_span_m);
        GetFloatParam(nh, STD_CHASSIS_PARAM_NS + "/carlike_axle_dist_m",    chassis_param_.carlike_axle_disit_m,    chassis_param_.carlike_axle_disit_m);
        GetFloatParam(nh, STD_CHASSIS_PARAM_NS + "/max_lin_acc_time_s", chassis_param_.max_lin_acc_time_s, chassis_param_.max_lin_acc_time_s);
        GetFloatParam(nh, STD_CHASSIS_PARAM_NS + "/max_ang_acc_time_s", chassis_param_.max_ang_acc_time_s, chassis_param_.max_ang_acc_time_s);
        // partial motor param
        nh.param<int>(STD_MOTOR_PARAM_NS + "/motor_max_spd_rpm", motor_param_.motor_max_spd_rpm, motor_param_.motor_max_spd_rpm);
        GetFloatParam(nh, STD_MOTOR_PARAM_NS + "/motor_rdc_ratio", motor_param_.motor_rdc_ratio, motor_param_.motor_rdc_ratio);
        nh.param<int>(STD_MOTOR_PARAM_NS + "/motor_enc_res_ppr", motor_param_.motor_enc_res_ppr, motor_param_.motor_enc_res_ppr);
    } else {
        ros::NodeHandle& prv_nh = *p_prv_nh_;
        GetEnumParam<wizrobo_npu::ChassisModelType>(prv_nh, "model_type", chassis_param_.model_type, DIFFDRV);
        GetFloatParam(prv_nh, "wheel_rdc_ratio", chassis_param_.wheel_rdc_ratio, chassis_param_.wheel_span_m);
        GetFloatParam(prv_nh, "wheel_radius_m",  chassis_param_.wheel_radius_m,  chassis_param_.wheel_radius_m);
        GetFloatParam(prv_nh, "wheel_span_m",    chassis_param_.wheel_span_m,    chassis_param_.wheel_span_m);
        GetFloatParam(prv_nh, "carlike_axle_dist_m", chassis_param_.carlike_axle_disit_m, chassis_param_.carlike_axle_disit_m);
        GetFloatParam(prv_nh, "max_lin_acc_time_s", chassis_param_.max_lin_acc_time_s, chassis_param_.max_lin_acc_time_s);
        GetFloatParam(prv_nh, "max_ang_acc_time_s", chassis_param_.max_ang_acc_time_s, chassis_param_.max_ang_acc_time_s);
        // partial motor param
        prv_nh.param<int>("motor_max_spd_rpm", motor_param_.motor_max_spd_rpm, motor_param_.motor_max_spd_rpm);
        GetFloatParam(prv_nh, "motor_rdc_ratio", motor_param_.motor_rdc_ratio, motor_param_.motor_rdc_ratio);
        prv_nh.param<int>("motor_enc_res_ppr", motor_param_.motor_enc_res_ppr, motor_param_.motor_enc_res_ppr);
    }
    if (!p_chassis_->SetParam(chassis_param_, motor_param_)) {
        LM_ERROR("ChassisModel(%s)::SetParam() failed, pls check param: %s",
                  EnumString<ChassisModelType>::EnumToStr(model_type_).c_str(),
                  chassis_param_.ToStr().c_str());
        return;
    }
    if (!p_chassis_->GetParam(chassis_param_)) {
        LM_ERROR("ChassisModel(%s)::GetParam() failed, pls check param: %s",
                  EnumString<ChassisModelType>::EnumToStr(model_type_).c_str(),
                  chassis_param_.ToStr().c_str());
        return;
    }
    LM_DEBUG("Modified chassis param: %s", chassis_param_.ToStr().c_str());
}

bool ChassisServer::ChassisDynParamCallback(
        wr_npu_msgs::DynParam::Request &req, wr_npu_msgs::DynParam::Response &res)
{
    SetChassisParam(enb_dyn_param_);
    return true;
}

void ChassisServer::DynamicConfigCallback(NpuDynamicConfig& config, uint32_t level)
{
    if (is_dyncfg_inited_ == false) {
        config.wheel_rdc_ratio = chassis_param_.wheel_rdc_ratio;
        config.wheel_radius_m = chassis_param_.wheel_radius_m;
        config.wheel_span_m = chassis_param_.wheel_span_m;
        is_dyncfg_inited_ = true;
    }
    chassis_param_.wheel_rdc_ratio = config.wheel_rdc_ratio;
    chassis_param_.wheel_radius_m = config.wheel_radius_m;
    chassis_param_.wheel_span_m = config.wheel_span_m;

    ros::NodeHandle private_nh("~");
    private_nh.setParam("wheel_rdc_ratio", chassis_param_.wheel_rdc_ratio);
    private_nh.setParam("wheel_radius_m", chassis_param_.wheel_radius_m);
    private_nh.setParam("wheel_span_m", chassis_param_.wheel_span_m);
}

void ChassisServer::MotorEncCallback(const wr_npu_msgs::MotorEnc::ConstPtr& msg)
{
    //LM_DEBUG("ChassisServer::MotorEncCallback()");
    const wr_npu_msgs::MotorEnc& motor_enc = *msg;
    ROS_ASSERT(EnumString<wizrobo_npu::ChassisModelType>::IsEqual(model_type_, motor_enc.model_type));

    nav_msgs::Odometry odom = p_chassis_->UpdateOdom(motor_enc);
    odom.header.stamp = ros::Time::now();
    if (odom.child_frame_id.empty()) {
        odom.child_frame_id = "base_frame";
    }
#if 0
    odom.twist.twist.linear.x = act_vel_.linear.x;
    odom.twist.twist.linear.y = act_vel_.linear.y;
    odom.twist.twist.linear.z = act_vel_.linear.z;
    odom.twist.twist.angular.x = act_vel_.angular.x;
    odom.twist.twist.angular.y = act_vel_.angular.y;
    odom.twist.twist.angular.z = act_vel_.angular.z;
#endif
    odom_pub_.publish(odom);
}



void ChassisServer::CmdVelCallback(const geometry_msgs::Twist::ConstPtr &msg)
{
    if (model_type_ != wizrobo_npu::ChassisModelType::STAGEDIFF) {
        const geometry_msgs::Twist& cmd_vel = *msg;
        wr_npu_msgs::MotorSpd cmd_motor_spd = p_chassis_->VelToMotorSpd(cmd_vel);
        cmd_motor_spd.header.stamp = ros::Time::now();
        cmd_motor_spd.header.frame_id = base_frame_id_;
        cmd_motor_spd.model_type = EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type_);
        cmd_motor_spd_pub_.publish(cmd_motor_spd);
    } else {
        stage_cmd_vel_ = *msg;
        stage_cmd_vel_pub_.publish(stage_cmd_vel_);
        act_vel_pub_.publish(stage_cmd_vel_);
    }
    return;
}

void ChassisServer::ActMotorSpdCallback(const wr_npu_msgs::MotorSpd::ConstPtr &msg)
{
    //LM_DEBUG("ChassisServer::ActMotorSpdCallback()");
    const wr_npu_msgs::MotorSpd& act_motor_spd = *msg;
    ROS_ASSERT(EnumString<ChassisModelType>::IsEqual(model_type_, act_motor_spd.model_type));
    geometry_msgs::Twist act_vel = p_chassis_->MotorSpdToVel(act_motor_spd);
    act_vel_ = act_vel;
    act_vel_pub_.publish(act_vel);
    geometry_msgs::TwistStamped act_stamp_vel;
    act_stamp_vel.header.frame_id = "base_frame";
    act_stamp_vel.header.stamp = ros::Time::now();
    act_stamp_vel.twist = act_vel;
    act_stmp_vel_pub_.publish(act_stamp_vel);
}

void ChassisServer::ImuCallback(const sensor_msgs::Imu::ConstPtr& msg)
{
    //LM_DEBUG("ChassisServer::IMUCallback()");
    if (!enb_imu_) {
        LOG_WARN << "The chassis node do not use imu msgs ! Plese check you parameter";
        return;
    }
    imu_msg_ = *msg;
//    quaternionMsgToTF(imu.orientation, imu_quat);
}

bool ChassisServer::ClearMotorEncCallback(
        wr_npu_msgs::ClearMotorEncRequest &rqst, wr_npu_msgs::ClearMotorEncResponse &resp)
{
    LM_INFO("ChassisServer::ClearMotorEncCallback()");
    if (!is_inited_) {
        return false;
    }
    ros::NodeHandle nh;
    bool enb_slave_mode;
    nh.param<bool>(STD_BASE_PARAM_NS + "/enb_slave_mode", enb_slave_mode, false);
    if (!enb_slave_mode) {
        wr_npu_msgs::ClearMotorEnc srv;
        srv.request = rqst;
        std::string srv_name = "/bcu_server_node/clear_motor_enc";
        ros::ServiceClient client = nh.serviceClient<wr_npu_msgs::ClearMotorEnc>(srv_name);
        bool rlt = client.call(srv);
        if (!rlt) {
            LM_ERROR("ChassisServer::ClearMotorEncCallback(): Call service \"%s\": rlt = %s",
                      srv_name.c_str(), BoolToStr(rlt).c_str());
            return false;
        }
        LM_INFO("ChassisServer::ClearMotorEncCallback(): Call service \"%s\": rlt = %s",
                 srv_name.c_str(), BoolToStr(rlt).c_str());
    }
    p_chassis_->ClearMotorEnc();
    return true;
}

void ChassisServer::StageOdomCallback(const nav_msgs::Odometry::ConstPtr& msg)
{
    if(!get_stage_odom_) {
        get_stage_odom_ = true;
    }
    stage_odom_ = *msg;
    stage_odom_.header.stamp = ros::Time::now();
    //LM_WARN("stamp: %.2f / %.2f", msg->header.stamp.toSec(), stage_odom_.header.stamp.toSec());
    stage_odom_.header.frame_id = odom_frame_id_;
    if (stage_odom_.child_frame_id.empty()) {
        stage_odom_.child_frame_id = "base_frame";
    }
    stage_odom_.twist.twist = stage_cmd_vel_;

    stage_odom_.pose.covariance[0] = 0.1f;
    stage_odom_.pose.covariance[7] = 0.1f;
    stage_odom_.pose.covariance[35] = 0.2f;

    stage_odom_.pose.covariance[14] = 1e10;
    stage_odom_.pose.covariance[21] = 1e10;
    stage_odom_.pose.covariance[28] = 1e10;

    odom_pub_.publish(stage_odom_);
}

void ChassisServer::ZeroOdomPubThread()
{
    ros::Rate r(30);
    nav_msgs::Odometry odom;
    odom.header.frame_id = odom_frame_id_;
    odom.child_frame_id = "base_frame";

    odom.pose.pose.position.x = 0.0;
    odom.pose.pose.position.y = 0.0;
    odom.pose.pose.position.z = 0.0;
    odom.pose.pose.orientation.x = 0.0;
    odom.pose.pose.orientation.y = 0.0;
    odom.pose.pose.orientation.z = 0.0;
    odom.pose.pose.orientation.w = 1.0;

    odom.twist.twist.linear.x = 0.0;
    odom.twist.twist.linear.y = 0.0;
    odom.twist.twist.linear.z = 0.0;
    odom.twist.twist.angular.x = 0.0;
    odom.twist.twist.angular.y = 0.0;
    odom.twist.twist.angular.z = 0.0;

    odom.pose.covariance[0] = 0.1f;
    odom.pose.covariance[7] = 0.1f;
    odom.pose.covariance[35] = 0.2f;

    odom.pose.covariance[14] = 1e10;
    odom.pose.covariance[21] = 1e10;
    odom.pose.covariance[28] = 1e10;

    while(ros::ok() && !get_stage_odom_) {
        odom.header.stamp = ros::Time::now();
        odom_pub_.publish(odom);
        r.sleep();
    }
}

}// namespace wizrobo
