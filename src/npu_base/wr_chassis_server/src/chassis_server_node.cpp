#include <usblock.h>
#include <nodelet/loader.h>

int main(int argc, char **argv)
{
#if WR_LOCK == true
    if (!wizrobo::UsbLocker::Unlock()) {
      ROS_FATAL("Failed to unlock! Pls heck you usblock.");
      return -1;
    }
#endif
    ros::init(argc, argv, "chassis_server_nodelet");
    ros::start();
    ros::NodeHandle prv_nh("~");

#if 1
    std::string logger_level;
    prv_nh.param<std::string>("logger_level", logger_level, "debug");
    if (logger_level.compare("debug") == 0) {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Debug);
    } else if (logger_level.compare("info") == 0) {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Info);
    } else if (logger_level.compare("warn") == 0) {
        ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Warn);
    }
#else
    ros::console::set_logger_level(ROSCONSOLE_DEFAULT_NAME, ros::console::levels::Debug);
#endif

    nodelet::Loader nodelet;
    nodelet::M_string remap(ros::names::getRemappings());
    nodelet::V_string nargv;
    std::string nodelet_name = ros::this_node::getName();
    nodelet.load(nodelet_name, "wr_chassis_server/ChassisNodelet", remap, nargv);

    ros::waitForShutdown();


    return 0;
}
