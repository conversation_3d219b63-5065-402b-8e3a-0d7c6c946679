<?xml version="1.0"?>
<package>
  <name>wr_chassis_server</name>
  <version>0.0.0</version>
  <description>The wr_chassis_server package</description>

  <!-- One maintainer tag required, multiple allowed, one person per tag --> 
  <!-- Example:  -->
  <!-- <maintainer email="<EMAIL>"><PERSON></maintainer> -->
  <maintainer email="<EMAIL>">lhan</maintainer>


  <!-- One license tag required, multiple allowed, one license per tag -->
  <!-- Commonly used license strings: -->
  <!--   BSD, MIT, Boost Software License, GPLv2, GPLv3, LGPLv2.1, LGPLv3 -->
  <license>TODO</license>


  <!-- Url tags are optional, but mutiple are allowed, one per tag -->
  <!-- Optional attribute type can be: website, bugtracker, or repository -->
  <!-- Example: -->
  <!-- <url type="website">http://wiki.ros.org/wr_basecontrol</url> -->


  <!-- Author tags are optional, mutiple are allowed, one per tag -->
  <!-- Authors do not have to be maintianers, but could be -->
  <!-- Example: -->
  <!-- <author email="<EMAIL>">Jane Doe</author> -->


  <!-- The *_depend tags are used to specify dependencies -->
  <!-- Dependencies can be catkin packages or system dependencies -->
  <!-- Examples: -->
  <!-- Use build_depend for packages you need at compile time: -->
  <!--   <build_depend>message_generation</build_depend> -->
  <!-- Use buildtool_depend for build tool packages: -->
  <!--   <buildtool_depend>catkin</buildtool_depend> -->
  <!-- Use run_depend for packages you need at runtime: -->
  <!--   <run_depend>message_runtime</run_depend> -->
  <!-- Use test_depend for packages you need only for testing: -->
  <!--   <test_depend>gtest</test_depend> -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- build -->
  <!-- ROS -->
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>tf</build_depend>
  <!-- Kobuki -->
  <!--build_depend>kobuki_msgs</build_depend>
  <build_depend>kobuki_driver</build_depend>
  <build_depend>kobuki_ftdi</build_depend>
  <build_depend>kobuki_keyop</build_depend>
  <build_depend>kobuki_safety_controller</build_depend-->
  <!-- ecl -->
  <!--build_depend>ecl_exceptions</build_depend>
  <build_depend>ecl_sigslots</build_depend>
  <build_depend>ecl_streams</build_depend>
  <build_depend>ecl_threads</build_depend-->
  <!-- npu -->
  <build_depend>wr_npu_core</build_depend>
  <build_depend>wr_npu_ice</build_depend>
  <build_depend>wr_npu_msgs</build_depend>
  <build_depend>wr_bcu_server</build_depend>

  <!-- run -->
  <!-- ROS -->
  <run_depend>capabilities</run_depend>
  <run_depend>roscpp</run_depend>
  <build_depend>nodelet</build_depend>
  <run_depend>geometry_msgs</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>nav_msgs</run_depend>
  <run_depend>std_msgs</run_depend>
  <run_depend>tf</run_depend>
  <run_depend>angles</run_depend>
  <run_depend>diagnostic_aggregator</run_depend>
  <run_depend>diagnostic_updater</run_depend>
  <run_depend>diagnostic_msgs</run_depend>
  <run_depend>nodelet</run_depend>
  <run_depend>pluginlib</run_depend>

  <!-- Kobuki -->
  <!--run_depend>kobuki_rapps</run_depend>
  <run_depend>kobuki_msgs</run_depend>
  <run_depend>kobuki_driver</run_depend>
  <run_depend>kobuki_ftdi</run_depend>
  <run_depend>kobuki_keyop</run_depend>
  <run_depend>kobuki_safety_controller</run_depend-->

  <!-- ECL -->
  <!--run_depend>ecl_exceptions</run_depend>
  <run_depend>ecl_sigslots</run_depend>
  <run_depend>ecl_streams</run_depend>
  <run_depend>ecl_threads</run_depend-->

  <run_depend>geometry_msgs</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>rospy</run_depend>
  <run_depend>nodelet</run_depend>
  <run_depend>std_msgs</run_depend>
  <run_depend>message_runtime</run_depend>
  <!-- npu -->
  <run_depend>wr_npu_core</run_depend>
  <run_depend>wr_npu_ice</run_depend>
  <run_depend>wr_npu_msgs</run_depend>
  <run_depend>wr_bcu_server</run_depend>

  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <!-- Other tools can request additional information be placed here -->
    <nodelet plugin="${prefix}/bcu_chassis_nodelet_plugin.xml" />

  </export>
</package>
