<?xml version="1.0"?>
<!-- -*- mode: XML -*- -->
<launch>
  <arg name="logger_level" default="INFO" />
  <arg name="model_type" default="DIFFDRV" /> <!-- mode={CARLIKE, DIFFDRV, UNIVWHEEL, OMNIWHEEL} -->
  <arg name="publish_tf" default="true" />
  <arg name="imu_used" default="true" />
  <arg name="enb_sensor_tf_pub" default="true" />
  <arg name="enb_dyn_param" default="true" />

  <!-- 1. start chassis_server -->
  <node pkg="wr_chassis_server" type="chassis_server_node" name="chassis_server_node"
    output="screen" respawn="false">
    <param name="logger_level" value="$(arg logger_level)" />
    <param name="publish_tf" value="$(arg publish_tf)" />
    <param name="model_type" value="$(arg model_type)" />
    <param name="imu_used" value="$(arg imu_used)" />
    <param name="enb_sensor_tf_pub" value="$(arg enb_sensor_tf_pub)" />
    <param name="enb_dyn_param" value="$(arg enb_dyn_param)" />

    <!-- chassis param -->
    <param name="wheel_rdc_ratio" value="1.0" />
    <param name="wheel_radius_m" value="0.0625" />
    <param name="wheel_span_m" value="0.312" />
    <param name="max_lin_spd_mps" value="0.5" />
    <param name="max_ang_spd_dps" value="90.0" />

    <!-- frame id -->
    <param name="odom_frame_id" value="/odom_frame" />
    <param name="base_frame_id" value="/base_frame" />
    <param name="lidar_frame_id" value="/lidar_frame" />
    <param name="sonar_frame_id" value="/sonar_frame" />
    <param name="infrd_frame_id" value="/infrd_frame" />
    <param name="bumper_frame_id" value="/bumper_frame" />
    <param name="imu_frame_id" value="/imu_frame" />
    <param name="gps_frame_id" value="/gps_frame" />
    <param name="motor_enc_topic" value="/motor_enc" />

    <!-- topic -->
    <param name="odom_topic" value="/odom" />
    <param name="cmd_vel_topic" value="/cmd_vel" />
    <param name="cmd_motor_spd_topic" value="/cmd_motor_spd" />
    <param name="act_motor_spd_topic" value="/act_motor_spd" />
    <param name="act_vel_topic" value="/act_vel" />
  </node>

</launch>
