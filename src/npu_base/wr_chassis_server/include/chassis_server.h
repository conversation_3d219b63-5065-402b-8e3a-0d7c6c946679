#ifndef WIZROBO_CHASSIS_SERVER_H
#define WIZROBO_CHASSIS_SERVER_H
#include <sstream>

#include <ros/ros.h>
#include <geometry_msgs/Vector3Stamped.h>
#include <dynamic_reconfigure/server.h>
#include <geometry_msgs/Twist.h>
#include <sensor_msgs/Imu.h>
#include <sensor_msgs/NavSatFix.h>
#include <sensor_msgs/NavSatStatus.h>
#include <sensor_msgs/LaserScan.h>

#include <nav_msgs/Odometry.h>
#include <tf/tf.h>
#include <tf/transform_broadcaster.h>
#include <tf/message_filter.h>
#include <message_filters/subscriber.h>

#include <npu.h>
#include <wr_npu_msgs/ComStatus.h>
#include <wr_npu_msgs/MotorSpd.h>
#include <wr_npu_msgs/MotorEnc.h>
#include <wr_npu_msgs/SonarData.h>
#include <wr_npu_msgs/InfrdData.h>
#include <wr_npu_msgs/BumperData.h>
#include <wr_npu_msgs/DynParam.h>
#include <wr_npu_msgs/ClearMotorEnc.h>

#include <bcu_driver/bcu_driver_param.h>
#include <wr_chassis_server/ChassisServerConfig.h>
//#include "ekf_estimate.h"
#include "chassis_model_creator.h"

namespace wizrobo {
using namespace ros_ext;
using namespace wizrobo::chassis_model;
using namespace wizrobo::bcu_driver;

class ChassisServer
{
    typedef wr_chassis_server::ChassisServerConfig NpuDynamicConfig;
    typedef dynamic_reconfigure::Server<NpuDynamicConfig> DynamicConfigServer;
public:
    ChassisServer(int argc, char **argv)
        : p_nh_(NULL)
        , p_prv_nh_(NULL)
        , is_inited_(false)
        , loop_rate_(100)
        , is_dyncfg_inited_(false)
        , p_dyncfg_(NULL)
        , p_chassis_(NULL)
        , enb_dyn_param_(false)
        , stage_cmd_vel_()
        , stage_odom_()
        , get_stage_odom_(false)
        , p_odom_thread_(nullptr)
    {
        p_nh_ = new ros::NodeHandle();
        p_prv_nh_ = new ros::NodeHandle("~");
    }

    virtual ~ChassisServer()
    {
        RELEASE_POINTER(p_dyncfg_);
        RELEASE_POINTER(p_chassis_);
//        RELEASE_POINTER(p_tf_pub_);
        RELEASE_POINTER(p_prv_nh_);
        RELEASE_POINTER(p_nh_);
    }
    void Init();
    inline bool IsInited()
    {
        return is_inited_;
    }
    void Run();

private:
    /// callback
    bool ChassisDynParamCallback(wr_npu_msgs::DynParam::Request& req, wr_npu_msgs::DynParam::Response& res);
    // [bcu] -{enc}-> [bcu_server] -*{/enc}*-> *[base_model]* -{/odom}->
    void MotorEncCallback(const wr_npu_msgs::MotorEnc::ConstPtr& msg);
    // [navi_core] -*{/cmd_vel}*-> *[base_model]* -{/cmd_spd}-> [bcu_server] -{spd}-> [bcu]
    void CmdVelCallback(const geometry_msgs::Twist::ConstPtr& msg);// cmd_vel -> cmd_spd
    // [bcu] -{spd}-> [bcu_server] -*{/act_spd}*-> *[base_model]* -{/act_vel}->
    void ActMotorSpdCallback(const wr_npu_msgs::MotorSpd::ConstPtr& msg);
    // callback function for imu data
    void ImuCallback(const sensor_msgs::Imu::ConstPtr& msg);
//    void ManualCmdVelCallback(const geometry_msgs::Twist::ConstPtr& msg);// cmd_vel -> cmd_spd
//    void AutoCmdVelCallback(const geometry_msgs::Twist::ConstPtr& msg);// cmd_vel -> cmd_spd
    bool ClearMotorEncCallback(wr_npu_msgs::ClearMotorEncRequest& rqst, wr_npu_msgs::ClearMotorEncResponse& resp);

    /// param
    void DynamicConfigCallback(NpuDynamicConfig& config, uint32_t level);
    void SetChassisParam(bool enb_dyn_param);

    void StageOdomCallback(const nav_msgs::Odometry::ConstPtr& msg);
    void ZeroOdomPubThread();


private:
    ros::NodeHandle* p_nh_;
    ros::NodeHandle* p_prv_nh_;
    bool is_inited_;
    int loop_rate_;
//    bool enb_odom_tf_pub;
    bool enb_imu_;
    sensor_msgs::Imu imu_msg_;
//    tf::Quaternion imu_quat;
    //wr_npu_msgs::MotorEnc last_motor_enc_;
    geometry_msgs::Twist act_vel_;

    // chassis model
    IChassisModel* p_chassis_;
    wizrobo_npu::ChassisModelType model_type_;
    chassis_model::ChassisParam chassis_param_;
    bcu_driver::MotorParam motor_param_;

    // frame id
    std::string odom_frame_id_;
    std::string base_frame_id_;
    std::string lidar_frame_id_;
    std::string sonar_frame_id_;
    std::string infrd_frame_id_;
    std::string bumper_frame_id_;
    std::string imu_frame_id_;
    std::string gps_frame_id_;

    // topic
    std::string motor_enc_topic_;
    std::string odom_topic_;
    std::string cmd_vel_topic_;
    std::string cmd_motor_spd_topic_;
    std::string act_motor_spd_topic_;
    std::string act_vel_topic_;
    std::string act_stamp_vel_topic_;

    // sub & pub
    ros::Subscriber motor_enc_sub_;
    ros::Publisher odom_pub_;
    ros::Subscriber cmd_vel_sub_;
    ros::Publisher cmd_motor_spd_pub_;
    ros::Subscriber act_motor_spd_sub_;
    ros::Publisher act_vel_pub_;
    ros::Publisher act_stmp_vel_pub_;
    ros::Subscriber imu_sub_;
//    tf::TransformBroadcaster* p_tf_pub_;
//    ros::Subscriber imu_sub_;
//    message_filters::Subscriber<sensor_msgs::LaserScan> lidar_scan_sub_;
//    tf::MessageFilter<sensor_msgs::LaserScan>* p_lidar_scan_tf_filter_;

    // srv
    ros::ServiceServer cme_srv_;

    // dyn param
    ros::ServiceServer chassis_dyn_param_srv_ ;
    bool enb_dyn_param_;

    // dyncfg
    bool is_dyncfg_inited_;
    DynamicConfigServer* p_dyncfg_;

    // for StageDiff
    geometry_msgs::Twist stage_cmd_vel_;
    nav_msgs::Odometry stage_odom_;
    ros::Subscriber stage_odom_sub_;
    ros::Publisher stage_cmd_vel_pub_;
    bool get_stage_odom_;
    boost::thread* p_odom_thread_;

};
}// namespace
#endif// WIZROBO_CHASSIS_SERVER_H
