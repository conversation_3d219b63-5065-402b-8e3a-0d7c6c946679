/**************************************************************************
**   WizRobo NPU (Navigation Processing Unit)
**   Date: 4/29/2017
**   Copyright: Shenzhen Linkmiao Robotics Co., Ltd. 2015-2017
**   Creator: lhan
**************************************************************************/

#ifndef WIZROBO_CHASSIS_MODEL_CHASSIS_MODEL_CREATOR_H
#define WIZROBO_CHASSIS_MODEL_CHASSIS_MODEL_CREATOR_H
#include "diffdrv_chassis_model.h"
#include "csgdrv_chassis_model.h"

namespace wizrobo { namespace chassis_model {

class ChassisModelCreator
{
public:
    IChassisModel* Create(ChassisModelType type)
    {
        IChassisModel* p_chassis = NULL;
        switch (type)
        {
        case CARLIKE:
            assert(0);
            break;
        case DIFFDRV:
            p_chassis = new DiffdrvChassisModel();
            break;
        case UNIVWHEEL:
            assert(0);
            break;
        case OMNIWHEEL:
            assert(0);
            break;
        case CSGDRV:
             p_chassis = new CsgdrvChassisModel();
            break;
        default:
            assert(0);
            break;
        }
        return p_chassis;
    }
};
}}
#endif // WIZROBO_CHASSIS_MODEL_CHASSIS_MODEL_CREATOR_H
