#ifndef CSGDRV_CHASSIS_MODEL_H
#define CSGDRV_CHASSIS_MODEL_H

#include <cmath>

#include <ros/ros.h>

#if WR_LEGACY == true
#  include <ecl/geometry/legacy_pose2d.hpp>
#else
#  include <ecl/geometry/pose2d.hpp>
#endif

#include <ecl/mobile_robot.hpp>
#include <tf/tf.h>

#include <npu.h>
#include <npu_math.h>
#include <npu_enum_ext.h>

#include <npuice_param.h>

#include "chassis_model_interface.h"

#define _esp_ (0.0001)

namespace wizrobo { namespace chassis_model {

using namespace wizrobo::enum_ext;

typedef ecl::mobile_robot::DifferentialDriveKinematics EclDiffdrvKinematics;

#if WR_LEGACY == true
  typedef ecl::LegacyPose2D<double> EclPose2D;
#else
  typedef ecl::Pose2D<double> EclPose2D;
#endif

typedef ecl::linear_algebra::Vector3d EclVector3D;

class CsgdrvChassisModel : public IChassisModel
{
private:
    EclDiffdrvKinematics* p_ecl_kine_;
    double mps_to_rps_fac_;
    EclPose2D last_back_pose_;
    EclPose2D last_origin_pose_;
    EclPose2D last_pose_;

    EclVector3D current_vel_;
    ros::Time vel_begin_stamp_;
    ros::Time vel_end_stamp_;
    float vel_interval_;
    EclPose2D vel_pose_change_;

    static const int front_right_i = 0;
    static const int front_left_i = 1;
    static const int back_left_i = 2;
    static const int back_right_i = 3;

    static const int mode_spin_around   = 0;
    static const int mode_carlike       = 1;
    static const int mode_shifting     = 2;
    int drv_mode_;
    ros::Time shift_stamp_;

    float shift_interval_;

    tf::Transform back_to_base_tf_;
    wizrobo_npu::SteerEncLocation steer_location_;

public:
    CsgdrvChassisModel()
        : p_ecl_kine_(NULL)
        , drv_mode_(mode_carlike)
        , shift_interval_(2.0)
        , shift_stamp_(ros::Time::now())
    {
        this->model_type_ = CSGDRV;

        ros::NodeHandle nh;
        nh.param<float>(STD_TELEOP_PARAM_NS + "/csg/shift_interval", shift_interval_, shift_interval_);
        nh.param<float>(STD_TELEOP_PARAM_NS + "/csg/vel_interval", vel_interval_, vel_interval_);
        current_vel_ << 0.0, 0.0, 0.0;
        vel_end_stamp_ = ros::Time::now();
        vel_begin_stamp_ = vel_end_stamp_;
        vel_pose_change_.setPose(0.0, 0.0, 0.0);
    }

    ~CsgdrvChassisModel()
    {
        RELEASE_POINTER(p_ecl_kine_);
    }

    bool SetParam(const ChassisParam& chassis_param, const MotorParam& motor_param)
    {
        if (chassis_param.wheel_rdc_ratio == 0
                || chassis_param.wheel_radius_m == 0) {
            WR_DEBUG("Invalid chassis param, cancel setting");
            return false;
        }
        if (motor_param.motor_rdc_ratio == 0) {
            WR_DEBUG("Invalid motor param, cancel setting");
            return false;
        }
        motor_param_ = motor_param;
        chassis_param_ = chassis_param;
        CsgdrvChassisModel::UpdateSpdLimits(motor_param_, chassis_param_);
        LM_WARN("- max_lin_spd_mps = %f", chassis_param_.autoset_max_lin_spd_mps);
        LM_WARN("- max_ang_spd_rps = %f", chassis_param_.autoset_max_ang_spd_rps);

        // internal param
        mps_to_rps_fac_ = 1.0
                / (2 * M_PI * chassis_param_.wheel_radius_m) * chassis_param_.wheel_rdc_ratio;
        enc_jump_thrs_tick_ = abs(chassis_param_.autoset_max_lin_spd_mps
                    / (2*M_PI*chassis_param_.wheel_radius_m)
                    * chassis_param_.wheel_rdc_ratio * motor_param_.motor_rdc_ratio
                    * motor_param_.motor_enc_res_ppr * 4 * 10);
        LM_WARN("- enc_jump_thrs_tick_ = %d", enc_jump_thrs_tick_);

        RELEASE_POINTER(p_ecl_kine_);

        LM_WARN("- motor_enc_res_ppr = %d", motor_param_.motor_enc_res_ppr);
        LM_WARN("-   motor_rdc_ratio = %.4f", motor_param_.motor_rdc_ratio);
        LM_WARN("-   wheel_rdc_ratio = %.4f", chassis_param_.wheel_rdc_ratio);
        LM_WARN("-      wheel_span_m = %.4f", chassis_param_.wheel_span_m);
        LM_WARN("-    wheel_radius_m = %.4f", chassis_param_.wheel_radius_m);
        p_ecl_kine_ = new EclDiffdrvKinematics(chassis_param_.wheel_span_m,
                                               chassis_param_.wheel_radius_m);

        geometry_msgs::Pose _pose;
        _pose.position.x = chassis_param_.carlike_axle_disit_m / 2;
        _pose.position.y = 0.0;
        _pose.position.z = 0.0;
        _pose.orientation = ros_ext::RPY2GeoQuat(0.0, 0.0, 0.0);
        tf::poseMsgToTF(_pose, back_to_base_tf_);

        is_enc_inited_ = false;

        return true;
    }

    EclPose2D GetPoseChangeCarlike(std::vector<long>& diff_ticks)
    {
        double back_left_wheel_rad_diff = 1.0 * diff_ticks[back_left_i]
                / (4 * motor_param_.motor_enc_res_ppr) * 2 * M_PI
                / motor_param_.motor_rdc_ratio / chassis_param_.wheel_rdc_ratio;
        double back_right_wheel_rad_diff = 1.0 * diff_ticks[back_right_i]
                / (4 * motor_param_.motor_enc_res_ppr) * 2 * M_PI
                / motor_param_.motor_rdc_ratio / chassis_param_.wheel_rdc_ratio;

        double _k_rad = chassis_param_.carlike_axle_disit_m / 2 / chassis_param_.wheel_radius_m;
        EclPose2D i_pose_change = p_ecl_kine_->forward(-_k_rad, -_k_rad);
        EclPose2D ii_pose_change = p_ecl_kine_->forward(_k_rad, _k_rad);
        EclPose2D back_pose_change = p_ecl_kine_->forward(back_left_wheel_rad_diff, back_right_wheel_rad_diff);
        EclPose2D origin_pose_change = i_pose_change * back_pose_change * ii_pose_change;
        LM_DEBUG("diff_ticks(%ld, %ld) origin_pose_change(%.2f, %.2f, %.2f)",
                  diff_ticks[back_left_i], diff_ticks[back_right_i],
                  static_cast<double>(origin_pose_change.x()),
                  static_cast<double>(origin_pose_change.y()),
                  static_cast<double>(origin_pose_change.heading()));
        return origin_pose_change;
    }

    EclPose2D GetPoseChangeSpinAround(std::vector<long>& diff_ticks)
    {
        double diff_tick = 0;
        LM_DEBUG("diff_ticks size is %d:", static_cast<int>(diff_ticks.size()));
        for (int i=0;i<diff_ticks.size();i++) {
            diff_tick += ABS(diff_ticks[i]);
            LM_DEBUG("diff_tick[%d]: %ld|%.2f", i, diff_ticks[i],diff_tick);
            LM_DEBUG("diff_tick[%d]: %.2f", i, diff_tick);
        }
        diff_tick /= diff_ticks.size();
        diff_tick *= SIGN(diff_ticks[back_right_i]);
        double wheel_meter_diff = 1.0 * diff_tick
                / (4 * motor_param_.motor_enc_res_ppr) * 2 * M_PI * chassis_param_.wheel_radius_m
                / motor_param_.motor_rdc_ratio / chassis_param_.wheel_rdc_ratio;
        LM_DEBUG("wheel_span/axle_dis:[%.2f/%.2f]", chassis_param_.wheel_span_m, chassis_param_.carlike_axle_disit_m);
        double radius = sqrt(SQU(chassis_param_.wheel_span_m/2) + SQU(chassis_param_.carlike_axle_disit_m/2));
        LM_DEBUG("radius: %.2f", radius);
        double theta = wheel_meter_diff / radius;
        LM_DEBUG("theta: %.2f", theta);
        LM_DEBUG("radius: %.2f", radius);
        LM_DEBUG("wheel_meter_diff: %.2f", wheel_meter_diff);
        LM_DEBUG("wheel_meter_diff: %.2f, radius: %.2f, theta: %.2f", wheel_meter_diff, radius, theta);

        EclPose2D origin_pose_change;
        origin_pose_change.setPose(0.0, 0.0, theta);
        LM_DEBUG("diff_ticks(%ld, %ld) origin_pose_change(%.2f, %.2f, %.2f)",
                  diff_ticks[back_left_i], diff_ticks[back_right_i],
                  static_cast<double>(origin_pose_change.x()),
                  static_cast<double>(origin_pose_change.y()),
                  static_cast<double>(origin_pose_change.heading()));
        return origin_pose_change;
    }

    nav_msgs::Odometry UpdateOdom(wr_npu_msgs::MotorEnc enc)
    {
        ROS_ASSERT(EnumString<wizrobo_npu::ChassisModelType>::IsEqual(model_type_, enc.model_type));
        ROS_ASSERT(enc.motor_num == enc.degs.size());
        if (!is_enc_inited_) {
            last_enc_ = enc;
            is_enc_inited_ = true;
        }
        const float overflow_thrs_fac = 0.9f;
        // overflow & jump check
        std::vector<long> diff_ticks;
        for (int i = 0; i < enc.ticks.size(); i++) {
            long diff_tick = enc.ticks[i] - last_enc_.ticks[i];
            if (last_enc_.ticks[i] >= INT_MAX*overflow_thrs_fac
                    && enc.ticks[i] <= INT_MIN*overflow_thrs_fac) {
                // P->N overflow
                diff_tick = 1 + (INT_MAX - INT_MIN) + enc.ticks[i] - last_enc_.ticks[i];
                LM_WARN("Recovered P->N overflow (%ld): ticks[%d]: %d -> %d",
                         diff_tick, i, last_enc_.ticks[i], enc.ticks[i]);
            } else if (last_enc_.ticks[i] <= INT_MIN*overflow_thrs_fac
                       && enc.ticks[i] >= INT_MAX*overflow_thrs_fac) {
                // N->P overflow
                diff_tick = - (1 + (INT_MAX - INT_MIN) + last_enc_.ticks[i] - last_enc_.ticks[i]);
                LM_WARN("Recovered N->P overflow (%ld): ticks[%d]: %d -> %d",
                         diff_tick, i, last_enc_.ticks[i], enc.ticks[i]);
            }
            if (abs(diff_tick) > enc_jump_thrs_tick_) {
                LM_WARN("Found motor_enc jump:(%ld), ticks[%d]: %d -> %d, threshold: %d",
                         diff_tick, i, last_enc_.ticks[i], enc.ticks[i], enc_jump_thrs_tick_);
                last_enc_ = enc;
                return last_odom_;
            }
            diff_ticks.push_back(diff_tick);
        }
        double time_interval = (enc.header.stamp - last_enc_.header.stamp).toSec();
        if (time_interval < 0.0) {
            return last_odom_;
        }
        if (time_interval > 1.0) {
            LM_WARN("Enc udpating is too slow! (%.2lf [s])", time_interval);
        }
        LM_DEBUG("Enc udpating interval: %.2lf", time_interval);
		EclPose2D pose_change;
        pose_change = (enc.carlike_steer_enc_location == EnumString<wizrobo_npu::SteerEncLocation>::EnumToStr(wizrobo_npu::CENTER))
                ?GetPoseChangeSpinAround(diff_ticks):GetPoseChangeCarlike(diff_ticks);
        EclPose2D pose = last_pose_ * pose_change;
        EclVector3D vel;
        if (fabs(time_interval) > 1e-5) {
            vel << pose_change.x() / time_interval,
                    pose_change.y() / time_interval,
                    pose_change.heading() / time_interval;
        }
        vel_end_stamp_ = ros::Time::now();
        ros::Duration d = vel_end_stamp_ - vel_begin_stamp_;
        double _interval = d.toSec();
        if (_interval > vel_interval_) {
            current_vel_ << vel_pose_change_.x() / _interval,
                            vel_pose_change_.y() / _interval,
                            vel_pose_change_.heading() / _interval;
            float vx = vel_pose_change_.x();
            float vyaw =  vel_pose_change_.heading();
            LM_DEBUG("current_vel(%.2f, %.2f) -> (%.2f, %.2f) / %.2f",
                    current_vel_[0], current_vel_[2], vx, vyaw, _interval);
            vel_pose_change_.setPose(0.0, 0.0, 0.0);
            vel_begin_stamp_ = vel_end_stamp_;
        } else {
            vel_pose_change_ *= pose_change;
        }

        nav_msgs::Odometry odom;
        odom.header = enc.header;
        odom.child_frame_id = enc.child_frame_id;

        // position
        odom.pose.pose.position.x = pose.x();
        odom.pose.pose.position.y = pose.y();
        odom.pose.pose.position.z = 0.0;
        // orientation
        odom.pose.pose.orientation = tf::createQuaternionMsgFromYaw(pose.heading());
        // vel
        odom.twist.twist.linear.x = vel[0];
        odom.twist.twist.linear.y = vel[1];
        odom.twist.twist.angular.z = vel[2];

        // Pose covariance (required by robot_pose_ekf) TODO: publish realistic values
        // Odometry yaw covariance must be much bigger than the covariance provided
        // by the imu, as the later takes much better measures
        // TODO: Auto-calibration
        odom.pose.covariance[0] = 0.1;
        odom.pose.covariance[7] = 0.1;
        odom.pose.covariance[35] = 0.2;

        //double orientation_x_stdev, orientation_y_stdev, orientation_z_stdev;
        double orientation_x_stdev = (0.15 * M_PI / 180.0);
        double orientation_y_stdev = (0.15 * M_PI / 180.0);
        double orientation_z_stdev = (0.15 * M_PI / 180.0);

        double orientation_x_covar = orientation_x_stdev * orientation_x_stdev;
        double orientation_y_covar = orientation_y_stdev * orientation_y_stdev;
        double orientation_z_covar = orientation_z_stdev * orientation_z_stdev;

        odom.pose.covariance[14] = orientation_x_covar; //DBL_MAX; // DBL_MAX  set a non-zero covariance on unused
        odom.pose.covariance[21] = orientation_y_covar; //DBL_MAX;// dimensions (z, pitch and roll); this
        odom.pose.covariance[28] = orientation_z_covar; //DBL_MAX; // is a requirement of robot_pose_ekf

        last_enc_ = enc;
        last_pose_ = pose;
        last_odom_ = odom;
        return odom;
    }

    wr_npu_msgs::MotorSpd VelToMotorSpdCarlike(geometry_msgs::Twist& vel)
    {
        LM_DEBUG("vel: %.2f/%.2f", vel.linear.x, vel.angular.z);
        /* In carlike mode, we use only one rpms to indicate the linear velocity of car. */
        const int motor_cnt = 1;
        wr_npu_msgs::MotorSpd spd;
        spd.model_type = EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type_);
        spd.motor_num = motor_cnt;
        spd.rdc_rpss.resize(motor_cnt);
        spd.rdc_rpss[0] = vel.linear.x * mps_to_rps_fac_;
        spd.rpss.resize(motor_cnt);
        spd.rpss[0] = spd.rdc_rpss[0] * motor_param_.motor_rdc_ratio;
        spd.rpms.resize(motor_cnt);
        spd.rpms[0] = spd.rpss[0] * 60.0;
        spd.rdc_rpss.resize(motor_cnt);
        /* Use steer_enc_location to indicate which way the car will do turning. */
        spd.carlike_steer_enc_location = (vel.angular.z >= 0)
            ?EnumString<wizrobo_npu::SteerEncLocation>::EnumToStr(wizrobo_npu::LEFT)
            :EnumString<wizrobo_npu::SteerEncLocation>::EnumToStr(wizrobo_npu::RIGHT);
        if (ABS(vel.angular.z) <= _esp_) {
            spd.carlike_steer_angle_deg = 0.0;
            return spd;
        }

        float radius_origin = ABS(vel.linear.x / vel.angular.z);
        LM_DEBUG("radius_origin: %.2f carlike_steer_enc_location: %s",
                  radius_origin, spd.carlike_steer_enc_location.c_str());

        float back_inner_side_radius =
                sqrt(SQU(radius_origin) - SQU(chassis_param_.carlike_axle_disit_m / 2))
                - chassis_param_.wheel_span_m / 2;
        LM_DEBUG("VelToMotorSpdCarlike carlike_axle_disit_m: %.2f wheel_span_m: %.2f back_inner_side_radius: %.2f",
                  chassis_param_.carlike_axle_disit_m, chassis_param_.wheel_span_m, back_inner_side_radius);
#if 0
        float front_inner_side_radius =
                sqrt(SQU(back_inner_side_radius) + SQU(chassis_param_.carlike_axle_disit_m));
#endif
        float front_inner_side_deg =
                atan2(chassis_param_.carlike_axle_disit_m, back_inner_side_radius);

        /* Variable carlike_steer_angle_deg indicate angle of turning. *
         * And the angle is aways refer to inner side. */
        spd.carlike_steer_angle_deg =
                (vel.angular.z >= 0)?(front_inner_side_deg):(-front_inner_side_deg);
        LM_DEBUG("VelToMotorSpdCarlike front_inner_side_deg: %.2f carlike_steer_angle_deg: %.2f",
                  front_inner_side_deg, spd.carlike_steer_angle_deg);
        return spd;
    }

    wr_npu_msgs::MotorSpd VelToMotorSpdSpinAround(geometry_msgs::Twist& vel)
    {
        const int motor_cnt = 1;
        float radius = sqrt(SQU(chassis_param_.wheel_span_m/2) + SQU(chassis_param_.carlike_axle_disit_m));
        float vel_linear = vel.angular.z * radius;
        LM_DEBUG("VelToMotorSpdCarlike vel: %.2f/%.2f vel_linear: %.2f",
                  vel.linear.x, vel.angular.z, vel_linear);

        wr_npu_msgs::MotorSpd spd;
        spd.model_type = EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type_);
        spd.motor_num = motor_cnt;

        spd.rdc_rpss.resize(motor_cnt);
        spd.rdc_rpss[0] = vel_linear * mps_to_rps_fac_;

        spd.rpss.resize(motor_cnt);
        spd.rpss[0] = spd.rdc_rpss[0] * motor_param_.motor_rdc_ratio;

        spd.rpms.resize(motor_cnt);
        spd.rpms[0] = spd.rpss[0] * 60.0;

        spd.rdc_rpss.resize(motor_cnt);
        spd.carlike_steer_enc_location =
            EnumString<wizrobo_npu::SteerEncLocation>::EnumToStr(wizrobo_npu::CENTER);
        spd.carlike_steer_angle_deg = 0.0;

        LM_DEBUG("VelToMotorSpdSpinAround radius: %.2f carlike_steer_enc_location: %s",
                  radius, spd.carlike_steer_enc_location.c_str());

        return spd;
    }

    wr_npu_msgs::MotorSpd VelToMotorSpd(geometry_msgs::Twist vel)
    {
        int target_drv_mode;
        float v_x = vel.linear.x;
        float a_z = vel.angular.z;
        geometry_msgs::Twist vel_zero;
        vel_zero.linear.x = 0.0;
        vel_zero.angular.z = 0.0;
        //CsgdrvChassisModel::LimitSpd(chassis_param_, v_x, a_z);
        double origin_radius = ABS(v_x / a_z);
        double back_inner_side_radius =
                sqrt(SQU(origin_radius) - SQU(chassis_param_.carlike_axle_disit_m / 2))
                - chassis_param_.wheel_span_m / 2;
        LM_DEBUG("VelToMotorSpd vel: %.4f/%.4f origin_radius: %.2f chassis_param_.wheel_span_m/2: %.2f",
                  v_x, a_z, origin_radius, chassis_param_.wheel_span_m/2);
        target_drv_mode = (ABS(a_z) <= _esp_ || back_inner_side_radius >= 0.3)?mode_carlike:mode_spin_around;
        typedef wr_npu_msgs::MotorSpd (CsgdrvChassisModel::*func_ptr)(geometry_msgs::Twist&);
        func_ptr func_vel_to_motor_spd = (target_drv_mode==mode_carlike)?&CsgdrvChassisModel::VelToMotorSpdCarlike:&CsgdrvChassisModel::VelToMotorSpdSpinAround;
        if (drv_mode_ == target_drv_mode) {
            return (this->*func_vel_to_motor_spd)(vel);
        } else if (drv_mode_ != mode_shifting) {
            drv_mode_ = mode_shifting;
            shift_stamp_ = ros::Time::now();
        } else if ((ros::Time::now() - shift_stamp_).toSec() > shift_interval_) {
            drv_mode_ = target_drv_mode;
        }
        return (this->*func_vel_to_motor_spd)(vel_zero);
    }

    geometry_msgs::Twist MotorSpdToVelCarlike(wr_npu_msgs::MotorSpd& spd)
    {
        geometry_msgs::Twist vel;
        float vel_linear_back_center =
                (spd.rdc_rpss[back_left_i] + spd.rdc_rpss[back_right_i]) / 2
                / mps_to_rps_fac_;

        float vel_angular_back_center =
                (spd.rdc_rpss[back_left_i] - spd.rdc_rpss[back_right_i])
                / chassis_param_.wheel_span_m / mps_to_rps_fac_;
        LM_DEBUG("spd.rdc_rpss[back_left_i]:%.2f", spd.rdc_rpss[back_left_i]);
        LM_DEBUG("spd.rdc_rpss[back_left_i]:%.2f", spd.rdc_rpss[back_right_i]);
        if ( ABS(vel_angular_back_center) <= _esp_ ) {
            vel.linear.x = vel_linear_back_center;
            vel.angular.z = 0.0;
            return vel;
        }
        float radius_back_center = vel_linear_back_center / vel_angular_back_center;
        float radius_origin =
            sqrt(SQU(chassis_param_.carlike_axle_disit_m/2)+SQU(radius_back_center));
        float vel_angular_origin = vel_angular_back_center;
        float vel_linear_origin = ABS(radius_origin * vel_angular_origin) * SIGN(vel_linear_back_center);

        vel.linear.x = vel_linear_origin;
        vel.angular.z = -vel_angular_origin;
        //LM_ERROR("MotorSpdToVelCarlike vel.angle.z: %.2f", vel.angular.z);
        return vel;
    }

    geometry_msgs::Twist MotorSpdToVelSpinAround(wr_npu_msgs::MotorSpd& spd)
    {
        float radius =
            sqrt(SQU(chassis_param_.wheel_span_m/2) + SQU(chassis_param_.carlike_axle_disit_m/2));
        LM_DEBUG("radius: %.2f", radius);
        float vel_linear_wheel = spd.rdc_rpss[back_left_i] / mps_to_rps_fac_;
        LM_DEBUG("spd.rdc_rpss[back_left_i]:%.2f", spd.rdc_rpss[back_left_i]);
        LM_DEBUG("spd.rdc_rpss[back_left_i]:%.2f | mps_to_rps_fac_:%.2f", spd.rdc_rpss[back_left_i], mps_to_rps_fac_);
        LM_DEBUG("vel_linear_wheel: %.2f", vel_linear_wheel);
        float vel_angular_wheel = vel_linear_wheel / radius;
        LM_DEBUG("vel_angular_wheel: %.2f", vel_angular_wheel);
        float vel_angular_origin = vel_angular_wheel;
        LM_DEBUG("vel_angular_origin: %.2f", vel_angular_origin);

        geometry_msgs::Twist vel;
        vel.angular.z = -vel_angular_origin;

        LM_DEBUG("MotorSpdToVelSpinAround vel.angle.z: %.2f", vel.angular.z);
        return vel;
    }

    geometry_msgs::Twist MotorSpdToVel(wr_npu_msgs::MotorSpd spd)
    {
#if 0
        WR_ASSERT(EnumString<ChassisModelType>::IsEqual(model_type_, spd.model_type));
        std::string steer_enc_location = EnumString<wizrobo_npu::SteerEncLocation>::EnumToStr(wizrobo_npu::CENTER);
        //std::string steer_enc_location = "CENTER";
        return (spd.carlike_steer_enc_location == steer_enc_location)
                    ?MotorSpdToVelSpinAround(spd)
                    :MotorSpdToVelCarlike(spd);
#else
        geometry_msgs::Twist vel;
        vel.linear.x = current_vel_[0];
        vel.linear.y = 0.0;
        vel.linear.z = 0.0;
        vel.angular.x = 0.0;
        vel.angular.y = 0.0;
        vel.angular.z = current_vel_[2];
        return vel;
#endif
    }
public:
    static inline void UpdateSpdLimits(const wizrobo_npu::MotorParam& npu_motor_param, wizrobo_npu::ChassisParam& npu_chassis_param)
    {
        MotorParam motor_param(npu_motor_param);
        ChassisParam chassis_param(npu_chassis_param);
        npu_chassis_param.autoset_max_lin_spd_mps = CsgdrvChassisModel::CalMaxLinSpdMps(motor_param, chassis_param);
        npu_chassis_param.autoset_max_ang_spd_rps = CsgdrvChassisModel::CalMaxAngSpdRps(motor_param, chassis_param);
    }

    static inline void UpdateSpdLimits(const MotorParam& motor_param, ChassisParam& chassis_param)
    {
        chassis_param.autoset_max_lin_spd_mps = CsgdrvChassisModel::CalMaxLinSpdMps(motor_param, chassis_param);
        chassis_param.autoset_max_ang_spd_rps = CsgdrvChassisModel::CalMaxAngSpdRps(motor_param, chassis_param);
    }

private:
    static inline float CalMaxLinSpdMps(const MotorParam& motor_param, const ChassisParam& chassis_param)
    {
        return fabs(0.8 * motor_param.motor_max_spd_rpm / 60 / motor_param.motor_rdc_ratio
                    / chassis_param.wheel_rdc_ratio * 2 * M_PI * chassis_param.wheel_radius_m);
    }
    static inline float CalMaxAngSpdRps(const MotorParam& motor_param, const ChassisParam& chassis_param)
    {
        return fabs(CalMaxLinSpdMps(motor_param, chassis_param) / (chassis_param.wheel_span_m / 2));
    }
    static inline void LimitSpd(const ChassisParam& chassis_param, float& lin_spd, float& ang_spd)
    {
        ang_spd = RANGE(ang_spd, -chassis_param.autoset_max_ang_spd_rps, chassis_param.autoset_max_ang_spd_rps);
        lin_spd = RANGE(lin_spd, -chassis_param.autoset_max_lin_spd_mps + fabs(ang_spd * chassis_param.wheel_span_m / 2)
                        , chassis_param.autoset_max_lin_spd_mps - fabs(ang_spd * chassis_param.wheel_span_m / 2));
    }

};
}}

#endif // CSGDRV_CHASSIS_MODEL_H
