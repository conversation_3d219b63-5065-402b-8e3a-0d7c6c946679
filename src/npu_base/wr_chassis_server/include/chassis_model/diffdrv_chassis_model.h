#ifndef WIZROBO_CHASSIS_MODEL_DIFFDRV_CHASSIS_MODEL_H
#define WIZROBO_CHASSIS_MODEL_DIFFDRV_CHASSIS_MODEL_H

#if WR_LEGACY == true
#include <ecl/geometry/legacy_pose2d.hpp>
#else
#include <ecl/geometry/pose2d.hpp>
#endif
#include <ecl/mobile_robot.hpp>
#include <tf/tf.h>

#include <npu.h>
#include <npu_math.h>
#include <npu_enum_ext.h>
#include "chassis_model_interface.h"

namespace wizrobo { namespace chassis_model {
using namespace wizrobo::enum_ext;

typedef ecl::mobile_robot::DifferentialDriveKinematics EclDiffdrvKinematics;

#if WR_LEGACY == true
typedef ecl::LegacyPose2D<double> EclPose2D;
#else
typedef ecl::Pose2D<double> EclPose2D;
#endif

typedef ecl::linear_algebra::Vector3d EclVector3D;

class DiffdrvChassisModel : public IChassisModel
{
public:
    DiffdrvChassisModel()
        : p_ecl_kine_(NULL)
    {
        this->model_type_ = DIFFDRV;
    }

    ~DiffdrvChassisModel()
    {
        RELEASE_POINTER(p_ecl_kine_);
    }

    bool SetParam(const ChassisParam& chassis_param, const MotorParam& motor_param)
    {
        if (chassis_param.wheel_rdc_ratio == 0 || chassis_param.wheel_radius_m == 0) {
            WR_DEBUG("Invalid chassis param, cancel setting");
            return false;
        }
        if (motor_param.motor_rdc_ratio == 0) {
            WR_DEBUG("Invalid motor param, cancel setting");
            return false;
        }
        motor_param_ = motor_param;
        chassis_param_ = chassis_param;
        DiffdrvChassisModel::UpdateSpdLimits(motor_param_, chassis_param_);
        ROS_WARN("- max_lin_spd_mps = %f", chassis_param_.autoset_max_lin_spd_mps);
        ROS_WARN("- max_ang_spd_rps = %f", chassis_param_.autoset_max_ang_spd_rps);

        // internal param
        mps_to_rps_fac_ = 1.0 / (2 * M_PI * chassis_param_.wheel_radius_m) * chassis_param_.wheel_rdc_ratio;
        enc_jump_thrs_tick_ = abs(chassis_param_.autoset_max_lin_spd_mps / (2*M_PI*chassis_param_.wheel_radius_m)
                * chassis_param_.wheel_rdc_ratio * motor_param_.motor_rdc_ratio * motor_param_.motor_enc_res_ppr * 4
                * 10);
        ROS_WARN("- enc_jump_thrs_tick_ = %d", enc_jump_thrs_tick_);

        RELEASE_POINTER(p_ecl_kine_);
        p_ecl_kine_ = new EclDiffdrvKinematics(chassis_param_.wheel_span_m, chassis_param_.wheel_radius_m);
        is_enc_inited_ = false;

        return true;
    }

    nav_msgs::Odometry UpdateOdom(wr_npu_msgs::MotorEnc enc)
    {
        ROS_ASSERT(EnumString<wizrobo_npu::ChassisModelType>::IsEqual(model_type_, enc.model_type));
        ROS_ASSERT(enc.motor_num == enc.degs.size());
        if (!is_enc_inited_) {
            last_enc_ = enc;
            is_enc_inited_ = true;
        }
        const float overflow_thrs_fac = 0.9f;
        // overflow & jump check
        std::vector<long> diff_ticks;
        for (int i = 0; i < enc.ticks.size(); i++) {
            long diff_tick = enc.ticks[i] - last_enc_.ticks[i];
            if (last_enc_.ticks[i] >= INT_MAX*overflow_thrs_fac && enc.ticks[i] <= INT_MIN*overflow_thrs_fac) { // P->N overflow
                diff_tick = 1 + (INT_MAX - INT_MIN) + enc.ticks[i] - last_enc_.ticks[i];
                ROS_WARN("Recovered P->N overflow (%ld): ticks[%d]: %d -> %d", diff_tick, i, last_enc_.ticks[i], enc.ticks[i]);
            } else if (last_enc_.ticks[i] <= INT_MIN*overflow_thrs_fac && enc.ticks[i] >= INT_MAX*overflow_thrs_fac) { // N->P overflow
                diff_tick = - (1 + (INT_MAX - INT_MIN) + last_enc_.ticks[i] - last_enc_.ticks[i]);
                ROS_WARN("Recovered N->P overflow (%ld): ticks[%d]: %d -> %d", diff_tick, i, last_enc_.ticks[i], enc.ticks[i]);
            }
            if (abs(diff_tick) > enc_jump_thrs_tick_) {
                ROS_WARN("Found motor_enc jump (%ld): ticks[%d]: %d -> %d", diff_tick, i, last_enc_.ticks[i], enc.ticks[i]);
                ROS_WARN("enc_jump_thrs_tick = %d", enc_jump_thrs_tick_);
                last_enc_ = enc;
                return last_odom_;
            }
            diff_ticks.push_back(diff_tick);
        }
        double left_wheel_rad_diff = 1.0 * diff_ticks[0] / (4 * motor_param_.motor_enc_res_ppr) * 2 * M_PI
                / motor_param_.motor_rdc_ratio / chassis_param_.wheel_rdc_ratio;
        double right_wheel_rad_diff = 1.0 * diff_ticks[1] / (4 * motor_param_.motor_enc_res_ppr) * 2 * M_PI
                / motor_param_.motor_rdc_ratio / chassis_param_.wheel_rdc_ratio;
        EclPose2D pose_change = p_ecl_kine_->forward(left_wheel_rad_diff, right_wheel_rad_diff);
        double time_interval = (enc.header.stamp - last_enc_.header.stamp).toSec();
        if (time_interval < 0) {
            return last_odom_;
        }
        if (time_interval > 1.0) {
            ROS_WARN("Enc udpating is too slow! (%.2lf [s])", time_interval);
        }
        last_enc_ = enc;
        EclVector3D vel;
        if (fabs(time_interval) > 1e-5) {
            vel << pose_change.x() / time_interval,
                    pose_change.y() / time_interval,
                    pose_change.heading() / time_interval;
        }
        EclPose2D pose = last_pose_ * pose_change;
        last_pose_ = pose;
        nav_msgs::Odometry odom;
        odom.header = enc.header;
        odom.child_frame_id = enc.child_frame_id;

        // position
        odom.pose.pose.position.x = pose.x();
        odom.pose.pose.position.y = pose.y();
        odom.pose.pose.position.z = 0.0;
        // orientation
        odom.pose.pose.orientation = tf::createQuaternionMsgFromYaw(pose.heading());
        // vel
        odom.twist.twist.linear.x = vel[0];
        odom.twist.twist.linear.y = vel[1];
        odom.twist.twist.angular.z = vel[2];

        // Pose covariance (required by robot_pose_ekf) TODO: publish realistic values
        // Odometry yaw covariance must be much bigger than the covariance provided
        // by the imu, as the later takes much better measures
        // TODO: Auto-calibration	
        odom.pose.covariance[0] = 0.1f;
        odom.pose.covariance[7] = 0.1f;
        odom.pose.covariance[35] = 0.2f;

        //double orientation_x_stdev, orientation_y_stdev, orientation_z_stdev;
//        double orientation_x_stdev = (0.15 * M_PI / 180.0);
//        double orientation_y_stdev = (0.15 * M_PI / 180.0);
//        double orientation_z_stdev = (0.15 * M_PI / 180.0);

//        double orientation_x_covar = orientation_x_stdev * orientation_x_stdev;
//        double orientation_y_covar = orientation_y_stdev * orientation_y_stdev;
//        double orientation_z_covar = orientation_z_stdev * orientation_z_stdev;

        odom.pose.covariance[14] = 1e10;//orientation_x_covar; //DBL_MAX; // DBL_MAX  set a non-zero covariance on unused
        odom.pose.covariance[21] = 1e10; //DBL_MAX;// dimensions (z, pitch and roll); this
        odom.pose.covariance[28] = 1e10; //DBL_MAX; // is a requirement of robot_pose_ekf

        last_odom_ = odom;
        last_enc_ = enc;
        return odom;
    }

    wr_npu_msgs::MotorSpd VelToMotorSpd(geometry_msgs::Twist vel)
    {
        wr_npu_msgs::MotorSpd spd;
        spd.model_type = EnumString<ChassisModelType>::EnumToStr(model_type_);
        spd.motor_num = 2;
        spd.rdc_rpss.resize(2);

        float vel_x = vel.linear.x;
        float ang_z = vel.angular.z;
        DiffdrvChassisModel::LimitSpd(chassis_param_, vel_x, ang_z);

        spd.rdc_rpss[0] = (vel_x - ang_z * chassis_param_.wheel_span_m / 2) * mps_to_rps_fac_;
        spd.rdc_rpss[1] = (vel_x + ang_z * chassis_param_.wheel_span_m / 2) * mps_to_rps_fac_;
        spd.rpss.resize(spd.rdc_rpss.size());
        spd.rpms.resize(spd.rdc_rpss.size());
        for (int i = 0; i < spd.rdc_rpss.size(); i++) {
            spd.rpss[i] = spd.rdc_rpss[i] * motor_param_.motor_rdc_ratio;
            spd.rpms[i] = spd.rpss[i] * 60.0;
        }
        return spd;
    }

    geometry_msgs::Twist MotorSpdToVel(wr_npu_msgs::MotorSpd spd)
    {
        WR_ASSERT(EnumString<ChassisModelType>::IsEqual(model_type_, spd.model_type));
        geometry_msgs::Twist vel;
        vel.linear.x = (spd.rdc_rpss[0] + spd.rdc_rpss[1]) / 2 / mps_to_rps_fac_;
        vel.angular.z = (spd.rdc_rpss[1] - spd.rdc_rpss[0]) / chassis_param_.wheel_span_m / mps_to_rps_fac_;
        return vel;
    }
public:
    static inline void UpdateSpdLimits(const wizrobo_npu::MotorParam& npu_motor_param, wizrobo_npu::ChassisParam& npu_chassis_param)
    {
        MotorParam motor_param(npu_motor_param);
        ChassisParam chassis_param(npu_chassis_param);
        npu_chassis_param.autoset_max_lin_spd_mps = DiffdrvChassisModel::CalMaxLinSpdMps(motor_param, chassis_param);
        npu_chassis_param.autoset_max_ang_spd_rps = DiffdrvChassisModel::CalMaxAngSpdRps(motor_param, chassis_param);
    }

    static inline void UpdateSpdLimits(const MotorParam& motor_param, ChassisParam& chassis_param)
    {
        chassis_param.autoset_max_lin_spd_mps = DiffdrvChassisModel::CalMaxLinSpdMps(motor_param, chassis_param);
        chassis_param.autoset_max_ang_spd_rps = DiffdrvChassisModel::CalMaxAngSpdRps(motor_param, chassis_param);
    }

private:
    static inline float CalMaxLinSpdMps(const MotorParam& motor_param, const ChassisParam& chassis_param)
    {
        return fabs(0.8 * motor_param.motor_max_spd_rpm / 60 / motor_param.motor_rdc_ratio
                    / chassis_param.wheel_rdc_ratio * 2 * M_PI * chassis_param.wheel_radius_m);
    }
    static inline float CalMaxAngSpdRps(const MotorParam& motor_param, const ChassisParam& chassis_param)
    {
        return fabs(CalMaxLinSpdMps(motor_param, chassis_param) / (chassis_param.wheel_span_m / 2));
    }
    static inline void LimitSpd(const ChassisParam& chassis_param, float& lin_spd, float& ang_spd)
    {
        ang_spd = RANGE(ang_spd, -chassis_param.autoset_max_ang_spd_rps, chassis_param.autoset_max_ang_spd_rps);
        lin_spd = RANGE(lin_spd, -chassis_param.autoset_max_lin_spd_mps + fabs(ang_spd * chassis_param.wheel_span_m / 2)
                        , chassis_param.autoset_max_lin_spd_mps - fabs(ang_spd * chassis_param.wheel_span_m / 2));
    }
private:
    EclDiffdrvKinematics* p_ecl_kine_;
    double mps_to_rps_fac_;
    EclPose2D last_pose_;
};
}}
#endif // WIZROBO_CHASSIS_MODEL_DIFFDRV_CHASSIS_MODEL_H
