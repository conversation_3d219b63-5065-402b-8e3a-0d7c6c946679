#ifndef WIZROBO_CHASSIS_MODEL_CHASSIS_MODEL_PARAM_H
#define WIZROBO_CHASSIS_MODEL_CHASSIS_MODEL_PARAM_H
#include <npu.h>
#include <npu_enum_ext.h>
#include <npuice_enum_ext.h>
#include <wr_npu_msgs/MotorEnc.h>
namespace wizrobo {  namespace chassis_model {
// bumper location
enum BumperLocation
{
    FRONT_BUMPER,
    FRONT_LEFT_BUMPER,
    FRONT_RIGHT_BUMPER,
    BACK_BUMPER,
    BACK_LEFT_BUMPER,
    BACK_RIGHT_BUMPER,
    LEFT_BUMPER,
    RIGHT_BUMPER,
};

// chassis param
struct ChassisParam
{
    ChassisParam()
    {
        model_type = wizrobo_npu::DIFFDRV;
        wheel_rdc_ratio = 1.0;
        wheel_radius_m = 0.05;
        wheel_span_m = 0.5;
        max_lin_acc_time_s = 3.0f;
        max_ang_acc_time_s = 10.0f;
        autoset_max_lin_spd_mps = 0;
        autoset_max_ang_spd_rps = 0;
        carlike_steer_enc_location = wizrobo_npu::CENTER;
        carlike_axle_disit_m = 0.0;
    }

    ChassisParam(const wizrobo_npu::ChassisParam& npu_chassis_param)
    {
        this->model_type = npu_chassis_param.model_type;
        this->wheel_rdc_ratio = fabs(npu_chassis_param.wheel_rdc_ratio);
        this->wheel_radius_m = fabs(npu_chassis_param.wheel_radius_m);
        this->wheel_span_m = fabs(npu_chassis_param.wheel_span_m);
        this->autoset_max_lin_spd_mps = fabs(npu_chassis_param.autoset_max_lin_spd_mps);
        this->autoset_max_ang_spd_rps = fabs(npu_chassis_param.autoset_max_ang_spd_rps);
        this->max_lin_acc_time_s = npu_chassis_param.max_lin_acc_time_s;
        this->max_ang_acc_time_s = npu_chassis_param.max_ang_acc_time_s;
        this->carlike_steer_enc_location = npu_chassis_param.carlike_steer_enc_location;
        this->carlike_axle_disit_m = npu_chassis_param.carlike_axle_dist_m;
    }

    ChassisParam& operator=(const ChassisParam& param_in)
    {
        this->model_type = param_in.model_type;
        this->wheel_rdc_ratio = fabs(param_in.wheel_rdc_ratio);
        this->wheel_radius_m = fabs(param_in.wheel_radius_m);
        this->wheel_span_m = fabs(param_in.wheel_span_m);
        this->autoset_max_lin_spd_mps = fabs(param_in.autoset_max_lin_spd_mps);
        this->autoset_max_ang_spd_rps = fabs(param_in.autoset_max_ang_spd_rps);
        this->max_lin_acc_time_s = param_in.max_lin_acc_time_s;
        this->max_ang_acc_time_s = param_in.max_ang_acc_time_s;
        this->carlike_steer_enc_location = param_in.carlike_steer_enc_location;
        this->carlike_axle_disit_m = param_in.carlike_axle_disit_m;
    }
    std::string ToStr() const
    {
        std::stringstream ss;
        ss << "\n-------- ChassisParam --------\n"
           << "- model_type: " << EnumString<wizrobo_npu::ChassisModelType>::EnumToStr(model_type) << "\n"
           << "- wheel_rdc_ratio: " << wheel_rdc_ratio << "\n"
           << "- wheel_radius_m: " << wheel_radius_m << "\n"
           << "- wheel_span_m: " << wheel_span_m << "\n"
           << "- max_lin_spd_mps: " << autoset_max_lin_spd_mps << "\n"
           << "- max_ang_spd_rps: " << autoset_max_ang_spd_rps << "\n"
           << "- max_lin_acc_time_s: " << max_lin_acc_time_s << "\n"
           << "- max_ang_acc_time_s: " << max_ang_acc_time_s << "\n"
           << "- carlike_steer_enc_location: " << EnumString<wizrobo_npu::SteerEncLocation>::EnumToStr(carlike_steer_enc_location) << "\n"
           << "- carlike_axle_disit_m: " << carlike_axle_disit_m << "\n"
           ;
        return ss.str();
    }
    /// common
    wizrobo_npu::ChassisModelType model_type;
    // wheel
    float wheel_rdc_ratio;// [x:1]
    float wheel_radius_m;// [m]
    float wheel_span_m;// [m]
    //// speed limits
    //// acc
    float autoset_max_lin_spd_mps;
    float autoset_max_ang_spd_rps;
    float max_lin_acc_time_s;
    float max_ang_acc_time_s;
    /// carlike
    wizrobo_npu::SteerEncLocation carlike_steer_enc_location;
    float carlike_axle_disit_m;
};
}}// namespace

namespace wizrobo { namespace enum_ext {
using namespace wizrobo::chassis_model;

// string support of BumperLocation
BEGIN_ENUM_STRING(BumperLocation)
{
    ENUM_STRING(FRONT_BUMPER);
    ENUM_STRING(FRONT_LEFT_BUMPER);
    ENUM_STRING(FRONT_RIGHT_BUMPER);
    ENUM_STRING(BACK_BUMPER);
    ENUM_STRING(BACK_LEFT_BUMPER);
    ENUM_STRING(BACK_RIGHT_BUMPER);
    ENUM_STRING(LEFT_BUMPER);
    ENUM_STRING(RIGHT_BUMPER);
}
END_ENUM_STRING;

}}// namespace

#endif // WIZROBO_CHASSIS_MODEL_CHASSIS_MODEL_PARAM_H
