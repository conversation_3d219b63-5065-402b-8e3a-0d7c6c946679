#ifndef WIZROBO_CHASSIS_MODEL_CHASSIS_MODEL_INTERFACE_H
#define WIZROBO_CHASSIS_MODEL_CHASSIS_MODEL_INTERFACE_H
#include <geometry_msgs/Twist.h>
#include <nav_msgs/Odometry.h>
#include <wr_npu_msgs/MotorEnc.h>
#include <wr_npu_msgs/MotorSpd.h>
#include <npu.h>
#include <bcu_driver_param.h>

#include "chassis_model_param.h"

namespace wizrobo { namespace chassis_model {
using namespace wizrobo::bcu_driver;
class IChassisModel
{
public:
    virtual ~IChassisModel()
    {}

    inline wizrobo_npu::ChassisModelType GetModelType()
    {
        return model_type_;
    }
    inline bool GetParam(ChassisParam& param)
    {
        param = chassis_param_;
        return true;
    }
    virtual bool SetParam(const ChassisParam& chassis_param, const MotorParam& motor_param) = 0;
    virtual nav_msgs::Odometry UpdateOdom(wr_npu_msgs::MotorEnc enc) = 0;
    virtual wr_npu_msgs::MotorSpd VelToMotorSpd(geometry_msgs::Twist vel) = 0;
    virtual geometry_msgs::Twist MotorSpdToVel(wr_npu_msgs::MotorSpd spd) = 0;
    virtual inline void ClearMotorEnc()
    {
        last_enc_.ticks.resize(last_enc_.ticks.size(), 0);
        last_enc_.degs.resize(last_enc_.ticks.size(), 0);
        last_enc_.rdc_degs.resize(last_enc_.ticks.size(), 0);
    }

protected:
    IChassisModel()
        : is_enc_inited_(false)
        , enc_jump_thrs_tick_(INT_MAX)
        //, max_move_spd_mps_(0)
    {
    }

protected:
    wizrobo_npu::ChassisModelType model_type_;
    ChassisParam chassis_param_;
    MotorParam motor_param_;
    bool is_enc_inited_;
    wr_npu_msgs::MotorEnc last_enc_;
    nav_msgs::Odometry last_odom_;
    int enc_jump_thrs_tick_;
//    float max_move_spd_mps_;//[m/s]
};

}}
#endif // WIZROBO_CHASSIS_MODEL_CHASSIS_MODEL_INTERFACE_H
