-------------------------------------------------
[2017-01-17] by <PERSON><PERSON><PERSON>@wizrobo.com
1. Add function of get IMU data from board. Use Function getAcc(),getGyr(),getMag() to get IMU data of accel, angular speed and roll pitch yaw.
2. Use EKF to estimate robot pose. EKF data is gotten from Functions in 1.
3. Stop subscribe topic of IMU, the subscribers have been commented.

-------------------------------------------------
[2016-06-08] by <EMAIL>
1. Fix a bug caused by unmatched parameter orders between WrBase::SetParam() and WrBaseCore::SetParam().
-------------------------------------------------
[2016-05-28] by <EMAIL>
1. Implement SetMaxRPM(),SetMax() &GetMaxRPM(), GetMax() in class WrBase.
2. Namechanging: reduce_ratio -> reducer_ratio, max_spd_rpm -> max_rpm
3. Add settings of max_lin_vel and max_ang_vel.
4. Add ENB_PID, DIS_PID, GET_MAX, SET_MAX in wrbase_test.

-------------------------------------------------
[2016-05-27] by <EMAIL>

1. Add GetMax() & SetMax() to WrBaseCore class.
2. Optimize key params of WrBaseCore as: motor_enc_line_num, motor_max_spd_rpm, reduer_ratio, wheel_radius, wheel_span, max_lin_vel, max_ang_vel and display all of them when inited. Also, corresponding strings in launch files are modified.
3. Remove all useless params
4. Remove protocol definitions into wrbase_core_protocol.h
5. Update CmdType definition according to latest communication protocols.
6. Change every "ARL" tag into "WR" tag.
-------------------------------------------------
[2016-05-26] by <EMAIL>

1. Add mechanism in CMakeList.txt. Which would disable the hasp encryption when and only when an environment variable called "WR_LOCK" is defined as "false".
2. Redirect the arm and x86_64 versions of the hasp static libraries into wr_hasp/lib/. 
3. Add a mechanism in CMakeLists.txt to automatically choose right version of hasp static libraries according to the valuel of "CMAKE_SYSTEM_PROCESSOR".
4. Add scripts to create and delete udev rules of wrbase into "scripts" folder.
