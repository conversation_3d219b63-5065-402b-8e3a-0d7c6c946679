#!/usr/bin/env python
# chassis_server_node configuration

PACKAGE='wr_chassis_server'
NODE='chassis_server_node'
FILE_NAME='ChassisServer'

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

#### logger level
logger_level_enum = gen.enum([gen.const("DEBUG",  int_t,  0,  "DEBUG"),
                              gen.const("INFO",   int_t,  1,  "INFO"),
                              gen.const("WARN",   int_t,  2,  "WARN"),
                              gen.const("ERROR",  int_t,  3,  "ERROR"),
                              gen.const("FATAL",  int_t,  4,  "FATAL")], "logger_level_enum")
gen.add("logger_level",  int_t,  0,  "logger_level",  0,  0,  4,  edit_method=logger_level_enum)
gen.add("loop_rate",     int_t,  0,  "loop_rate",     100, 1)

#### model type
model_type_enum = gen.enum([ gen.const("CARLIKE",     int_t,  0,  "CARLIKE"),
                              gen.const("DIFFDRV",    int_t,  1,  "DIFFDRV"),
                              gen.const("UNIVWHEEL",  int_t,  2,  "UNIVWHEEL"),
                              gen.const("OMNIWHEEL",  int_t,  3,  "OMNIWHEEL")], "model_type_enum")
gen.add("model_type",  int_t,  0,  "model_type",  0,  0,  3,  edit_method=model_type_enum)

#### chassis param
gen.add("wheel_rdc_ratio",    double_t, 0, "wheel_rdc_ratio",     1.0,   0)
gen.add("wheel_radius_m",     double_t, 0, "wheel_radius_m",      0.0625, 0)
gen.add("wheel_span_m",       double_t, 0, "wheel_span_m",        0.50,   0)
gen.add("max_lin_spd_mps",    double_t, 0, "max_lin_spd_mps",     0.50,   0)
gen.add("max_ang_spd_dps",    double_t, 0, "max_ang_spd_dps",     90,     0)

exit(gen.generate(PACKAGE, NODE, FILE_NAME))
