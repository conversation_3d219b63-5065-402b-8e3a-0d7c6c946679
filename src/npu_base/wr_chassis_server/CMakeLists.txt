cmake_minimum_required(VERSION 2.8.3)
project(wr_chassis_server)

#c++11 support
include(CheckCXXCompilerFlag)
CHECK_CXX_COMPILER_FLAG("-std=c++11" COMPILER_SUPPORTS_CXX11)
CHECK_CXX_COMPILER_FLAG("-std=c++0x" COMPILER_SUPPORTS_CXX0X)

if(COMPILER_SUPPORTS_CXX11)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
elseif(COMPILER_SUPPORTS_CXX0X)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++0x")
else()
     message(STATUS "The compiler ${CMAKE_CXX_COMPILER} has no C++11 support. Please use a different C++ compiler.")
endif()

## Find catkin macros and libraries
## if COMPONENTS list like find_package(catkin REQUIRED COMPONENTS xyz)
## is used, also find other catkin packages
find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  nodelet
  geometry_msgs
  sensor_msgs
  nav_msgs 
  dynamic_reconfigure
  tf
  ecl_mobile_robot
  wr_npu_core
  wr_npu_msgs
  wr_npu_ice
  wr_bcu_server
)

## System dependencies are found with CMake's conventions
# find_package(Boost REQUIRED COMPONENTS system)
#find_package(Eigen3 REQUIRED)

#find_package(Ice REQUIRED Ice IceUtil)

## Uncomment this if the package has a setup.py. This macro ensures
## modules and global scripts declared therein get installed
## See http://ros.org/doc/api/catkin/html/user_guide/setup_dot_py.html
# catkin_python_setup()

################################################
## Declare ROS messages, services and actions ##
################################################

## To declare and build messages, services or actions from within this
## package, follow these steps:
## * Let MSG_DEP_SET be the set of packages whose message types you use in
##   your messages/services/actions (e.g. std_msgs, actionlib_msgs, ...).
## * In the file package.xml:
##   * add a build_depend and a run_depend tag for each package in MSG_DEP_SET
##   * If MSG_DEP_SET isn't empty the following dependencies might have been
##     pulled in transitively but can be declared for certainty nonetheless:
##     * add a build_depend tag for "message_generation"
##     * add a run_depend tag for "message_runtime"
## * In this file (CMakeLists.txt):
##   * add "message_generation" and every package in MSG_DEP_SET to
##     find_package(catkin REQUIRED COMPONENTS ...)
##   * add "message_runtime" and every package in MSG_DEP_SET to
##     catkin_package(CATKIN_DEPENDS ...)
##   * uncomment the add_*_files sections below as needed
##     and list every .msg/.srv/.action file to be processed
##   * uncomment the generate_messages entry below
##   * add every package in MSG_DEP_SET to generate_messages(DEPENDENCIES ...)

## Generate messages in the 'msg' folder
# add_message_files(
#   FILES
# )

## Generate services in the 'srv' folder
# add_service_files(
#   FILES
#   Service1.srv
#   Service2.srv
# )

## Generate actions in the 'action' folder
# add_action_files(
#   FILES
#   Action1.action
#   Action2.action
# )

## Generate added messages and services with any dependencies listed here
# generate_messages(
#   DEPENDENCIES
#   geometry_msgs
#   std_msgs
# )

generate_dynamic_reconfigure_options(
    cfg/ChassisServer.cfg
)

###################################
## catkin specific configuration ##
###################################
## The catkin_package macro generates cmake config files for your package
## Declare things to be passed to dependent projects
## INCLUDE_DIRS: uncomment this if you package contains header files
## LIBRARIES: libraries you create in this project that dependent projects also need
## CATKIN_DEPENDS: catkin_packages dependent projects also need
## DEPENDS: system dependencies of this project that dependent projects also need
catkin_package(
  INCLUDE_DIRS include include/chassis_model
  LIBRARIES chassis_server_nodelet
#  LIBRARIES chassis_server_node
#  CATKIN_DEPENDS message_runtime roscpp rospy
#            std_msgs geometry_msgs sensor_msgs nav_msgs
#            tf angles
#            kobuki_msgs kobuki_driver kobuki_keyop kobuki_safety_controller
#            ecl_exceptions ecl_sigslots ecl_streams ecl_threads
#            wr_npu_core wr_npu_msgs wr_chassis_server
#  DEPENDS system_lib
)

###########
## Build ##
###########

## Specify additional locations of header files
## Your package locations should be listed before other locations
 include_directories(
  ${WR_LOCK_INCLUDE}
  ${catkin_INCLUDE_DIRS}
  #${ICE_INCLUDE_DIRS}
  #${EIGEN3_INCLUDE_DIR}
  include
  include/chassis_model
)

## Declare a cpp library
 add_library(wr_chassis_model
     src/chassis_model/diffdrv_chassis_model.cpp
     src/chassis_model/csgdrv_chassis_model.cpp
 )

add_library(chassis_server_nodelet
    src/chassis_server.cpp
    src/chassis_server_nodelet.cpp
)

add_executable(chassis_server_node
    src/chassis_server_node.cpp
)

add_dependencies(wr_chassis_model
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

add_dependencies(chassis_server_nodelet
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

add_dependencies(chassis_server_node
    ${PROJECT_NAME}_gencfg
    ${wr_npu_msgs_EXPORTED_TARGETS}
)

## Declare a cpp executable
# add_executable(wr_basecontrol_node src/wr_basecontrol_node.cpp)

## Add cmake target dependencies of the executable/library
## as an example, message headers may need to be generated before nodes
#add_dependencies(chassis_server_node wr_npu_msgs_generate_messages_cpp)

## Specify libraries to link a library or executable target against

target_link_libraries(wr_chassis_model
   ${catkin_LIBRARIES}
   #${EIGEN3_catkin_LIBRARIES}
)

target_link_libraries(chassis_server_nodelet
   ${catkin_LIBRARIES}
   #${EIGEN3_catkin_LIBRARIES}
   wr_chassis_model
)

target_link_libraries(chassis_server_node
  ${WR_LOCK_LIBS}
  ${catkin_LIBRARIES}
  #${Ice_LIBRARIES}
  wr_chassis_model
  chassis_server_nodelet
)
#############
## Install ##
#############

# all install targets should use catkin DESTINATION variables
# See http://ros.org/doc/api/catkin/html/adv_user_guide/variables.html

## Mark executable scripts (Python etc.) for installation
## in contrast to setup.py, you can choose the destination
# install(PROGRAMS
#   scripts/my_python_script
#   DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
# )

### Mark executables and/or libraries for installation
install(TARGETS wr_chassis_model chassis_server_node chassis_server_nodelet
 ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
 LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
 RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

### Mark cpp header files for installation
install(DIRECTORY include/
  DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}
)

install(DIRECTORY launch
 DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
 USE_SOURCE_PERMISSIONS
)

install(FILES plugin/bcu_chassis_nodelet_plugin.xml
   DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)

#install(FILES script/57-wrbase.rules
#  DESTINATION ./rules
#)


## Mark other files for installation (e.g. launch and bag files, etc.)
# install(FILES
#   # myfile1
#   # myfile2
#   DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
# )

#############
## Testing ##
#############

## Add gtest based cpp test target and link libraries
# catkin_add_gtest(${PROJECT_NAME}-test test/test_wr_basecontrol.cpp)
# if(TARGET ${PROJECT_NAME}-test)
#   target_link_libraries(${PROJECT_NAME}-test ${PROJECT_NAME})
# endif()

## Add folders to be run by python nosetests
# catkin_add_nosetests(test)
